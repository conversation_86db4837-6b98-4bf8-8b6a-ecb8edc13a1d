#!/usr/bin/env node

/**
 * Component Size Checker
 * 
 * Automated script to enforce component size guidelines and prevent
 * large components from being committed to the codebase.
 * 
 * Usage:
 *   node scripts/check-component-sizes.js
 *   npm run check-component-sizes
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CONFIG = {
  // Size limits by component type
  limits: {
    page: 300,        // Page components (src/pages/)
    feature: 250,     // Feature components (complex functionality)
    ui: 200,          // UI components (src/components/ui/)
    utility: 150     // Utility components (helpers, hooks)
  },
  
  // Directories to scan
  scanDirs: [
    'src/pages',
    'src/components',
    'src/hooks',
    'src/utils'
  ],
  
  // File patterns to include
  includePatterns: [
    /\.(tsx|ts|jsx|js)$/,
    /^[A-Z].*\.(tsx|ts)$/  // Component files (PascalCase)
  ],
  
  // Files to exclude
  excludePatterns: [
    /\.test\./,
    /\.spec\./,
    /\.stories\./,
    /\.d\.ts$/,
    /index\.(ts|js)$/
  ],
  
  // Exit codes
  exitCodes: {
    success: 0,
    violations: 1,
    error: 2
  }
};

/**
 * Get component type based on file path
 */
function getComponentType(filePath) {
  if (filePath.includes('/pages/')) return 'page';
  if (filePath.includes('/components/ui/')) return 'ui';
  if (filePath.includes('/hooks/') || filePath.includes('/utils/')) return 'utility';
  return 'feature';
}

/**
 * Count lines in a file (excluding empty lines and comments)
 */
function countLines(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    let count = 0;
    let inBlockComment = false;
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      // Skip empty lines
      if (!trimmed) continue;
      
      // Handle block comments
      if (trimmed.includes('/*')) {
        inBlockComment = true;
      }
      if (trimmed.includes('*/')) {
        inBlockComment = false;
        continue;
      }
      if (inBlockComment) continue;
      
      // Skip single-line comments
      if (trimmed.startsWith('//')) continue;
      
      count++;
    }
    
    return count;
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return 0;
  }
}

/**
 * Check if file should be included in scan
 */
function shouldIncludeFile(filePath) {
  const fileName = path.basename(filePath);
  
  // Check exclude patterns first
  for (const pattern of CONFIG.excludePatterns) {
    if (pattern.test(fileName) || pattern.test(filePath)) {
      return false;
    }
  }
  
  // Check include patterns
  for (const pattern of CONFIG.includePatterns) {
    if (pattern.test(fileName) || pattern.test(filePath)) {
      return true;
    }
  }
  
  return false;
}

/**
 * Recursively scan directory for component files
 */
function scanDirectory(dirPath, results = []) {
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other common directories
        if (!item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath, results);
        }
      } else if (stat.isFile() && shouldIncludeFile(fullPath)) {
        results.push(fullPath);
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}:`, error.message);
  }
  
  return results;
}

/**
 * Analyse component sizes
 */
function analyzeComponents() {
  console.log('🔍 COMPONENT SIZE ANALYSIS');
  console.log('='.repeat(50));
  
  const violations = [];
  const results = [];
  
  // Scan all configured directories
  for (const scanDir of CONFIG.scanDirs) {
    const fullPath = path.resolve(scanDir);
    if (fs.existsSync(fullPath)) {
      console.log(`📂 Scanning: ${scanDir}`);
      const files = scanDirectory(fullPath);
      
      for (const filePath of files) {
        const lineCount = countLines(filePath);
        const componentType = getComponentType(filePath);
        const limit = CONFIG.limits[componentType];
        const relativePath = path.relative(process.cwd(), filePath);
        
        const result = {
          file: relativePath,
          lines: lineCount,
          type: componentType,
          limit: limit,
          violation: lineCount > limit,
          excess: Math.max(0, lineCount - limit)
        };
        
        results.push(result);
        
        if (result.violation) {
          violations.push(result);
        }
      }
    } else {
      console.log(`⚠️  Directory not found: ${scanDir}`);
    }
  }
  
  return { results, violations };
}

/**
 * Generate report
 */
function generateReport(results, violations) {
  console.log(`\n📊 ANALYSIS RESULTS`);
  console.log('='.repeat(30));
  console.log(`Total files analyzed: ${results.length}`);
  console.log(`Components within limits: ${results.length - violations.length}`);
  console.log(`Components exceeding limits: ${violations.length}`);
  
  if (violations.length > 0) {
    console.log(`\n❌ SIZE VIOLATIONS (${violations.length} files)`);
    console.log('='.repeat(40));
    
    // Sort by excess lines (worst first)
    violations.sort((a, b) => b.excess - a.excess);
    
    for (const violation of violations) {
      const excessPercent = Math.round((violation.excess / violation.limit) * 100);
      console.log(`\n📄 ${violation.file}`);
      console.log(`   Lines: ${violation.lines} (limit: ${violation.limit})`);
      console.log(`   Type: ${violation.type}`);
      console.log(`   Excess: ${violation.excess} lines (+${excessPercent}%)`);
    }
    
    console.log(`\n🔧 RECOMMENDATIONS`);
    console.log('='.repeat(20));
    console.log('1. Refactor largest components first');
    console.log('2. Extract reusable sub-components');
    console.log('3. Move static content to separate files');
    console.log('4. Use composition over large monolithic components');
    console.log('\nSee COMPONENT_SIZE_GUIDELINES.md for detailed refactoring strategies.');
  } else {
    console.log(`\n✅ All components are within size limits!`);
  }
  
  // Generate JSON report
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalFiles: results.length,
      compliantFiles: results.length - violations.length,
      violatingFiles: violations.length,
      worstViolation: violations.length > 0 ? violations[0] : null
    },
    violations: violations,
    limits: CONFIG.limits
  };
  
  fs.writeFileSync('component-size-report.json', JSON.stringify(report, null, 2));
  console.log(`\n📄 Detailed report saved to: component-size-report.json`);
  
  return violations.length === 0;
}

/**
 * Main execution
 */
function main() {
  try {
    const { results, violations } = analyzeComponents();
    const success = generateReport(results, violations);
    
    if (success) {
      console.log(`\n🎉 Component size check passed!`);
      process.exit(CONFIG.exitCodes.success);
    } else {
      console.log(`\n💥 Component size check failed!`);
      console.log(`Found ${violations.length} components exceeding size limits.`);
      process.exit(CONFIG.exitCodes.violations);
    }
  } catch (error) {
    console.error('❌ Error during component size analysis:', error.message);
    process.exit(CONFIG.exitCodes.error);
  }
}

// Run if called directly
if (process.argv[1] && process.argv[1].endsWith('check-component-sizes.js')) {
  main();
}

export { analyzeComponents, generateReport, CONFIG };
