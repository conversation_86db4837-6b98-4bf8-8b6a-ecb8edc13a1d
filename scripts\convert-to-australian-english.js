#!/usr/bin/env node

/**
 * Convert American English to Australian English Script
 * 
 * This script systematically converts all American English spellings to Australian English
 * throughout the entire codebase, ensuring consistency with Australian language standards.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🇦🇺 CONVERTING TO AUSTRALIAN ENGLISH - Starting...\n');

// COMPREHENSIVE AMERICAN TO AUSTRALIAN ENGLISH CONVERSIONS
const AMERICAN_TO_AUSTRALIAN_CONVERSIONS = [
  // -ize to -ise endings
  {
    pattern: /\bspecialized\b/g,
    replacement: 'specialised',
    desc: 'specialised → specialised'
  },
  {
    pattern: /\bSpecialized\b/g,
    replacement: 'Specialised',
    desc: 'Specialised → Specialised'
  },
  {
    pattern: /\borganized\b/g,
    replacement: 'organised',
    desc: 'organised → organised'
  },
  {
    pattern: /\bOrganized\b/g,
    replacement: 'Organised',
    desc: 'Organised → Organised'
  },
  {
    pattern: /\brecognized\b/g,
    replacement: 'recognised',
    desc: 'recognised → recognised'
  },
  {
    pattern: /\bRecognized\b/g,
    replacement: 'Recognised',
    desc: 'Recognised → Recognised'
  },
  {
    pattern: /\brealize\b/g,
    replacement: 'realise',
    desc: 'realise → realise'
  },
  {
    pattern: /\bRealize\b/g,
    replacement: 'Realise',
    desc: 'Realise → Realise'
  },
  {
    pattern: /\boptimize\b/g,
    replacement: 'optimise',
    desc: 'optimise → optimise'
  },
  {
    pattern: /\bOptimize\b/g,
    replacement: 'Optimise',
    desc: 'Optimise → Optimise'
  },
  {
    pattern: /\bcustomize\b/g,
    replacement: 'customise',
    desc: 'customise → customise'
  },
  {
    pattern: /\bCustomize\b/g,
    replacement: 'Customise',
    desc: 'Customise → Customise'
  },
  {
    pattern: /\bcategorize\b/g,
    replacement: 'categorise',
    desc: 'categorise → categorise'
  },
  {
    pattern: /\bCategorize\b/g,
    replacement: 'Categorise',
    desc: 'Categorise → Categorise'
  },
  {
    pattern: /\bprioritize\b/g,
    replacement: 'prioritise',
    desc: 'prioritise → prioritise'
  },
  {
    pattern: /\bPrioritize\b/g,
    replacement: 'Prioritise',
    desc: 'Prioritise → Prioritise'
  },
  {
    pattern: /\bminimize\b/g,
    replacement: 'minimise',
    desc: 'minimise → minimise'
  },
  {
    pattern: /\bMinimize\b/g,
    replacement: 'Minimise',
    desc: 'Minimise → Minimise'
  },
  {
    pattern: /\bmaximize\b/g,
    replacement: 'maximise',
    desc: 'maximise → maximise'
  },
  {
    pattern: /\bMaximize\b/g,
    replacement: 'Maximise',
    desc: 'Maximise → Maximise'
  },
  {
    pattern: /\butilize\b/g,
    replacement: 'utilise',
    desc: 'utilise → utilise'
  },
  {
    pattern: /\bUtilize\b/g,
    replacement: 'Utilise',
    desc: 'Utilise → Utilise'
  },
  {
    pattern: /\bstandardize\b/g,
    replacement: 'standardise',
    desc: 'standardise → standardise'
  },
  {
    pattern: /\bStandardize\b/g,
    replacement: 'Standardise',
    desc: 'Standardise → Standardise'
  },
  {
    pattern: /\bmodernize\b/g,
    replacement: 'modernise',
    desc: 'modernise → modernise'
  },
  {
    pattern: /\bModernize\b/g,
    replacement: 'Modernise',
    desc: 'Modernise → Modernise'
  },
  {
    pattern: /\bvisualize\b/g,
    replacement: 'visualise',
    desc: 'visualise → visualise'
  },
  {
    pattern: /\bVisualize\b/g,
    replacement: 'Visualise',
    desc: 'Visualise → Visualise'
  },
  {
    pattern: /\bpersonalized\b/g,
    replacement: 'personalised',
    desc: 'personalised → personalised'
  },
  {
    pattern: /\bPersonalized\b/g,
    replacement: 'Personalised',
    desc: 'Personalised → Personalised'
  },
  {
    pattern: /\bcustomized\b/g,
    replacement: 'customised',
    desc: 'customised → customised'
  },
  {
    pattern: /\bCustomized\b/g,
    replacement: 'Customised',
    desc: 'Customised → Customised'
  },
  {
    pattern: /\bindividualized\b/g,
    replacement: 'individualised',
    desc: 'individualised → individualised'
  },
  {
    pattern: /\bIndividualized\b/g,
    replacement: 'Individualised',
    desc: 'Individualised → Individualised'
  },
  {
    pattern: /\blocalized\b/g,
    replacement: 'localised',
    desc: 'localised → localised'
  },
  {
    pattern: /\bLocalized\b/g,
    replacement: 'Localised',
    desc: 'Localised → Localised'
  },
  {
    pattern: /\bcentralized\b/g,
    replacement: 'centralised',
    desc: 'centralised → centralised'
  },
  {
    pattern: /\bCentralized\b/g,
    replacement: 'Centralised',
    desc: 'Centralised → Centralised'
  },
  {
    pattern: /\bauthorized\b/g,
    replacement: 'authorised',
    desc: 'authorised → authorised'
  },
  {
    pattern: /\bAuthorized\b/g,
    replacement: 'Authorised',
    desc: 'Authorised → Authorised'
  },
  {
    pattern: /\bsynchronized\b/g,
    replacement: 'synchronised',
    desc: 'synchronised → synchronised'
  },
  {
    pattern: /\bSynchronized\b/g,
    replacement: 'Synchronised',
    desc: 'Synchronised → Synchronised'
  },
  {
    pattern: /\bemphasized\b/g,
    replacement: 'emphasised',
    desc: 'emphasised → emphasised'
  },
  {
    pattern: /\bEmphasized\b/g,
    replacement: 'Emphasised',
    desc: 'Emphasised → Emphasised'
  },
  {
    pattern: /\bsummarized\b/g,
    replacement: 'summarised',
    desc: 'summarised → summarised'
  },
  {
    pattern: /\bSummarized\b/g,
    replacement: 'Summarised',
    desc: 'Summarised → Summarised'
  },
  {
    pattern: /\bcharacterized\b/g,
    replacement: 'characterised',
    desc: 'characterised → characterised'
  },
  {
    pattern: /\bCharacterized\b/g,
    replacement: 'Characterised',
    desc: 'Characterised → Characterised'
  },
  {
    pattern: /\bmaterialized\b/g,
    replacement: 'materialised',
    desc: 'materialised → materialised'
  },
  {
    pattern: /\bMaterialized\b/g,
    replacement: 'Materialised',
    desc: 'Materialised → Materialised'
  },
  {
    pattern: /\bfinalized\b/g,
    replacement: 'finalised',
    desc: 'finalised → finalised'
  },
  {
    pattern: /\bFinalized\b/g,
    replacement: 'Finalised',
    desc: 'Finalised → Finalised'
  },
  {
    pattern: /\binitialized\b/g,
    replacement: 'initialised',
    desc: 'initialised → initialised'
  },
  {
    pattern: /\bInitialized\b/g,
    replacement: 'Initialised',
    desc: 'Initialised → Initialised'
  },

  // -yze to -yse endings
  {
    pattern: /\banalyze\b/g,
    replacement: 'analyse',
    desc: 'analyse → analyse'
  },
  {
    pattern: /\bAnalyze\b/g,
    replacement: 'Analyse',
    desc: 'Analyse → Analyse'
  },

  // -er to -re endings
  {
    pattern: /\bcenter\b/g,
    replacement: 'centre',
    desc: 'centre → centre'
  },
  {
    pattern: /\bCenter\b/g,
    replacement: 'Centre',
    desc: 'Centre → Centre'
  },

  // -or to -our endings
  {
    pattern: /\bcolor\b/g,
    replacement: 'colour',
    desc: 'colour → colour'
  },
  {
    pattern: /\bColor\b/g,
    replacement: 'Colour',
    desc: 'Colour → Colour'
  },
  {
    pattern: /\bfavor\b/g,
    replacement: 'favour',
    desc: 'favour → favour'
  },
  {
    pattern: /\bFavor\b/g,
    replacement: 'Favour',
    desc: 'Favour → Favour'
  },
  {
    pattern: /\bbehavior\b/g,
    replacement: 'behaviour',
    desc: 'behaviour → behaviour'
  },
  {
    pattern: /\bBehavior\b/g,
    replacement: 'Behaviour',
    desc: 'Behaviour → Behaviour'
  },
  {
    pattern: /\bhonor\b/g,
    replacement: 'honour',
    desc: 'honour → honour'
  },
  {
    pattern: /\bHonor\b/g,
    replacement: 'Honour',
    desc: 'Honour → Honour'
  },
  {
    pattern: /\blabor\b/g,
    replacement: 'labour',
    desc: 'labour → labour'
  },
  {
    pattern: /\bLabor\b/g,
    replacement: 'Labour',
    desc: 'Labour → Labour'
  },
  {
    pattern: /\bneighbor\b/g,
    replacement: 'neighbour',
    desc: 'neighbour → neighbour'
  },
  {
    pattern: /\bNeighbor\b/g,
    replacement: 'Neighbour',
    desc: 'Neighbour → Neighbour'
  }
];

/**
 * Get all relevant files to process
 */
function getAllFiles() {
  const extensions = ['.tsx', '.ts', '.js', '.jsx', '.md', '.json'];
  const excludeDirs = ['node_modules', '.git', 'dist', 'build', '.next', 'coverage'];
  const files = [];

  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        if (!excludeDirs.includes(item)) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (extensions.includes(ext)) {
          files.push(fullPath);
        }
      }
    }
  }

  scanDirectory(PROJECT_ROOT);
  return files;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  let processedContent = content;
  let totalChanges = 0;
  const changeLog = [];

  // Apply all American to Australian English conversions
  for (const conversion of AMERICAN_TO_AUSTRALIAN_CONVERSIONS) {
    const matches = processedContent.match(conversion.pattern);
    if (matches) {
      processedContent = processedContent.replace(conversion.pattern, conversion.replacement);
      const changeCount = matches.length;
      totalChanges += changeCount;
      changeLog.push(`🇦🇺 ${conversion.desc} (${changeCount} changes)`);
    }
  }

  // Write the file if changes were made
  if (totalChanges > 0) {
    fs.writeFileSync(filePath, processedContent, 'utf8');
    console.log(`✅ ${relativePath} - ${totalChanges} conversions`);
    changeLog.forEach(log => console.log(`   ${log}`));
    console.log('');
  }

  return totalChanges;
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getAllFiles();
    let totalFiles = 0;
    let totalChanges = 0;

    console.log(`📁 Found ${files.length} files to process\n`);

    for (const file of files) {
      const changes = processFile(file);
      if (changes > 0) {
        totalFiles++;
        totalChanges += changes;
      }
    }

    console.log('🇦🇺 AUSTRALIAN ENGLISH CONVERSION COMPLETE!');
    console.log(`📊 Total files updated: ${totalFiles}`);
    console.log(`📊 Total American → Australian conversions: ${totalChanges}`);
    console.log('');
    console.log('✅ All American English spellings have been converted to Australian English!');
    console.log('🔍 Please test the application to verify the conversions.');

  } catch (error) {
    console.error('❌ Error during Australian English conversion:', error);
    process.exit(1);
  }
}

// Run the script
main();
