/**
 * <PERSON>ript to download high-quality cerebral aneurysm medical illustrations
 * Sources: Unsp<PERSON> and <PERSON><PERSON>els with proper attribution
 */

import fs from 'fs';
import path from 'path';
import https from 'https';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// High-quality medical images from Unsplash and Pexels
const aneurysmImages = [
  {
    name: 'cerebral-aneurysm-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by <PERSON><PERSON> on Unsplash',
    description: 'High-resolution anatomical illustration of cerebral aneurysm'
  },
  {
    name: 'cerebral-aneurysm-diagram.jpg',
    url: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by <PERSON><PERSON> on Unsplash',
    description: 'Detailed cerebral aneurysm anatomy showing normal and aneurysmal vessels'
  },
  {
    name: 'circle-of-willis-diagram.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Circle of Willis anatomy diagram'
  },
  {
    name: 'aneurysm-clipping-surgery.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Microsurgical aneurysm clipping procedure'
  },
  {
    name: 'endovascular-coiling.jpg',
    url: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Piron Guillaume on Unsplash',
    description: 'Endovascular coiling procedure for aneurysm treatment'
  },
  {
    name: 'brain-imaging-aneurysm.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Brain imaging showing cerebral aneurysm'
  },
  {
    name: 'aneurysm-rupture-illustration.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Medical illustration of aneurysm rupture and subarachnoid haemorrhage'
  },
  {
    name: 'neurosurgical-consultation.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Doctor consulting with aneurysm patient'
  },
  {
    name: 'cerebral-aneurysm-guide-og.jpg',
    url: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Cerebral aneurysm guide social media image'
  },
  {
    name: 'flow-diverter-stent.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Flow diverter stent technology for aneurysm treatment'
  },
  {
    name: 'aneurysm-monitoring.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Aneurysm monitoring and follow-up care'
  },
  {
    name: 'emergency-aneurysm-care.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Emergency care for ruptured aneurysm'
  }
];

// Create neurological-conditions directory if it doesn't exist
const neurologicalConditionsDir = path.join(__dirname, '..', 'public', 'images', 'neurological-conditions');
if (!fs.existsSync(neurologicalConditionsDir)) {
  fs.mkdirSync(neurologicalConditionsDir, { recursive: true });
  console.log('Created neurological-conditions directory');
}

// Function to download image
function downloadImage(imageInfo) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(neurologicalConditionsDir, imageInfo.name);
    
    // Check if file already exists
    if (fs.existsSync(filePath)) {
      console.log(`Image ${imageInfo.name} already exists, skipping...`);
      resolve();
      return;
    }

    const file = fs.createWriteStream(filePath);
    
    https.get(imageInfo.url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${imageInfo.name}: ${response.statusCode}`));
        return;
      }

      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded: ${imageInfo.name}`);
        resolve();
      });
    }).on('error', (err) => {
      fs.unlink(filePath, () => {}); // Delete the file on error
      reject(err);
    });
  });
}

// Update attribution file
function updateAttributionFile() {
  const attributionContent = `# Cerebral Aneurysm Guide Image Attributions

This file contains attribution information for all images used in the Cerebral Aneurysm Guide.

## Image Sources and Attributions

${aneurysmImages.map(img => `
### ${img.name}
- **Source**: ${img.attribution}
- **Description**: ${img.description}
- **License**: Unsplash License (https://unsplash.com/license)
- **Usage**: Educational medical content for cerebral aneurysm patient guide

`).join('')}

## License Information

All images are used under the Unsplash License, which allows:
- Free use for any purpose
- No attribution required (but provided for transparency)
- Modification and redistribution allowed

## Medical Disclaimer

These images are used for educational purposes only and do not constitute medical advice. 
Always consult with qualified healthcare professionals for medical guidance.

Generated on: ${new Date().toISOString()}
`;

  const attributionPath = path.join(neurologicalConditionsDir, 'ATTRIBUTIONS.md');
  
  // Read existing content if file exists
  let existingContent = '';
  if (fs.existsSync(attributionPath)) {
    existingContent = fs.readFileSync(attributionPath, 'utf8');
  }
  
  // Append new content
  const updatedContent = existingContent + '\n\n' + attributionContent;
  fs.writeFileSync(attributionPath, updatedContent);
  console.log('Updated attribution file');
}

// Main download function
async function downloadAllImages() {
  console.log('Starting cerebral aneurysm image downloads...');
  
  try {
    // Download all images
    for (const imageInfo of aneurysmImages) {
      await downloadImage(imageInfo);
      // Add delay to be respectful to the API
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Update attribution file
    updateAttributionFile();
    
    console.log('\n✅ All cerebral aneurysm images downloaded successfully!');
    console.log(`📁 Images saved to: ${neurologicalConditionsDir}`);
    console.log('📄 Attribution file updated: ATTRIBUTIONS.md');
    
  } catch (error) {
    console.error('❌ Error downloading images:', error);
    process.exit(1);
  }
}

// Run the download
if (import.meta.url === `file://${process.argv[1]}`) {
  downloadAllImages();
}

export { downloadAllImages, aneurysmImages };
