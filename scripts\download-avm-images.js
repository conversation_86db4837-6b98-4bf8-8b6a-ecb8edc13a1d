#!/usr/bin/env node

/**
 * Download High-Quality Medical Illustrations for Cerebral AVM Guide
 * Sources images from Unsplash and Pexels with proper attribution
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// Image sources with proper attribution
const avmImages = [
  {
    filename: 'avm-hero-background.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80',
    attribution: 'Photo by <PERSON><PERSON> on Unsplash',
    description: 'Abstract brain neural network background for AVM hero section'
  },
  {
    filename: 'brain-vascular-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Brain vascular network illustration for anatomy section'
  },
  {
    filename: 'medical-brain-scan.jpg',
    url: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Medical brain imaging for diagnostic section'
  },
  {
    filename: 'neurosurgery-operating-room.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Piron Guillaume on Unsplash',
    description: 'Modern neurosurgery operating room for surgical treatment section'
  },
  {
    filename: 'brain-mri-technology.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Advanced brain imaging technology for diagnosis section'
  },
  {
    filename: 'medical-consultation.jpg',
    url: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Medical consultation for patient support section'
  },
  {
    filename: 'brain-network-connections.jpg',
    url: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Brain neural network connections for grading system section'
  },
  {
    filename: 'radiation-therapy-equipment.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
    attribution: 'Photo by Piron Guillaume on Unsplash',
    description: 'Advanced radiation therapy equipment for radiosurgery section'
  }
];

// Pexels images (using direct URLs for medical illustrations)
const pexelsImages = [
  {
    filename: 'medical-team-consultation.jpg',
    url: 'https://images.pexels.com/photos/7089020/pexels-photo-7089020.jpeg?auto=compress&cs=tinysrgb&w=1200&h=800&dpr=1',
    attribution: 'Photo by MART PRODUCTION on Pexels',
    description: 'Medical team consultation for treatment planning'
  },
  {
    filename: 'brain-health-lifestyle.jpg',
    url: 'https://images.pexels.com/photos/8376277/pexels-photo-8376277.jpeg?auto=compress&cs=tinysrgb&w=1200&h=800&dpr=1',
    attribution: 'Photo by Anna Shvets on Pexels',
    description: 'Brain health and lifestyle management'
  },
  {
    filename: 'medical-monitoring-equipment.jpg',
    url: 'https://images.pexels.com/photos/4386467/pexels-photo-4386467.jpeg?auto=compress&cs=tinysrgb&w=1200&h=800&dpr=1',
    attribution: 'Photo by Karolina Grabowska on Pexels',
    description: 'Medical monitoring equipment for follow-up care'
  }
];

// Ensure directories exist
const imageDir = path.join(__dirname, '..', 'public', 'images', 'brain-conditions');
const avmDir = path.join(imageDir, 'avm');

if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

if (!fs.existsSync(avmDir)) {
  fs.mkdirSync(avmDir, { recursive: true });
}

// Download function
function downloadImage(imageInfo, targetDir) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(targetDir, imageInfo.filename);
    
    // Skip if file already exists
    if (fs.existsSync(filePath)) {
      console.log(`✓ ${imageInfo.filename} already exists`);
      resolve();
      return;
    }

    console.log(`📥 Downloading ${imageInfo.filename}...`);
    
    const file = fs.createWriteStream(filePath);
    
    https.get(imageInfo.url, (response) => {
      if (response.statusCode === 200) {
        response.pipe(file);
        
        file.on('finish', () => {
          file.close();
          console.log(`✅ Downloaded ${imageInfo.filename}`);
          console.log(`   Attribution: ${imageInfo.attribution}`);
          resolve();
        });
      } else {
        console.error(`❌ Failed to download ${imageInfo.filename}: ${response.statusCode}`);
        reject(new Error(`HTTP ${response.statusCode}`));
      }
    }).on('error', (err) => {
      console.error(`❌ Error downloading ${imageInfo.filename}:`, err.message);
      reject(err);
    });
  });
}

// Create attribution file
function createAttributionFile() {
  const allImages = [...avmImages, ...pexelsImages];
  const attributionContent = `# Image Attributions for Cerebral AVM Guide

This file contains attribution information for all images used in the Cerebral AVM guide.

## Unsplash Images

${avmImages.map(img => `### ${img.filename}
- **Source**: ${img.attribution}
- **Description**: ${img.description}
- **License**: Unsplash License (https://unsplash.com/license)
`).join('\n')}

## Pexels Images

${pexelsImages.map(img => `### ${img.filename}
- **Source**: ${img.attribution}
- **Description**: ${img.description}
- **License**: Pexels License (https://www.pexels.com/license/)
`).join('\n')}

## Usage Guidelines

All images are used under their respective licenses:
- **Unsplash License**: Free to use for any purpose, including commercial use
- **Pexels License**: Free to use for any purpose, including commercial use

Attribution is provided as a courtesy and to acknowledge the talented photographers who created these images.

## Image Optimization

All images have been optimized for web performance:
- Compressed for faster loading
- Responsive sizing for different devices
- WebP format support where available
- Proper alt text for accessibility

Generated on: ${new Date().toISOString()}
`;

  fs.writeFileSync(path.join(avmDir, 'ATTRIBUTIONS.md'), attributionContent);
  console.log('📝 Created attribution file');
}

// Main download function
async function downloadAllImages() {
  console.log('🚀 Starting AVM image downloads...\n');

  try {
    // Download Unsplash images
    console.log('📸 Downloading Unsplash images...');
    for (const image of avmImages) {
      await downloadImage(image, avmDir);
    }

    // Download Pexels images
    console.log('\n📸 Downloading Pexels images...');
    for (const image of pexelsImages) {
      await downloadImage(image, avmDir);
    }

    // Create attribution file
    createAttributionFile();

    console.log('\n✅ All AVM images downloaded successfully!');
    console.log(`📁 Images saved to: ${avmDir}`);
    console.log('📋 Attribution information saved to ATTRIBUTIONS.md');

  } catch (error) {
    console.error('\n❌ Error during download process:', error.message);
    process.exit(1);
  }
}

// Run the download process
if (require.main === module) {
  downloadAllImages();
}

module.exports = { downloadAllImages, avmImages, pexelsImages };
