/**
 * Script to download high-quality brain tumor medical illustrations
 * Sources: Unsp<PERSON> and <PERSON><PERSON>els with proper attribution
 */

import fs from 'fs';
import path from 'path';
import https from 'https';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// High-quality medical images from Unsplash and Pexels
const brainImages = [
  {
    name: 'brain-tumour-mri-scan.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by <PERSON><PERSON> on Unsplash',
    description: 'High-resolution MRI brain scan showing detailed brain anatomy'
  },
  {
    name: 'brain-anatomy-tumour-location.jpg',
    url: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by <PERSON><PERSON> on Unsplash',
    description: 'Brain anatomy illustration showing common tumor locations'
  },
  {
    name: 'brain-surgery-operating-room.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Piron Guillaume on Unsplash',
    description: 'Modern neurosurgical operating room with advanced equipment'
  },
  {
    name: 'brain-scan-analysis.jpg',
    url: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Medical professional analyzing brain scans'
  },
  {
    name: 'neurosurgical-instruments.jpg',
    url: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Piron Guillaume on Unsplash',
    description: 'Precision neurosurgical instruments for brain surgery'
  },
  {
    name: 'brain-imaging-technology.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Advanced brain imaging technology and MRI scanner'
  },
  {
    name: 'patient-consultation-brain.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Doctor consulting with patient about brain condition'
  },
  {
    name: 'brain-tumour-guide-og.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Brain tumor guide social media image'
  },
  {
    name: 'minimally-invasive-brain-surgery.jpg',
    url: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Piron Guillaume on Unsplash',
    description: 'Minimally invasive brain surgery techniques'
  },
  {
    name: 'brain-recovery-rehabilitation.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Brain tumor patient recovery and rehabilitation'
  }
];

// Create brain-conditions directory if it doesn't exist
const brainConditionsDir = path.join(__dirname, '..', 'public', 'images', 'brain-conditions');
if (!fs.existsSync(brainConditionsDir)) {
  fs.mkdirSync(brainConditionsDir, { recursive: true });
  console.log('Created brain-conditions directory');
}

// Function to download image
function downloadImage(imageInfo) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(brainConditionsDir, imageInfo.name);
    
    // Check if file already exists
    if (fs.existsSync(filePath)) {
      console.log(`Image ${imageInfo.name} already exists, skipping...`);
      resolve();
      return;
    }

    const file = fs.createWriteStream(filePath);
    
    https.get(imageInfo.url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${imageInfo.name}: ${response.statusCode}`));
        return;
      }

      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded: ${imageInfo.name}`);
        resolve();
      });
    }).on('error', (err) => {
      fs.unlink(filePath, () => {}); // Delete the file on error
      reject(err);
    });
  });
}

// Create attribution file
function createAttributionFile() {
  const attributionContent = `# Brain Tumor Guide Image Attributions

This file contains attribution information for all images used in the Brain Tumor Guide.

## Image Sources and Attributions

${brainImages.map(img => `
### ${img.name}
- **Source**: ${img.attribution}
- **Description**: ${img.description}
- **License**: Unsplash License (https://unsplash.com/license)
- **Usage**: Educational medical content for brain tumor patient guide

`).join('')}

## License Information

All images are used under the Unsplash License, which allows:
- Free use for any purpose
- No attribution required (but provided for transparency)
- Modification and redistribution allowed

## Medical Disclaimer

These images are used for educational purposes only and do not constitute medical advice. 
Always consult with qualified healthcare professionals for medical guidance.

Generated on: ${new Date().toISOString()}
`;

  const attributionPath = path.join(brainConditionsDir, 'ATTRIBUTIONS.md');
  fs.writeFileSync(attributionPath, attributionContent);
  console.log('Created attribution file');
}

// Main download function
async function downloadAllImages() {
  console.log('Starting brain tumor image downloads...');
  
  try {
    // Download all images
    for (const imageInfo of brainImages) {
      await downloadImage(imageInfo);
      // Add delay to be respectful to the API
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Create attribution file
    createAttributionFile();
    
    console.log('\n✅ All brain tumor images downloaded successfully!');
    console.log(`📁 Images saved to: ${brainConditionsDir}`);
    console.log('📄 Attribution file created: ATTRIBUTIONS.md');
    
  } catch (error) {
    console.error('❌ Error downloading images:', error);
    process.exit(1);
  }
}

// Run the download
if (import.meta.url === `file://${process.argv[1]}`) {
  downloadAllImages();
}

export { downloadAllImages, brainImages };
