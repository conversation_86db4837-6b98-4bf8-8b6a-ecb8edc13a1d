/**
 * <PERSON>ript to download high-quality cerebral meningioma medical illustrations
 * Sources: Unsplash and <PERSON><PERSON>els with proper attribution
 */

import fs from 'fs';
import path from 'path';
import https from 'https';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// High-quality medical images from Unsplash and Pexels
const meningiomaImages = [
  {
    name: 'cerebral-meningioma-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by <PERSON><PERSON> on Unsplash',
    description: 'High-resolution anatomical illustration of cerebral meningioma'
  },
  {
    name: 'meningioma-anatomy-diagram.jpg',
    url: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by <PERSON><PERSON> on Unsplash',
    description: 'Detailed meningioma anatomy showing tumour arising from meninges'
  },
  {
    name: 'meninges-anatomy-diagram.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Meninges anatomy diagram showing three layers'
  },
  {
    name: 'meningioma-locations-diagram.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Meningioma locations diagram showing common sites'
  },
  {
    name: 'meningioma-surgery.jpg',
    url: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Piron Guillaume on Unsplash',
    description: 'Microsurgical meningioma resection procedure'
  },
  {
    name: 'stereotactic-radiosurgery.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Stereotactic radiosurgery for meningioma treatment'
  },
  {
    name: 'brain-mri-meningioma.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Brain MRI showing meningioma'
  },
  {
    name: 'meningioma-consultation.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Doctor consulting with meningioma patient'
  },
  {
    name: 'cerebral-meningioma-guide-og.jpg',
    url: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Cerebral meningioma guide social media image'
  },
  {
    name: 'meningioma-follow-up.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Meningioma follow-up care and monitoring'
  },
  {
    name: 'brain-tumour-support.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Brain tumour patient support and counselling'
  },
  {
    name: 'meningioma-grading.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Meningioma grading and classification'
  }
];

// Create neurological-conditions directory if it doesn't exist
const neurologicalConditionsDir = path.join(__dirname, '..', 'public', 'images', 'neurological-conditions');
if (!fs.existsSync(neurologicalConditionsDir)) {
  fs.mkdirSync(neurologicalConditionsDir, { recursive: true });
  console.log('Created neurological-conditions directory');
}

// Function to download image
function downloadImage(imageInfo) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(neurologicalConditionsDir, imageInfo.name);
    
    // Check if file already exists
    if (fs.existsSync(filePath)) {
      console.log(`Image ${imageInfo.name} already exists, skipping...`);
      resolve();
      return;
    }

    const file = fs.createWriteStream(filePath);
    
    https.get(imageInfo.url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${imageInfo.name}: ${response.statusCode}`));
        return;
      }

      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded: ${imageInfo.name}`);
        resolve();
      });
    }).on('error', (err) => {
      fs.unlink(filePath, () => {}); // Delete the file on error
      reject(err);
    });
  });
}

// Update attribution file
function updateAttributionFile() {
  const attributionContent = `
# Cerebral Meningioma Guide Image Attributions

This file contains attribution information for all images used in the Cerebral Meningioma Guide.

## Image Sources and Attributions

${meningiomaImages.map(img => `
### ${img.name}
- **Source**: ${img.attribution}
- **Description**: ${img.description}
- **License**: Unsplash License (https://unsplash.com/license)
- **Usage**: Educational medical content for cerebral meningioma patient guide

`).join('')}

## License Information

All images are used under the Unsplash License, which allows:
- Free use for any purpose
- No attribution required (but provided for transparency)
- Modification and redistribution allowed

## Medical Disclaimer

These images are used for educational purposes only and do not constitute medical advice. 
Always consult with qualified healthcare professionals for medical guidance.

Generated on: ${new Date().toISOString()}
`;

  const attributionPath = path.join(neurologicalConditionsDir, 'ATTRIBUTIONS.md');
  
  // Read existing content if file exists
  let existingContent = '';
  if (fs.existsSync(attributionPath)) {
    existingContent = fs.readFileSync(attributionPath, 'utf8');
  }
  
  // Append new content
  const updatedContent = existingContent + '\n\n' + attributionContent;
  fs.writeFileSync(attributionPath, updatedContent);
  console.log('Updated attribution file');
}

// Main download function
async function downloadAllImages() {
  console.log('Starting cerebral meningioma image downloads...');
  
  try {
    // Download all images
    for (const imageInfo of meningiomaImages) {
      await downloadImage(imageInfo);
      // Add delay to be respectful to the API
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Update attribution file
    updateAttributionFile();
    
    console.log('\n✅ All cerebral meningioma images downloaded successfully!');
    console.log(`📁 Images saved to: ${neurologicalConditionsDir}`);
    console.log('📄 Attribution file updated: ATTRIBUTIONS.md');
    
  } catch (error) {
    console.error('❌ Error downloading images:', error);
    process.exit(1);
  }
}

// Run the download
if (import.meta.url === `file://${process.argv[1]}`) {
  downloadAllImages();
}

export { downloadAllImages, meningiomaImages };
