/**
 * Download High-Quality Peripheral Nerve Condition Images
 * Sources images from Unsplash and Pexels for peripheral nerve conditions library
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// High-quality medical images for peripheral nerve conditions
const images = [
  // Main page images
  {
    filename: 'peripheral-nerve-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Peripheral nerve anatomy background for main page'
  },
  {
    filename: 'nerve-anatomy-overview.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Comprehensive nerve anatomy overview'
  },
  {
    filename: 'peripheral-nerve-system.jpg',
    url: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Complete peripheral nerve system anatomy'
  },

  // Nerve Entrapments category
  {
    filename: 'nerve-entrapment-sites.jpg',
    url: 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Common nerve entrapment sites anatomy'
  },

  // Individual condition images
  {
    filename: 'carpal-tunnel-syndrome.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Carpal tunnel syndrome anatomy and compression'
  },
  {
    filename: 'ulnar-neuropathy.jpg',
    url: 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Ulnar nerve anatomy and entrapment sites'
  },
  {
    filename: 'meralgia-paresthetica.jpg',
    url: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Lateral femoral cutaneous nerve anatomy'
  },
  {
    filename: 'tarsal-tunnel-syndrome.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Tarsal tunnel anatomy and nerve compression'
  },

  // Nerve Compressions category
  {
    filename: 'nerve-compression-mechanisms.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Nerve compression mechanisms and pathophysiology'
  },
  {
    filename: 'thoracic-outlet-syndrome.jpg',
    url: 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Thoracic outlet anatomy and compression sites'
  },
  {
    filename: 'peroneal-nerve-palsy.jpg',
    url: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Peroneal nerve anatomy and foot drop'
  },

  // Nerve Tumors category
  {
    filename: 'nerve-tumor-types.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Types of peripheral nerve tumors'
  },
  {
    filename: 'peripheral-nerve-tumors.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Peripheral nerve tumors and schwannomas'
  },

  // Peripheral Neuropathy category
  {
    filename: 'neuropathy-patterns.jpg',
    url: 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Peripheral neuropathy distribution patterns'
  },
  {
    filename: 'peripheral-neuropathy.jpg',
    url: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Peripheral neuropathy nerve damage patterns'
  },

  // Fallback images
  {
    filename: 'default-nerve-overview.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Default nerve anatomy overview fallback'
  },
  {
    filename: 'default-nerve-system.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Default nerve system fallback image'
  }
];

// Ensure directories exist
const imageDir = path.join(__dirname, '..', 'public', 'images', 'peripheral-nerve-conditions');

if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
  console.log('Created peripheral-nerve-conditions directory');
}

// Function to download image
function downloadImage(imageData) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(imageDir, imageData.filename);
    
    // Skip if file already exists
    if (fs.existsSync(filePath)) {
      console.log(`✓ ${imageData.filename} already exists`);
      resolve();
      return;
    }

    const file = fs.createWriteStream(filePath);
    
    https.get(imageData.url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${imageData.filename}: ${response.statusCode}`));
        return;
      }

      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`✓ Downloaded ${imageData.filename}`);
        resolve();
      });
    }).on('error', (err) => {
      fs.unlink(filePath, () => {}); // Delete the file on error
      reject(err);
    });
  });
}

// Download all images
async function downloadAllImages() {
  console.log('Starting download of peripheral nerve condition images...\n');
  
  try {
    for (const imageData of images) {
      await downloadImage(imageData);
      // Small delay to be respectful to the server
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log('\n✅ All peripheral nerve condition images downloaded successfully!');
    
    // Create attribution file
    const attributionContent = images.map(img => 
      `${img.filename}: ${img.attribution} - ${img.description}`
    ).join('\n');
    
    fs.writeFileSync(
      path.join(imageDir, 'ATTRIBUTIONS.md'), 
      `# Peripheral Nerve Conditions Image Attributions\n\n${attributionContent}\n`
    );
    
    console.log('✅ Attribution file created');
    
  } catch (error) {
    console.error('❌ Error downloading images:', error);
    process.exit(1);
  }
}

// Run the download
downloadAllImages();
