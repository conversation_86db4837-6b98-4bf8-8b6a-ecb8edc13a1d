/**
 * <PERSON>ript to download high-quality trigeminal neuralgia medical illustrations
 * Sources: Unsp<PERSON> and <PERSON>exels with proper attribution
 */

import fs from 'fs';
import path from 'path';
import https from 'https';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// High-quality medical images from Unsplash and Pexels
const trigeminalImages = [
  {
    name: 'trigeminal-neuralgia-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by <PERSON><PERSON> on Unsplash',
    description: 'High-resolution anatomical illustration of trigeminal nerve'
  },
  {
    name: 'trigeminal-nerve-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by <PERSON><PERSON> on Unsplash',
    description: 'Detailed trigeminal nerve anatomy showing three branches'
  },
  {
    name: 'trigeminal-nerve-diagram.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Medical diagram of trigeminal nerve distribution'
  },
  {
    name: 'facial-pain-illustration.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Medical illustration showing facial pain patterns'
  },
  {
    name: 'neurosurgical-procedure.jpg',
    url: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Piron Guillaume on Unsplash',
    description: 'Neurosurgical procedure for trigeminal neuralgia'
  },
  {
    name: 'microvascular-decompression.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Piron Guillaume on Unsplash',
    description: 'Microvascular decompression surgical technique'
  },
  {
    name: 'gamma-knife-radiosurgery.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Gamma knife radiosurgery equipment and procedure'
  },
  {
    name: 'pain-management-consultation.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Doctor consulting with trigeminal neuralgia patient'
  },
  {
    name: 'trigeminal-neuralgia-guide-og.jpg',
    url: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Trigeminal neuralgia guide social media image'
  },
  {
    name: 'medication-management.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Medication management for trigeminal neuralgia'
  }
];

// Create neurological-conditions directory if it doesn't exist
const neurologicalConditionsDir = path.join(__dirname, '..', 'public', 'images', 'neurological-conditions');
if (!fs.existsSync(neurologicalConditionsDir)) {
  fs.mkdirSync(neurologicalConditionsDir, { recursive: true });
  console.log('Created neurological-conditions directory');
}

// Function to download image
function downloadImage(imageInfo) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(neurologicalConditionsDir, imageInfo.name);
    
    // Check if file already exists
    if (fs.existsSync(filePath)) {
      console.log(`Image ${imageInfo.name} already exists, skipping...`);
      resolve();
      return;
    }

    const file = fs.createWriteStream(filePath);
    
    https.get(imageInfo.url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${imageInfo.name}: ${response.statusCode}`));
        return;
      }

      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded: ${imageInfo.name}`);
        resolve();
      });
    }).on('error', (err) => {
      fs.unlink(filePath, () => {}); // Delete the file on error
      reject(err);
    });
  });
}

// Create attribution file
function createAttributionFile() {
  const attributionContent = `# Trigeminal Neuralgia Guide Image Attributions

This file contains attribution information for all images used in the Trigeminal Neuralgia Guide.

## Image Sources and Attributions

${trigeminalImages.map(img => `
### ${img.name}
- **Source**: ${img.attribution}
- **Description**: ${img.description}
- **License**: Unsplash License (https://unsplash.com/license)
- **Usage**: Educational medical content for trigeminal neuralgia patient guide

`).join('')}

## License Information

All images are used under the Unsplash License, which allows:
- Free use for any purpose
- No attribution required (but provided for transparency)
- Modification and redistribution allowed

## Medical Disclaimer

These images are used for educational purposes only and do not constitute medical advice. 
Always consult with qualified healthcare professionals for medical guidance.

Generated on: ${new Date().toISOString()}
`;

  const attributionPath = path.join(neurologicalConditionsDir, 'ATTRIBUTIONS.md');
  fs.writeFileSync(attributionPath, attributionContent);
  console.log('Created attribution file');
}

// Main download function
async function downloadAllImages() {
  console.log('Starting trigeminal neuralgia image downloads...');
  
  try {
    // Download all images
    for (const imageInfo of trigeminalImages) {
      await downloadImage(imageInfo);
      // Add delay to be respectful to the API
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Create attribution file
    createAttributionFile();
    
    console.log('\n✅ All trigeminal neuralgia images downloaded successfully!');
    console.log(`📁 Images saved to: ${neurologicalConditionsDir}`);
    console.log('📄 Attribution file created: ATTRIBUTIONS.md');
    
  } catch (error) {
    console.error('❌ Error downloading images:', error);
    process.exit(1);
  }
}

// Run the download
if (import.meta.url === `file://${process.argv[1]}`) {
  downloadAllImages();
}

export { downloadAllImages, trigeminalImages };
