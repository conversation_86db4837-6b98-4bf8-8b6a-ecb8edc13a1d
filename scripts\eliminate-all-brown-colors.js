#!/usr/bin/env node

/**
 * EMERGENCY BROWN COLOR ELIMINATION SCRIPT
 * 
 * This script systematically finds and eliminates ALL brown, amber, orange, tan,
 * and similar colors across the entire codebase to ensure professional appearance.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🚨 EMERGENCY BROWN COLOR ELIMINATION - Starting...\n');

// COMPREHENSIVE BROWN COLOR ELIMINATION PATTERNS
const BROWN_COLOR_ELIMINATION_FIXES = [
  // 1. AMBER COLORS (BROWN-ISH)
  {
    pattern: /\bbg-amber-([0-9]+)\b/g,
    replacement: 'bg-info-light',
    desc: 'ELIMINATED: bg-amber-* → bg-info-light'
  },
  {
    pattern: /\btext-amber-([0-9]+)\b/g,
    replacement: 'text-info',
    desc: 'ELIMINATED: text-amber-* → text-info'
  },
  {
    pattern: /\bborder-amber-([0-9]+)\b/g,
    replacement: 'border-info/30',
    desc: 'ELIMINATED: border-amber-* → border-info/30'
  },

  // 2. ORANGE COLORS (BROWNISH WHEN DARK)
  {
    pattern: /\bbg-orange-([5-9][0-9][0-9]|[5-9]00)\b/g,
    replacement: 'bg-info-light',
    desc: 'ELIMINATED: Dark orange (brownish) → bg-info-light'
  },
  {
    pattern: /\btext-orange-([5-9][0-9][0-9]|[5-9]00)\b/g,
    replacement: 'text-info',
    desc: 'ELIMINATED: Dark orange text → text-info'
  },
  {
    pattern: /\bborder-orange-([5-9][0-9][0-9]|[5-9]00)\b/g,
    replacement: 'border-info/30',
    desc: 'ELIMINATED: Dark orange borders → border-info/30'
  },

  // 3. LIGHT ORANGE COLORS (STILL BROWNISH)
  {
    pattern: /\bbg-orange-([1-4][0-9][0-9]|[1-4]00)\b/g,
    replacement: 'bg-info-light',
    desc: 'ELIMINATED: Light orange → bg-info-light'
  },
  {
    pattern: /\btext-orange-([1-4][0-9][0-9]|[1-4]00)\b/g,
    replacement: 'text-info',
    desc: 'ELIMINATED: Light orange text → text-info'
  },
  {
    pattern: /\bborder-orange-([1-4][0-9][0-9]|[1-4]00)\b/g,
    replacement: 'border-info/30',
    desc: 'ELIMINATED: Light orange borders → border-info/30'
  },

  // 4. YELLOW-ORANGE COMBINATIONS (BROWNISH)
  {
    pattern: /\bbg-yellow-([7-9][0-9][0-9]|[7-9]00)\b/g,
    replacement: 'bg-info-light',
    desc: 'ELIMINATED: Dark yellow (brownish) → bg-info-light'
  },
  {
    pattern: /\btext-yellow-([7-9][0-9][0-9]|[7-9]00)\b/g,
    replacement: 'text-info',
    desc: 'ELIMINATED: Dark yellow text → text-info'
  },

  // 5. SPECIFIC BROWN HEX CODES
  {
    pattern: /hsl(var(--warning))/gi,
    replacement: 'hsl(var(--warning))',
    desc: 'ELIMINATED: Brown hex hsl(var(--warning)) → warning theme'
  },
  {
    pattern: /hsl(var(--warning))/gi,
    replacement: 'hsl(var(--warning))',
    desc: 'ELIMINATED: Brown hex hsl(var(--warning)) → warning theme'
  },
  {
    pattern: /hsl(var(--warning))/gi,
    replacement: 'hsl(var(--warning))',
    desc: 'ELIMINATED: Brown hex hsl(var(--warning)) → warning theme'
  },
  {
    pattern: /hsl(var(--warning-light))/gi,
    replacement: 'hsl(var(--warning-light))',
    desc: 'ELIMINATED: Brown hex hsl(var(--warning-light)) → warning-light theme'
  },
  {
    pattern: /hsl(var(--warning-light))/gi,
    replacement: 'hsl(var(--warning-light))',
    desc: 'ELIMINATED: Brown hex hsl(var(--warning-light)) → warning-light theme'
  },
  {
    pattern: /hsl(var(--warning))/gi,
    replacement: 'hsl(var(--warning))',
    desc: 'ELIMINATED: Brown hex hsl(var(--warning)) → warning theme'
  },

  // 6. GRADIENT BACKGROUNDS WITH BROWN/AMBER/ORANGE
  {
    pattern: /\bfrom-amber-([0-9]+)\b/g,
    replacement: 'from-info-light',
    desc: 'ELIMINATED: Amber gradients → warning-light gradients'
  },
  {
    pattern: /\bto-amber-([0-9]+)\b/g,
    replacement: 'to-info-light',
    desc: 'ELIMINATED: Amber gradients → warning-light gradients'
  },
  {
    pattern: /\bvia-amber-([0-9]+)\b/g,
    replacement: 'via-info-light',
    desc: 'ELIMINATED: Amber gradients → warning-light gradients'
  },
  {
    pattern: /\bfrom-orange-([5-9][0-9][0-9]|[5-9]00)\b/g,
    replacement: 'from-info-light',
    desc: 'ELIMINATED: Dark orange gradients → warning-light gradients'
  },
  {
    pattern: /\bto-orange-([5-9][0-9][0-9]|[5-9]00)\b/g,
    replacement: 'to-info-light',
    desc: 'ELIMINATED: Dark orange gradients → warning-light gradients'
  },

  // 7. SPECIFIC PROBLEMATIC COMBINATIONS FROM HOMEPAGE DATA
  {
    pattern: /from-info-light\/20 via-primary\/15 to-info-light\/20/g,
    replacement: 'from-info-light via-primary/15 to-info-light',
    desc: 'ELIMINATED: Amber-orange gradient → warning theme'
  },
  {
    pattern: /from-yellow-500\/20 via-primary\/15 to-info-light\/20/g,
    replacement: 'from-info-light via-primary/15 to-info-light',
    desc: 'ELIMINATED: Yellow-amber gradient → warning theme'
  },

  // 8. DARK THEME BROWN/AMBER COLORS
  {
    pattern: /\bdark:bg-amber-([0-9]+)\b/g,
    replacement: 'dark:bg-info-light',
    desc: 'ELIMINATED: Dark theme amber → dark:bg-info-light'
  },
  {
    pattern: /\bdark:text-amber-([0-9]+)\b/g,
    replacement: 'dark:text-info',
    desc: 'ELIMINATED: Dark theme amber text → dark:text-info'
  },
  {
    pattern: /\bdark:border-amber-([0-9]+)\b/g,
    replacement: 'dark:border-info/30',
    desc: 'ELIMINATED: Dark theme amber borders → dark:border-info/30'
  },

  // 9. BROWN/TAN NAMED COLORS
  {
    pattern: /\bbg-brown\b/g,
    replacement: 'bg-info-light',
    desc: 'ELIMINATED: bg-info-light → bg-info-light'
  },
  {
    pattern: /\btext-brown\b/g,
    replacement: 'text-info',
    desc: 'ELIMINATED: text-info → text-info'
  },
  {
    pattern: /\bbg-tan\b/g,
    replacement: 'bg-info-light',
    desc: 'ELIMINATED: bg-info-light → bg-info-light'
  },
  {
    pattern: /\btext-tan\b/g,
    replacement: 'text-info',
    desc: 'ELIMINATED: text-info → text-info'
  },

  // 10. SPECIFIC TAILWIND BROWN SHADES
  {
    pattern: /\bbg-orange-([1-9][0-9][0-9]|[1-9]00)\b/g,
    replacement: 'bg-info-light',
    desc: 'ELIMINATED: All orange backgrounds → bg-info-light'
  },
  {
    pattern: /\btext-orange-([1-9][0-9][0-9]|[1-9]00)\b/g,
    replacement: 'text-info',
    desc: 'ELIMINATED: All orange text → text-info'
  },
  {
    pattern: /\bborder-orange-([1-9][0-9][0-9]|[1-9]00)\b/g,
    replacement: 'border-info/30',
    desc: 'ELIMINATED: All orange borders → border-info/30'
  }
];

/**
 * Get all TypeScript and JavaScript files in the project
 */
function getAllFiles() {
  const files = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules, .git, dist, and other build directories
        if (!['node_modules', '.git', 'dist', '.next', 'build', '.vite'].includes(item)) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && /\.(tsx?|jsx?|css|scss|sass|less)$/.test(item)) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(PROJECT_ROOT);
  return files;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  let processedContent = content;
  let totalChanges = 0;
  const changeLog = [];

  // Apply all brown color elimination fixes
  for (const fix of BROWN_COLOR_ELIMINATION_FIXES) {
    const matches = processedContent.match(fix.pattern);
    if (matches) {
      processedContent = processedContent.replace(fix.pattern, fix.replacement);
      const changeCount = matches.length;
      totalChanges += changeCount;
      changeLog.push(`✅ ${fix.desc} (${changeCount} fixes)`);
    }
  }

  // Write the file if changes were made
  if (totalChanges > 0) {
    fs.writeFileSync(filePath, processedContent, 'utf8');
    console.log(`🔧 ELIMINATED ${totalChanges} brown colors in ${relativePath}`);
    changeLog.forEach(change => console.log(`   ${change}`));
    console.log('');
  }

  return totalChanges;
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getAllFiles();
    let totalFiles = 0;
    let totalChanges = 0;

    console.log(`📁 Found ${files.length} files to process\n`);

    for (const file of files) {
      const changes = processFile(file);
      if (changes > 0) {
        totalFiles++;
        totalChanges += changes;
      }
    }

    console.log('🎯 BROWN COLOR ELIMINATION COMPLETE!');
    console.log(`📊 Total files updated: ${totalFiles}`);
    console.log(`📊 Total brown colors eliminated: ${totalChanges}`);
    console.log('');
    console.log('✅ ALL brown, amber, orange, and tan colors have been eliminated!');
    console.log('🔍 Please test the application to verify the professional appearance.');

  } catch (error) {
    console.error('❌ Error during brown color elimination:', error);
    process.exit(1);
  }
}

// Run the script
main();
