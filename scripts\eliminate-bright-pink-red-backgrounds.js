#!/usr/bin/env node

/**
 * EMERGENCY BRIGHT PINK/RED BACKGROUND ELIMINATION
 * 
 * This script systematically eliminates ALL bright pink/red backgrounds
 * and replaces them with professional medical-appropriate colors.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🚨 EMERGENCY BRIGHT PINK/RED BACKGROUND ELIMINATION - Starting...\n');

// COMPREHENSIVE BRIGHT COLOR ELIMINATION FIXES
const BRIGHT_COLOR_ELIMINATION_FIXES = [
  // 1. CRITICAL: Bright destructive/error backgrounds (BRIGHT PINK/RED)
  {
    pattern: /bg-error-light\/30/g,
    replacement: 'bg-muted/50',
    desc: 'PROFESSIONAL: Bright error background → subtle muted background'
  },
  {
    pattern: /bg-error-light\/20/g,
    replacement: 'bg-muted/30',
    desc: 'PROFESSIONAL: Light error background → subtle muted background'
  },
  {
    pattern: /bg-error\/20/g,
    replacement: 'bg-muted/40',
    desc: 'PROFESSIONAL: Error background → muted background'
  },
  {
    pattern: /bg-error\/10/g,
    replacement: 'bg-muted/30',
    desc: 'PROFESSIONAL: Light error background → muted background'
  },

  // 2. CRITICAL: Bright destructive borders
  {
    pattern: /border-error\/50/g,
    replacement: 'border-border',
    desc: 'PROFESSIONAL: Bright error border → subtle border'
  },
  {
    pattern: /border-error\/30/g,
    replacement: 'border-border/70',
    desc: 'PROFESSIONAL: Error border → subtle border'
  },

  // 3. CRITICAL: Emergency section backgrounds
  {
    pattern: /className="([^"]*?)bg-error-light\/30([^"]*?)"/g,
    replacement: 'className="$1bg-muted/50$2"',
    desc: 'PROFESSIONAL: Emergency section → subtle background'
  },
  {
    pattern: /className="([^"]*?)bg-error-light\/20([^"]*?)"/g,
    replacement: 'className="$1bg-muted/30$2"',
    desc: 'PROFESSIONAL: Emergency section → subtle background'
  },

  // 4. CRITICAL: Card backgrounds with bright colors
  {
    pattern: /bg-error-light\/30 dark:bg-error-light\/20 border-error\/50/g,
    replacement: 'bg-muted/50 dark:bg-muted/30 border-border',
    desc: 'PROFESSIONAL: Emergency card → professional styling'
  },
  {
    pattern: /bg-error-light\/30 text-foreground border-error\/50/g,
    replacement: 'bg-muted/50 text-foreground border-border',
    desc: 'PROFESSIONAL: Emergency card → professional styling'
  },

  // 5. CRITICAL: Dark theme bright colors
  {
    pattern: /dark:bg-error-light\/20/g,
    replacement: 'dark:bg-muted/30',
    desc: 'PROFESSIONAL: Dark theme error background → muted'
  },
  {
    pattern: /dark:bg-error-light\/10/g,
    replacement: 'dark:bg-muted/20',
    desc: 'PROFESSIONAL: Dark theme error background → muted'
  },
  {
    pattern: /dark:border-error\/30/g,
    replacement: 'dark:border-border',
    desc: 'PROFESSIONAL: Dark theme error border → subtle'
  },

  // 6. CRITICAL: Gradient backgrounds with destructive colors
  {
    pattern: /bg-gradient-to-r from-destructive\/5 to-destructive\/10/g,
    replacement: 'bg-muted/30',
    desc: 'PROFESSIONAL: Destructive gradient → subtle background'
  },
  {
    pattern: /bg-gradient-to-r from-destructive\/10 to-destructive\/5/g,
    replacement: 'bg-muted/30',
    desc: 'PROFESSIONAL: Destructive gradient → subtle background'
  },
  {
    pattern: /dark:from-destructive\/5 dark:to-destructive\/10/g,
    replacement: 'dark:bg-muted/20',
    desc: 'PROFESSIONAL: Dark destructive gradient → subtle'
  },

  // 7. CRITICAL: Emergency alert styling
  {
    pattern: /border-error\/50 bg-error-light\/30/g,
    replacement: 'border-border bg-muted/50',
    desc: 'PROFESSIONAL: Emergency alert → professional styling'
  },
  {
    pattern: /border-error\/30\/30 dark:bg-error-light\/10/g,
    replacement: 'border-border dark:bg-muted/20',
    desc: 'PROFESSIONAL: Alert styling → professional'
  },

  // 8. CRITICAL: Icon backgrounds with bright colors
  {
    pattern: /bg-error\/20 dark:bg-error\/10/g,
    replacement: 'bg-muted/40 dark:bg-muted/30',
    desc: 'PROFESSIONAL: Icon background → subtle'
  },
  {
    pattern: /bg-error/g,
    replacement: 'bg-muted',
    desc: 'PROFESSIONAL: Error background → muted background'
  },

  // 9. CRITICAL: Section backgrounds
  {
    pattern: /py-16 bg-error-light\/20 dark:bg-error-light\/10/g,
    replacement: 'py-16 bg-muted/30 dark:bg-muted/20',
    desc: 'PROFESSIONAL: Section background → subtle'
  },

  // 10. CRITICAL: Medical card borders
  {
    pattern: /border border-error\/30/g,
    replacement: 'border border-border/50',
    desc: 'PROFESSIONAL: Medical card border → subtle'
  },
  {
    pattern: /border-error/g,
    replacement: 'border-border',
    desc: 'PROFESSIONAL: Error border → theme border'
  },

  // 11. CRITICAL: Emergency contact styling
  {
    pattern: /bg-error-light\/30 rounded-lg p-4/g,
    replacement: 'bg-muted/50 rounded-lg p-4',
    desc: 'PROFESSIONAL: Emergency contact → subtle styling'
  },

  // 12. CRITICAL: Assessment tool styling
  {
    pattern: /bg-error hover:bg-error dark:bg-error dark:hover:bg-error/g,
    replacement: 'bg-primary hover:bg-primary/90 dark:bg-primary dark:hover:bg-primary/90',
    desc: 'PROFESSIONAL: Assessment button → primary theme'
  },

  // 13. CRITICAL: Warning section styling
  {
    pattern: /bg-error-light\/30 dark:bg-error-light\/20 border border-error\/50/g,
    replacement: 'bg-muted/50 dark:bg-muted/30 border border-border',
    desc: 'PROFESSIONAL: Warning section → professional styling'
  },

  // 14. CRITICAL: Emergency protocol styling
  {
    pattern: /bg-gradient-to-r from-destructive\/10 to-destructive\/5 dark:from-destructive\/5 dark:to-destructive\/10 border-error\/20/g,
    replacement: 'bg-muted/30 dark:bg-muted/20 border-border/50',
    desc: 'PROFESSIONAL: Emergency protocol → professional'
  },

  // 15. CRITICAL: Specific component fixes
  {
    pattern: /className="bg-error-light\/30 text-foreground border-error\/50"/g,
    replacement: 'className="bg-muted/50 text-foreground border-border"',
    desc: 'PROFESSIONAL: Component styling → professional'
  },

  // 16. CRITICAL: Alert component fixes
  {
    pattern: /Alert className="mb-8 border-error\/50 bg-error-light\/30 dark:border-error\/30\/30 dark:bg-error-light\/10"/g,
    replacement: 'Alert className="mb-8 border-border bg-muted/50 dark:border-border dark:bg-muted/30"',
    desc: 'PROFESSIONAL: Alert component → professional styling'
  },

  // 17. CRITICAL: Emergency assessment fixes
  {
    pattern: /bg-error-light\/30 dark:bg-error-light\/20 border border-error\/50 shadow-xl/g,
    replacement: 'bg-muted/50 dark:bg-muted/30 border border-border shadow-xl',
    desc: 'PROFESSIONAL: Emergency assessment → professional'
  },

  // 18. CRITICAL: Icon container fixes
  {
    pattern: /p-3 rounded-xl bg-error\/20 dark:bg-error\/10/g,
    replacement: 'p-3 rounded-xl bg-muted/40 dark:bg-muted/30',
    desc: 'PROFESSIONAL: Icon container → subtle background'
  },
  {
    pattern: /p-3 rounded-full bg-error/g,
    replacement: 'p-3 rounded-full bg-muted',
    desc: 'PROFESSIONAL: Icon container → muted background'
  },

  // 19. CRITICAL: Emergency button fixes
  {
    pattern: /Button variant="destructive"/g,
    replacement: 'Button variant="default"',
    desc: 'PROFESSIONAL: Emergency button → primary button'
  },

  // 20. CRITICAL: Final cleanup - any remaining error colors
  {
    pattern: /text-error/g,
    replacement: 'text-foreground',
    desc: 'PROFESSIONAL: Error text → foreground text'
  },

  // 21. CRITICAL: Badge fixes
  {
    pattern: /bg-info text-primary-foreground/g,
    replacement: 'bg-primary text-primary-foreground',
    desc: 'PROFESSIONAL: Badge styling → primary theme'
  },

  // 22. CRITICAL: Outline button fixes
  {
    pattern: /border-error\/50 text-foreground hover:bg-error\/10/g,
    replacement: 'border-border text-foreground hover:bg-muted/50',
    desc: 'PROFESSIONAL: Outline button → subtle styling'
  },

  // 23. CRITICAL: Medical card background fixes
  {
    pattern: /medical-card p-4 rounded-lg border border-error\/30/g,
    replacement: 'medical-card p-4 rounded-lg border border-border/50',
    desc: 'PROFESSIONAL: Medical card → subtle border'
  },

  // 24. CRITICAL: Emergency information styling
  {
    pattern: /bg-error-light\/20 border border-error\/50 rounded p-3/g,
    replacement: 'bg-muted/40 border border-border rounded p-3',
    desc: 'PROFESSIONAL: Emergency info → subtle styling'
  },

  // 25. CRITICAL: Any remaining bright backgrounds
  {
    pattern: /bg-red-([0-9]+)/g,
    replacement: 'bg-muted',
    desc: 'PROFESSIONAL: Red background → muted background'
  },
  {
    pattern: /bg-pink-([0-9]+)/g,
    replacement: 'bg-muted',
    desc: 'PROFESSIONAL: Pink background → muted background'
  },
  {
    pattern: /bg-rose-([0-9]+)/g,
    replacement: 'bg-muted',
    desc: 'PROFESSIONAL: Rose background → muted background'
  },

  // 26. CRITICAL: Text color fixes
  {
    pattern: /text-red-([0-9]+)/g,
    replacement: 'text-foreground',
    desc: 'PROFESSIONAL: Red text → foreground text'
  },
  {
    pattern: /text-pink-([0-9]+)/g,
    replacement: 'text-foreground',
    desc: 'PROFESSIONAL: Pink text → foreground text'
  },
  {
    pattern: /text-rose-([0-9]+)/g,
    replacement: 'text-foreground',
    desc: 'PROFESSIONAL: Rose text → foreground text'
  }
];

/**
 * Get all TypeScript, JavaScript, and CSS files in the project
 */
function getAllFiles() {
  const files = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules, .git, dist, and other build directories
        if (!['node_modules', '.git', 'dist', '.next', 'build', '.vite', 'scripts'].includes(item)) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && /\.(tsx?|jsx?|css|scss|sass|less)$/.test(item)) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(PROJECT_ROOT);
  return files;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  let processedContent = content;
  let totalChanges = 0;
  const changeLog = [];

  // Apply all bright color elimination fixes
  for (const fix of BRIGHT_COLOR_ELIMINATION_FIXES) {
    const matches = processedContent.match(fix.pattern);
    if (matches) {
      processedContent = processedContent.replace(fix.pattern, fix.replacement);
      const changeCount = matches.length;
      totalChanges += changeCount;
      changeLog.push(`🎨 ${fix.desc} (${changeCount} fixes)`);
    }
  }

  // Write the file if changes were made
  if (totalChanges > 0) {
    fs.writeFileSync(filePath, processedContent, 'utf8');
    console.log(`🔧 PROFESSIONAL STYLING: ${totalChanges} bright colors eliminated in ${relativePath}`);
    changeLog.forEach(change => console.log(`   ${change}`));
    console.log('');
  }

  return totalChanges;
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getAllFiles();
    let totalFiles = 0;
    let totalChanges = 0;

    console.log(`📁 Found ${files.length} files to process\n`);

    for (const file of files) {
      const changes = processFile(file);
      if (changes > 0) {
        totalFiles++;
        totalChanges += changes;
      }
    }

    console.log('🎯 BRIGHT PINK/RED BACKGROUND ELIMINATION COMPLETE!');
    console.log(`📊 Total files updated: ${totalFiles}`);
    console.log(`📊 Total bright colors eliminated: ${totalChanges}`);
    console.log('');
    console.log('✅ ALL bright pink/red backgrounds replaced with professional colors!');
    console.log('🔍 The design is now uniformly professional and medical-appropriate.');

  } catch (error) {
    console.error('❌ Error during bright color elimination:', error);
    process.exit(1);
  }
}

// Run the script
main();
