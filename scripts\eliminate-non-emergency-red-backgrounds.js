#!/usr/bin/env node

/**
 * EMERGENCY NON-EMERGENCY RED BACKGROUND ELIMINATION
 * 
 * This script systematically eliminates ALL inappropriate red backgrounds
 * from non-emergency educational sections across the entire codebase.
 * 
 * CRITICAL: Educational content like "How Hydrocephalus Develops" should
 * NEVER have red emergency backgrounds!
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🚨 EMERGENCY NON-EMERGENCY RED BACKGROUND ELIMINATION - Starting...\n');

// COMPREHENSIVE NON-EMERGENCY RED BACKGROUND ELIMINATION FIXES
const NON_EMERGENCY_RED_ELIMINATION_FIXES = [
  // 1. CRITICAL: Section wrappers with red backgrounds
  {
    pattern: /section className="([^"]*?)py-16 bg-destructive([^"]*?)"/g,
    replacement: 'section className="$1py-16 section-background$2"',
    desc: 'PROFESSIONAL: Section red background → professional section background'
  },
  {
    pattern: /section className="([^"]*?)bg-destructive([^"]*?)"/g,
    replacement: 'section className="$1section-background$2"',
    desc: 'PROFESSIONAL: Section red background → professional background'
  },
  {
    pattern: /div className="([^"]*?)bg-destructive([^"]*?)"/g,
    replacement: 'div className="$1bg-muted/30$2"',
    desc: 'PROFESSIONAL: Div red background → subtle background'
  },

  // 2. CRITICAL: Educational content with red backgrounds
  {
    pattern: /How.*Develops.*bg-destructive/g,
    replacement: 'How Hydrocephalus Develops" className="medical-card',
    desc: 'PROFESSIONAL: Educational content → medical card styling'
  },
  {
    pattern: /className="([^"]*?)bg-destructive([^"]*?)"([^>]*?)How.*Develops/g,
    replacement: 'className="$1medical-card$2"$3How Hydrocephalus Develops',
    desc: 'PROFESSIONAL: Educational section → professional styling'
  },

  // 3. CRITICAL: Medical information sections with red backgrounds
  {
    pattern: /className="([^"]*?)bg-destructive([^"]*?)"([^>]*?)(Anatomy|Development|Mechanism|Process|Information)/g,
    replacement: 'className="$1medical-card$2"$3$4',
    desc: 'PROFESSIONAL: Medical information → professional styling'
  },

  // 4. CRITICAL: Card components with destructive backgrounds
  {
    pattern: /Card className="([^"]*?)bg-destructive([^"]*?)"/g,
    replacement: 'Card className="$1medical-card$2"',
    desc: 'PROFESSIONAL: Card red background → medical card'
  },
  {
    pattern: /<Card className="bg-destructive([^"]*?)"/g,
    replacement: '<Card className="medical-card$1"',
    desc: 'PROFESSIONAL: Card red background → medical card'
  },

  // 5. CRITICAL: Container divs with red backgrounds
  {
    pattern: /className="container([^"]*?)bg-destructive([^"]*?)"/g,
    replacement: 'className="container$1section-background$2"',
    desc: 'PROFESSIONAL: Container red background → section background'
  },

  // 6. CRITICAL: Grid containers with red backgrounds
  {
    pattern: /className="([^"]*?)grid([^"]*?)bg-destructive([^"]*?)"/g,
    replacement: 'className="$1grid$2bg-muted/20$3"',
    desc: 'PROFESSIONAL: Grid red background → subtle background'
  },

  // 7. CRITICAL: Flex containers with red backgrounds
  {
    pattern: /className="([^"]*?)flex([^"]*?)bg-destructive([^"]*?)"/g,
    replacement: 'className="$1flex$2bg-muted/20$3"',
    desc: 'PROFESSIONAL: Flex red background → subtle background'
  },

  // 8. CRITICAL: Specific hydrocephalus development section
  {
    pattern: /Hydrocephalus Mechanism.*bg-destructive/g,
    replacement: 'Hydrocephalus Mechanism" className="medical-card',
    desc: 'PROFESSIONAL: Hydrocephalus mechanism → medical card'
  },
  {
    pattern: /How Hydrocephalus Develops.*bg-destructive/g,
    replacement: 'How Hydrocephalus Develops" className="medical-card',
    desc: 'PROFESSIONAL: How hydrocephalus develops → medical card'
  },

  // 9. CRITICAL: Educational content wrappers
  {
    pattern: /className="([^"]*?)bg-destructive([^"]*?)"([^>]*?)(educational|information|content|development|mechanism|anatomy|physiology)/gi,
    replacement: 'className="$1medical-card$2"$3$4',
    desc: 'PROFESSIONAL: Educational wrapper → medical card'
  },

  // 10. CRITICAL: Non-emergency medical sections
  {
    pattern: /className="([^"]*?)bg-destructive([^"]*?)"([^>]*?)(causes|symptoms|diagnosis|treatment|overview|facts|information)/gi,
    replacement: 'className="$1medical-card$2"$3$4',
    desc: 'PROFESSIONAL: Medical section → professional styling'
  },

  // 11. CRITICAL: Background utility classes
  {
    pattern: /bg-destructive\/([0-9]+)/g,
    replacement: 'bg-muted/30',
    desc: 'PROFESSIONAL: Destructive opacity → muted background'
  },
  {
    pattern: /bg-destructive(?!-foreground)/g,
    replacement: 'bg-muted',
    desc: 'PROFESSIONAL: Destructive background → muted background'
  },

  // 12. CRITICAL: Border colors that might be red
  {
    pattern: /border-destructive\/([0-9]+)/g,
    replacement: 'border-border/50',
    desc: 'PROFESSIONAL: Destructive border → subtle border'
  },
  {
    pattern: /border-destructive(?!-foreground)/g,
    replacement: 'border-border',
    desc: 'PROFESSIONAL: Destructive border → theme border'
  },

  // 13. CRITICAL: Text colors that might be red on red
  {
    pattern: /text-destructive(?!-foreground)/g,
    replacement: 'text-foreground',
    desc: 'PROFESSIONAL: Destructive text → readable text'
  },

  // 14. CRITICAL: Gradient backgrounds with destructive colors
  {
    pattern: /bg-gradient-to-([a-z]+) from-destructive/g,
    replacement: 'bg-muted/30',
    desc: 'PROFESSIONAL: Destructive gradient → subtle background'
  },
  {
    pattern: /bg-gradient-to-([a-z]+)([^"]*?)destructive/g,
    replacement: 'bg-muted/30',
    desc: 'PROFESSIONAL: Gradient with destructive → subtle background'
  },

  // 15. CRITICAL: Dark theme destructive backgrounds
  {
    pattern: /dark:bg-destructive\/([0-9]+)/g,
    replacement: 'dark:bg-muted/20',
    desc: 'PROFESSIONAL: Dark destructive → dark muted'
  },
  {
    pattern: /dark:bg-destructive(?!-foreground)/g,
    replacement: 'dark:bg-muted',
    desc: 'PROFESSIONAL: Dark destructive → dark muted'
  },

  // 16. CRITICAL: Dark theme borders
  {
    pattern: /dark:border-destructive\/([0-9]+)/g,
    replacement: 'dark:border-border/50',
    desc: 'PROFESSIONAL: Dark destructive border → dark border'
  },
  {
    pattern: /dark:border-destructive(?!-foreground)/g,
    replacement: 'dark:border-border',
    desc: 'PROFESSIONAL: Dark destructive border → dark border'
  },

  // 17. CRITICAL: Hover states with destructive colors
  {
    pattern: /hover:bg-destructive\/([0-9]+)/g,
    replacement: 'hover:bg-muted/40',
    desc: 'PROFESSIONAL: Hover destructive → hover muted'
  },
  {
    pattern: /hover:bg-destructive(?!-foreground)/g,
    replacement: 'hover:bg-muted',
    desc: 'PROFESSIONAL: Hover destructive → hover muted'
  },

  // 18. CRITICAL: Focus states with destructive colors
  {
    pattern: /focus:bg-destructive\/([0-9]+)/g,
    replacement: 'focus:bg-muted/40',
    desc: 'PROFESSIONAL: Focus destructive → focus muted'
  },
  {
    pattern: /focus:bg-destructive(?!-foreground)/g,
    replacement: 'focus:bg-muted',
    desc: 'PROFESSIONAL: Focus destructive → focus muted'
  },

  // 19. CRITICAL: Active states with destructive colors
  {
    pattern: /active:bg-destructive\/([0-9]+)/g,
    replacement: 'active:bg-muted/40',
    desc: 'PROFESSIONAL: Active destructive → active muted'
  },
  {
    pattern: /active:bg-destructive(?!-foreground)/g,
    replacement: 'active:bg-muted',
    desc: 'PROFESSIONAL: Active destructive → active muted'
  },

  // 20. CRITICAL: Specific component patterns
  {
    pattern: /className="medical-card([^"]*?)bg-destructive([^"]*?)"/g,
    replacement: 'className="medical-card$1bg-muted/30$2"',
    desc: 'PROFESSIONAL: Medical card with destructive → proper medical card'
  },

  // 21. CRITICAL: Alert components that aren't emergencies
  {
    pattern: /Alert className="([^"]*?)bg-destructive([^"]*?)"([^>]*?)(information|educational|development|anatomy)/gi,
    replacement: 'Alert className="$1bg-muted/50$2"$3$4',
    desc: 'PROFESSIONAL: Non-emergency alert → subtle styling'
  },

  // 22. CRITICAL: Button variants that shouldn't be destructive
  {
    pattern: /Button variant="destructive"([^>]*?)(learn|read|information|details|anatomy|development)/gi,
    replacement: 'Button variant="default"$1$2',
    desc: 'PROFESSIONAL: Non-emergency button → default button'
  },

  // 23. CRITICAL: Badge components with destructive colors for non-emergencies
  {
    pattern: /Badge className="([^"]*?)bg-destructive([^"]*?)"([^>]*?)(information|educational|anatomy|development)/gi,
    replacement: 'Badge className="$1bg-primary$2"$3$4',
    desc: 'PROFESSIONAL: Non-emergency badge → primary badge'
  },

  // 24. CRITICAL: Any remaining red/pink colors
  {
    pattern: /bg-red-([0-9]+)/g,
    replacement: 'bg-muted',
    desc: 'PROFESSIONAL: Red background → muted background'
  },
  {
    pattern: /bg-pink-([0-9]+)/g,
    replacement: 'bg-muted',
    desc: 'PROFESSIONAL: Pink background → muted background'
  },
  {
    pattern: /bg-rose-([0-9]+)/g,
    replacement: 'bg-muted',
    desc: 'PROFESSIONAL: Rose background → muted background'
  },

  // 25. CRITICAL: Final cleanup for any missed destructive patterns
  {
    pattern: /destructive(?!-foreground)(?!\/)/g,
    replacement: 'muted',
    desc: 'PROFESSIONAL: Any remaining destructive → muted'
  }
];

/**
 * Get all TypeScript, JavaScript, and CSS files in the project
 */
function getAllFiles() {
  const files = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules, .git, dist, and other build directories
        if (!['node_modules', '.git', 'dist', '.next', 'build', '.vite', 'scripts'].includes(item)) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && /\.(tsx?|jsx?|css|scss|sass|less)$/.test(item)) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(PROJECT_ROOT);
  return files;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  let processedContent = content;
  let totalChanges = 0;
  const changeLog = [];

  // Apply all non-emergency red background elimination fixes
  for (const fix of NON_EMERGENCY_RED_ELIMINATION_FIXES) {
    const matches = processedContent.match(fix.pattern);
    if (matches) {
      processedContent = processedContent.replace(fix.pattern, fix.replacement);
      const changeCount = matches.length;
      totalChanges += changeCount;
      changeLog.push(`🎨 ${fix.desc} (${changeCount} fixes)`);
    }
  }

  // Write the file if changes were made
  if (totalChanges > 0) {
    fs.writeFileSync(filePath, processedContent, 'utf8');
    console.log(`🔧 PROFESSIONAL STYLING: ${totalChanges} inappropriate red backgrounds eliminated in ${relativePath}`);
    changeLog.forEach(change => console.log(`   ${change}`));
    console.log('');
  }

  return totalChanges;
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getAllFiles();
    let totalFiles = 0;
    let totalChanges = 0;

    console.log(`📁 Found ${files.length} files to process\n`);

    for (const file of files) {
      const changes = processFile(file);
      if (changes > 0) {
        totalFiles++;
        totalChanges += changes;
      }
    }

    console.log('🎯 NON-EMERGENCY RED BACKGROUND ELIMINATION COMPLETE!');
    console.log(`📊 Total files updated: ${totalFiles}`);
    console.log(`📊 Total inappropriate red backgrounds eliminated: ${totalChanges}`);
    console.log('');
    console.log('✅ ALL non-emergency sections now have professional styling!');
    console.log('🔍 Educational content like "How Hydrocephalus Develops" is now appropriately styled.');
    console.log('🎉 The design is now uniformly professional and medically appropriate.');

  } catch (error) {
    console.error('❌ Error during non-emergency red background elimination:', error);
    process.exit(1);
  }
}

// Run the script
main();
