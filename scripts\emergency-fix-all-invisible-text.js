#!/usr/bin/env node

/**
 * EMERGENCY FIX FOR ALL INVISIBLE TEXT ISSUES
 * 
 * This script performs an EMERGENCY comprehensive fix for ALL remaining
 * text visibility issues that are causing unreadable text across the codebase.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🚨 EMERGENCY INVISIBLE TEXT FIX - COMPREHENSIVE RESOLUTION...\n');

// EMERGENCY FIXES FOR ALL REMAINING INVISIBLE TEXT ISSUES
const EMERGENCY_INVISIBLE_TEXT_FIXES = [
  // 1. CRITICAL: Solid destructive backgrounds (RED BACKGROUNDS)
  {
    pattern: /className="bg-destructive text-destructive-foreground border-destructive"/g,
    replacement: 'className="bg-error-light/30 text-foreground border-error/50"',
    desc: 'EMERGENCY: Solid red background → readable light background'
  },
  {
    pattern: /className="bg-destructive text-destructive-foreground"/g,
    replacement: 'className="bg-error-light/30 text-foreground"',
    desc: 'EMERGENCY: Solid red background → readable light background'
  },
  {
    pattern: /className="bg-destructive border-destructive"/g,
    replacement: 'className="bg-error-light/30 border-error/50"',
    desc: 'EMERGENCY: Solid red background → readable light background'
  },

  // 2. CRITICAL: Error backgrounds with error text (INVISIBLE)
  {
    pattern: /className="bg-error text-error"/g,
    replacement: 'className="bg-error-light/30 text-foreground"',
    desc: 'EMERGENCY: Error text on error background → visible'
  },
  {
    pattern: /className="bg-error border-error"/g,
    replacement: 'className="bg-error-light/30 border-error/50"',
    desc: 'EMERGENCY: Error background → readable'
  },

  // 3. CRITICAL: Info backgrounds with info text (INVISIBLE)
  {
    pattern: /className="bg-info text-info"/g,
    replacement: 'className="bg-info-light/30 text-foreground"',
    desc: 'EMERGENCY: Info text on info background → visible'
  },
  {
    pattern: /className="bg-info border-info"/g,
    replacement: 'className="bg-info-light/30 border-info/50"',
    desc: 'EMERGENCY: Info background → readable'
  },

  // 4. CRITICAL: Success backgrounds with success text (INVISIBLE)
  {
    pattern: /className="bg-success text-success"/g,
    replacement: 'className="bg-success-light/30 text-foreground"',
    desc: 'EMERGENCY: Success text on success background → visible'
  },
  {
    pattern: /className="bg-success border-success"/g,
    replacement: 'className="bg-success-light/30 border-success/50"',
    desc: 'EMERGENCY: Success background → readable'
  },

  // 5. CRITICAL: Medical blue backgrounds with medical blue text (INVISIBLE)
  {
    pattern: /className="bg-medical-blue text-medical-blue"/g,
    replacement: 'className="bg-medical-blue-light/30 text-foreground"',
    desc: 'EMERGENCY: Medical blue text on medical blue background → visible'
  },

  // 6. CRITICAL: Specific emergency component fixes
  {
    pattern: /className="text-error"/g,
    replacement: 'className="text-foreground"',
    desc: 'EMERGENCY: Error text colour → readable foreground'
  },
  {
    pattern: /text-error/g,
    replacement: 'text-foreground',
    desc: 'EMERGENCY: All error text → readable foreground'
  },

  // 7. CRITICAL: Emergency contact card fixes
  {
    pattern: /border-destructive\/30/g,
    replacement: 'border-error/30',
    desc: 'EMERGENCY: Destructive borders → error theme'
  },
  {
    pattern: /bg-destructive\/20/g,
    replacement: 'bg-error-light/30',
    desc: 'EMERGENCY: Destructive backgrounds → error light'
  },
  {
    pattern: /bg-destructive\/10/g,
    replacement: 'bg-error-light/20',
    desc: 'EMERGENCY: Light destructive backgrounds → error light'
  },

  // 8. CRITICAL: Dark theme fixes
  {
    pattern: /dark:bg-destructive\/5/g,
    replacement: 'dark:bg-error-light/10',
    desc: 'EMERGENCY: Dark destructive backgrounds → error light'
  },
  {
    pattern: /dark:border-destructive/g,
    replacement: 'dark:border-error/30',
    desc: 'EMERGENCY: Dark destructive borders → error theme'
  },

  // 9. CRITICAL: Alert component fixes
  {
    pattern: /border-destructive\/50 bg-error-light\/30 dark:border-error\/30\/30 dark:bg-destructive\/5/g,
    replacement: 'border-error/50 bg-error-light/30 dark:border-error/30 dark:bg-error-light/10',
    desc: 'EMERGENCY: Alert styling → proper contrast'
  },

  // 10. CRITICAL: Emergency protocol fixes
  {
    pattern: /bg-destructive/g,
    replacement: 'bg-error',
    desc: 'EMERGENCY: All destructive backgrounds → error theme'
  },
  {
    pattern: /text-destructive/g,
    replacement: 'text-foreground',
    desc: 'EMERGENCY: All destructive text → readable foreground'
  },
  {
    pattern: /border-destructive/g,
    replacement: 'border-error',
    desc: 'EMERGENCY: All destructive borders → error theme'
  },

  // 11. CRITICAL: Specific component pattern fixes
  {
    pattern: /className="([^"]*?)bg-info([^"]*?)text-info([^"]*?)"/g,
    replacement: 'className="$1bg-info-light/30$2text-foreground$3"',
    desc: 'EMERGENCY: Info background with info text → visible'
  },
  {
    pattern: /className="([^"]*?)text-info([^"]*?)bg-info([^"]*?)"/g,
    replacement: 'className="$1text-foreground$2bg-info-light/30$3"',
    desc: 'EMERGENCY: Info text with info background → visible'
  },
  {
    pattern: /className="([^"]*?)bg-success([^"]*?)text-success([^"]*?)"/g,
    replacement: 'className="$1bg-success-light/30$2text-foreground$3"',
    desc: 'EMERGENCY: Success background with success text → visible'
  },
  {
    pattern: /className="([^"]*?)text-success([^"]*?)bg-success([^"]*?)"/g,
    replacement: 'className="$1text-foreground$2bg-success-light/30$3"',
    desc: 'EMERGENCY: Success text with success background → visible'
  },
  {
    pattern: /className="([^"]*?)bg-error([^"]*?)text-error([^"]*?)"/g,
    replacement: 'className="$1bg-error-light/30$2text-foreground$3"',
    desc: 'EMERGENCY: Error background with error text → visible'
  },
  {
    pattern: /className="([^"]*?)text-error([^"]*?)bg-error([^"]*?)"/g,
    replacement: 'className="$1text-foreground$2bg-error-light/30$3"',
    desc: 'EMERGENCY: Error text with error background → visible'
  },

  // 12. CRITICAL: Badge and button fixes
  {
    pattern: /variant="destructive"/g,
    replacement: 'variant="destructive"',
    desc: 'KEEP: Destructive variant is correct for buttons/badges'
  },

  // 13. CRITICAL: Specific emergency text fixes
  {
    pattern: /"text-enhanced-heading text-2xl font-bold mb-2">Emergency Contact</g,
    replacement: '"text-enhanced-heading text-2xl font-bold mb-2 text-foreground">Emergency Contact',
    desc: 'EMERGENCY: Emergency contact heading → visible'
  },
  {
    pattern: /"text-xl mb-4">Call 000 immediately for any sudden severe symptoms</g,
    replacement: '"text-xl mb-4 text-foreground">Call 000 immediately for any sudden severe symptoms',
    desc: 'EMERGENCY: Emergency instruction text → visible'
  },

  // 14. CRITICAL: Medical emergency card fixes
  {
    pattern: /bg-destructive\/20 rounded-lg p-4/g,
    replacement: 'bg-error-light/30 rounded-lg p-4',
    desc: 'EMERGENCY: Emergency card backgrounds → readable'
  },

  // 15. CRITICAL: Time-critical component fixes
  {
    pattern: /getUrgencyColor = \(urgency: string\) => \{[^}]*case 'immediate': return 'bg-destructive\/10 text-foreground border-destructive\/50';[^}]*\}/g,
    replacement: `getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return 'bg-error-light/30 text-foreground border-error/50';
      case 'urgent': return 'bg-info-light/30 text-foreground border-info/50';
      default: return 'bg-success-light/30 text-foreground border-success/50';
    }
  }`,
    desc: 'EMERGENCY: Urgency colour function → readable colors'
  },

  // 16. CRITICAL: Surgical risk component fixes
  {
    pattern: /'bg-error text-error'/g,
    replacement: "'bg-error-light/30 text-foreground'",
    desc: 'EMERGENCY: Surgical risk colors → readable'
  },
  {
    pattern: /'bg-info text-info'/g,
    replacement: "'bg-info-light/30 text-foreground'",
    desc: 'EMERGENCY: Surgical risk colors → readable'
  },
  {
    pattern: /'bg-success text-success'/g,
    replacement: "'bg-success-light/30 text-foreground'",
    desc: 'EMERGENCY: Surgical risk colors → readable'
  },

  // 17. CRITICAL: Final catch-all patterns
  {
    pattern: /text-info(?=.*bg-info(?!-light))/g,
    replacement: 'text-foreground',
    desc: 'EMERGENCY: Info text on info background → foreground'
  },
  {
    pattern: /text-success(?=.*bg-success(?!-light))/g,
    replacement: 'text-foreground',
    desc: 'EMERGENCY: Success text on success background → foreground'
  },
  {
    pattern: /text-error(?=.*bg-error(?!-light))/g,
    replacement: 'text-foreground',
    desc: 'EMERGENCY: Error text on error background → foreground'
  },

  // 18. CRITICAL: Emergency assessment tool fixes
  {
    pattern: /border-info\/30\/30\/50 bg-info\/10/g,
    replacement: 'border-info/50 bg-info-light/20',
    desc: 'EMERGENCY: Assessment tool styling → readable'
  },

  // 19. CRITICAL: Spinal anatomy emergency warning fix
  {
    pattern: /className="bg-error border-error"/g,
    replacement: 'className="bg-error-light/30 border-error/50"',
    desc: 'EMERGENCY: Spinal anatomy warning → readable'
  },
  {
    pattern: /className="flex items-centre gap-2 text-error"/g,
    replacement: 'className="flex items-centre gap-2 text-foreground"',
    desc: 'EMERGENCY: Warning title → readable'
  },
  {
    pattern: /className="text-error text-sm"/g,
    replacement: 'className="text-foreground text-sm"',
    desc: 'EMERGENCY: Warning text → readable'
  }
];

/**
 * Get all TypeScript, JavaScript, and CSS files in the project
 */
function getAllFiles() {
  const files = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules, .git, dist, and other build directories
        if (!['node_modules', '.git', 'dist', '.next', 'build', '.vite', 'scripts'].includes(item)) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && /\.(tsx?|jsx?|css|scss|sass|less)$/.test(item)) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(PROJECT_ROOT);
  return files;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  let processedContent = content;
  let totalChanges = 0;
  const changeLog = [];

  // Apply all emergency fixes
  for (const fix of EMERGENCY_INVISIBLE_TEXT_FIXES) {
    const matches = processedContent.match(fix.pattern);
    if (matches) {
      processedContent = processedContent.replace(fix.pattern, fix.replacement);
      const changeCount = matches.length;
      totalChanges += changeCount;
      changeLog.push(`🚨 ${fix.desc} (${changeCount} fixes)`);
    }
  }

  // Write the file if changes were made
  if (totalChanges > 0) {
    fs.writeFileSync(filePath, processedContent, 'utf8');
    console.log(`🔧 EMERGENCY FIX: ${totalChanges} invisible text issues fixed in ${relativePath}`);
    changeLog.forEach(change => console.log(`   ${change}`));
    console.log('');
  }

  return totalChanges;
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getAllFiles();
    let totalFiles = 0;
    let totalChanges = 0;

    console.log(`📁 Found ${files.length} files to process\n`);

    for (const file of files) {
      const changes = processFile(file);
      if (changes > 0) {
        totalFiles++;
        totalChanges += changes;
      }
    }

    console.log('🚨 EMERGENCY INVISIBLE TEXT FIX COMPLETE!');
    console.log(`📊 Total files updated: ${totalFiles}`);
    console.log(`📊 Total invisible text issues fixed: ${totalChanges}`);
    console.log('');
    console.log('✅ ALL invisible text issues should now be resolved!');
    console.log('🔍 All text should now be readable with proper contrast.');

  } catch (error) {
    console.error('❌ Error during emergency invisible text fix:', error);
    process.exit(1);
  }
}

// Run the script
main();
