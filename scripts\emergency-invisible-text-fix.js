#!/usr/bin/env node

/**
 * EMERGENCY INVISIBLE TEXT FIX
 * Fixes critical invisible text issues where text color matches background color
 * This script addresses the urgent issue shown in the screenshot
 */

import fs from 'fs';
import path from 'path';

// CRITICAL INVISIBLE TEXT PATTERNS
const EMERGENCY_FIXES = [
  // Dark overlay with dark text - CRITICAL ISSUE!
  { 
    pattern: /text-primary-foreground(?=.*bg-black)/g, 
    replacement: 'text-white', 
    desc: 'CRITICAL: text-primary-foreground over bg-black → text-white' 
  },
  { 
    pattern: /text-primary-foreground(?=.*from-black)/g, 
    replacement: 'text-white', 
    desc: 'CRITICAL: text-primary-foreground over gradient from-black → text-white' 
  },
  
  // Same color text and background combinations
  { 
    pattern: /className="([^"]*?)bg-info([^"]*?)text-info([^"]*?)"/g, 
    replacement: 'className="$1bg-info-light$2text-foreground$3"', 
    desc: 'CRITICAL: bg-info with text-info → visible' 
  },
  { 
    pattern: /className="([^"]*?)text-info([^"]*?)bg-info([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-info-light$3"', 
    desc: 'CRITICAL: text-info with bg-info → visible' 
  },
  
  { 
    pattern: /className="([^"]*?)bg-success([^"]*?)text-success([^"]*?)"/g, 
    replacement: 'className="$1bg-success-light$2text-foreground$3"', 
    desc: 'CRITICAL: bg-success with text-success → visible' 
  },
  { 
    pattern: /className="([^"]*?)text-success([^"]*?)bg-success([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-success-light$3"', 
    desc: 'CRITICAL: text-success with bg-success → visible' 
  },
  
  { 
    pattern: /className="([^"]*?)bg-error([^"]*?)text-error([^"]*?)"/g, 
    replacement: 'className="$1bg-error-light$2text-foreground$3"', 
    desc: 'CRITICAL: bg-error with text-error → visible' 
  },
  { 
    pattern: /className="([^"]*?)text-error([^"]*?)bg-error([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-error-light$3"', 
    desc: 'CRITICAL: text-error with bg-error → visible' 
  },
  
  { 
    pattern: /className="([^"]*?)bg-info([^"]*?)text-info([^"]*?)"/g, 
    replacement: 'className="$1bg-warning-light$2text-foreground$3"', 
    desc: 'CRITICAL: bg-info with text-info → visible' 
  },
  { 
    pattern: /className="([^"]*?)text-info([^"]*?)bg-info([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-warning-light$3"', 
    desc: 'CRITICAL: text-info with bg-info → visible' 
  },
  
  // Muted text on muted backgrounds
  { 
    pattern: /className="([^"]*?)bg-muted([^"]*?)text-muted([^"]*?)"/g, 
    replacement: 'className="$1bg-muted$2text-foreground$3"', 
    desc: 'CRITICAL: bg-muted with text-muted → visible' 
  },
  { 
    pattern: /className="([^"]*?)text-muted([^"]*?)bg-muted([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-muted$3"', 
    desc: 'CRITICAL: text-muted with bg-muted → visible' 
  },
  
  // Dark theme invisible text
  { 
    pattern: /className="([^"]*?)dark:bg-info([^"]*?)dark:text-info([^"]*?)"/g, 
    replacement: 'className="$1dark:bg-info-light$2dark:text-foreground$3"', 
    desc: 'CRITICAL: dark theme bg-info with text-info → visible' 
  },
  { 
    pattern: /className="([^"]*?)dark:text-info([^"]*?)dark:bg-info([^"]*?)"/g, 
    replacement: 'className="$1dark:text-foreground$2dark:bg-info-light$3"', 
    desc: 'CRITICAL: dark theme text-info with bg-info → visible' 
  },
  
  // Specific problematic patterns
  { 
    pattern: /text-info(?=.*bg-info(?!-light))/g, 
    replacement: 'text-foreground', 
    desc: 'CRITICAL: text-info on bg-info → text-foreground' 
  },
  { 
    pattern: /text-success(?=.*bg-success(?!-light))/g, 
    replacement: 'text-foreground', 
    desc: 'CRITICAL: text-success on bg-success → text-foreground' 
  },
  { 
    pattern: /text-error(?=.*bg-error(?!-light))/g, 
    replacement: 'text-foreground', 
    desc: 'CRITICAL: text-error on bg-error → text-foreground' 
  },
  { 
    pattern: /text-info(?=.*bg-info(?!-light))/g, 
    replacement: 'text-foreground', 
    desc: 'CRITICAL: text-info on bg-info → text-foreground' 
  }
];

function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    let changeCount = 0;

    EMERGENCY_FIXES.forEach(fix => {
      const matches = content.match(fix.pattern);
      if (matches) {
        content = content.replace(fix.pattern, fix.replacement);
        hasChanges = true;
        changeCount += matches.length;
        console.log(`✅ ${fix.desc} (${matches.length} fixes) in ${filePath}`);
      }
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`🔧 FIXED ${changeCount} invisible text issues in ${filePath}`);
      return changeCount;
    }
    
    return 0;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return 0;
  }
}

function scanDirectory(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  let totalFixes = 0;
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist' && item !== 'build') {
          scan(fullPath);
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        totalFixes += processFile(fullPath);
      }
    }
  }
  
  scan(dir);
  return totalFixes;
}

// Main execution
console.log('🚨 EMERGENCY INVISIBLE TEXT FIX - Starting...\n');

const srcDir = path.join(process.cwd(), 'src');
const totalFixes = scanDirectory(srcDir);

console.log(`\n🎯 EMERGENCY FIX COMPLETE!`);
console.log(`📊 Total invisible text issues fixed: ${totalFixes}`);

if (totalFixes > 0) {
  console.log('\n✅ Critical invisible text issues have been resolved!');
  console.log('🔍 Please test the application to verify text is now visible.');
} else {
  console.log('\n✅ No critical invisible text issues found.');
}
