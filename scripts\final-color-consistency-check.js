#!/usr/bin/env node

/**
 * FINAL COLOR CONSISTENCY CHECK SCRIPT
 * 
 * This script performs a comprehensive final check to ensure ALL colors
 * are consistent, professional, and follow the established theme system.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🔍 FINAL COLOR CONSISTENCY CHECK - Starting...\n');

// FINAL COLOR CONSISTENCY PATTERNS
const FINAL_COLOR_FIXES = [
  // 1. ANY REMAINING HARDCODED COLORS
  {
    pattern: /\bbg-slate-([0-9]+)\b/g,
    replacement: 'bg-muted',
    desc: 'FINAL: bg-slate-* → bg-muted'
  },
  {
    pattern: /\btext-slate-([0-9]+)\b/g,
    replacement: 'text-muted-foreground',
    desc: 'FINAL: text-slate-* → text-muted-foreground'
  },
  {
    pattern: /\bborder-slate-([0-9]+)\b/g,
    replacement: 'border-border',
    desc: 'FINAL: border-slate-* → border-border'
  },

  // 2. ZINC COLORS
  {
    pattern: /\bbg-zinc-([0-9]+)\b/g,
    replacement: 'bg-muted',
    desc: 'FINAL: bg-zinc-* → bg-muted'
  },
  {
    pattern: /\btext-zinc-([0-9]+)\b/g,
    replacement: 'text-muted-foreground',
    desc: 'FINAL: text-zinc-* → text-muted-foreground'
  },

  // 3. NEUTRAL COLORS
  {
    pattern: /\bbg-neutral-([0-9]+)\b/g,
    replacement: 'bg-muted',
    desc: 'FINAL: bg-neutral-* → bg-muted'
  },
  {
    pattern: /\btext-neutral-([0-9]+)\b/g,
    replacement: 'text-muted-foreground',
    desc: 'FINAL: text-neutral-* → text-muted-foreground'
  },

  // 4. STONE COLORS
  {
    pattern: /\bbg-stone-([0-9]+)\b/g,
    replacement: 'bg-muted',
    desc: 'FINAL: bg-stone-* → bg-muted'
  },
  {
    pattern: /\btext-stone-([0-9]+)\b/g,
    replacement: 'text-muted-foreground',
    desc: 'FINAL: text-stone-* → text-muted-foreground'
  },

  // 5. INDIGO COLORS (SHOULD USE PRIMARY)
  {
    pattern: /\bbg-indigo-([0-9]+)\b/g,
    replacement: 'bg-primary',
    desc: 'FINAL: bg-indigo-* → bg-primary'
  },
  {
    pattern: /\btext-indigo-([0-9]+)\b/g,
    replacement: 'text-primary',
    desc: 'FINAL: text-indigo-* → text-primary'
  },

  // 6. VIOLET/PURPLE COLORS (SHOULD USE MEDICAL-BLUE)
  {
    pattern: /\bbg-violet-([0-9]+)\b/g,
    replacement: 'bg-medical-blue-light',
    desc: 'FINAL: bg-violet-* → bg-medical-blue-light'
  },
  {
    pattern: /\btext-violet-([0-9]+)\b/g,
    replacement: 'text-medical-blue',
    desc: 'FINAL: text-violet-* → text-medical-blue'
  },

  // 7. FUCHSIA/PINK COLORS (SHOULD USE ERROR)
  {
    pattern: /\bbg-fuchsia-([0-9]+)\b/g,
    replacement: 'bg-error-light',
    desc: 'FINAL: bg-fuchsia-* → bg-error-light'
  },
  {
    pattern: /\btext-fuchsia-([0-9]+)\b/g,
    replacement: 'text-error',
    desc: 'FINAL: text-fuchsia-* → text-error'
  },
  {
    pattern: /\bbg-pink-([0-9]+)\b/g,
    replacement: 'bg-error-light',
    desc: 'FINAL: bg-pink-* → bg-error-light'
  },
  {
    pattern: /\btext-pink-([0-9]+)\b/g,
    replacement: 'text-error',
    desc: 'FINAL: text-pink-* → text-error'
  },

  // 8. ROSE COLORS (SHOULD USE ERROR)
  {
    pattern: /\bbg-rose-([0-9]+)\b/g,
    replacement: 'bg-error-light',
    desc: 'FINAL: bg-rose-* → bg-error-light'
  },
  {
    pattern: /\btext-rose-([0-9]+)\b/g,
    replacement: 'text-error',
    desc: 'FINAL: text-rose-* → text-error'
  },

  // 9. LIME COLORS (SHOULD USE SUCCESS)
  {
    pattern: /\bbg-lime-([0-9]+)\b/g,
    replacement: 'bg-success-light',
    desc: 'FINAL: bg-lime-* → bg-success-light'
  },
  {
    pattern: /\btext-lime-([0-9]+)\b/g,
    replacement: 'text-success',
    desc: 'FINAL: text-lime-* → text-success'
  },

  // 10. EMERALD COLORS (SHOULD USE SUCCESS)
  {
    pattern: /\bbg-emerald-([0-9]+)\b/g,
    replacement: 'bg-success-light',
    desc: 'FINAL: bg-emerald-* → bg-success-light'
  },
  {
    pattern: /\btext-emerald-([0-9]+)\b/g,
    replacement: 'text-success',
    desc: 'FINAL: text-emerald-* → text-success'
  },

  // 11. SKY/CYAN COLORS (SHOULD USE INFO)
  {
    pattern: /\bbg-sky-([0-9]+)\b/g,
    replacement: 'bg-info-light',
    desc: 'FINAL: bg-sky-* → bg-info-light'
  },
  {
    pattern: /\btext-sky-([0-9]+)\b/g,
    replacement: 'text-info',
    desc: 'FINAL: text-sky-* → text-info'
  },

  // 12. REMAINING GRADIENT FIXES
  {
    pattern: /\bfrom-slate-([0-9]+)\b/g,
    replacement: 'from-muted',
    desc: 'FINAL: Slate gradients → muted gradients'
  },
  {
    pattern: /\bto-slate-([0-9]+)\b/g,
    replacement: 'to-muted',
    desc: 'FINAL: Slate gradients → muted gradients'
  },
  {
    pattern: /\bvia-slate-([0-9]+)\b/g,
    replacement: 'via-muted',
    desc: 'FINAL: Slate gradients → muted gradients'
  },

  // 13. OPACITY FIXES
  {
    pattern: /\bbg-gray-([0-9]+)\/([0-9]+)\b/g,
    replacement: 'bg-muted/$2',
    desc: 'FINAL: Gray opacity → muted opacity'
  },
  {
    pattern: /\bbg-blue-([0-9]+)\/([0-9]+)\b/g,
    replacement: 'bg-info/$2',
    desc: 'FINAL: Blue opacity → info opacity'
  },

  // 14. DARK THEME CONSISTENCY
  {
    pattern: /\bdark:bg-gray-([0-9]+)\b/g,
    replacement: 'dark:bg-muted',
    desc: 'FINAL: Dark gray → dark:bg-muted'
  },
  {
    pattern: /\bdark:text-gray-([0-9]+)\b/g,
    replacement: 'dark:text-muted-foreground',
    desc: 'FINAL: Dark gray text → dark:text-muted-foreground'
  },

  // 15. BORDER CONSISTENCY
  {
    pattern: /\bborder-gray-([0-9]+)\/([0-9]+)\b/g,
    replacement: 'border-border/$2',
    desc: 'FINAL: Gray border opacity → border-border opacity'
  },

  // 16. HOVER STATE CONSISTENCY
  {
    pattern: /\bhover:bg-gray-([0-9]+)\b/g,
    replacement: 'hover:bg-muted',
    desc: 'FINAL: Gray hover → hover:bg-muted'
  },
  {
    pattern: /\bhover:text-gray-([0-9]+)\b/g,
    replacement: 'hover:text-muted-foreground',
    desc: 'FINAL: Gray hover text → hover:text-muted-foreground'
  }
];

/**
 * Get all TypeScript and JavaScript files in the project
 */
function getAllFiles() {
  const files = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules, .git, dist, and other build directories
        if (!['node_modules', '.git', 'dist', '.next', 'build', '.vite'].includes(item)) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && /\.(tsx?|jsx?|css|scss|sass|less)$/.test(item)) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(PROJECT_ROOT);
  return files;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  let processedContent = content;
  let totalChanges = 0;
  const changeLog = [];

  // Apply all final color consistency fixes
  for (const fix of FINAL_COLOR_FIXES) {
    const matches = processedContent.match(fix.pattern);
    if (matches) {
      processedContent = processedContent.replace(fix.pattern, fix.replacement);
      const changeCount = matches.length;
      totalChanges += changeCount;
      changeLog.push(`✅ ${fix.desc} (${changeCount} fixes)`);
    }
  }

  // Write the file if changes were made
  if (totalChanges > 0) {
    fs.writeFileSync(filePath, processedContent, 'utf8');
    console.log(`🔧 FINAL FIXES: ${totalChanges} color consistency improvements in ${relativePath}`);
    changeLog.forEach(change => console.log(`   ${change}`));
    console.log('');
  }

  return totalChanges;
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getAllFiles();
    let totalFiles = 0;
    let totalChanges = 0;

    console.log(`📁 Found ${files.length} files to process\n`);

    for (const file of files) {
      const changes = processFile(file);
      if (changes > 0) {
        totalFiles++;
        totalChanges += changes;
      }
    }

    console.log('🎯 FINAL COLOR CONSISTENCY CHECK COMPLETE!');
    console.log(`📊 Total files updated: ${totalFiles}`);
    console.log(`📊 Total final color fixes applied: ${totalChanges}`);
    console.log('');
    console.log('✅ ALL colors are now consistent and professional!');
    console.log('🔍 Color system is now uniformly flawless across the entire codebase.');

  } catch (error) {
    console.error('❌ Error during final color consistency check:', error);
    process.exit(1);
  }
}

// Run the script
main();
