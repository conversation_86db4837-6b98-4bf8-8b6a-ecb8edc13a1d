#!/usr/bin/env node

/**
 * FINAL SCAN: Find and fix any remaining invisible text patterns
 * This is a comprehensive scan to catch any remaining same-color text/background combinations
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

// Directories to scan
const SCAN_DIRECTORIES = [
  'src/components',
  'src/pages',
  'docs'
];

// FINAL INVISIBLE TEXT PATTERNS to catch any remaining issues
const FINAL_INVISIBLE_TEXT_FIXES = [
  // Any remaining bg-[color] with text-[same-color] patterns
  { 
    pattern: /className="([^"]*?)bg-info([^"]*?)text-info([^"]*?)"/g, 
    replacement: 'className="$1bg-info-light$2text-foreground$3"', 
    desc: 'FINAL: bg-info with text-info → visible' 
  },
  { 
    pattern: /className="([^"]*?)text-info([^"]*?)bg-info([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-info-light$3"', 
    desc: 'FINAL: text-info with bg-info → visible' 
  },
  
  { 
    pattern: /className="([^"]*?)bg-success([^"]*?)text-success([^"]*?)"/g, 
    replacement: 'className="$1bg-success-light$2text-foreground$3"', 
    desc: 'FINAL: bg-success with text-success → visible' 
  },
  { 
    pattern: /className="([^"]*?)text-success([^"]*?)bg-success([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-success-light$3"', 
    desc: 'FINAL: text-success with bg-success → visible' 
  },
  
  { 
    pattern: /className="([^"]*?)bg-error([^"]*?)text-error([^"]*?)"/g, 
    replacement: 'className="$1bg-error-light$2text-foreground$3"', 
    desc: 'FINAL: bg-error with text-error → visible' 
  },
  { 
    pattern: /className="([^"]*?)text-error([^"]*?)bg-error([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-error-light$3"', 
    desc: 'FINAL: text-error with bg-error → visible' 
  },
  
  { 
    pattern: /className="([^"]*?)bg-info([^"]*?)text-info([^"]*?)"/g, 
    replacement: 'className="$1bg-warning-light$2text-foreground$3"', 
    desc: 'FINAL: bg-info with text-info → visible' 
  },
  { 
    pattern: /className="([^"]*?)text-info([^"]*?)bg-info([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-warning-light$3"', 
    desc: 'FINAL: text-info with bg-info → visible' 
  },
  
  { 
    pattern: /className="([^"]*?)bg-medical-blue([^"]*?)text-medical-blue([^"]*?)"/g, 
    replacement: 'className="$1bg-medical-blue-light$2text-foreground$3"', 
    desc: 'FINAL: bg-medical-blue with text-medical-blue → visible' 
  },
  { 
    pattern: /className="([^"]*?)text-medical-blue([^"]*?)bg-medical-blue([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-medical-blue-light$3"', 
    desc: 'FINAL: text-medical-blue with bg-medical-blue → visible' 
  },
  
  // Catch any remaining dark theme invisible text
  { 
    pattern: /className="([^"]*?)dark:bg-info([^"]*?)dark:text-info([^"]*?)"/g, 
    replacement: 'className="$1dark:bg-info-light$2dark:text-foreground$3"', 
    desc: 'FINAL: dark theme bg-info with text-info → visible' 
  },
  { 
    pattern: /className="([^"]*?)dark:text-info([^"]*?)dark:bg-info([^"]*?)"/g, 
    replacement: 'className="$1dark:text-foreground$2dark:bg-info-light$3"', 
    desc: 'FINAL: dark theme text-info with bg-info → visible' 
  },
  
  // Catch any remaining colored text on same colored backgrounds in specific patterns
  { 
    pattern: /text-info(?=.*bg-info(?!-light))/g, 
    replacement: 'text-foreground', 
    desc: 'FINAL: text-info on bg-info background → text-foreground' 
  },
  { 
    pattern: /text-success(?=.*bg-success(?!-light))/g, 
    replacement: 'text-foreground', 
    desc: 'FINAL: text-success on bg-success background → text-foreground' 
  },
  { 
    pattern: /text-error(?=.*bg-error(?!-light))/g, 
    replacement: 'text-foreground', 
    desc: 'FINAL: text-error on bg-error background → text-foreground' 
  },
  { 
    pattern: /text-info(?=.*bg-info(?!-light))/g, 
    replacement: 'text-foreground', 
    desc: 'FINAL: text-info on bg-info background → text-foreground' 
  },
  
  // Fix any remaining muted text on muted backgrounds
  { 
    pattern: /className="([^"]*?)bg-muted([^"]*?)text-muted([^"]*?)"/g, 
    replacement: 'className="$1bg-muted$2text-foreground$3"', 
    desc: 'FINAL: bg-muted with text-muted → visible' 
  },
  { 
    pattern: /className="([^"]*?)text-muted([^"]*?)bg-muted([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-muted$3"', 
    desc: 'FINAL: text-muted with bg-muted → visible' 
  },
  
  // Fix any remaining border color issues that might affect visibility
  { 
    pattern: /border-info(?=.*bg-info(?!-light))/g, 
    replacement: 'border-info/30', 
    desc: 'FINAL: border-info on bg-info → border-info/30' 
  },
  { 
    pattern: /border-success(?=.*bg-success(?!-light))/g, 
    replacement: 'border-success/30', 
    desc: 'FINAL: border-success on bg-success → border-success/30' 
  },
  { 
    pattern: /border-error(?=.*bg-error(?!-light))/g, 
    replacement: 'border-error/30', 
    desc: 'FINAL: border-error on bg-error → border-error/30' 
  },
  { 
    pattern: /border-info(?=.*bg-info(?!-light))/g, 
    replacement: 'border-info/30', 
    desc: 'FINAL: border-info on bg-info → border-info/30' 
  }
];

/**
 * Get all files to process
 */
function getAllFiles(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  const files = [];
  
  function scanDirectory(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not read directory ${currentDir}: ${error.message}`);
    }
  }
  
  scanDirectory(dir);
  return files;
}

/**
 * Scan for invisible text patterns in a single file
 */
function scanForInvisibleText(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasIssues = false;
    const foundIssues = [];

    FINAL_INVISIBLE_TEXT_FIXES.forEach(({ pattern, replacement, desc }) => {
      const matches = content.match(pattern);
      if (matches) {
        content = content.replace(pattern, replacement);
        hasIssues = true;
        foundIssues.push(`${desc} (${matches.length} instances)`);
      }
    });

    if (hasIssues) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`🚨 FINAL FIX applied to: ${path.relative(PROJECT_ROOT, filePath)}`);
      foundIssues.forEach(issue => console.log(`   - ${issue}`));
      return true;
    } else {
      console.log(`✅ No invisible text found in: ${path.relative(PROJECT_ROOT, filePath)}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Main execution
 */
function main() {
  console.log('🔍 FINAL SCAN: Looking for any remaining invisible text patterns...\n');
  
  let totalFiles = 0;
  let fixedFiles = 0;
  
  SCAN_DIRECTORIES.forEach(dir => {
    const fullDir = path.join(PROJECT_ROOT, dir);
    if (fs.existsSync(fullDir)) {
      console.log(`📁 Scanning directory: ${dir}`);
      const files = getAllFiles(fullDir);
      
      files.forEach(file => {
        totalFiles++;
        if (scanForInvisibleText(file)) {
          fixedFiles++;
        }
      });
      
      console.log('');
    }
  });
  
  console.log('🎯 FINAL SCAN Summary:');
  console.log(`   Total files scanned: ${totalFiles}`);
  console.log(`   Files with remaining invisible text FIXED: ${fixedFiles}`);
  console.log(`   Files already clean: ${totalFiles - fixedFiles}`);
  
  if (fixedFiles > 0) {
    console.log('\n🚨 FINAL FIXES APPLIED!');
    console.log('✅ All remaining invisible text has been made visible!');
    console.log('🎨 The codebase should now be completely free of invisible text issues.');
  } else {
    console.log('\n✅ PERFECT! No remaining invisible text found!');
    console.log('🎨 The codebase is completely clean of invisible text issues.');
  }
}

main();
