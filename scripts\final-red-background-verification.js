#!/usr/bin/env node

/**
 * FINAL RED BACKGROUND VERIFICATION
 * 
 * This script performs a comprehensive final verification that NO red backgrounds
 * exist on non-emergency sections, especially the Hydrocephalus page.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🔍 FINAL RED BACKGROUND VERIFICATION - Starting comprehensive scan...\n');

// CRITICAL PATTERNS TO DETECT ANY REMAINING RED BACKGROUNDS
const CRITICAL_RED_PATTERNS = [
  // 1. CRITICAL: Any destructive backgrounds in non-emergency contexts
  { pattern: /section.*bg-destructive/g, desc: 'CRITICAL: Section with destructive background' },
  { pattern: /div.*bg-destructive/g, desc: 'CRITICAL: Div with destructive background' },
  { pattern: /Card.*bg-destructive/g, desc: 'CRITICAL: Card with destructive background' },
  
  // 2. CRITICAL: Hydrocephalus specific issues
  { pattern: /hydrocephalus.*bg-destructive/gi, desc: 'CRITICAL: Hydrocephalus with destructive background' },
  { pattern: /CSF.*bg-destructive/gi, desc: 'CRITICAL: CSF content with destructive background' },
  { pattern: /How.*Develops.*bg-destructive/gi, desc: 'CRITICAL: How Develops with destructive background' },
  
  // 3. CRITICAL: CSS variable issues
  { pattern: /--muted:\s*0\s+[0-9]+%\s+[0-9]+%/g, desc: 'CRITICAL: Muted variable set to red' },
  { pattern: /section-background-alt.*bg-muted/g, desc: 'CRITICAL: section-background-alt using muted' },
  
  // 4. CRITICAL: Direct red color classes
  { pattern: /bg-red-([0-9]+)/g, desc: 'CRITICAL: Direct red background' },
  { pattern: /bg-pink-([0-9]+)/g, desc: 'CRITICAL: Direct pink background' },
  { pattern: /bg-rose-([0-9]+)/g, desc: 'CRITICAL: Direct rose background' },
  
  // 5. CRITICAL: Educational content with emergency styling
  { pattern: /(educational|information|anatomy|development|mechanism).*destructive/gi, desc: 'CRITICAL: Educational content with emergency styling' },
  
  // 6. WARNING: Potential issues
  { pattern: /bg-gradient.*destructive/g, desc: 'WARNING: Gradient with destructive colors' },
  { pattern: /from-destructive|to-destructive/g, desc: 'WARNING: Gradient using destructive' },
  
  // 7. CRITICAL: Specific component issues
  { pattern: /CSFAnatomySection.*destructive/gi, desc: 'CRITICAL: CSFAnatomySection with destructive styling' },
  { pattern: /section-background-alt.*destructive/g, desc: 'CRITICAL: section-background-alt with destructive' }
];

/**
 * Get all relevant files
 */
function getAllFiles() {
  const files = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        if (!['node_modules', '.git', 'dist', '.next', 'build', '.vite', 'scripts'].includes(item)) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && /\.(tsx?|jsx?|css|scss|sass|less)$/.test(item)) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(PROJECT_ROOT);
  return files;
}

/**
 * Scan a single file for red background issues
 */
function scanFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  const foundIssues = [];

  // Check each pattern
  for (const redPattern of CRITICAL_RED_PATTERNS) {
    const matches = content.match(redPattern.pattern);
    if (matches) {
      foundIssues.push({
        pattern: redPattern.desc,
        count: matches.length,
        examples: [...new Set(matches)].slice(0, 3)
      });
    }
  }

  return foundIssues.length > 0 ? { file: relativePath, issues: foundIssues } : null;
}

/**
 * Check specific hydrocephalus files
 */
function checkHydrocephalusFiles() {
  const hydrocephalusFiles = [
    'src/components/medical-conditions/hydrocephalus/CSFAnatomySection.tsx',
    'src/pages/patient-resources/conditions/Hydrocephalus.tsx',
    'src/index.css'
  ];

  console.log('🎯 HYDROCEPHALUS SPECIFIC VERIFICATION:\n');

  for (const file of hydrocephalusFiles) {
    const fullPath = path.join(PROJECT_ROOT, file);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // Check for specific issues
      const hasDestructive = content.includes('bg-destructive');
      const hasSectionBackgroundAlt = content.includes('section-background-alt');
      const hasRedColors = /bg-red-|bg-pink-|bg-rose-/.test(content);
      const hasMutedRed = /--muted:\s*0\s+[0-9]+%/.test(content);
      
      console.log(`📁 ${file}:`);
      console.log(`   ✅ No bg-destructive: ${!hasDestructive}`);
      console.log(`   ✅ No section-background-alt: ${!hasSectionBackgroundAlt}`);
      console.log(`   ✅ No direct red colors: ${!hasRedColors}`);
      console.log(`   ✅ No red muted variables: ${!hasMutedRed}`);
      
      if (hasDestructive || hasSectionBackgroundAlt || hasRedColors || hasMutedRed) {
        console.log(`   🚨 ISSUES FOUND IN ${file}!`);
      } else {
        console.log(`   ✅ CLEAN - No red background issues`);
      }
      console.log('');
    }
  }
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getAllFiles();
    const results = [];
    let totalFiles = 0;
    let totalIssues = 0;
    let criticalIssues = 0;
    let warningIssues = 0;

    console.log(`📁 Scanning ${files.length} files for red background issues...\n`);

    for (const file of files) {
      const result = scanFile(file);
      if (result) {
        results.push(result);
        totalFiles++;
        
        const fileIssueCount = result.issues.reduce((sum, issue) => sum + issue.count, 0);
        totalIssues += fileIssueCount;
        
        console.log(`🚨 ${result.file} (${fileIssueCount} issues)`);
        result.issues.forEach(issue => {
          console.log(`   ${issue.pattern}: ${issue.count} instances`);
          if (issue.examples.length > 0) {
            console.log(`   Examples: ${issue.examples.join(', ')}`);
          }
          
          if (issue.pattern.includes('CRITICAL')) criticalIssues += issue.count;
          else if (issue.pattern.includes('WARNING')) warningIssues += issue.count;
        });
        console.log('');
      }
    }

    // Check hydrocephalus files specifically
    checkHydrocephalusFiles();

    console.log('📊 FINAL RED BACKGROUND VERIFICATION COMPLETE!');
    console.log(`📁 Files with red background issues: ${totalFiles}`);
    console.log(`🚨 Total red background issues found: ${totalIssues}`);
    console.log(`🚨 Critical issues: ${criticalIssues}`);
    console.log(`⚠️  Warning issues: ${warningIssues}`);
    
    if (totalIssues === 0) {
      console.log('\n🎉 PERFECT! NO RED BACKGROUND ISSUES FOUND!');
      console.log('✅ The "How Hydrocephalus Develops" section is now properly styled.');
      console.log('✅ All educational content has appropriate professional styling.');
      console.log('✅ The design is uniformly professional and medically appropriate.');
      console.log('🔍 The red background issue has been completely resolved!');
    } else {
      console.log('\n🚨 RED BACKGROUND ISSUES STILL EXIST!');
      if (criticalIssues > 0) {
        console.log('🚨 CRITICAL: Red backgrounds still present on non-emergency content!');
      }
      if (warningIssues > 0) {
        console.log('⚠️  WARNING: Potentially inappropriate red styling detected.');
      }
    }

  } catch (error) {
    console.error('❌ Error during final red background verification:', error);
    process.exit(1);
  }
}

// Run the script
main();
