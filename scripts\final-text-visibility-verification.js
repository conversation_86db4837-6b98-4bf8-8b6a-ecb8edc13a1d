#!/usr/bin/env node

/**
 * FINAL TEXT VISIBILITY VERIFICATION
 * 
 * This script performs a comprehensive scan to verify that ALL text visibility
 * issues have been resolved and no invisible text remains in the codebase.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🔍 FINAL TEXT VISIBILITY VERIFICATION - Starting comprehensive scan...\n');

// PATTERNS TO DETECT REMAINING TEXT VISIBILITY ISSUES
const VISIBILITY_ISSUE_PATTERNS = [
  // Same colour text and background combinations (INVISIBLE TEXT)
  { pattern: /className="[^"]*bg-info[^"]*text-info[^"]*"/g, desc: 'CRITICAL: bg-info with text-info (invisible)' },
  { pattern: /className="[^"]*text-info[^"]*bg-info[^"]*"/g, desc: 'CRITICAL: text-info with bg-info (invisible)' },
  { pattern: /className="[^"]*bg-success[^"]*text-success[^"]*"/g, desc: 'CRITICAL: bg-success with text-success (invisible)' },
  { pattern: /className="[^"]*text-success[^"]*bg-success[^"]*"/g, desc: 'CRITICAL: text-success with bg-success (invisible)' },
  { pattern: /className="[^"]*bg-error[^"]*text-error[^"]*"/g, desc: 'CRITICAL: bg-error with text-error (invisible)' },
  { pattern: /className="[^"]*text-error[^"]*bg-error[^"]*"/g, desc: 'CRITICAL: text-error with bg-error (invisible)' },
  { pattern: /className="[^"]*bg-muted[^"]*text-muted[^"]*"/g, desc: 'CRITICAL: bg-muted with text-muted (invisible)' },
  { pattern: /className="[^"]*text-muted[^"]*bg-muted[^"]*"/g, desc: 'CRITICAL: text-muted with bg-muted (invisible)' },
  
  // Destructive colour contrast issues
  { pattern: /text-destructive(?=.*bg-destructive)/g, desc: 'WARNING: text-destructive on destructive background (poor contrast)' },
  { pattern: /className="[^"]*bg-destructive[^"]*text-destructive[^"]*"/g, desc: 'WARNING: destructive text on destructive background' },
  
  // Dark theme invisible text
  { pattern: /className="[^"]*dark:bg-info[^"]*dark:text-info[^"]*"/g, desc: 'CRITICAL: dark theme bg-info with text-info (invisible)' },
  { pattern: /className="[^"]*dark:text-info[^"]*dark:bg-info[^"]*"/g, desc: 'CRITICAL: dark theme text-info with bg-info (invisible)' },
  
  // Specific problematic patterns
  { pattern: /text-info(?=.*bg-info(?!-light))/g, desc: 'WARNING: text-info on bg-info background' },
  { pattern: /text-success(?=.*bg-success(?!-light))/g, desc: 'WARNING: text-success on bg-success background' },
  { pattern: /text-error(?=.*bg-error(?!-light))/g, desc: 'WARNING: text-error on bg-error background' },
  
  // Invalid CSS classes
  { pattern: /bg-error-light-light/g, desc: 'ERROR: Invalid CSS class bg-error-light-light' },
  { pattern: /bg-info-light-light/g, desc: 'ERROR: Invalid CSS class bg-info-light-light' },
  { pattern: /bg-success-light-light/g, desc: 'ERROR: Invalid CSS class bg-success-light-light' },
  
  // Hardcoded colors that might have been missed
  { pattern: /\bbg-red-[0-9]+\b/g, desc: 'HARDCODED: bg-red-* (should use theme)' },
  { pattern: /\btext-red-[0-9]+\b/g, desc: 'HARDCODED: text-red-* (should use theme)' },
  { pattern: /\bbg-blue-[0-9]+\b/g, desc: 'HARDCODED: bg-blue-* (should use theme)' },
  { pattern: /\btext-blue-[0-9]+\b/g, desc: 'HARDCODED: text-blue-* (should use theme)' },
  { pattern: /\bbg-green-[0-9]+\b/g, desc: 'HARDCODED: bg-green-* (should use theme)' },
  { pattern: /\btext-green-[0-9]+\b/g, desc: 'HARDCODED: text-green-* (should use theme)' },
  { pattern: /\bbg-yellow-[0-9]+\b/g, desc: 'HARDCODED: bg-yellow-* (should use theme)' },
  { pattern: /\btext-yellow-[0-9]+\b/g, desc: 'HARDCODED: text-yellow-* (should use theme)' },
  { pattern: /\bbg-gray-[0-9]+\b/g, desc: 'HARDCODED: bg-gray-* (should use theme)' },
  { pattern: /\btext-gray-[0-9]+\b/g, desc: 'HARDCODED: text-gray-* (should use theme)' }
];

/**
 * Get all TypeScript, JavaScript, and CSS files in the project
 */
function getAllFiles() {
  const files = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules, .git, dist, and other build directories
        if (!['node_modules', '.git', 'dist', '.next', 'build', '.vite', 'scripts'].includes(item)) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && /\.(tsx?|jsx?|css|scss|sass|less)$/.test(item)) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(PROJECT_ROOT);
  return files;
}

/**
 * Scan a single file for text visibility issues
 */
function scanFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  const foundIssues = [];

  // Check each pattern
  for (const issuePattern of VISIBILITY_ISSUE_PATTERNS) {
    const matches = content.match(issuePattern.pattern);
    if (matches) {
      foundIssues.push({
        pattern: issuePattern.desc,
        count: matches.length,
        examples: [...new Set(matches)].slice(0, 3) // Show up to 3 unique examples
      });
    }
  }

  return foundIssues.length > 0 ? { file: relativePath, issues: foundIssues } : null;
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getAllFiles();
    const results = [];
    let totalFiles = 0;
    let totalIssues = 0;
    let criticalIssues = 0;
    let warningIssues = 0;
    let errorIssues = 0;
    let hardcodedIssues = 0;

    console.log(`📁 Scanning ${files.length} files for text visibility issues...\n`);

    for (const file of files) {
      const result = scanFile(file);
      if (result) {
        results.push(result);
        totalFiles++;
        
        const fileIssueCount = result.issues.reduce((sum, issue) => sum + issue.count, 0);
        totalIssues += fileIssueCount;
        
        console.log(`⚠️  ${result.file} (${fileIssueCount} issues)`);
        result.issues.forEach(issue => {
          console.log(`   ${issue.pattern}: ${issue.count} instances`);
          if (issue.examples.length > 0) {
            console.log(`   Examples: ${issue.examples.join(', ')}`);
          }
          
          // Categorise issues
          if (issue.pattern.includes('CRITICAL')) criticalIssues += issue.count;
          else if (issue.pattern.includes('WARNING')) warningIssues += issue.count;
          else if (issue.pattern.includes('ERROR')) errorIssues += issue.count;
          else if (issue.pattern.includes('HARDCODED')) hardcodedIssues += issue.count;
        });
        console.log('');
      }
    }

    console.log('📊 FINAL TEXT VISIBILITY VERIFICATION COMPLETE!');
    console.log(`📁 Files with issues: ${totalFiles}`);
    console.log(`⚠️  Total issues found: ${totalIssues}`);
    console.log(`🚨 Critical issues (invisible text): ${criticalIssues}`);
    console.log(`⚠️  Warning issues (poor contrast): ${warningIssues}`);
    console.log(`❌ Error issues (invalid classes): ${errorIssues}`);
    console.log(`🎨 Hardcoded colour issues: ${hardcodedIssues}`);
    
    if (totalIssues === 0) {
      console.log('\n✅ PERFECT! NO TEXT VISIBILITY ISSUES FOUND!');
      console.log('🎉 All text is readable with proper contrast ratios.');
      console.log('🔍 The codebase is completely clean and professional.');
    } else {
      console.log('\n⚠️  TEXT VISIBILITY ISSUES STILL EXIST!');
      if (criticalIssues > 0) {
        console.log('🚨 CRITICAL: Invisible text issues need immediate attention!');
      }
      if (warningIssues > 0) {
        console.log('⚠️  WARNING: Poor contrast issues should be addressed.');
      }
      if (errorIssues > 0) {
        console.log('❌ ERROR: Invalid CSS classes need to be fixed.');
      }
      if (hardcodedIssues > 0) {
        console.log('🎨 HARDCODED: Colors should use theme variables.');
      }
    }

  } catch (error) {
    console.error('❌ Error during text visibility verification:', error);
    process.exit(1);
  }
}

// Run the script
main();
