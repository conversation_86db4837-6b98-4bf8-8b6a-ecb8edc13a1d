#!/usr/bin/env node

/**
 * COMPREHENSIVE HARDCODED COLOR FINDER
 * 
 * This script finds ALL instances of hardcoded Tailwind colors
 * across the entire codebase that need to be converted to theme variables.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🔍 FINDING ALL HARDCODED COLORS IN CODEBASE...\n');

// COMPREHENSIVE HARDCODED COLOR PATTERNS TO FIND
const HARDCODED_COLOR_PATTERNS = [
  // White colors
  { pattern: /\bbg-white\b/g, desc: 'bg-white' },
  { pattern: /\btext-white\b/g, desc: 'text-white' },
  { pattern: /\bborder-white\b/g, desc: 'border-white' },
  
  // Black colors
  { pattern: /\bbg-black\b/g, desc: 'bg-black' },
  { pattern: /\btext-black\b/g, desc: 'text-black' },
  { pattern: /\bborder-black\b/g, desc: 'border-black' },
  
  // Gray colors
  { pattern: /\bbg-gray-([0-9]+)\b/g, desc: 'bg-gray-*' },
  { pattern: /\btext-gray-([0-9]+)\b/g, desc: 'text-gray-*' },
  { pattern: /\bborder-gray-([0-9]+)\b/g, desc: 'border-gray-*' },
  
  // Slate colors
  { pattern: /\bbg-slate-([0-9]+)\b/g, desc: 'bg-slate-*' },
  { pattern: /\btext-slate-([0-9]+)\b/g, desc: 'text-slate-*' },
  { pattern: /\bborder-slate-([0-9]+)\b/g, desc: 'border-slate-*' },
  
  // Zinc colors
  { pattern: /\bbg-zinc-([0-9]+)\b/g, desc: 'bg-zinc-*' },
  { pattern: /\btext-zinc-([0-9]+)\b/g, desc: 'text-zinc-*' },
  { pattern: /\bborder-zinc-([0-9]+)\b/g, desc: 'border-zinc-*' },
  
  // Neutral colors
  { pattern: /\bbg-neutral-([0-9]+)\b/g, desc: 'bg-neutral-*' },
  { pattern: /\btext-neutral-([0-9]+)\b/g, desc: 'text-neutral-*' },
  { pattern: /\bborder-neutral-([0-9]+)\b/g, desc: 'border-neutral-*' },
  
  // Stone colors
  { pattern: /\bbg-stone-([0-9]+)\b/g, desc: 'bg-stone-*' },
  { pattern: /\btext-stone-([0-9]+)\b/g, desc: 'text-stone-*' },
  { pattern: /\bborder-stone-([0-9]+)\b/g, desc: 'border-stone-*' },
  
  // Blue colors
  { pattern: /\bbg-blue-([0-9]+)\b/g, desc: 'bg-blue-*' },
  { pattern: /\btext-blue-([0-9]+)\b/g, desc: 'text-blue-*' },
  { pattern: /\bborder-blue-([0-9]+)\b/g, desc: 'border-blue-*' },
  
  // Indigo colors
  { pattern: /\bbg-indigo-([0-9]+)\b/g, desc: 'bg-indigo-*' },
  { pattern: /\btext-indigo-([0-9]+)\b/g, desc: 'text-indigo-*' },
  { pattern: /\bborder-indigo-([0-9]+)\b/g, desc: 'border-indigo-*' },
  
  // Purple colors
  { pattern: /\bbg-purple-([0-9]+)\b/g, desc: 'bg-purple-*' },
  { pattern: /\btext-purple-([0-9]+)\b/g, desc: 'text-purple-*' },
  { pattern: /\bborder-purple-([0-9]+)\b/g, desc: 'border-purple-*' },
  
  // Violet colors
  { pattern: /\bbg-violet-([0-9]+)\b/g, desc: 'bg-violet-*' },
  { pattern: /\btext-violet-([0-9]+)\b/g, desc: 'text-violet-*' },
  { pattern: /\bborder-violet-([0-9]+)\b/g, desc: 'border-violet-*' },
  
  // Pink colors
  { pattern: /\bbg-pink-([0-9]+)\b/g, desc: 'bg-pink-*' },
  { pattern: /\btext-pink-([0-9]+)\b/g, desc: 'text-pink-*' },
  { pattern: /\bborder-pink-([0-9]+)\b/g, desc: 'border-pink-*' },
  
  // Rose colors
  { pattern: /\bbg-rose-([0-9]+)\b/g, desc: 'bg-rose-*' },
  { pattern: /\btext-rose-([0-9]+)\b/g, desc: 'text-rose-*' },
  { pattern: /\bborder-rose-([0-9]+)\b/g, desc: 'border-rose-*' },
  
  // Red colors
  { pattern: /\bbg-red-([0-9]+)\b/g, desc: 'bg-red-*' },
  { pattern: /\btext-red-([0-9]+)\b/g, desc: 'text-red-*' },
  { pattern: /\bborder-red-([0-9]+)\b/g, desc: 'border-red-*' },
  
  // Orange colors
  { pattern: /\bbg-orange-([0-9]+)\b/g, desc: 'bg-orange-*' },
  { pattern: /\btext-orange-([0-9]+)\b/g, desc: 'text-orange-*' },
  { pattern: /\bborder-orange-([0-9]+)\b/g, desc: 'border-orange-*' },
  
  // Amber colors
  { pattern: /\bbg-amber-([0-9]+)\b/g, desc: 'bg-amber-*' },
  { pattern: /\btext-amber-([0-9]+)\b/g, desc: 'text-amber-*' },
  { pattern: /\bborder-amber-([0-9]+)\b/g, desc: 'border-amber-*' },
  
  // Yellow colors
  { pattern: /\bbg-yellow-([0-9]+)\b/g, desc: 'bg-yellow-*' },
  { pattern: /\btext-yellow-([0-9]+)\b/g, desc: 'text-yellow-*' },
  { pattern: /\bborder-yellow-([0-9]+)\b/g, desc: 'border-yellow-*' },
  
  // Lime colors
  { pattern: /\bbg-lime-([0-9]+)\b/g, desc: 'bg-lime-*' },
  { pattern: /\btext-lime-([0-9]+)\b/g, desc: 'text-lime-*' },
  { pattern: /\bborder-lime-([0-9]+)\b/g, desc: 'border-lime-*' },
  
  // Green colors
  { pattern: /\bbg-green-([0-9]+)\b/g, desc: 'bg-green-*' },
  { pattern: /\btext-green-([0-9]+)\b/g, desc: 'text-green-*' },
  { pattern: /\bborder-green-([0-9]+)\b/g, desc: 'border-green-*' },
  
  // Emerald colors
  { pattern: /\bbg-emerald-([0-9]+)\b/g, desc: 'bg-emerald-*' },
  { pattern: /\btext-emerald-([0-9]+)\b/g, desc: 'text-emerald-*' },
  { pattern: /\bborder-emerald-([0-9]+)\b/g, desc: 'border-emerald-*' },
  
  // Teal colors
  { pattern: /\bbg-teal-([0-9]+)\b/g, desc: 'bg-teal-*' },
  { pattern: /\btext-teal-([0-9]+)\b/g, desc: 'text-teal-*' },
  { pattern: /\bborder-teal-([0-9]+)\b/g, desc: 'border-teal-*' },
  
  // Cyan colors
  { pattern: /\bbg-cyan-([0-9]+)\b/g, desc: 'bg-cyan-*' },
  { pattern: /\btext-cyan-([0-9]+)\b/g, desc: 'text-cyan-*' },
  { pattern: /\bborder-cyan-([0-9]+)\b/g, desc: 'border-cyan-*' },
  
  // Sky colors
  { pattern: /\bbg-sky-([0-9]+)\b/g, desc: 'bg-sky-*' },
  { pattern: /\btext-sky-([0-9]+)\b/g, desc: 'text-sky-*' },
  { pattern: /\bborder-sky-([0-9]+)\b/g, desc: 'border-sky-*' },
  
  // Gradient patterns
  { pattern: /\bbg-gradient-to-([a-z]+) from-([a-z]+)-([0-9]+) to-([a-z]+)-([0-9]+)\b/g, desc: 'gradient backgrounds' },
  { pattern: /\bfrom-([a-z]+)-([0-9]+)\b/g, desc: 'gradient from-*' },
  { pattern: /\bto-([a-z]+)-([0-9]+)\b/g, desc: 'gradient to-*' },
  { pattern: /\bvia-([a-z]+)-([0-9]+)\b/g, desc: 'gradient via-*' },
  
  // Dark theme variants
  { pattern: /\bdark:bg-([a-z]+)-([0-9]+)\b/g, desc: 'dark:bg-*' },
  { pattern: /\bdark:text-([a-z]+)-([0-9]+)\b/g, desc: 'dark:text-*' },
  { pattern: /\bdark:border-([a-z]+)-([0-9]+)\b/g, desc: 'dark:border-*' },
  
  // Hover states
  { pattern: /\bhover:bg-([a-z]+)-([0-9]+)\b/g, desc: 'hover:bg-*' },
  { pattern: /\bhover:text-([a-z]+)-([0-9]+)\b/g, desc: 'hover:text-*' },
  { pattern: /\bhover:border-([a-z]+)-([0-9]+)\b/g, desc: 'hover:border-*' },
  
  // Focus states
  { pattern: /\bfocus:bg-([a-z]+)-([0-9]+)\b/g, desc: 'focus:bg-*' },
  { pattern: /\bfocus:text-([a-z]+)-([0-9]+)\b/g, desc: 'focus:text-*' },
  { pattern: /\bfocus:border-([a-z]+)-([0-9]+)\b/g, desc: 'focus:border-*' }
];

/**
 * Get all TypeScript, JavaScript, and CSS files in the project
 */
function getAllFiles() {
  const files = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules, .git, dist, and other build directories
        if (!['node_modules', '.git', 'dist', '.next', 'build', '.vite', 'scripts'].includes(item)) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && /\.(tsx?|jsx?|css|scss|sass|less)$/.test(item)) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(PROJECT_ROOT);
  return files;
}

/**
 * Scan a single file for hardcoded colors
 */
function scanFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  const foundColors = [];

  // Check each pattern
  for (const colorPattern of HARDCODED_COLOR_PATTERNS) {
    const matches = content.match(colorPattern.pattern);
    if (matches) {
      foundColors.push({
        pattern: colorPattern.desc,
        count: matches.length,
        examples: [...new Set(matches)].slice(0, 3) // Show up to 3 unique examples
      });
    }
  }

  return foundColors.length > 0 ? { file: relativePath, colors: foundColors } : null;
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getAllFiles();
    const results = [];
    let totalFiles = 0;
    let totalColors = 0;

    console.log(`📁 Scanning ${files.length} files for hardcoded colors...\n`);

    for (const file of files) {
      const result = scanFile(file);
      if (result) {
        results.push(result);
        totalFiles++;
        
        const fileColorCount = result.colors.reduce((sum, color) => sum + color.count, 0);
        totalColors += fileColorCount;
        
        console.log(`🎨 ${result.file} (${fileColorCount} hardcoded colors)`);
        result.colors.forEach(color => {
          console.log(`   ${color.pattern}: ${color.count} instances`);
          if (color.examples.length > 0) {
            console.log(`   Examples: ${color.examples.join(', ')}`);
          }
        });
        console.log('');
      }
    }

    console.log('📊 HARDCODED COLOR AUDIT COMPLETE!');
    console.log(`📁 Files with hardcoded colors: ${totalFiles}`);
    console.log(`🎨 Total hardcoded color instances: ${totalColors}`);
    
    if (totalColors > 0) {
      console.log('\n⚠️  HARDCODED COLORS FOUND - NEED TO BE CONVERTED TO THEME VARIABLES!');
    } else {
      console.log('\n✅ NO HARDCODED COLORS FOUND - CODEBASE IS CLEAN!');
    }

  } catch (error) {
    console.error('❌ Error during hardcoded color scan:', error);
    process.exit(1);
  }
}

// Run the script
main();
