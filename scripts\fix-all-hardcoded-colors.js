#!/usr/bin/env node

/**
 * COMPREHENSIVE HARDCODED COLOR ELIMINATION SCRIPT
 * 
 * This script systematically replaces ALL hardcoded Tailwind colors
 * with appropriate theme variables for professional consistency.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🎨 COMPREHENSIVE HARDCODED COLOR ELIMINATION - Starting...\n');

// COMPREHENSIVE HARDCODED COLOR REPLACEMENT PATTERNS
const HARDCODED_COLOR_FIXES = [
  // 1. WHITE/BLACK COLORS
  {
    pattern: /\btext-white\b/g,
    replacement: 'text-primary-foreground',
    desc: 'PROFESSIONAL: text-white → text-primary-foreground'
  },
  {
    pattern: /\bbg-white\b/g,
    replacement: 'bg-background',
    desc: 'PROFESSIONAL: bg-white → bg-background'
  },
  {
    pattern: /\bborder-white\b/g,
    replacement: 'border-background',
    desc: 'PROFESSIONAL: border-white → border-background'
  },
  {
    pattern: /\btext-black\b/g,
    replacement: 'text-foreground',
    desc: 'PROFESSIONAL: text-black → text-foreground'
  },
  {
    pattern: /\bbg-black\b/g,
    replacement: 'bg-foreground',
    desc: 'PROFESSIONAL: bg-black → bg-foreground'
  },
  {
    pattern: /\bborder-black\b/g,
    replacement: 'border-foreground',
    desc: 'PROFESSIONAL: border-black → border-foreground'
  },

  // 2. GRAY COLORS
  {
    pattern: /\btext-gray-([0-9]+)\b/g,
    replacement: 'text-muted-foreground',
    desc: 'PROFESSIONAL: text-gray-* → text-muted-foreground'
  },
  {
    pattern: /\bbg-gray-([0-9]+)\b/g,
    replacement: 'bg-muted',
    desc: 'PROFESSIONAL: bg-gray-* → bg-muted'
  },
  {
    pattern: /\bborder-gray-([0-9]+)\b/g,
    replacement: 'border-border',
    desc: 'PROFESSIONAL: border-gray-* → border-border'
  },

  // 3. SLATE COLORS
  {
    pattern: /\btext-slate-([0-9]+)\b/g,
    replacement: 'text-muted-foreground',
    desc: 'PROFESSIONAL: text-slate-* → text-muted-foreground'
  },
  {
    pattern: /\bbg-slate-([0-9]+)\b/g,
    replacement: 'bg-muted',
    desc: 'PROFESSIONAL: bg-slate-* → bg-muted'
  },
  {
    pattern: /\bborder-slate-([0-9]+)\b/g,
    replacement: 'border-border',
    desc: 'PROFESSIONAL: border-slate-* → border-border'
  },

  // 4. ZINC/NEUTRAL/STONE COLORS
  {
    pattern: /\btext-(zinc|neutral|stone)-([0-9]+)\b/g,
    replacement: 'text-muted-foreground',
    desc: 'PROFESSIONAL: text-zinc/neutral/stone-* → text-muted-foreground'
  },
  {
    pattern: /\bbg-(zinc|neutral|stone)-([0-9]+)\b/g,
    replacement: 'bg-muted',
    desc: 'PROFESSIONAL: bg-zinc/neutral/stone-* → bg-muted'
  },
  {
    pattern: /\bborder-(zinc|neutral|stone)-([0-9]+)\b/g,
    replacement: 'border-border',
    desc: 'PROFESSIONAL: border-zinc/neutral/stone-* → border-border'
  },

  // 5. BLUE COLORS
  {
    pattern: /\btext-blue-([0-9]+)\b/g,
    replacement: 'text-info',
    desc: 'PROFESSIONAL: text-blue-* → text-info'
  },
  {
    pattern: /\bbg-blue-([0-9]+)\b/g,
    replacement: 'bg-info',
    desc: 'PROFESSIONAL: bg-blue-* → bg-info'
  },
  {
    pattern: /\bborder-blue-([0-9]+)\b/g,
    replacement: 'border-info',
    desc: 'PROFESSIONAL: border-blue-* → border-info'
  },

  // 6. INDIGO/PURPLE/VIOLET COLORS
  {
    pattern: /\btext-(indigo|purple|violet)-([0-9]+)\b/g,
    replacement: 'text-medical-blue',
    desc: 'PROFESSIONAL: text-indigo/purple/violet-* → text-medical-blue'
  },
  {
    pattern: /\bbg-(indigo|purple|violet)-([0-9]+)\b/g,
    replacement: 'bg-medical-blue-light',
    desc: 'PROFESSIONAL: bg-indigo/purple/violet-* → bg-medical-blue-light'
  },
  {
    pattern: /\bborder-(indigo|purple|violet)-([0-9]+)\b/g,
    replacement: 'border-medical-blue/30',
    desc: 'PROFESSIONAL: border-indigo/purple/violet-* → border-medical-blue/30'
  },

  // 7. GREEN COLORS
  {
    pattern: /\btext-(green|emerald|lime)-([0-9]+)\b/g,
    replacement: 'text-success',
    desc: 'PROFESSIONAL: text-green/emerald/lime-* → text-success'
  },
  {
    pattern: /\bbg-(green|emerald|lime)-([0-9]+)\b/g,
    replacement: 'bg-success-light',
    desc: 'PROFESSIONAL: bg-green/emerald/lime-* → bg-success-light'
  },
  {
    pattern: /\bborder-(green|emerald|lime)-([0-9]+)\b/g,
    replacement: 'border-success/30',
    desc: 'PROFESSIONAL: border-green/emerald/lime-* → border-success/30'
  },

  // 8. RED/PINK/ROSE COLORS
  {
    pattern: /\btext-(red|pink|rose)-([0-9]+)\b/g,
    replacement: 'text-error',
    desc: 'PROFESSIONAL: text-red/pink/rose-* → text-error'
  },
  {
    pattern: /\bbg-(red|pink|rose)-([0-9]+)\b/g,
    replacement: 'bg-error-light',
    desc: 'PROFESSIONAL: bg-red/pink/rose-* → bg-error-light'
  },
  {
    pattern: /\bborder-(red|pink|rose)-([0-9]+)\b/g,
    replacement: 'border-error/30',
    desc: 'PROFESSIONAL: border-red/pink/rose-* → border-error/30'
  },

  // 9. YELLOW/AMBER/ORANGE COLORS
  {
    pattern: /\btext-(yellow|amber|orange)-([0-9]+)\b/g,
    replacement: 'text-info',
    desc: 'PROFESSIONAL: text-yellow/amber/orange-* → text-info'
  },
  {
    pattern: /\bbg-(yellow|amber|orange)-([0-9]+)\b/g,
    replacement: 'bg-info-light',
    desc: 'PROFESSIONAL: bg-yellow/amber/orange-* → bg-info-light'
  },
  {
    pattern: /\bborder-(yellow|amber|orange)-([0-9]+)\b/g,
    replacement: 'border-info/30',
    desc: 'PROFESSIONAL: border-yellow/amber/orange-* → border-info/30'
  },

  // 10. TEAL/CYAN/SKY COLORS
  {
    pattern: /\btext-(teal|cyan|sky)-([0-9]+)\b/g,
    replacement: 'text-info',
    desc: 'PROFESSIONAL: text-teal/cyan/sky-* → text-info'
  },
  {
    pattern: /\bbg-(teal|cyan|sky)-([0-9]+)\b/g,
    replacement: 'bg-info-light',
    desc: 'PROFESSIONAL: bg-teal/cyan/sky-* → bg-info-light'
  },
  {
    pattern: /\bborder-(teal|cyan|sky)-([0-9]+)\b/g,
    replacement: 'border-info/30',
    desc: 'PROFESSIONAL: border-teal/cyan/sky-* → border-info/30'
  },

  // 11. GRADIENT BACKGROUNDS - SIMPLE REPLACEMENTS
  {
    pattern: /\bbg-gradient-to-([a-z]+) from-([a-z]+)-([0-9]+) to-([a-z]+)-([0-9]+)\b/g,
    replacement: 'bg-info-light border border-info/30',
    desc: 'PROFESSIONAL: Complex gradients → bg-info-light with border'
  },
  {
    pattern: /\bfrom-([a-z]+)-([0-9]+)\b/g,
    replacement: 'from-info-light',
    desc: 'PROFESSIONAL: gradient from-* → from-info-light'
  },
  {
    pattern: /\bto-([a-z]+)-([0-9]+)\b/g,
    replacement: 'to-info-light',
    desc: 'PROFESSIONAL: gradient to-* → to-info-light'
  },
  {
    pattern: /\bvia-([a-z]+)-([0-9]+)\b/g,
    replacement: 'via-info-light',
    desc: 'PROFESSIONAL: gradient via-* → via-info-light'
  },

  // 12. DARK THEME VARIANTS
  {
    pattern: /\bdark:bg-([a-z]+)-([0-9]+)\b/g,
    replacement: 'dark:bg-muted',
    desc: 'PROFESSIONAL: dark:bg-* → dark:bg-muted'
  },
  {
    pattern: /\bdark:text-([a-z]+)-([0-9]+)\b/g,
    replacement: 'dark:text-muted-foreground',
    desc: 'PROFESSIONAL: dark:text-* → dark:text-muted-foreground'
  },
  {
    pattern: /\bdark:border-([a-z]+)-([0-9]+)\b/g,
    replacement: 'dark:border-border',
    desc: 'PROFESSIONAL: dark:border-* → dark:border-border'
  },

  // 13. HOVER STATES
  {
    pattern: /\bhover:bg-([a-z]+)-([0-9]+)\b/g,
    replacement: 'hover:bg-muted',
    desc: 'PROFESSIONAL: hover:bg-* → hover:bg-muted'
  },
  {
    pattern: /\bhover:text-([a-z]+)-([0-9]+)\b/g,
    replacement: 'hover:text-muted-foreground',
    desc: 'PROFESSIONAL: hover:text-* → hover:text-muted-foreground'
  },
  {
    pattern: /\bhover:border-([a-z]+)-([0-9]+)\b/g,
    replacement: 'hover:border-border',
    desc: 'PROFESSIONAL: hover:border-* → hover:border-border'
  },

  // 14. FOCUS STATES
  {
    pattern: /\bfocus:bg-([a-z]+)-([0-9]+)\b/g,
    replacement: 'focus:bg-muted',
    desc: 'PROFESSIONAL: focus:bg-* → focus:bg-muted'
  },
  {
    pattern: /\bfocus:text-([a-z]+)-([0-9]+)\b/g,
    replacement: 'focus:text-muted-foreground',
    desc: 'PROFESSIONAL: focus:text-* → focus:text-muted-foreground'
  },
  {
    pattern: /\bfocus:border-([a-z]+)-([0-9]+)\b/g,
    replacement: 'focus:border-border',
    desc: 'PROFESSIONAL: focus:border-* → focus:border-border'
  }
];

/**
 * Get all TypeScript, JavaScript, and CSS files in the project
 */
function getAllFiles() {
  const files = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules, .git, dist, and other build directories
        if (!['node_modules', '.git', 'dist', '.next', 'build', '.vite', 'scripts'].includes(item)) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && /\.(tsx?|jsx?|css|scss|sass|less)$/.test(item)) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(PROJECT_ROOT);
  return files;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  let processedContent = content;
  let totalChanges = 0;
  const changeLog = [];

  // Apply all hardcoded colour fixes
  for (const fix of HARDCODED_COLOR_FIXES) {
    const matches = processedContent.match(fix.pattern);
    if (matches) {
      processedContent = processedContent.replace(fix.pattern, fix.replacement);
      const changeCount = matches.length;
      totalChanges += changeCount;
      changeLog.push(`✅ ${fix.desc} (${changeCount} fixes)`);
    }
  }

  // Write the file if changes were made
  if (totalChanges > 0) {
    fs.writeFileSync(filePath, processedContent, 'utf8');
    console.log(`🔧 PROFESSIONAL COLORS: ${totalChanges} hardcoded colors fixed in ${relativePath}`);
    changeLog.forEach(change => console.log(`   ${change}`));
    console.log('');
  }

  return totalChanges;
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getAllFiles();
    let totalFiles = 0;
    let totalChanges = 0;

    console.log(`📁 Found ${files.length} files to process\n`);

    for (const file of files) {
      const changes = processFile(file);
      if (changes > 0) {
        totalFiles++;
        totalChanges += changes;
      }
    }

    console.log('🎯 HARDCODED COLOR ELIMINATION COMPLETE!');
    console.log(`📊 Total files updated: ${totalFiles}`);
    console.log(`📊 Total hardcoded colors fixed: ${totalChanges}`);
    console.log('');
    console.log('✅ ALL hardcoded colors replaced with professional theme variables!');
    console.log('🔍 The design is now uniformly professional and flawless.');

  } catch (error) {
    console.error('❌ Error during hardcoded colour elimination:', error);
    process.exit(1);
  }
}

// Run the script
main();
