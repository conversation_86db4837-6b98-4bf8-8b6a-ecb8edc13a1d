#!/usr/bin/env node

/**
 * Comprehensive script to find and fix ALL remaining hardcoded colors across the entire codebase
 * This script addresses every single instance of hardcoded colors that were missed in previous fixes
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

// Directories to scan
const SCAN_DIRECTORIES = [
  'src/components',
  'src/pages',
  'src/lib'
];

// Comprehensive hardcoded colour patterns to fix
const HARDCODED_COLOR_FIXES = [
  // Specific hardcoded colors
  { pattern: /\btext-white\b/g, replacement: 'text-primary-foreground', desc: 'text-white → text-primary-foreground' },
  { pattern: /\bbg-white\b/g, replacement: 'bg-background', desc: 'bg-white → bg-background' },
  
  // Gray colors
  { pattern: /\btext-gray-([0-9]+)\b/g, replacement: 'text-muted-foreground', desc: 'text-gray-* → text-muted-foreground' },
  { pattern: /\bbg-gray-([0-9]+)\b/g, replacement: 'bg-muted', desc: 'bg-gray-* → bg-muted' },
  { pattern: /\bborder-gray-([0-9]+)\b/g, replacement: 'border-border', desc: 'border-gray-* → border-border' },
  
  // Blue colors
  { pattern: /\btext-blue-([0-9]+)\b/g, replacement: 'text-info', desc: 'text-blue-* → text-info' },
  { pattern: /\bbg-blue-([0-9]+)\b/g, replacement: 'bg-info', desc: 'bg-blue-* → bg-info' },
  { pattern: /\bborder-blue-([0-9]+)\b/g, replacement: 'border-info', desc: 'border-blue-* → border-info' },
  
  // Green colors
  { pattern: /\btext-green-([0-9]+)\b/g, replacement: 'text-success', desc: 'text-green-* → text-success' },
  { pattern: /\bbg-green-([0-9]+)\b/g, replacement: 'bg-success', desc: 'bg-green-* → bg-success' },
  { pattern: /\bborder-green-([0-9]+)\b/g, replacement: 'border-success', desc: 'border-green-* → border-success' },
  
  // Red colors
  { pattern: /\btext-red-([0-9]+)\b/g, replacement: 'text-error', desc: 'text-red-* → text-error' },
  { pattern: /\bbg-red-([0-9]+)\b/g, replacement: 'bg-error', desc: 'bg-red-* → bg-error' },
  { pattern: /\bborder-red-([0-9]+)\b/g, replacement: 'border-error', desc: 'border-red-* → border-error' },
  
  // Yellow colors
  { pattern: /\btext-yellow-([0-9]+)\b/g, replacement: 'text-info', desc: 'text-yellow-* → text-info' },
  { pattern: /\bbg-yellow-([0-9]+)\b/g, replacement: 'bg-info', desc: 'bg-yellow-* → bg-info' },
  { pattern: /\bborder-yellow-([0-9]+)\b/g, replacement: 'border-info', desc: 'border-yellow-* → border-info' },
  
  // Orange colors
  { pattern: /\btext-orange-([0-9]+)\b/g, replacement: 'text-info', desc: 'text-orange-* → text-info' },
  { pattern: /\bbg-orange-([0-9]+)\b/g, replacement: 'bg-info', desc: 'bg-orange-* → bg-info' },
  { pattern: /\bborder-orange-([0-9]+)\b/g, replacement: 'border-info', desc: 'border-orange-* → border-info' },
  
  // Purple colors
  { pattern: /\btext-purple-([0-9]+)\b/g, replacement: 'text-medical-blue', desc: 'text-purple-* → text-medical-blue' },
  { pattern: /\bbg-purple-([0-9]+)\b/g, replacement: 'bg-medical-blue', desc: 'bg-purple-* → bg-medical-blue' },
  { pattern: /\bborder-purple-([0-9]+)\b/g, replacement: 'border-medical-blue', desc: 'border-purple-* → border-medical-blue' },
  
  // Teal colors
  { pattern: /\btext-teal-([0-9]+)\b/g, replacement: 'text-info', desc: 'text-teal-* → text-info' },
  { pattern: /\bbg-teal-([0-9]+)\b/g, replacement: 'bg-info', desc: 'bg-teal-* → bg-info' },
  { pattern: /\bborder-teal-([0-9]+)\b/g, replacement: 'border-info', desc: 'border-teal-* → border-info' },
  
  // Slate colors
  { pattern: /\btext-slate-([0-9]+)\b/g, replacement: 'text-muted-foreground', desc: 'text-slate-* → text-muted-foreground' },
  { pattern: /\bbg-slate-([0-9]+)\b/g, replacement: 'bg-muted', desc: 'bg-slate-* → bg-muted' },
  { pattern: /\bborder-slate-([0-9]+)\b/g, replacement: 'border-border', desc: 'border-slate-* → border-border' },
  
  // Gradient backgrounds
  { pattern: /\bbg-gradient-to-r from-blue-([0-9]+) to-purple-([0-9]+)\b/g, replacement: 'bg-info-light border border-info/30', desc: 'blue-purple gradient → bg-info-light' },
  { pattern: /\bbg-gradient-to-br from-([a-z]+)-([0-9]+)\/([0-9]+) to-([a-z]+)-([0-9]+)\/([0-9]+)\b/g, replacement: 'bg-muted', desc: 'complex gradient → bg-muted' },
  { pattern: /\bbg-gradient-to-([a-z]+) from-([a-z]+)-([0-9]+) to-([a-z]+)-([0-9]+)\b/g, replacement: 'bg-muted', desc: 'gradient → bg-muted' }
];

/**
 * Get all files to process
 */
function getAllFiles(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  const files = [];
  
  function scanDirectory(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scanDirectory(fullPath);
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(dir);
  return files;
}

/**
 * Fix hardcoded colors in a single file
 */
function fixHardcodedColors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    const appliedFixes = [];

    HARDCODED_COLOR_FIXES.forEach(({ pattern, replacement, desc }) => {
      const matches = content.match(pattern);
      if (matches) {
        content = content.replace(pattern, replacement);
        hasChanges = true;
        appliedFixes.push(`${desc} (${matches.length} instances)`);
      }
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed hardcoded colors in: ${path.relative(PROJECT_ROOT, filePath)}`);
      appliedFixes.forEach(fix => console.log(`   - ${fix}`));
      return true;
    } else {
      console.log(`ℹ️  No hardcoded colors found in: ${path.relative(PROJECT_ROOT, filePath)}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Main execution
 */
function main() {
  console.log('🎨 Starting comprehensive hardcoded colour fix...\n');
  
  let totalFiles = 0;
  let fixedFiles = 0;
  
  SCAN_DIRECTORIES.forEach(dir => {
    const fullDir = path.join(PROJECT_ROOT, dir);
    if (fs.existsSync(fullDir)) {
      console.log(`📁 Scanning directory: ${dir}`);
      const files = getAllFiles(fullDir);
      
      files.forEach(file => {
        totalFiles++;
        if (fixHardcodedColors(file)) {
          fixedFiles++;
        }
      });
      
      console.log('');
    }
  });
  
  console.log('🎯 Summary:');
  console.log(`   Total files scanned: ${totalFiles}`);
  console.log(`   Files with fixes applied: ${fixedFiles}`);
  console.log(`   Files already compliant: ${totalFiles - fixedFiles}`);
  
  if (fixedFiles > 0) {
    console.log('\n✅ All hardcoded colors have been systematically fixed!');
    console.log('🎨 The codebase now uses consistent theme-aware styling.');
  } else {
    console.log('\n✅ No hardcoded colors found - codebase is already compliant!');
  }
}

main();
