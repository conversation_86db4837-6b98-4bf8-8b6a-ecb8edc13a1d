#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix card styling inconsistencies visible in the screenshot
 * This addresses inconsistent card backgrounds, borders, and styling patterns
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

// Directories to scan
const SCAN_DIRECTORIES = [
  'src/components',
  'src/pages',
  'docs'
];

// Card styling fixes to standardise appearance
const CARD_STYLING_FIXES = [
  // Replace inconsistent card backgrounds with medical-card classes
  { 
    pattern: /className="([^"]*?)bg-white([^"]*?)"/g, 
    replacement: 'className="$1medical-card$2"', 
    desc: 'bg-white cards → medical-card' 
  },
  { 
    pattern: /className="([^"]*?)bg-card([^"]*?)"/g, 
    replacement: 'className="$1medical-card$2"', 
    desc: 'bg-card → medical-card' 
  },
  
  // Fix gradient card backgrounds
  { 
    pattern: /className="([^"]*?)bg-gradient-to-r from-[a-z]+-[0-9]+ to-[a-z]+-[0-9]+([^"]*?)"/g, 
    replacement: 'className="$1medical-card bg-info-light border border-info/30$2"', 
    desc: 'Gradient cards → medical-card with info theme' 
  },
  { 
    pattern: /className="([^"]*?)bg-gradient-to-br from-[a-z]+-[0-9]+\/[0-9]+ to-[a-z]+-[0-9]+\/[0-9]+([^"]*?)"/g, 
    replacement: 'className="$1medical-card$2"', 
    desc: 'Complex gradient cards → medical-card' 
  },
  
  // Standardise card padding and spacing
  { 
    pattern: /className="([^"]*?)p-6 bg-white rounded-lg shadow-sm border([^"]*?)"/g, 
    replacement: 'className="$1medical-card p-6$2"', 
    desc: 'Standard card pattern → medical-card' 
  },
  { 
    pattern: /className="([^"]*?)p-8 bg-white rounded-xl shadow-lg border([^"]*?)"/g, 
    replacement: 'className="$1medical-card p-8$2"', 
    desc: 'Large card pattern → medical-card' 
  },
  
  // Fix colored card backgrounds that create poor contrast
  { 
    pattern: /className="([^"]*?)bg-blue-[0-9]+ ([^"]*?)"/g, 
    replacement: 'className="$1bg-info-light border border-info/30 $2"', 
    desc: 'Blue card backgrounds → info theme' 
  },
  { 
    pattern: /className="([^"]*?)bg-green-[0-9]+ ([^"]*?)"/g, 
    replacement: 'className="$1bg-success-light border border-success/30 $2"', 
    desc: 'Green card backgrounds → success theme' 
  },
  { 
    pattern: /className="([^"]*?)bg-red-[0-9]+ ([^"]*?)"/g, 
    replacement: 'className="$1bg-error-light border border-error/30 $2"', 
    desc: 'Red card backgrounds → error theme' 
  },
  { 
    pattern: /className="([^"]*?)bg-yellow-[0-9]+ ([^"]*?)"/g, 
    replacement: 'className="$1bg-warning-light border border-info/30 $2"', 
    desc: 'Yellow card backgrounds → warning theme' 
  },
  { 
    pattern: /className="([^"]*?)bg-orange-[0-9]+ ([^"]*?)"/g, 
    replacement: 'className="$1bg-warning-light border border-info/30 $2"', 
    desc: 'Orange card backgrounds → warning theme' 
  },
  { 
    pattern: /className="([^"]*?)bg-purple-[0-9]+ ([^"]*?)"/g, 
    replacement: 'className="$1bg-medical-blue-light border border-medical-blue/30 $2"', 
    desc: 'Purple card backgrounds → medical-blue theme' 
  },
  { 
    pattern: /className="([^"]*?)bg-teal-[0-9]+ ([^"]*?)"/g, 
    replacement: 'className="$1bg-info-light border border-info/30 $2"', 
    desc: 'Teal card backgrounds → info theme' 
  },
  
  // Fix dark theme card backgrounds that cause poor contrast
  { 
    pattern: /className="([^"]*?)dark:bg-blue-[5-9][0-9][0-9]\/[0-9]+([^"]*?)"/g, 
    replacement: 'className="$1dark:bg-info-light$2"', 
    desc: 'Dark theme blue cards → dark:bg-info-light' 
  },
  { 
    pattern: /className="([^"]*?)dark:bg-green-[5-9][0-9][0-9]\/[0-9]+([^"]*?)"/g, 
    replacement: 'className="$1dark:bg-success-light$2"', 
    desc: 'Dark theme green cards → dark:bg-success-light' 
  },
  
  // Fix inconsistent border styling
  { 
    pattern: /border-blue-[0-9]+/g, 
    replacement: 'border-info/30', 
    desc: 'Blue borders → border-info/30' 
  },
  { 
    pattern: /border-green-[0-9]+/g, 
    replacement: 'border-success/30', 
    desc: 'Green borders → border-success/30' 
  },
  { 
    pattern: /border-red-[0-9]+/g, 
    replacement: 'border-error/30', 
    desc: 'Red borders → border-error/30' 
  },
  { 
    pattern: /border-yellow-[0-9]+/g, 
    replacement: 'border-info/30', 
    desc: 'Yellow borders → border-info/30' 
  },
  { 
    pattern: /border-orange-[0-9]+/g, 
    replacement: 'border-info/30', 
    desc: 'Orange borders → border-info/30' 
  },
  { 
    pattern: /border-purple-[0-9]+/g, 
    replacement: 'border-medical-blue/30', 
    desc: 'Purple borders → border-medical-blue/30' 
  },
  { 
    pattern: /border-teal-[0-9]+/g, 
    replacement: 'border-info/30', 
    desc: 'Teal borders → border-info/30' 
  },
  
  // Fix shadow inconsistencies
  { 
    pattern: /shadow-blue-[0-9]+\/[0-9]+/g, 
    replacement: 'shadow-md', 
    desc: 'Blue shadows → shadow-md' 
  },
  { 
    pattern: /shadow-green-[0-9]+\/[0-9]+/g, 
    replacement: 'shadow-md', 
    desc: 'Green shadows → shadow-md' 
  },
  
  // Fix specific problematic card patterns from medical condition pages
  { 
    pattern: /className="p-4 bg-blue-50 dark:bg-blue-950\/20 rounded-lg"/g, 
    replacement: 'className="p-4 bg-info-light border border-info/30 rounded-lg"', 
    desc: 'Medical info cards → theme-aware styling' 
  },
  { 
    pattern: /className="p-4 bg-green-50 dark:bg-green-950\/20 rounded-lg"/g, 
    replacement: 'className="p-4 bg-success-light border border-success/30 rounded-lg"', 
    desc: 'Medical success cards → theme-aware styling' 
  },
  { 
    pattern: /className="p-4 bg-red-50 dark:bg-red-950\/20 rounded-lg"/g, 
    replacement: 'className="p-4 bg-error-light border border-error/30 rounded-lg"', 
    desc: 'Medical error cards → theme-aware styling' 
  },
  { 
    pattern: /className="p-4 bg-yellow-50 dark:bg-info-light\/20 rounded-lg"/g, 
    replacement: 'className="p-4 bg-info-light border border-info/30 rounded-lg"', 
    desc: 'Medical warning cards → theme-aware styling' 
  },
  { 
    pattern: /className="p-4 bg-orange-50 dark:bg-info-light\/20 rounded-lg"/g, 
    replacement: 'className="p-4 bg-info-light border border-info/30 rounded-lg"', 
    desc: 'Medical orange cards → theme-aware styling' 
  },
  { 
    pattern: /className="p-4 bg-purple-50 dark:bg-purple-950\/20 rounded-lg"/g, 
    replacement: 'className="p-4 bg-medical-blue-light border border-medical-blue/30 rounded-lg"', 
    desc: 'Medical purple cards → theme-aware styling' 
  },
  
  // Fix card content text styling
  { 
    pattern: /className="([^"]*?)text-blue-[5-9][0-9][0-9]([^"]*?)"/g, 
    replacement: 'className="$1text-info$2"', 
    desc: 'Dark blue text → text-info' 
  },
  { 
    pattern: /className="([^"]*?)text-green-[5-9][0-9][0-9]([^"]*?)"/g, 
    replacement: 'className="$1text-success$2"', 
    desc: 'Dark green text → text-success' 
  },
  { 
    pattern: /className="([^"]*?)text-red-[5-9][0-9][0-9]([^"]*?)"/g, 
    replacement: 'className="$1text-error$2"', 
    desc: 'Dark red text → text-error' 
  }
];

/**
 * Get all files to process
 */
function getAllFiles(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  const files = [];
  
  function scanDirectory(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not read directory ${currentDir}: ${error.message}`);
    }
  }
  
  scanDirectory(dir);
  return files;
}

/**
 * Fix card styling inconsistencies in a single file
 */
function fixCardStyling(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    const appliedFixes = [];

    CARD_STYLING_FIXES.forEach(({ pattern, replacement, desc }) => {
      const matches = content.match(pattern);
      if (matches) {
        content = content.replace(pattern, replacement);
        hasChanges = true;
        appliedFixes.push(`${desc} (${matches.length} instances)`);
      }
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed card styling in: ${path.relative(PROJECT_ROOT, filePath)}`);
      appliedFixes.forEach(fix => console.log(`   - ${fix}`));
      return true;
    } else {
      console.log(`ℹ️  No card styling issues found in: ${path.relative(PROJECT_ROOT, filePath)}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Main execution
 */
function main() {
  console.log('🎨 Starting card styling consistency fixes...\n');
  
  let totalFiles = 0;
  let fixedFiles = 0;
  
  SCAN_DIRECTORIES.forEach(dir => {
    const fullDir = path.join(PROJECT_ROOT, dir);
    if (fs.existsSync(fullDir)) {
      console.log(`📁 Scanning directory: ${dir}`);
      const files = getAllFiles(fullDir);
      
      files.forEach(file => {
        totalFiles++;
        if (fixCardStyling(file)) {
          fixedFiles++;
        }
      });
      
      console.log('');
    }
  });
  
  console.log('🎯 Card Styling Fixes Summary:');
  console.log(`   Total files scanned: ${totalFiles}`);
  console.log(`   Files with card fixes: ${fixedFiles}`);
  console.log(`   Files already compliant: ${totalFiles - fixedFiles}`);
  
  if (fixedFiles > 0) {
    console.log('\n✅ Card styling inconsistencies have been fixed!');
    console.log('🎨 All cards now use consistent medical-card styling with proper theme support.');
  } else {
    console.log('\n✅ No card styling issues found - all cards are already consistent!');
  }
}

main();
