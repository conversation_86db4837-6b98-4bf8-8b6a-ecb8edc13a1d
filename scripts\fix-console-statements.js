#!/usr/bin/env node

/**
 * Console Statements Fix Script
 * 
 * This script finds and fixes console statements by wrapping them in development checks
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.dirname(__dirname);

// ANSI colour codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, colour = 'reset') {
  console.log(`${colors[colour]}${message}${colors.reset}`);
}

/**
 * Fix console statements in a file
 */
function fixConsoleStatements(content, filePath) {
  let fixed = content;
  let changes = 0;

  // Pattern to match console statements that aren't already wrapped
  const consolePattern = /^(\s*)(console\.(log|warn|error|info|debug)\([^;]*\);?)$/gm;
  
  fixed = fixed.replace(consolePattern, (match, indent, consoleStatement) => {
    // Skip if already wrapped in dev check
    const lines = fixed.split('\n');
    const matchIndex = fixed.indexOf(match);
    const lineIndex = fixed.substring(0, matchIndex).split('\n').length - 1;
    
    // Check if previous lines contain dev check
    const prevLines = lines.slice(Math.max(0, lineIndex - 3), lineIndex);
    const hasDevCheck = prevLines.some(line => 
      line.includes('import.meta.env.DEV') || 
      line.includes('process.env.NODE_ENV')
    );
    
    if (hasDevCheck) {
      return match; // Already wrapped
    }
    
    changes++;
    return `${indent}if (import.meta.env.DEV) {\n${indent}  ${consoleStatement}\n${indent}}`;
  });

  return { content: fixed, changes };
}

/**
 * Process a single file
 */
function processFile(filePath) {
  if (!fs.existsSync(filePath)) {
    log(`⚠️  File not found: ${filePath}`, 'yellow');
    return 0;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  // Skip files that don't have console statements
  if (!content.match(/console\.(log|warn|error|info|debug)/)) {
    return 0;
  }
  
  const result = fixConsoleStatements(content, filePath);
  
  if (result.changes > 0) {
    fs.writeFileSync(filePath, result.content, 'utf8');
    log(`✅ Fixed ${result.changes} console statement(s) in ${relativePath}`, 'green');
  }
  
  return result.changes;
}

/**
 * Get all TypeScript files
 */
function getAllTSFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build', 'coverage'].includes(item)) {
        getAllTSFiles(fullPath, files);
      }
    } else if (['.tsx', '.ts'].includes(path.extname(item))) {
      if (!['vite-env.d.ts'].includes(item)) {
        files.push(fullPath);
      }
    }
  }
  
  return files;
}

/**
 * Main function
 */
function main() {
  log('🔧 FIXING CONSOLE STATEMENTS', 'cyan');
  log('='.repeat(50), 'cyan');
  log('Wrapping console statements in development checks...', 'cyan');
  log();

  const allFiles = getAllTSFiles(path.join(PROJECT_ROOT, 'src'));
  
  log(`📁 Found ${allFiles.length} TypeScript files to check`, 'blue');
  log();

  let totalFiles = 0;
  let totalFixes = 0;

  allFiles.forEach(file => {
    const fixes = processFile(file);
    if (fixes > 0) {
      totalFiles++;
      totalFixes += fixes;
    }
  });

  log();
  log('📊 CONSOLE STATEMENTS FIX SUMMARY', 'cyan');
  log('─'.repeat(40), 'cyan');
  log(`📁 Files processed: ${allFiles.length}`, 'blue');
  log(`✅ Files fixed: ${totalFiles}`, 'green');
  log(`🔧 Total fixes: ${totalFixes}`, 'green');
  
  if (totalFixes > 0) {
    log();
    log('🎯 Next steps:', 'yellow');
    log('1. Run npm run build to verify production build', 'yellow');
    log('2. Run node scripts/verify-no-console.js to verify fixes', 'yellow');
    log('3. Test the application in development mode', 'yellow');
  } else {
    log();
    log('🎉 No console statements needed fixing!', 'green');
  }
}

// Run the script
main();
