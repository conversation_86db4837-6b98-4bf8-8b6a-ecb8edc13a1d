#!/usr/bin/env node

/**
 * EMERGENCY FIX: Text color matches background color making text invisible
 * This fixes blue text on blue background, green text on green background, etc.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

// Directories to scan
const SCAN_DIRECTORIES = [
  'src/components',
  'src/pages',
  'docs'
];

// EMERGENCY FIXES for invisible text (same color as background)
const INVISIBLE_TEXT_FIXES = [
  // Blue background with blue text - INVISIBLE!
  { 
    pattern: /className="([^"]*?)bg-info([^"]*?)text-info([^"]*?)"/g, 
    replacement: 'className="$1bg-info-light$2text-foreground$3"', 
    desc: 'FIXED: Blue text on blue background → visible text' 
  },
  { 
    pattern: /className="([^"]*?)text-info([^"]*?)bg-info([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-info-light$3"', 
    desc: 'FIXED: Blue text on blue background → visible text' 
  },
  
  // Green background with green text - INVISIBLE!
  { 
    pattern: /className="([^"]*?)bg-success([^"]*?)text-success([^"]*?)"/g, 
    replacement: 'className="$1bg-success-light$2text-foreground$3"', 
    desc: 'FIXED: Green text on green background → visible text' 
  },
  { 
    pattern: /className="([^"]*?)text-success([^"]*?)bg-success([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-success-light$3"', 
    desc: 'FIXED: Green text on green background → visible text' 
  },
  
  // Red background with red text - INVISIBLE!
  { 
    pattern: /className="([^"]*?)bg-error([^"]*?)text-error([^"]*?)"/g, 
    replacement: 'className="$1bg-error-light$2text-foreground$3"', 
    desc: 'FIXED: Red text on red background → visible text' 
  },
  { 
    pattern: /className="([^"]*?)text-error([^"]*?)bg-error([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-error-light$3"', 
    desc: 'FIXED: Red text on red background → visible text' 
  },
  
  // Warning background with warning text - INVISIBLE!
  { 
    pattern: /className="([^"]*?)bg-info([^"]*?)text-info([^"]*?)"/g, 
    replacement: 'className="$1bg-warning-light$2text-foreground$3"', 
    desc: 'FIXED: Warning text on warning background → visible text' 
  },
  { 
    pattern: /className="([^"]*?)text-info([^"]*?)bg-info([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-warning-light$3"', 
    desc: 'FIXED: Warning text on warning background → visible text' 
  },
  
  // Medical blue background with medical blue text - INVISIBLE!
  { 
    pattern: /className="([^"]*?)bg-medical-blue([^"]*?)text-medical-blue([^"]*?)"/g, 
    replacement: 'className="$1bg-medical-blue-light$2text-foreground$3"', 
    desc: 'FIXED: Medical blue text on medical blue background → visible text' 
  },
  { 
    pattern: /className="([^"]*?)text-medical-blue([^"]*?)bg-medical-blue([^"]*?)"/g, 
    replacement: 'className="$1text-foreground$2bg-medical-blue-light$3"', 
    desc: 'FIXED: Medical blue text on medical blue background → visible text' 
  },
  
  // Specific patterns from the screenshot components
  { 
    pattern: /className="bg-info border border-info rounded-lg p-4"/g, 
    replacement: 'className="bg-info-light border border-info/30 rounded-lg p-4"', 
    desc: 'FIXED: Clinical considerations box background' 
  },
  { 
    pattern: /className="font-semibold text-info mb-2"/g, 
    replacement: 'className="font-semibold text-foreground mb-2"', 
    desc: 'FIXED: Clinical considerations heading text' 
  },
  { 
    pattern: /className="text-sm text-info"/g, 
    replacement: 'className="text-sm text-foreground"', 
    desc: 'FIXED: Clinical considerations body text' 
  },
  
  // Fix any remaining invisible text patterns
  { 
    pattern: /text-info(?=.*bg-info)/g, 
    replacement: 'text-foreground', 
    desc: 'FIXED: Info text on info background' 
  },
  { 
    pattern: /text-success(?=.*bg-success)/g, 
    replacement: 'text-foreground', 
    desc: 'FIXED: Success text on success background' 
  },
  { 
    pattern: /text-error(?=.*bg-error)/g, 
    replacement: 'text-foreground', 
    desc: 'FIXED: Error text on error background' 
  },
  { 
    pattern: /text-info(?=.*bg-info)/g, 
    replacement: 'text-foreground', 
    desc: 'FIXED: Warning text on warning background' 
  },
  
  // Fix specific invisible text in colored sections
  { 
    pattern: /<h4 className="font-semibold text-info mb-2">Clinical Considerations<\/h4>/g, 
    replacement: '<h4 className="font-semibold text-foreground mb-2">Clinical Considerations</h4>', 
    desc: 'FIXED: Clinical Considerations heading visibility' 
  },
  { 
    pattern: /<p className="text-sm text-info">/g, 
    replacement: '<p className="text-sm text-foreground">', 
    desc: 'FIXED: Clinical Considerations paragraph visibility' 
  },
  
  // Fix list items with invisible text
  { 
    pattern: /<span className="text-sm text-info">/g, 
    replacement: '<span className="text-sm text-foreground">', 
    desc: 'FIXED: List item text visibility' 
  },
  { 
    pattern: /<li([^>]*?)className="([^"]*?)text-info([^"]*?)">/g, 
    replacement: '<li$1className="$2text-foreground$3">', 
    desc: 'FIXED: List item text visibility' 
  }
];

/**
 * Get all files to process
 */
function getAllFiles(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  const files = [];
  
  function scanDirectory(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not read directory ${currentDir}: ${error.message}`);
    }
  }
  
  scanDirectory(dir);
  return files;
}

/**
 * Fix invisible text in a single file
 */
function fixInvisibleText(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    const appliedFixes = [];

    INVISIBLE_TEXT_FIXES.forEach(({ pattern, replacement, desc }) => {
      const matches = content.match(pattern);
      if (matches) {
        content = content.replace(pattern, replacement);
        hasChanges = true;
        appliedFixes.push(`${desc} (${matches.length} instances)`);
      }
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`🚨 EMERGENCY FIX applied to: ${path.relative(PROJECT_ROOT, filePath)}`);
      appliedFixes.forEach(fix => console.log(`   - ${fix}`));
      return true;
    } else {
      console.log(`✅ No invisible text found in: ${path.relative(PROJECT_ROOT, filePath)}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Main execution
 */
function main() {
  console.log('🚨 EMERGENCY FIX: Fixing invisible text (same color as background)...\n');
  
  let totalFiles = 0;
  let fixedFiles = 0;
  
  SCAN_DIRECTORIES.forEach(dir => {
    const fullDir = path.join(PROJECT_ROOT, dir);
    if (fs.existsSync(fullDir)) {
      console.log(`📁 Scanning directory: ${dir}`);
      const files = getAllFiles(fullDir);
      
      files.forEach(file => {
        totalFiles++;
        if (fixInvisibleText(file)) {
          fixedFiles++;
        }
      });
      
      console.log('');
    }
  });
  
  console.log('🎯 EMERGENCY FIX Summary:');
  console.log(`   Total files scanned: ${totalFiles}`);
  console.log(`   Files with invisible text FIXED: ${fixedFiles}`);
  console.log(`   Files already visible: ${totalFiles - fixedFiles}`);
  
  if (fixedFiles > 0) {
    console.log('\n🚨 EMERGENCY FIX COMPLETE!');
    console.log('✅ All invisible text has been made visible!');
    console.log('🎨 Text now uses proper contrast colors on colored backgrounds.');
  } else {
    console.log('\n✅ No invisible text found - all text is already visible!');
  }
}

main();
