#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to systematically fix theme and styling issues across all medical condition pages
 * Addresses: Poor color contrast, inconsistent card styling, weak visual hierarchy,
 * background color problems, badge inconsistencies, and dark theme issues
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

// Medical condition pages directory
const MEDICAL_CONDITIONS_DIR = path.join(PROJECT_ROOT, 'src/pages/patient-resources/conditions');

// Theme replacement patterns
const THEME_REPLACEMENTS = [
  // Background color fixes
  {
    pattern: /className="py-16 bg-gray-50"/g,
    replacement: 'className="section-spacing section-background-alt"'
  },
  {
    pattern: /className="py-16"/g,
    replacement: 'className="section-spacing section-background"'
  },
  {
    pattern: /className="py-12 bg-gray-50"/g,
    replacement: 'className="section-spacing-sm section-background-alt"'
  },
  {
    pattern: /className="py-12"/g,
    replacement: 'className="section-spacing-sm section-background"'
  },
  
  // Card styling fixes
  {
    pattern: /className="bg-white rounded-lg p-6 shadow-md border"/g,
    replacement: 'className="medical-card p-6"'
  },
  {
    pattern: /className="bg-card rounded-lg p-6 shadow-md border"/g,
    replacement: 'className="medical-card p-6"'
  },
  
  // Text hierarchy fixes
  {
    pattern: /className="text-3xl font-bold mb-4"/g,
    replacement: 'className="text-enhanced-heading text-3xl font-bold mb-4"'
  },
  {
    pattern: /className="text-2xl font-bold mb-6"/g,
    replacement: 'className="text-enhanced-heading text-2xl font-bold mb-6"'
  },
  {
    pattern: /className="text-xl font-semibold"/g,
    replacement: 'className="text-enhanced-heading text-xl font-semibold"'
  },
  {
    pattern: /className="font-semibold text-xl"/g,
    replacement: 'className="text-enhanced-heading font-semibold text-xl"'
  },
  {
    pattern: /className="font-semibold text-lg"/g,
    replacement: 'className="text-enhanced-heading font-semibold text-lg"'
  },
  {
    pattern: /className="font-medium"/g,
    replacement: 'className="text-enhanced-strong font-medium"'
  },
  {
    pattern: /className="text-lg text-muted-foreground"/g,
    replacement: 'className="text-enhanced-body text-lg"'
  },
  {
    pattern: /className="text-muted-foreground"/g,
    replacement: 'className="text-enhanced-muted"'
  },
  {
    pattern: /className="text-sm text-muted-foreground"/g,
    replacement: 'className="text-enhanced-muted text-sm"'
  },
  {
    pattern: /className="text-xs text-muted-foreground"/g,
    replacement: 'className="text-enhanced-muted text-xs"'
  },
  {
    pattern: /className="text-sm"/g,
    replacement: 'className="text-enhanced-body text-sm"'
  },
  
  // Border fixes
  {
    pattern: /border-t"/g,
    replacement: 'border-t border-border"'
  },
  {
    pattern: /className="border-l-4 border-info\/30"/g,
    replacement: 'className="border-l-4 border-primary/30"'
  },
  {
    pattern: /className="border-l-4 border-success/30"/g,
    replacement: 'className="border-l-4 border-success/30"'
  },
  {
    pattern: /className="border-l-4 border-info/30"/g,
    replacement: 'className="border-l-4 border-info/30"'
  },
  {
    pattern: /className="border-l-4 border-red-200"/g,
    replacement: 'className="border-l-4 border-error/30"'
  },
  
  // Spacing fixes
  {
    pattern: /className="space-y-4"/g,
    replacement: 'className="content-spacing-sm"'
  },
  {
    pattern: /className="space-y-6"/g,
    replacement: 'className="content-spacing"'
  },
  {
    pattern: /className="space-y-8"/g,
    replacement: 'className="content-spacing"'
  }
];

// Badge color replacements
const BADGE_REPLACEMENTS = [
  {
    pattern: /bg-blue-100 text-blue-800/g,
    replacement: 'badge-info'
  },
  {
    pattern: /bg-green-100 text-green-800/g,
    replacement: 'badge-routine'
  },
  {
    pattern: /bg-info-light text-info/g,
    replacement: 'badge-info'
  },
  {
    pattern: /bg-red-100 text-red-800/g,
    replacement: 'badge-emergency'
  },
  {
    pattern: /bg-purple-100 text-purple-800/g,
    replacement: 'badge-medical'
  },
  {
    pattern: /bg-yellow-100 text-info/g,
    replacement: 'badge-routine'
  },
  {
    pattern: /bg-gray-100 text-gray-800/g,
    replacement: 'badge-info'
  }
];

// Background color replacements for info boxes
const INFO_BOX_REPLACEMENTS = [
  {
    pattern: /bg-blue-50 border border-blue-200/g,
    replacement: 'bg-info-light border border-info/30'
  },
  {
    pattern: /bg-green-50 border border-green-200/g,
    replacement: 'bg-success-light border border-success/30'
  },
  {
    pattern: /bg-orange-50 border border-info/30/g,
    replacement: 'bg-info-light border border-info/30'
  },
  {
    pattern: /bg-red-50 border border-red-200/g,
    replacement: 'bg-error-light border border-error/30'
  },
  {
    pattern: /text-blue-900/g,
    replacement: 'text-info'
  },
  {
    pattern: /text-blue-800/g,
    replacement: 'text-info'
  },
  {
    pattern: /text-green-900/g,
    replacement: 'text-success'
  },
  {
    pattern: /text-green-800/g,
    replacement: 'text-success'
  },
  {
    pattern: /text-info/g,
    replacement: 'text-info'
  },
  {
    pattern: /text-info/g,
    replacement: 'text-info'
  },
  {
    pattern: /text-red-900/g,
    replacement: 'text-error'
  },
  {
    pattern: /text-red-800/g,
    replacement: 'text-error'
  }
];

// Bullet point color fixes
const BULLET_REPLACEMENTS = [
  {
    pattern: /bg-blue-500/g,
    replacement: 'bg-primary'
  },
  {
    pattern: /bg-green-500/g,
    replacement: 'bg-success'
  },
  {
    pattern: /bg-info-light/g,
    replacement: 'bg-info'
  },
  {
    pattern: /bg-red-500/g,
    replacement: 'bg-error'
  }
];

/**
 * Apply theme fixes to a single file
 */
function fixFileTheme(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // Apply all replacement patterns
    const allReplacements = [
      ...THEME_REPLACEMENTS,
      ...BADGE_REPLACEMENTS,
      ...INFO_BOX_REPLACEMENTS,
      ...BULLET_REPLACEMENTS
    ];

    allReplacements.forEach(({ pattern, replacement }) => {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        hasChanges = true;
      }
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed theme issues in: ${path.basename(filePath)}`);
      return true;
    } else {
      console.log(`ℹ️  No theme issues found in: ${path.basename(filePath)}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Main execution function
 */
function main() {
  console.log('🎨 Starting Medical Condition Theme Fix...\n');

  if (!fs.existsSync(MEDICAL_CONDITIONS_DIR)) {
    console.error(`❌ Medical conditions directory not found: ${MEDICAL_CONDITIONS_DIR}`);
    process.exit(1);
  }

  const files = fs.readdirSync(MEDICAL_CONDITIONS_DIR)
    .filter(file => file.endsWith('.tsx'))
    .map(file => path.join(MEDICAL_CONDITIONS_DIR, file));

  console.log(`📁 Found ${files.length} medical condition files to process\n`);

  let fixedCount = 0;
  files.forEach(file => {
    if (fixFileTheme(file)) {
      fixedCount++;
    }
  });

  console.log(`\n🎉 Theme fix complete!`);
  console.log(`📊 Files processed: ${files.length}`);
  console.log(`✅ Files fixed: ${fixedCount}`);
  console.log(`ℹ️  Files unchanged: ${files.length - fixedCount}`);
}

// Run the script
main();
