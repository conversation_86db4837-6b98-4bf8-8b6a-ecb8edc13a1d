#!/usr/bin/env node

/**
 * Critical script to fix ALL remaining hardcoded colors that are still visible in the screenshot
 * This addresses the specific dark background and color inconsistency issues
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

// Directories to scan
const SCAN_DIRECTORIES = [
  'src/components',
  'src/pages',
  'src/data',
  'docs'
];

// Critical hardcoded color patterns that are still causing issues
const CRITICAL_COLOR_FIXES = [
  // Dark blue/teal backgrounds that create poor contrast
  { pattern: /\bbg-blue-([5-9][0-9][0-9]|[5-9]00)\b/g, replacement: 'bg-info-light', desc: 'Dark blue backgrounds → bg-info-light' },
  { pattern: /\bbg-teal-([5-9][0-9][0-9]|[5-9]00)\b/g, replacement: 'bg-info-light', desc: 'Dark teal backgrounds → bg-info-light' },
  { pattern: /\bbg-cyan-([5-9][0-9][0-9]|[5-9]00)\b/g, replacement: 'bg-info-light', desc: 'Dark cyan backgrounds → bg-info-light' },
  
  // Light blue/teal backgrounds that should use theme colors
  { pattern: /\bbg-blue-([1-4][0-9]|50)\b/g, replacement: 'bg-info-light', desc: 'Light blue backgrounds → bg-info-light' },
  { pattern: /\bbg-teal-([1-4][0-9]|50)\b/g, replacement: 'bg-info-light', desc: 'Light teal backgrounds → bg-info-light' },
  { pattern: /\bbg-cyan-([1-4][0-9]|50)\b/g, replacement: 'bg-info-light', desc: 'Light cyan backgrounds → bg-info-light' },
  
  // Text colors on colored backgrounds
  { pattern: /\btext-blue-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'text-info', desc: 'Blue text → text-info' },
  { pattern: /\btext-teal-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'text-info', desc: 'Teal text → text-info' },
  { pattern: /\btext-cyan-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'text-info', desc: 'Cyan text → text-info' },
  
  // Border colors
  { pattern: /\bborder-blue-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'border-info/30', desc: 'Blue borders → border-info/30' },
  { pattern: /\bborder-teal-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'border-info/30', desc: 'Teal borders → border-info/30' },
  { pattern: /\bborder-cyan-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'border-info/30', desc: 'Cyan borders → border-info/30' },
  
  // Green colors that should use success theme
  { pattern: /\bbg-green-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'bg-success-light', desc: 'Green backgrounds → bg-success-light' },
  { pattern: /\btext-green-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'text-success', desc: 'Green text → text-success' },
  { pattern: /\bborder-green-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'border-success/30', desc: 'Green borders → border-success/30' },
  
  // Red colors that should use error theme
  { pattern: /\bbg-red-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'bg-error-light', desc: 'Red backgrounds → bg-error-light' },
  { pattern: /\btext-red-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'text-error', desc: 'Red text → text-error' },
  { pattern: /\bborder-red-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'border-error/30', desc: 'Red borders → border-error/30' },
  
  // Yellow/Orange colors that should use warning theme
  { pattern: /\bbg-yellow-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'bg-info-light', desc: 'Yellow backgrounds → bg-info-light' },
  { pattern: /\btext-yellow-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'text-info', desc: 'Yellow text → text-info' },
  { pattern: /\bborder-yellow-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'border-info/30', desc: 'Yellow borders → border-info/30' },
  
  { pattern: /\bbg-orange-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'bg-info-light', desc: 'Orange backgrounds → bg-info-light' },
  { pattern: /\btext-orange-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'text-info', desc: 'Orange text → text-info' },
  { pattern: /\bborder-orange-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'border-info/30', desc: 'Orange borders → border-info/30' },
  
  // Purple colors that should use medical-blue theme
  { pattern: /\bbg-purple-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'bg-medical-blue-light', desc: 'Purple backgrounds → bg-medical-blue-light' },
  { pattern: /\btext-purple-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'text-medical-blue', desc: 'Purple text → text-medical-blue' },
  { pattern: /\bborder-purple-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'border-medical-blue/30', desc: 'Purple borders → border-medical-blue/30' },
  
  // Indigo colors
  { pattern: /\bbg-indigo-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'bg-medical-blue-light', desc: 'Indigo backgrounds → bg-medical-blue-light' },
  { pattern: /\btext-indigo-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'text-medical-blue', desc: 'Indigo text → text-medical-blue' },
  { pattern: /\bborder-indigo-([1-9][0-9][0-9]|[1-9]00)\b/g, replacement: 'border-medical-blue/30', desc: 'Indigo borders → border-medical-blue/30' },
  
  // Specific dark theme patterns that cause poor contrast
  { pattern: /\bdark:bg-blue-([5-9][0-9][0-9]|950)\/([0-9]+)\b/g, replacement: 'dark:bg-info-light', desc: 'Dark theme blue backgrounds → dark:bg-info-light' },
  { pattern: /\bdark:text-blue-([1-4][0-9][0-9]|[1-4]00)\b/g, replacement: 'dark:text-info', desc: 'Dark theme blue text → dark:text-info' },
  { pattern: /\bdark:border-blue-([5-9][0-9][0-9]|800)\b/g, replacement: 'dark:border-info/30', desc: 'Dark theme blue borders → dark:border-info/30' },
  
  // White text on colored backgrounds (poor contrast)
  { pattern: /\btext-white\b(?=.*bg-(?:blue|teal|cyan|green|red|yellow|orange|purple|indigo))/g, replacement: 'text-primary-foreground', desc: 'White text on colored backgrounds → text-primary-foreground' },
  
  // Gradient backgrounds that create poor contrast
  { pattern: /\bbg-gradient-to-r from-blue-([0-9]+) to-teal-([0-9]+)\b/g, replacement: 'bg-info-light border border-info/30', desc: 'Blue-teal gradient → bg-info-light' },
  { pattern: /\bbg-gradient-to-br from-blue-([0-9]+) to-purple-([0-9]+)\b/g, replacement: 'bg-info-light border border-info/30', desc: 'Blue-purple gradient → bg-info-light' },
  { pattern: /\bbg-gradient-to-l from-teal-([0-9]+) to-blue-([0-9]+)\b/g, replacement: 'bg-info-light border border-info/30', desc: 'Teal-blue gradient → bg-info-light' },
  
  // Complex gradient patterns
  { pattern: /\bbg-gradient-to-([a-z]+) from-([a-z]+)-([0-9]+)\/([0-9]+) to-([a-z]+)-([0-9]+)\/([0-9]+)\b/g, replacement: 'bg-muted border border-border/30', desc: 'Complex gradient → bg-muted' },
  
  // Specific problematic combinations from screenshot
  { pattern: /className="[^"]*bg-blue-[5-9]00[^"]*"/g, replacement: (match) => match.replace(/bg-blue-[5-9]00/g, 'bg-info-light'), desc: 'Dark blue in className → bg-info-light' },
  { pattern: /className="[^"]*bg-teal-[5-9]00[^"]*"/g, replacement: (match) => match.replace(/bg-teal-[5-9]00/g, 'bg-info-light'), desc: 'Dark teal in className → bg-info-light' },
  { pattern: /className="[^"]*bg-green-[5-9]00[^"]*"/g, replacement: (match) => match.replace(/bg-green-[5-9]00/g, 'bg-success-light'), desc: 'Dark green in className → bg-success-light' }
];

/**
 * Get all files to process
 */
function getAllFiles(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  const files = [];
  
  function scanDirectory(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not read directory ${currentDir}: ${error.message}`);
    }
  }
  
  scanDirectory(dir);
  return files;
}

/**
 * Fix critical hardcoded colors in a single file
 */
function fixCriticalColors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    const appliedFixes = [];

    CRITICAL_COLOR_FIXES.forEach(({ pattern, replacement, desc }) => {
      const matches = content.match(pattern);
      if (matches) {
        if (typeof replacement === 'function') {
          content = content.replace(pattern, replacement);
        } else {
          content = content.replace(pattern, replacement);
        }
        hasChanges = true;
        appliedFixes.push(`${desc} (${matches.length} instances)`);
      }
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed critical colors in: ${path.relative(PROJECT_ROOT, filePath)}`);
      appliedFixes.forEach(fix => console.log(`   - ${fix}`));
      return true;
    } else {
      console.log(`ℹ️  No critical color issues found in: ${path.relative(PROJECT_ROOT, filePath)}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Main execution
 */
function main() {
  console.log('🚨 Starting critical hardcoded color fixes...\n');
  
  let totalFiles = 0;
  let fixedFiles = 0;
  
  SCAN_DIRECTORIES.forEach(dir => {
    const fullDir = path.join(PROJECT_ROOT, dir);
    if (fs.existsSync(fullDir)) {
      console.log(`📁 Scanning directory: ${dir}`);
      const files = getAllFiles(fullDir);
      
      files.forEach(file => {
        totalFiles++;
        if (fixCriticalColors(file)) {
          fixedFiles++;
        }
      });
      
      console.log('');
    }
  });
  
  console.log('🎯 Critical Fixes Summary:');
  console.log(`   Total files scanned: ${totalFiles}`);
  console.log(`   Files with critical fixes: ${fixedFiles}`);
  console.log(`   Files already compliant: ${totalFiles - fixedFiles}`);
  
  if (fixedFiles > 0) {
    console.log('\n✅ Critical hardcoded color issues have been fixed!');
    console.log('🎨 Dark backgrounds and poor contrast issues should now be resolved.');
  } else {
    console.log('\n✅ No critical color issues found - codebase is compliant!');
  }
}

main();
