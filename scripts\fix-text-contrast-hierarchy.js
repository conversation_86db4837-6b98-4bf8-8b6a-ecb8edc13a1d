#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to fix text contrast and visual hierarchy issues
 * This addresses poor text readability and weak visual hierarchy visible in the screenshot
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

// Directories to scan
const SCAN_DIRECTORIES = [
  'src/components',
  'src/pages',
  'docs'
];

// Text contrast and hierarchy fixes
const TEXT_HIERARCHY_FIXES = [
  // Fix heading text that lacks proper contrast
  { 
    pattern: /className="([^"]*?)text-gray-([5-9][0-9][0-9]|[5-9]00)([^"]*?)font-bold([^"]*?)"/g, 
    replacement: 'className="$1text-enhanced-heading$3font-bold$4"', 
    desc: 'Dark gray headings → text-enhanced-heading' 
  },
  { 
    pattern: /className="([^"]*?)font-bold([^"]*?)text-gray-([5-9][0-9][0-9]|[5-9]00)([^"]*?)"/g, 
    replacement: 'className="$1font-bold$2text-enhanced-heading$4"', 
    desc: 'Dark gray bold text → text-enhanced-heading' 
  },
  
  // Fix body text with poor contrast
  { 
    pattern: /className="([^"]*?)text-gray-([4-6][0-9][0-9]|[4-6]00)([^"]*?)"/g, 
    replacement: 'className="$1text-enhanced-body$3"', 
    desc: 'Medium gray text → text-enhanced-body' 
  },
  { 
    pattern: /className="([^"]*?)text-slate-([4-6][0-9][0-9]|[4-6]00)([^"]*?)"/g, 
    replacement: 'className="$1text-enhanced-body$3"', 
    desc: 'Medium slate text → text-enhanced-body' 
  },
  
  // Fix light text that's hard to read
  { 
    pattern: /className="([^"]*?)text-gray-([1-3][0-9][0-9]|[1-3]00)([^"]*?)"/g, 
    replacement: 'className="$1text-muted-foreground$3"', 
    desc: 'Light gray text → text-muted-foreground' 
  },
  { 
    pattern: /className="([^"]*?)text-slate-([1-3][0-9][0-9]|[1-3]00)([^"]*?)"/g, 
    replacement: 'className="$1text-muted-foreground$3"', 
    desc: 'Light slate text → text-muted-foreground' 
  },
  
  // Fix headings that need enhanced styling
  { 
    pattern: /className="([^"]*?)text-2xl([^"]*?)font-bold([^"]*?)"/g, 
    replacement: 'className="$1text-enhanced-heading text-2xl$2font-bold$3"', 
    desc: '2xl headings → enhanced heading' 
  },
  { 
    pattern: /className="([^"]*?)text-3xl([^"]*?)font-bold([^"]*?)"/g, 
    replacement: 'className="$1text-enhanced-heading text-3xl$2font-bold$3"', 
    desc: '3xl headings → enhanced heading' 
  },
  { 
    pattern: /className="([^"]*?)text-4xl([^"]*?)font-bold([^"]*?)"/g, 
    replacement: 'className="$1text-enhanced-heading text-4xl$2font-bold$3"', 
    desc: '4xl headings → enhanced heading' 
  },
  
  // Fix semibold text that needs enhancement
  { 
    pattern: /className="([^"]*?)font-semibold([^"]*?)text-gray-([5-9][0-9][0-9]|[5-9]00)([^"]*?)"/g, 
    replacement: 'className="$1font-semibold$2text-enhanced-strong$4"', 
    desc: 'Semibold gray text → text-enhanced-strong' 
  },
  { 
    pattern: /className="([^"]*?)text-gray-([5-9][0-9][0-9]|[5-9]00)([^"]*?)font-semibold([^"]*?)"/g, 
    replacement: 'className="$1text-enhanced-strong$3font-semibold$4"', 
    desc: 'Gray semibold text → text-enhanced-strong' 
  },
  
  // Fix medium weight text
  { 
    pattern: /className="([^"]*?)font-medium([^"]*?)text-gray-([5-9][0-9][0-9]|[5-9]00)([^"]*?)"/g, 
    replacement: 'className="$1font-medium$2text-enhanced-body$4"', 
    desc: 'Medium gray text → text-enhanced-body' 
  },
  { 
    pattern: /className="([^"]*?)text-gray-([5-9][0-9][0-9]|[5-9]00)([^"]*?)font-medium([^"]*?)"/g, 
    replacement: 'className="$1text-enhanced-body$3font-medium$4"', 
    desc: 'Gray medium text → text-enhanced-body' 
  },
  
  // Fix colored text that may have poor contrast
  { 
    pattern: /className="([^"]*?)text-blue-([1-4][0-9][0-9]|[1-4]00)([^"]*?)"/g, 
    replacement: 'className="$1text-info$3"', 
    desc: 'Light blue text → text-info' 
  },
  { 
    pattern: /className="([^"]*?)text-green-([1-4][0-9][0-9]|[1-4]00)([^"]*?)"/g, 
    replacement: 'className="$1text-success$3"', 
    desc: 'Light green text → text-success' 
  },
  { 
    pattern: /className="([^"]*?)text-red-([1-4][0-9][0-9]|[1-4]00)([^"]*?)"/g, 
    replacement: 'className="$1text-error$3"', 
    desc: 'Light red text → text-error' 
  },
  { 
    pattern: /className="([^"]*?)text-yellow-([1-4][0-9][0-9]|[1-4]00)([^"]*?)"/g, 
    replacement: 'className="$1text-warning$3"', 
    desc: 'Light yellow text → text-info' 
  },
  { 
    pattern: /className="([^"]*?)text-orange-([1-4][0-9][0-9]|[1-4]00)([^"]*?)"/g, 
    replacement: 'className="$1text-warning$3"', 
    desc: 'Light orange text → text-info' 
  },
  { 
    pattern: /className="([^"]*?)text-purple-([1-4][0-9][0-9]|[1-4]00)([^"]*?)"/g, 
    replacement: 'className="$1text-medical-blue$3"', 
    desc: 'Light purple text → text-medical-blue' 
  },
  
  // Fix dark theme text issues
  { 
    pattern: /className="([^"]*?)dark:text-gray-([1-3][0-9][0-9]|[1-3]00)([^"]*?)"/g, 
    replacement: 'className="$1dark:text-muted-foreground$3"', 
    desc: 'Dark theme light text → dark:text-muted-foreground' 
  },
  { 
    pattern: /className="([^"]*?)dark:text-blue-([1-4][0-9][0-9]|[1-4]00)([^"]*?)"/g, 
    replacement: 'className="$1dark:text-info$3"', 
    desc: 'Dark theme blue text → dark:text-info' 
  },
  { 
    pattern: /className="([^"]*?)dark:text-green-([1-4][0-9][0-9]|[1-4]00)([^"]*?)"/g, 
    replacement: 'className="$1dark:text-success$3"', 
    desc: 'Dark theme green text → dark:text-success' 
  },
  
  // Fix specific problematic text patterns from medical pages
  { 
    pattern: /className="font-semibold text-blue-800 dark:text-blue-200"/g, 
    replacement: 'className="font-semibold text-info"', 
    desc: 'Medical info headings → text-info' 
  },
  { 
    pattern: /className="font-semibold text-green-800 dark:text-green-200"/g, 
    replacement: 'className="font-semibold text-success"', 
    desc: 'Medical success headings → text-success' 
  },
  { 
    pattern: /className="font-semibold text-red-800 dark:text-red-200"/g, 
    replacement: 'className="font-semibold text-error"', 
    desc: 'Medical error headings → text-error' 
  },
  { 
    pattern: /className="font-semibold text-info dark:text-yellow-200"/g, 
    replacement: 'className="font-semibold text-info"', 
    desc: 'Medical warning headings → text-info' 
  },
  { 
    pattern: /className="font-semibold text-info dark:text-info"/g, 
    replacement: 'className="font-semibold text-info"', 
    desc: 'Medical orange headings → text-info' 
  },
  { 
    pattern: /className="font-semibold text-purple-800 dark:text-purple-200"/g, 
    replacement: 'className="font-semibold text-medical-blue"', 
    desc: 'Medical purple headings → text-medical-blue' 
  },
  
  // Fix body text in medical cards
  { 
    pattern: /className="text-sm text-blue-700 dark:text-blue-300"/g, 
    replacement: 'className="text-enhanced-body text-sm"', 
    desc: 'Medical info body text → text-enhanced-body' 
  },
  { 
    pattern: /className="text-sm text-green-700 dark:text-green-300"/g, 
    replacement: 'className="text-enhanced-body text-sm"', 
    desc: 'Medical success body text → text-enhanced-body' 
  },
  { 
    pattern: /className="text-sm text-red-700 dark:text-red-300"/g, 
    replacement: 'className="text-enhanced-body text-sm"', 
    desc: 'Medical error body text → text-enhanced-body' 
  },
  { 
    pattern: /className="text-sm text-info dark:text-yellow-300"/g, 
    replacement: 'className="text-enhanced-body text-sm"', 
    desc: 'Medical warning body text → text-enhanced-body' 
  },
  { 
    pattern: /className="text-sm text-info dark:text-info"/g, 
    replacement: 'className="text-enhanced-body text-sm"', 
    desc: 'Medical orange body text → text-enhanced-body' 
  },
  { 
    pattern: /className="text-sm text-purple-700 dark:text-purple-300"/g, 
    replacement: 'className="text-enhanced-body text-sm"', 
    desc: 'Medical purple body text → text-enhanced-body' 
  },
  
  // Fix list item text
  { 
    pattern: /className="([^"]*?)text-sm([^"]*?)text-gray-([5-7][0-9][0-9]|[5-7]00)([^"]*?)"/g, 
    replacement: 'className="$1text-enhanced-body text-sm$2$4"', 
    desc: 'Small gray text → text-enhanced-body' 
  },
  
  // Fix button text that may have poor contrast
  { 
    pattern: /className="([^"]*?)text-white([^"]*?)bg-blue-([5-9][0-9][0-9]|[5-9]00)([^"]*?)"/g, 
    replacement: 'className="$1text-primary-foreground$2bg-primary$4"', 
    desc: 'White text on blue buttons → primary theme' 
  },
  { 
    pattern: /className="([^"]*?)bg-blue-([5-9][0-9][0-9]|[5-9]00)([^"]*?)text-white([^"]*?)"/g, 
    replacement: 'className="$1bg-primary$3text-primary-foreground$4"', 
    desc: 'Blue buttons with white text → primary theme' 
  }
];

/**
 * Get all files to process
 */
function getAllFiles(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  const files = [];
  
  function scanDirectory(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not read directory ${currentDir}: ${error.message}`);
    }
  }
  
  scanDirectory(dir);
  return files;
}

/**
 * Fix text contrast and hierarchy in a single file
 */
function fixTextHierarchy(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    const appliedFixes = [];

    TEXT_HIERARCHY_FIXES.forEach(({ pattern, replacement, desc }) => {
      const matches = content.match(pattern);
      if (matches) {
        content = content.replace(pattern, replacement);
        hasChanges = true;
        appliedFixes.push(`${desc} (${matches.length} instances)`);
      }
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed text hierarchy in: ${path.relative(PROJECT_ROOT, filePath)}`);
      appliedFixes.forEach(fix => console.log(`   - ${fix}`));
      return true;
    } else {
      console.log(`ℹ️  No text hierarchy issues found in: ${path.relative(PROJECT_ROOT, filePath)}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Main execution
 */
function main() {
  console.log('📝 Starting text contrast and hierarchy fixes...\n');
  
  let totalFiles = 0;
  let fixedFiles = 0;
  
  SCAN_DIRECTORIES.forEach(dir => {
    const fullDir = path.join(PROJECT_ROOT, dir);
    if (fs.existsSync(fullDir)) {
      console.log(`📁 Scanning directory: ${dir}`);
      const files = getAllFiles(fullDir);
      
      files.forEach(file => {
        totalFiles++;
        if (fixTextHierarchy(file)) {
          fixedFiles++;
        }
      });
      
      console.log('');
    }
  });
  
  console.log('🎯 Text Hierarchy Fixes Summary:');
  console.log(`   Total files scanned: ${totalFiles}`);
  console.log(`   Files with text fixes: ${fixedFiles}`);
  console.log(`   Files already compliant: ${totalFiles - fixedFiles}`);
  
  if (fixedFiles > 0) {
    console.log('\n✅ Text contrast and hierarchy issues have been fixed!');
    console.log('📝 All text now uses enhanced contrast and proper visual hierarchy.');
  } else {
    console.log('\n✅ No text hierarchy issues found - all text is already optimized!');
  }
}

main();
