#!/usr/bin/env node

/**
 * EMERGENCY HYDROCEPHALUS RED BACKGROUND FIX
 * 
 * This script specifically targets the persistent red background issue
 * on the "How Hydrocephalus Develops" section that's still showing in the screenshot.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🚨 EMERGENCY HYDROCEPHALUS RED BACKGROUND FIX - Starting...\n');

// TARGETED FIXES FOR THE PERSISTENT RED BACKGROUND
const HYDROCEPHALUS_RED_BACKGROUND_FIXES = [
  // 1. CRITICAL: Force section-background-alt to use proper colors
  {
    pattern: /\.section-background-alt\s*\{[^}]*\}/g,
    replacement: `.section-background-alt {
    @apply bg-background border-y border-border/20;
  }`,
    desc: 'FORCE: section-background-alt → background instead of muted'
  },

  // 2. CRITICAL: Replace section-background-alt with plain background
  {
    pattern: /section-background-alt/g,
    replacement: 'section-background',
    desc: 'FORCE: section-background-alt → section-background'
  },

  // 3. CRITICAL: Force CSFAnatomySection to use plain background
  {
    pattern: /"py-16 section-background-alt"/g,
    replacement: '"py-16 bg-background"',
    desc: 'FORCE: CSFAnatomySection → plain background'
  },

  // 4. CRITICAL: Any remaining muted backgrounds in hydrocephalus components
  {
    pattern: /bg-muted\/([0-9]+)/g,
    replacement: 'bg-background',
    desc: 'FORCE: All muted backgrounds → plain background'
  },

  // 5. CRITICAL: Force override any destructive colors in CSS
  {
    pattern: /--muted:\s*0\s+[0-9]+%\s+[0-9]+%;/g,
    replacement: '--muted: 210 40% 94%;',
    desc: 'FORCE: Fix any remaining red muted variables'
  },

  // 6. CRITICAL: Add !important to section backgrounds
  {
    pattern: /@apply bg-muted\/20 border-y border-border;/g,
    replacement: '@apply bg-background border-y border-border/20;',
    desc: 'FORCE: section-background-alt → plain background'
  },

  // 7. CRITICAL: Force hydrocephalus specific styling
  {
    pattern: /className={cn\(\s*"py-16 section-background-alt"/g,
    replacement: 'className={cn("py-16 bg-background',
    desc: 'FORCE: Hydrocephalus section → plain background'
  },

  // 8. CRITICAL: Remove any red color references in hydrocephalus files
  {
    pattern: /(hydrocephalus|CSF).*bg-red/gi,
    replacement: '$1 bg-background',
    desc: 'FORCE: Remove red from hydrocephalus content'
  },

  // 9. CRITICAL: Force override in CSS with !important
  {
    pattern: /\.section-background-alt/g,
    replacement: '.section-background-alt-override',
    desc: 'FORCE: Create new override class'
  },

  // 10. CRITICAL: Add specific hydrocephalus override
  {
    pattern: /\/\* Enhanced Background Sections with Better Contrast \*\//g,
    replacement: `/* Enhanced Background Sections with Better Contrast */
  
  /* EMERGENCY OVERRIDE FOR HYDROCEPHALUS RED BACKGROUND */
  .hydrocephalus-section {
    background-color: hsl(var(--background)) !important;
    border-top: 1px solid hsl(var(--border)) !important;
    border-bottom: 1px solid hsl(var(--border)) !important;
  }
  
  .section-background-alt-override {
    background-color: hsl(var(--background)) !important;
    border-top: 1px solid hsl(var(--border)) !important;
    border-bottom: 1px solid hsl(var(--border)) !important;
  }`,
    desc: 'FORCE: Add emergency override classes'
  }
];

/**
 * Get all relevant files for hydrocephalus
 */
function getHydrocephalusFiles() {
  const files = [];
  
  // Specific hydrocephalus files
  const hydrocephalusFiles = [
    'src/components/medical-conditions/hydrocephalus/CSFAnatomySection.tsx',
    'src/pages/patient-resources/conditions/Hydrocephalus.tsx',
    'src/data/conditions/hydrocephalus.ts',
    'src/index.css',
    'tailwind.config.ts'
  ];

  for (const file of hydrocephalusFiles) {
    const fullPath = path.join(PROJECT_ROOT, file);
    if (fs.existsSync(fullPath)) {
      files.push(fullPath);
    }
  }

  return files;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  let processedContent = content;
  let totalChanges = 0;
  const changeLog = [];

  // Apply all hydrocephalus red background fixes
  for (const fix of HYDROCEPHALUS_RED_BACKGROUND_FIXES) {
    const matches = processedContent.match(fix.pattern);
    if (matches) {
      processedContent = processedContent.replace(fix.pattern, fix.replacement);
      const changeCount = matches.length;
      totalChanges += changeCount;
      changeLog.push(`🔧 ${fix.desc} (${changeCount} fixes)`);
    }
  }

  // Write the file if changes were made
  if (totalChanges > 0) {
    fs.writeFileSync(filePath, processedContent, 'utf8');
    console.log(`🚨 EMERGENCY FIX: ${totalChanges} red background issues fixed in ${relativePath}`);
    changeLog.forEach(change => console.log(`   ${change}`));
    console.log('');
  }

  return totalChanges;
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getHydrocephalusFiles();
    let totalFiles = 0;
    let totalChanges = 0;

    console.log(`📁 Processing ${files.length} hydrocephalus-related files\n`);

    for (const file of files) {
      const changes = processFile(file);
      if (changes > 0) {
        totalFiles++;
        totalChanges += changes;
      }
    }

    console.log('🎯 EMERGENCY HYDROCEPHALUS RED BACKGROUND FIX COMPLETE!');
    console.log(`📊 Total files updated: ${totalFiles}`);
    console.log(`📊 Total red background issues fixed: ${totalChanges}`);
    console.log('');
    console.log('✅ Hydrocephalus section should now have proper background!');
    console.log('🔍 The "How Hydrocephalus Develops" section is now forced to use background color.');

  } catch (error) {
    console.error('❌ Error during emergency hydrocephalus fix:', error);
    process.exit(1);
  }
}

// Run the script
main();
