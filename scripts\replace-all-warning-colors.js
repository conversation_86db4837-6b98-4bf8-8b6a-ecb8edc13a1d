#!/usr/bin/env node

/**
 * REPLACE ALL WARNING COLORS WITH PROFESSIONAL MEDICAL COLORS
 * 
 * This script replaces ALL instances of warning colors (which are brown/olive)
 * with professional medical colors throughout the entire codebase.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🚨 REPLACING ALL WARNING COLORS WITH PROFESSIONAL MEDICAL COLORS...\n');

// COMPREHENSIVE WARNING COLOR REPLACEMENT PATTERNS
const WARNING_COLOR_REPLACEMENTS = [
  // 1. BACKGROUND COLORS
  {
    pattern: /\bbg-warning-light\b/g,
    replacement: 'bg-info-light',
    desc: 'PROFESSIONAL: bg-info-light → bg-info-light'
  },
  {
    pattern: /\bbg-warning\b/g,
    replacement: 'bg-info',
    desc: 'PROFESSIONAL: bg-info → bg-info'
  },

  // 2. TEXT COLORS
  {
    pattern: /\btext-warning\b/g,
    replacement: 'text-info',
    desc: 'PROFESSIONAL: text-info → text-info'
  },

  // 3. BORDER COLORS
  {
    pattern: /\bborder-warning\/30\b/g,
    replacement: 'border-info/30',
    desc: 'PROFESSIONAL: border-info/30 → border-info/30'
  },
  {
    pattern: /\bborder-warning\/50\b/g,
    replacement: 'border-info/50',
    desc: 'PROFESSIONAL: border-info/50 → border-info/50'
  },
  {
    pattern: /\bborder-warning\b/g,
    replacement: 'border-info',
    desc: 'PROFESSIONAL: border-info → border-info'
  },

  // 4. HOVER STATES
  {
    pattern: /\bhover:bg-info\/80\b/g,
    replacement: 'hover:bg-info/80',
    desc: 'PROFESSIONAL: hover:bg-info/80 → hover:bg-info/80'
  },
  {
    pattern: /\bhover:bg-info\b/g,
    replacement: 'hover:bg-info',
    desc: 'PROFESSIONAL: hover:bg-info → hover:bg-info'
  },

  // 5. DARK THEME VARIANTS
  {
    pattern: /\bdark:bg-info-light\b/g,
    replacement: 'dark:bg-info-light',
    desc: 'PROFESSIONAL: dark:bg-info-light → dark:bg-info-light'
  },
  {
    pattern: /\bdark:bg-info\b/g,
    replacement: 'dark:bg-info',
    desc: 'PROFESSIONAL: dark:bg-info → dark:bg-info'
  },
  {
    pattern: /\bdark:text-info\b/g,
    replacement: 'dark:text-info',
    desc: 'PROFESSIONAL: dark:text-info → dark:text-info'
  },
  {
    pattern: /\bdark:border-info\/30\b/g,
    replacement: 'dark:border-info/30',
    desc: 'PROFESSIONAL: dark:border-info/30 → dark:border-info/30'
  },

  // 6. BADGE CLASSES
  {
    pattern: /\bbadge-warning\b/g,
    replacement: 'badge-info',
    desc: 'PROFESSIONAL: badge-info → badge-info'
  },
  {
    pattern: /\bbadge-urgent\b/g,
    replacement: 'badge-info',
    desc: 'PROFESSIONAL: badge-info → badge-info'
  },

  // 7. SPECIFIC COMPONENT PATTERNS
  {
    pattern: /'bg-info-light text-info border-info\/30'/g,
    replacement: "'bg-info-light text-info border-info/30'",
    desc: 'PROFESSIONAL: Warning component styling → Info styling'
  },
  {
    pattern: /'bg-info-light text-info border border-info\/50'/g,
    replacement: "'bg-info-light text-info border border-info/50'",
    desc: 'PROFESSIONAL: Warning component styling → Info styling'
  },

  // 8. SEVERITY MAPPINGS
  {
    pattern: /'border-info\/30 bg-info-light'/g,
    replacement: "'border-info/30 bg-info-light'",
    desc: 'PROFESSIONAL: Severity warning → Info'
  },

  // 9. URGENCY MAPPINGS
  {
    pattern: /'text-foreground bg-info-light border-info\/30'/g,
    replacement: "'text-foreground bg-info-light border-info/30'",
    desc: 'PROFESSIONAL: Urgency warning → Info'
  },

  // 10. GRADIENT PATTERNS
  {
    pattern: /\bfrom-warning-light\b/g,
    replacement: 'from-info-light',
    desc: 'PROFESSIONAL: from-info-light → from-info-light'
  },
  {
    pattern: /\bto-warning-light\b/g,
    replacement: 'to-info-light',
    desc: 'PROFESSIONAL: to-info-light → to-info-light'
  },
  {
    pattern: /\bvia-warning-light\b/g,
    replacement: 'via-info-light',
    desc: 'PROFESSIONAL: via-info-light → via-info-light'
  },

  // 11. SPECIFIC ICON COLORS
  {
    pattern: /"h-5 w-5 text-info"/g,
    replacement: '"h-5 w-5 text-info"',
    desc: 'PROFESSIONAL: Icon warning color → Info color'
  },
  {
    pattern: /"h-4 w-4 text-info"/g,
    replacement: '"h-4 w-4 text-info"',
    desc: 'PROFESSIONAL: Icon warning color → Info color'
  },

  // 12. ALERT PATTERNS
  {
    pattern: /'border-info\/30\/30 bg-info'/g,
    replacement: "'border-info/30 bg-info'",
    desc: 'PROFESSIONAL: Alert warning → Info'
  },

  // 13. SPECIFIC COMPONENT FIXES
  {
    pattern: /case 'urgent': return 'bg-info-light text-info border-info\/30';/g,
    replacement: "case 'urgent': return 'bg-info-light text-info border-info/30';",
    desc: 'PROFESSIONAL: Urgent case → Info styling'
  },
  {
    pattern: /default: return 'bg-info-light text-info border-info\/30';/g,
    replacement: "default: return 'bg-info-light text-info border-info/30';",
    desc: 'PROFESSIONAL: Default warning → Info styling'
  },

  // 14. BADGE VARIANT FIXES
  {
    pattern: /"border-transparent bg-info text-foreground hover:bg-info\/80 shadow-sm"/g,
    replacement: '"border-transparent bg-info text-foreground hover:bg-info/80 shadow-sm"',
    desc: 'PROFESSIONAL: Badge warning variant → Info variant'
  },

  // 15. CSS CLASS DEFINITIONS
  {
    pattern: /@apply bg-info-light text-info border border-info\/30 font-semibold shadow-sm;/g,
    replacement: '@apply bg-info-light text-info border border-info/30 font-semibold shadow-sm;',
    desc: 'PROFESSIONAL: CSS warning classes → Info classes'
  }
];

/**
 * Get all TypeScript and JavaScript files in the project
 */
function getAllFiles() {
  const files = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules, .git, dist, and other build directories
        if (!['node_modules', '.git', 'dist', '.next', 'build', '.vite'].includes(item)) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && /\.(tsx?|jsx?|css|scss|sass|less)$/.test(item)) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(PROJECT_ROOT);
  return files;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  let processedContent = content;
  let totalChanges = 0;
  const changeLog = [];

  // Apply all warning color replacements
  for (const fix of WARNING_COLOR_REPLACEMENTS) {
    const matches = processedContent.match(fix.pattern);
    if (matches) {
      processedContent = processedContent.replace(fix.pattern, fix.replacement);
      const changeCount = matches.length;
      totalChanges += changeCount;
      changeLog.push(`✅ ${fix.desc} (${changeCount} fixes)`);
    }
  }

  // Write the file if changes were made
  if (totalChanges > 0) {
    fs.writeFileSync(filePath, processedContent, 'utf8');
    console.log(`🔧 PROFESSIONAL COLORS: ${totalChanges} warning colors replaced in ${relativePath}`);
    changeLog.forEach(change => console.log(`   ${change}`));
    console.log('');
  }

  return totalChanges;
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getAllFiles();
    let totalFiles = 0;
    let totalChanges = 0;

    console.log(`📁 Found ${files.length} files to process\n`);

    for (const file of files) {
      const changes = processFile(file);
      if (changes > 0) {
        totalFiles++;
        totalChanges += changes;
      }
    }

    console.log('🎯 PROFESSIONAL COLOR REPLACEMENT COMPLETE!');
    console.log(`📊 Total files updated: ${totalFiles}`);
    console.log(`📊 Total warning colors replaced: ${totalChanges}`);
    console.log('');
    console.log('✅ ALL warning colors replaced with professional medical colors!');
    console.log('🔍 The design is now uniformly professional and flawless.');

  } catch (error) {
    console.error('❌ Error during warning color replacement:', error);
    process.exit(1);
  }
}

// Run the script
main();
