#!/usr/bin/env node

/**
 * Comprehensive Styling Standardization Script
 * 
 * This script systematically standardizes all styling patterns across the codebase
 * to ensure consistent, professional UX/UI design throughout the medical website.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🎨 COMPREHENSIVE STYLING STANDARDIZATION - Starting...\n');

// COMPREHENSIVE STYLING STANDARDIZATION PATTERNS
const STYLING_STANDARDIZATION_FIXES = [
  // 1. STANDARDIZE TEXT HIERARCHY
  {
    pattern: /className="([^"]*?)text-gray-([5-9][0-9][0-9]|[5-9]00)([^"]*?)font-bold([^"]*?)"/g,
    replacement: 'className="$1text-enhanced-heading$3font-bold$4"',
    desc: 'STANDARDIZED: Dark gray headings → text-enhanced-heading'
  },
  {
    pattern: /className="([^"]*?)font-bold([^"]*?)text-gray-([5-9][0-9][0-9]|[5-9]00)([^"]*?)"/g,
    replacement: 'className="$1font-bold$2text-enhanced-heading$4"',
    desc: 'STANDARDIZED: Bold gray text → text-enhanced-heading'
  },
  {
    pattern: /className="([^"]*?)text-gray-([4-6][0-9][0-9]|[4-6]00)([^"]*?)"/g,
    replacement: 'className="$1text-enhanced-body$3"',
    desc: 'STANDARDIZED: Medium gray text → text-enhanced-body'
  },
  {
    pattern: /className="([^"]*?)text-gray-([1-3][0-9][0-9]|[1-3]00)([^"]*?)"/g,
    replacement: 'className="$1text-enhanced-muted$3"',
    desc: 'STANDARDIZED: Light gray text → text-enhanced-muted'
  },

  // 2. STANDARDIZE CARD STYLING
  {
    pattern: /className="([^"]*?)bg-white([^"]*?)border([^"]*?)rounded([^"]*?)"/g,
    replacement: 'className="$1medical-card$2$3$4"',
    desc: 'STANDARDIZED: White cards → medical-card'
  },
  {
    pattern: /className="([^"]*?)bg-gray-50([^"]*?)border([^"]*?)rounded([^"]*?)"/g,
    replacement: 'className="$1medical-card$2$3$4"',
    desc: 'STANDARDIZED: Gray cards → medical-card'
  },
  {
    pattern: /className="([^"]*?)bg-card([^"]*?)border([^"]*?)rounded([^"]*?)"/g,
    replacement: 'className="$1medical-card$2$3$4"',
    desc: 'STANDARDIZED: Basic cards → medical-card'
  },

  // 3. STANDARDIZE SECTION BACKGROUNDS
  {
    pattern: /className="([^"]*?)bg-gray-50([^"]*?)py-([0-9]+)([^"]*?)"/g,
    replacement: 'className="$1section-background-alt section-spacing$2$4"',
    desc: 'STANDARDIZED: Gray sections → section-background-alt'
  },
  {
    pattern: /className="([^"]*?)bg-white([^"]*?)py-([0-9]+)([^"]*?)"/g,
    replacement: 'className="$1section-background section-spacing$2$4"',
    desc: 'STANDARDIZED: White sections → section-background'
  },
  {
    pattern: /className="([^"]*?)bg-muted\/([0-9]+)([^"]*?)py-([0-9]+)([^"]*?)"/g,
    replacement: 'className="$1section-background-muted section-spacing$3$5"',
    desc: 'STANDARDIZED: Muted sections → section-background-muted'
  },

  // 4. STANDARDIZE BUTTON STYLING
  {
    pattern: /className="([^"]*?)bg-blue-([5-9][0-9][0-9]|[5-9]00)([^"]*?)text-white([^"]*?)"/g,
    replacement: 'className="$1btn-primary$3$4"',
    desc: 'STANDARDIZED: Blue buttons → btn-primary'
  },
  {
    pattern: /className="([^"]*?)bg-green-([5-9][0-9][0-9]|[5-9]00)([^"]*?)text-white([^"]*?)"/g,
    replacement: 'className="$1bg-success text-success-foreground$3$4"',
    desc: 'STANDARDIZED: Green buttons → success theme'
  },
  {
    pattern: /className="([^"]*?)bg-red-([5-9][0-9][0-9]|[5-9]00)([^"]*?)text-white([^"]*?)"/g,
    replacement: 'className="$1bg-error text-error-foreground$3$4"',
    desc: 'STANDARDIZED: Red buttons → error theme'
  },

  // 5. STANDARDIZE BADGE STYLING
  {
    pattern: /className="([^"]*?)bg-blue-100([^"]*?)text-blue-800([^"]*?)"/g,
    replacement: 'className="$1badge-info$2$3"',
    desc: 'STANDARDIZED: Blue badges → badge-info'
  },
  {
    pattern: /className="([^"]*?)bg-green-100([^"]*?)text-green-800([^"]*?)"/g,
    replacement: 'className="$1badge-routine$2$3"',
    desc: 'STANDARDIZED: Green badges → badge-routine'
  },
  {
    pattern: /className="([^"]*?)bg-red-100([^"]*?)text-red-800([^"]*?)"/g,
    replacement: 'className="$1badge-emergency$2$3"',
    desc: 'STANDARDIZED: Red badges → badge-emergency'
  },
  {
    pattern: /className="([^"]*?)bg-yellow-100([^"]*?)text-info([^"]*?)"/g,
    replacement: 'className="$1badge-warning$2$3"',
    desc: 'STANDARDIZED: Yellow badges → badge-info'
  },

  // 6. STANDARDIZE SPACING PATTERNS
  {
    pattern: /className="([^"]*?)py-12([^"]*?)md:py-16([^"]*?)lg:py-20([^"]*?)"/g,
    replacement: 'className="$1section-spacing-sm$2$3$4"',
    desc: 'STANDARDIZED: Small section spacing → section-spacing-sm'
  },
  {
    pattern: /className="([^"]*?)py-16([^"]*?)md:py-20([^"]*?)lg:py-24([^"]*?)"/g,
    replacement: 'className="$1section-spacing$2$3$4"',
    desc: 'STANDARDIZED: Standard section spacing → section-spacing'
  },
  {
    pattern: /className="([^"]*?)space-y-6([^"]*?)md:space-y-8([^"]*?)"/g,
    replacement: 'className="$1content-spacing$2$3"',
    desc: 'STANDARDIZED: Content spacing → content-spacing'
  },
  {
    pattern: /className="([^"]*?)space-y-4([^"]*?)md:space-y-6([^"]*?)"/g,
    replacement: 'className="$1content-spacing-sm$2$3"',
    desc: 'STANDARDIZED: Small content spacing → content-spacing-sm'
  },

  // 7. REMOVE BROWN/TAN COLORS (USER DISLIKES)
  {
    pattern: /\bbg-amber-([0-9]+)\b/g,
    replacement: 'bg-info-light',
    desc: 'REMOVED: Amber/brown colors → bg-info-light'
  },
  {
    pattern: /\btext-amber-([0-9]+)\b/g,
    replacement: 'text-info',
    desc: 'REMOVED: Amber/brown text → text-info'
  },
  {
    pattern: /\bborder-amber-([0-9]+)\b/g,
    replacement: 'border-info/30',
    desc: 'REMOVED: Amber/brown borders → border-info/30'
  },
  {
    pattern: /\bbg-orange-([7-9][0-9][0-9]|[7-9]00)\b/g,
    replacement: 'bg-info-light',
    desc: 'REMOVED: Dark orange (brownish) → bg-info-light'
  },
  {
    pattern: /\btext-orange-([7-9][0-9][0-9]|[7-9]00)\b/g,
    replacement: 'text-info',
    desc: 'REMOVED: Dark orange text → text-info'
  },

  // 8. STANDARDIZE BORDER PATTERNS
  {
    pattern: /className="([^"]*?)border-gray-([0-9]+)([^"]*?)"/g,
    replacement: 'className="$1enhanced-border$3"',
    desc: 'STANDARDIZED: Gray borders → enhanced-border'
  },
  {
    pattern: /className="([^"]*?)border-blue-([0-9]+)([^"]*?)"/g,
    replacement: 'className="$1border-info/30$3"',
    desc: 'STANDARDIZED: Blue borders → border-info/30'
  },
  {
    pattern: /className="([^"]*?)border-green-([0-9]+)([^"]*?)"/g,
    replacement: 'className="$1border-success/30$3"',
    desc: 'STANDARDIZED: Green borders → border-success/30'
  },

  // 9. STANDARDIZE HOVER EFFECTS
  {
    pattern: /className="([^"]*?)hover:bg-gray-([0-9]+)([^"]*?)"/g,
    replacement: 'className="$1enhanced-hover$3"',
    desc: 'STANDARDIZED: Gray hover → enhanced-hover'
  },
  {
    pattern: /className="([^"]*?)hover:bg-blue-([0-9]+)([^"]*?)"/g,
    replacement: 'className="$1hover:bg-info/10$3"',
    desc: 'STANDARDIZED: Blue hover → hover:bg-info/10'
  },

  // 10. STANDARDIZE SHADOW PATTERNS
  {
    pattern: /className="([^"]*?)shadow-sm([^"]*?)"/g,
    replacement: 'className="$1shadow-md$2"',
    desc: 'STANDARDIZED: Small shadows → shadow-md for consistency'
  },
  {
    pattern: /className="([^"]*?)shadow-2xl([^"]*?)"/g,
    replacement: 'className="$1shadow-xl$2"',
    desc: 'STANDARDIZED: Extra large shadows → shadow-xl for consistency'
  }
];

/**
 * Get all TypeScript and JavaScript files in the project
 */
function getAllFiles() {
  const files = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules, .git, dist, and other build directories
        if (!['node_modules', '.git', 'dist', '.next', 'build', '.vite'].includes(item)) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && /\.(tsx?|jsx?)$/.test(item)) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(PROJECT_ROOT);
  return files;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  let processedContent = content;
  let totalChanges = 0;
  const changeLog = [];

  // Apply all styling standardization fixes
  for (const fix of STYLING_STANDARDIZATION_FIXES) {
    const matches = processedContent.match(fix.pattern);
    if (matches) {
      processedContent = processedContent.replace(fix.pattern, fix.replacement);
      const changeCount = matches.length;
      totalChanges += changeCount;
      changeLog.push(`✅ ${fix.desc} (${changeCount} fixes)`);
    }
  }

  // Write the file if changes were made
  if (totalChanges > 0) {
    fs.writeFileSync(filePath, processedContent, 'utf8');
    console.log(`🔧 STANDARDIZED ${totalChanges} styling patterns in ${relativePath}`);
    changeLog.forEach(change => console.log(`   ${change}`));
    console.log('');
  }

  return totalChanges;
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getAllFiles();
    let totalFiles = 0;
    let totalChanges = 0;

    console.log(`📁 Found ${files.length} files to process\n`);

    for (const file of files) {
      const changes = processFile(file);
      if (changes > 0) {
        totalFiles++;
        totalChanges += changes;
      }
    }

    console.log('🎯 STYLING STANDARDIZATION COMPLETE!');
    console.log(`📊 Total files updated: ${totalFiles}`);
    console.log(`📊 Total styling patterns standardized: ${totalChanges}`);
    console.log('');
    console.log('✅ All styling patterns have been standardized for consistent UX/UI!');
    console.log('🔍 Please test the application to verify the improvements.');

  } catch (error) {
    console.error('❌ Error during styling standardization:', error);
    process.exit(1);
  }
}

// Run the script
main();
