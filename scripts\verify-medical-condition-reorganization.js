#!/usr/bin/env node

/**
 * MEDICAL CONDITION REORGANIZATION VERIFICATION
 * 
 * This script verifies that Occipital Neuralgia has been correctly moved to Brain Conditions
 * and Cauda Equina Syndrome has been correctly moved to Spine Conditions.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🔍 MEDICAL CONDITION REORGANIZATION VERIFICATION\n');

/**
 * Verification checks
 */
const verificationChecks = [
  {
    name: 'Occipital Neuralgia Data File',
    file: 'src/data/conditions/occipitalneuralgia.ts',
    checks: [
      { pattern: /badge:\s*["']Brain Conditions Library["']/, description: 'Badge set to Brain Conditions Library' },
      { pattern: /backgroundImage:\s*["'][^"']*brain-conditions[^"']*["']/, description: 'Background image from brain-conditions folder' },
      { pattern: /imageSrc:\s*["'][^"']*brain-conditions[^"']*["']/, description: 'Image source from brain-conditions folder' }
    ]
  },
  {
    name: 'Cauda Equina Syndrome Data File',
    file: 'src/data/conditions/caudaEquinaSyndrome.ts',
    checks: [
      { pattern: /badge:\s*["']Spine Conditions Library["']/, description: 'Badge set to Spine Conditions Library' },
      { pattern: /backgroundImage:\s*["'][^"']*spine-conditions[^"']*["']/, description: 'Background image from spine-conditions folder' }
    ]
  },
  {
    name: 'Spine Conditions Data - Occipital Neuralgia Removed',
    file: 'src/data/spine-conditions/spineConditionsData.tsx',
    checks: [
      { pattern: /occipital-neuralgia/, description: 'Occipital Neuralgia should NOT be found', shouldNotExist: true }
    ]
  },
  {
    name: 'Spine Conditions Data - Cauda Equina Added',
    file: 'src/data/spine-conditions/spineConditionsData.tsx',
    checks: [
      { pattern: /cauda-equina-syndrome/, description: 'Cauda Equina Syndrome should be found' },
      { pattern: /Emergency Symptoms:/, description: 'Emergency symptoms label should be present' },
      { pattern: /Bladder and bowel dysfunction/, description: 'Emergency symptoms should be listed' }
    ]
  },
  {
    name: 'Documentation Files Updated',
    file: 'docs/pages/patient-resources/conditions/occipital-neuralgia.md',
    checks: [
      { pattern: /Brain Conditions Library/, description: 'Documentation shows Brain Conditions Library badge' },
      { pattern: /brain-conditions\/occipital-neuralgia\.jpg/, description: 'Documentation shows brain-conditions image path' }
    ]
  }
];

/**
 * Run verification checks
 */
function runVerification() {
  let totalChecks = 0;
  let passedChecks = 0;
  let failedChecks = 0;

  console.log('📋 Running verification checks...\n');

  for (const verification of verificationChecks) {
    const filePath = path.join(PROJECT_ROOT, verification.file);
    
    console.log(`📁 ${verification.name}`);
    console.log(`   File: ${verification.file}`);
    
    if (!fs.existsSync(filePath)) {
      console.log(`   ❌ File does not exist!`);
      failedChecks += verification.checks.length;
      totalChecks += verification.checks.length;
      continue;
    }

    const content = fs.readFileSync(filePath, 'utf8');

    for (const check of verification.checks) {
      totalChecks++;
      const matches = check.pattern.test(content);
      
      if (check.shouldNotExist) {
        if (!matches) {
          console.log(`   ✅ ${check.description}`);
          passedChecks++;
        } else {
          console.log(`   ❌ ${check.description} (FOUND when it should NOT exist)`);
          failedChecks++;
        }
      } else {
        if (matches) {
          console.log(`   ✅ ${check.description}`);
          passedChecks++;
        } else {
          console.log(`   ❌ ${check.description} (NOT FOUND)`);
          failedChecks++;
        }
      }
    }
    console.log('');
  }

  // Summary
  console.log('📊 VERIFICATION SUMMARY');
  console.log(`Total checks: ${totalChecks}`);
  console.log(`Passed: ${passedChecks}`);
  console.log(`Failed: ${failedChecks}`);
  console.log(`Success rate: ${Math.round((passedChecks / totalChecks) * 100)}%`);

  if (failedChecks === 0) {
    console.log('\n🎉 ALL VERIFICATION CHECKS PASSED!');
    console.log('✅ Occipital Neuralgia successfully moved to Brain Conditions');
    console.log('✅ Cauda Equina Syndrome successfully moved to Spine Conditions');
    console.log('✅ All data files updated correctly');
    console.log('✅ Documentation updated correctly');
    console.log('✅ Medical condition reorganization completed successfully!');
  } else {
    console.log('\n🚨 SOME VERIFICATION CHECKS FAILED!');
    console.log('Please review the failed checks above and make necessary corrections.');
  }

  return failedChecks === 0;
}

/**
 * Additional checks for route consistency
 */
function checkRouteConsistency() {
  console.log('\n🔗 CHECKING ROUTE CONSISTENCY...\n');

  const routeFiles = [
    'src/routes/route-definitions.ts',
    'src/routes/routeConfig.tsx'
  ];

  let routeIssues = 0;

  for (const routeFile of routeFiles) {
    const filePath = path.join(PROJECT_ROOT, routeFile);
    
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      console.log(`📁 ${routeFile}`);
      
      // Check for occipital neuralgia route
      if (content.includes('OCCIPITAL_NEURALGIA') || content.includes('occipital-neuralgia')) {
        console.log('   ✅ Occipital Neuralgia route exists');
      } else {
        console.log('   ❌ Occipital Neuralgia route missing');
        routeIssues++;
      }

      // Check for cauda equina syndrome route
      if (content.includes('CAUDA_EQUINA_SYNDROME') || content.includes('cauda-equina-syndrome')) {
        console.log('   ✅ Cauda Equina Syndrome route exists');
      } else {
        console.log('   ❌ Cauda Equina Syndrome route missing');
        routeIssues++;
      }
      
      console.log('');
    }
  }

  if (routeIssues === 0) {
    console.log('✅ All routes are consistent and properly configured!');
  } else {
    console.log(`❌ Found ${routeIssues} route consistency issues!`);
  }

  return routeIssues === 0;
}

/**
 * Main execution
 */
function main() {
  try {
    const verificationPassed = runVerification();
    const routesPassed = checkRouteConsistency();
    
    if (verificationPassed && routesPassed) {
      console.log('\n🎯 MEDICAL CONDITION REORGANIZATION VERIFICATION COMPLETE!');
      console.log('🎉 All checks passed successfully!');
      console.log('\n📋 SUMMARY OF CHANGES:');
      console.log('• Occipital Neuralgia: Spine Conditions → Brain Conditions');
      console.log('• Cauda Equina Syndrome: Brain Conditions → Spine Conditions');
      console.log('• All data files updated with correct categorization');
      console.log('• All documentation updated');
      console.log('• All routes working correctly');
      console.log('\n✅ The medical conditions are now properly organized!');
      process.exit(0);
    } else {
      console.log('\n❌ VERIFICATION FAILED!');
      console.log('Please review and fix the issues above before proceeding.');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Error during verification:', error);
    process.exit(1);
  }
}

// Run the verification
main();
