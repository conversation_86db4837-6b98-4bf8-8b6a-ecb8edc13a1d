#!/usr/bin/env node

/**
 * COMPREHENSIVE RED BACKGROUND VERIFICATION
 * 
 * This script verifies that NO inappropriate red backgrounds exist
 * on non-emergency educational sections across the entire codebase.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🔍 COMPREHENSIVE RED BACKGROUND VERIFICATION - Starting...\n');

// PATTERNS TO DETECT INAPPROPRIATE RED BACKGROUNDS
const INAPPROPRIATE_RED_PATTERNS = [
  // 1. CRITICAL: Non-emergency sections with red backgrounds
  { pattern: /section.*bg-destructive(?!-foreground)/g, desc: 'CRITICAL: Section with destructive background' },
  { pattern: /div.*bg-destructive(?!-foreground)/g, desc: 'CRITICAL: Div with destructive background' },
  { pattern: /Card.*bg-destructive(?!-foreground)/g, desc: 'CRITICAL: Card with destructive background' },
  
  // 2. CRITICAL: Educational content with red backgrounds
  { pattern: /(How.*Develops|Anatomy|Development|Mechanism|Information).*bg-destructive/gi, desc: 'CRITICAL: Educational content with red background' },
  { pattern: /bg-destructive.*?(How.*Develops|Anatomy|Development|Mechanism|Information)/gi, desc: 'CRITICAL: Red background on educational content' },
  
  // 3. CRITICAL: CSS variable issues
  { pattern: /--muted:\s*0\s+[0-9]+%\s+[0-9]+%/g, desc: 'CRITICAL: Muted variable set to red colour' },
  { pattern: /--destructive.*muted/g, desc: 'CRITICAL: Destructive variable mixed with muted' },
  
  // 4. CRITICAL: Tailwind config issues
  { pattern: /muted:.*destructive/g, desc: 'CRITICAL: Muted config using destructive colors' },
  
  // 5. WARNING: Potential red backgrounds
  { pattern: /bg-red-([0-9]+)/g, desc: 'WARNING: Direct red background classes' },
  { pattern: /bg-pink-([0-9]+)/g, desc: 'WARNING: Direct pink background classes' },
  { pattern: /bg-rose-([0-9]+)/g, desc: 'WARNING: Direct rose background classes' },
  
  // 6. WARNING: Gradient backgrounds with red
  { pattern: /bg-gradient.*destructive/g, desc: 'WARNING: Gradient with destructive colors' },
  { pattern: /from-destructive|to-destructive/g, desc: 'WARNING: Gradient using destructive colors' },
  
  // 7. CRITICAL: Emergency styling on non-emergency content
  { pattern: /(educational|information|anatomy|development|mechanism|overview|facts).*destructive/gi, desc: 'CRITICAL: Non-emergency content with emergency styling' },
  
  // 8. CRITICAL: Specific hydrocephalus issues
  { pattern: /Hydrocephalus.*bg-destructive/gi, desc: 'CRITICAL: Hydrocephalus content with red background' },
  { pattern: /CSF.*bg-destructive/gi, desc: 'CRITICAL: CSF content with red background' },
  
  // 9. WARNING: Button variants that might be inappropriate
  { pattern: /Button.*variant="destructive".*?(learn|read|information|details|anatomy|development)/gi, desc: 'WARNING: Destructive button for non-emergency action' },
  
  // 10. CRITICAL: Alert components with inappropriate styling
  { pattern: /Alert.*bg-destructive.*?(information|educational|development|anatomy)/gi, desc: 'CRITICAL: Non-emergency alert with destructive styling' }
];

/**
 * Get all TypeScript, JavaScript, and CSS files in the project
 */
function getAllFiles() {
  const files = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules, .git, dist, and other build directories
        if (!['node_modules', '.git', 'dist', '.next', 'build', '.vite', 'scripts'].includes(item)) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && /\.(tsx?|jsx?|css|scss|sass|less)$/.test(item)) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(PROJECT_ROOT);
  return files;
}

/**
 * Scan a single file for inappropriate red backgrounds
 */
function scanFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  const foundIssues = [];

  // Check each pattern
  for (const redPattern of INAPPROPRIATE_RED_PATTERNS) {
    const matches = content.match(redPattern.pattern);
    if (matches) {
      foundIssues.push({
        pattern: redPattern.desc,
        count: matches.length,
        examples: [...new Set(matches)].slice(0, 3) // Show up to 3 unique examples
      });
    }
  }

  return foundIssues.length > 0 ? { file: relativePath, issues: foundIssues } : null;
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getAllFiles();
    const results = [];
    let totalFiles = 0;
    let totalIssues = 0;
    let criticalIssues = 0;
    let warningIssues = 0;

    console.log(`📁 Scanning ${files.length} files for inappropriate red backgrounds...\n`);

    for (const file of files) {
      const result = scanFile(file);
      if (result) {
        results.push(result);
        totalFiles++;
        
        const fileIssueCount = result.issues.reduce((sum, issue) => sum + issue.count, 0);
        totalIssues += fileIssueCount;
        
        console.log(`🚨 ${result.file} (${fileIssueCount} issues)`);
        result.issues.forEach(issue => {
          console.log(`   ${issue.pattern}: ${issue.count} instances`);
          if (issue.examples.length > 0) {
            console.log(`   Examples: ${issue.examples.join(', ')}`);
          }
          
          // Categorise issues
          if (issue.pattern.includes('CRITICAL')) criticalIssues += issue.count;
          else if (issue.pattern.includes('WARNING')) warningIssues += issue.count;
        });
        console.log('');
      }
    }

    console.log('📊 RED BACKGROUND VERIFICATION COMPLETE!');
    console.log(`📁 Files with red background issues: ${totalFiles}`);
    console.log(`🚨 Total inappropriate red backgrounds found: ${totalIssues}`);
    console.log(`🚨 Critical issues (inappropriate red backgrounds): ${criticalIssues}`);
    console.log(`⚠️  Warning issues (potentially inappropriate): ${warningIssues}`);
    
    if (totalIssues === 0) {
      console.log('\n✅ PERFECT! NO INAPPROPRIATE RED BACKGROUNDS FOUND!');
      console.log('🎉 All educational content has appropriate professional styling.');
      console.log('🔍 The "How Hydrocephalus Develops" section and similar content is properly styled.');
      console.log('✨ The design is now uniformly professional and medically appropriate.');
    } else {
      console.log('\n🚨 INAPPROPRIATE RED BACKGROUNDS STILL EXIST!');
      if (criticalIssues > 0) {
        console.log('🚨 CRITICAL: Non-emergency content still has red backgrounds!');
        console.log('🔧 These need immediate attention to maintain professional appearance.');
      }
      if (warningIssues > 0) {
        console.log('⚠️  WARNING: Potentially inappropriate red styling should be reviewed.');
      }
    }

  } catch (error) {
    console.error('❌ Error during red background verification:', error);
    process.exit(1);
  }
}

// Run the script
main();
