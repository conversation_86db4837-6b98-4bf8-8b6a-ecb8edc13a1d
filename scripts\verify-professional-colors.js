#!/usr/bin/env node

/**
 * PROFESSIONAL COLOR VERIFICATION
 * 
 * This script verifies that ALL bright pink/red backgrounds have been eliminated
 * and only professional medical-appropriate colors remain in the codebase.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🔍 PROFESSIONAL COLOR VERIFICATION - Starting comprehensive scan...\n');

// PATTERNS TO DETECT REMAINING UNPROFESSIONAL COLORS
const UNPROFESSIONAL_COLOR_PATTERNS = [
  // Bright red/pink backgrounds
  { pattern: /bg-red-([0-9]+)/g, desc: 'UNPROFESSIONAL: bg-red-* (bright red background)' },
  { pattern: /bg-pink-([0-9]+)/g, desc: 'UNPROFESSIONAL: bg-pink-* (bright pink background)' },
  { pattern: /bg-rose-([0-9]+)/g, desc: 'UNPROFESSIONAL: bg-rose-* (bright rose background)' },
  
  // Bright red/pink text
  { pattern: /text-red-([0-9]+)/g, desc: 'UNPROFESSIONAL: text-red-* (bright red text)' },
  { pattern: /text-pink-([0-9]+)/g, desc: 'UNPROFESSIONAL: text-pink-* (bright pink text)' },
  { pattern: /text-rose-([0-9]+)/g, desc: 'UNPROFESSIONAL: text-rose-* (bright rose text)' },
  
  // Bright red/pink borders
  { pattern: /border-red-([0-9]+)/g, desc: 'UNPROFESSIONAL: border-red-* (bright red border)' },
  { pattern: /border-pink-([0-9]+)/g, desc: 'UNPROFESSIONAL: border-pink-* (bright pink border)' },
  { pattern: /border-rose-([0-9]+)/g, desc: 'UNPROFESSIONAL: border-rose-* (bright rose border)' },
  
  // Remaining bright error colors
  { pattern: /bg-error-light\/30/g, desc: 'WARNING: bg-error-light/30 (should be bg-muted/50)' },
  { pattern: /bg-error-light\/20/g, desc: 'WARNING: bg-error-light/20 (should be bg-muted/30)' },
  { pattern: /border-error\/50/g, desc: 'WARNING: border-error/50 (should be border-border)' },
  { pattern: /border-error\/30/g, desc: 'WARNING: border-error/30 (should be border-border/70)' },
  
  // Bright destructive gradients
  { pattern: /bg-gradient-to-r from-destructive/g, desc: 'UNPROFESSIONAL: destructive gradient (bright colors)' },
  { pattern: /bg-gradient-to-r.*destructive/g, desc: 'UNPROFESSIONAL: destructive gradient (bright colors)' },
  
  // Other bright colors that might be unprofessional
  { pattern: /bg-yellow-([0-9]+)/g, desc: 'WARNING: bg-yellow-* (might be unprofessional)' },
  { pattern: /bg-orange-([0-9]+)/g, desc: 'WARNING: bg-orange-* (might be unprofessional)' },
  { pattern: /bg-amber-([0-9]+)/g, desc: 'WARNING: bg-amber-* (might be unprofessional)' },
  
  // Bright text colors
  { pattern: /text-yellow-([0-9]+)/g, desc: 'WARNING: text-yellow-* (might be unprofessional)' },
  { pattern: /text-orange-([0-9]+)/g, desc: 'WARNING: text-orange-* (might be unprofessional)' },
  { pattern: /text-amber-([0-9]+)/g, desc: 'WARNING: text-amber-* (might be unprofessional)' },
  
  // Emergency/destructive styling that might still be bright
  { pattern: /bg-destructive(?!-foreground)/g, desc: 'CHECK: bg-destructive (verify professional appearance)' },
  { pattern: /text-destructive(?!-foreground)/g, desc: 'CHECK: text-destructive (verify professional appearance)' },
  
  // High saturation HSL colors in CSS
  { pattern: /hsl\([^)]*[8-9][0-9]%[^)]*\)/g, desc: 'WARNING: High saturation HSL color (might be too bright)' },
  
  // RGB colors that might be bright red/pink
  { pattern: /rgb\(25[0-5]|2[4-5][0-9]|rgb\(1[0-9][0-9], ?[0-9]{1,2}, ?[0-9]{1,2}\)/g, desc: 'CHECK: RGB color (verify not bright red/pink)' },
  
  // Hex colors that might be bright red/pink
  { pattern: /#[fF][0-9a-fA-F][0-9a-fA-F][0-9a-fA-F][0-9a-fA-F][0-9a-fA-F]/g, desc: 'CHECK: Bright hex color (verify professional)' },
  { pattern: /#[eE][0-9a-fA-F][0-9a-fA-F][0-9a-fA-F][0-9a-fA-F][0-9a-fA-F]/g, desc: 'CHECK: Bright hex color (verify professional)' }
];

/**
 * Get all TypeScript, JavaScript, and CSS files in the project
 */
function getAllFiles() {
  const files = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules, .git, dist, and other build directories
        if (!['node_modules', '.git', 'dist', '.next', 'build', '.vite', 'scripts'].includes(item)) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && /\.(tsx?|jsx?|css|scss|sass|less)$/.test(item)) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(PROJECT_ROOT);
  return files;
}

/**
 * Scan a single file for unprofessional colors
 */
function scanFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  const foundIssues = [];

  // Check each pattern
  for (const colorPattern of UNPROFESSIONAL_COLOR_PATTERNS) {
    const matches = content.match(colorPattern.pattern);
    if (matches) {
      foundIssues.push({
        pattern: colorPattern.desc,
        count: matches.length,
        examples: [...new Set(matches)].slice(0, 3) // Show up to 3 unique examples
      });
    }
  }

  return foundIssues.length > 0 ? { file: relativePath, issues: foundIssues } : null;
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getAllFiles();
    const results = [];
    let totalFiles = 0;
    let totalIssues = 0;
    let criticalIssues = 0;
    let warningIssues = 0;
    let checkIssues = 0;

    console.log(`📁 Scanning ${files.length} files for unprofessional colors...\n`);

    for (const file of files) {
      const result = scanFile(file);
      if (result) {
        results.push(result);
        totalFiles++;
        
        const fileIssueCount = result.issues.reduce((sum, issue) => sum + issue.count, 0);
        totalIssues += fileIssueCount;
        
        console.log(`⚠️  ${result.file} (${fileIssueCount} issues)`);
        result.issues.forEach(issue => {
          console.log(`   ${issue.pattern}: ${issue.count} instances`);
          if (issue.examples.length > 0) {
            console.log(`   Examples: ${issue.examples.join(', ')}`);
          }
          
          // Categorize issues
          if (issue.pattern.includes('UNPROFESSIONAL')) criticalIssues += issue.count;
          else if (issue.pattern.includes('WARNING')) warningIssues += issue.count;
          else if (issue.pattern.includes('CHECK')) checkIssues += issue.count;
        });
        console.log('');
      }
    }

    console.log('📊 PROFESSIONAL COLOR VERIFICATION COMPLETE!');
    console.log(`📁 Files with color issues: ${totalFiles}`);
    console.log(`⚠️  Total color issues found: ${totalIssues}`);
    console.log(`🚨 Critical issues (unprofessional colors): ${criticalIssues}`);
    console.log(`⚠️  Warning issues (potentially unprofessional): ${warningIssues}`);
    console.log(`🔍 Check issues (need manual verification): ${checkIssues}`);
    
    if (totalIssues === 0) {
      console.log('\n✅ PERFECT! NO UNPROFESSIONAL COLORS FOUND!');
      console.log('🎉 All colors are professional and medical-appropriate.');
      console.log('🔍 The design is uniformly professional and flawless.');
    } else {
      console.log('\n⚠️  COLOR ISSUES STILL EXIST!');
      if (criticalIssues > 0) {
        console.log('🚨 CRITICAL: Unprofessional bright colors need immediate attention!');
      }
      if (warningIssues > 0) {
        console.log('⚠️  WARNING: Potentially unprofessional colors should be reviewed.');
      }
      if (checkIssues > 0) {
        console.log('🔍 CHECK: Colors need manual verification for professional appearance.');
      }
    }

  } catch (error) {
    console.error('❌ Error during professional color verification:', error);
    process.exit(1);
  }
}

// Run the script
main();
