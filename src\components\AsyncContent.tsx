
// TODO: Add explicit return types to functions
// Example: const myFunction = (): ReturnType => {
import { RefreshCw } from 'lucide-react';
import React from 'react';


import EmptyState, { LoadingErrorState, NetworkErrorState } from '@/components/EmptyState';
import Loading, { CardSkeleton, GridSkeleton, ListSkeleton } from '@/components/Loading';
import { Button } from '@/components/ui/button';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { AsyncDataState } from '@/hooks/useAsyncData';
import { cn } from '@/lib/utils';

export type LoadingVariant = 'spinner' | 'skeleton' | 'card' | 'grid' | 'list';

export interface AsyncContentProps {
  state: AsyncDataState;
  error?: Error | null;
  isEmpty?: boolean;
  onRetry?: () => void;
  onRefresh?: () => void;
  loadingVariant?: LoadingVariant;
  loadingText?: string;
  emptyTitle?: string;
  emptyDescription?: string;
  errorTitle?: string;
  errorDescription?: string;
  showRetryButton?: boolean;
  showRefreshButton?: boolean;
  className?: string;
  children: React.ReactNode;

  // Loading skeleton options
  skeletonItems?: number;
  skeletonColumns?: number;
  showSkeletonImage?: boolean;
  showSkeletonAvatar?: boolean;

  // Empty state options
  emptyStateType?: 'no-data' | 'no-results' | 'no-content' | 'no-reviews' | 'no-testimonials' | 'no-procedures' | 'no-conditions';
  emptyStateSize?: 'sm' | 'md' | 'lg';
  emptyStateActions?: Array<{
    label: string;
    onClick: () => void;
    variant?: 'default' | 'outline' | 'secondary';
  }>;
}

/**
 * Comprehensive async content wrapper with loading, error, and empty states
 */
export const AsyncContent: React.FC<AsyncContentProps> = ({
  state,
  error,
  isEmpty = false,
  onRetry,
  onRefresh,
  loadingVariant = 'spinner',
  loadingText = 'Loading...',
  emptyTitle,
  emptyDescription,
  errorTitle,
  errorDescription,
  showRetryButton: _showRetryButton = true,
  showRefreshButton = false,
  className,
  children,
  skeletonItems = 6,
  skeletonColumns = 3,
  showSkeletonImage = true,
  showSkeletonAvatar = false,
  emptyStateType = 'no-data',
  emptyStateSize = 'md',
  emptyStateActions = []
}) => {
  const deviceInfo = useDeviceDetection();

  // Loading state
  if (state === 'loading') {
    const loadingContent = (() => {
      switch (loadingVariant) {
        case 'skeleton':
          return (
            <div className={cn('space-y-4', className)}>
              <div className="space-y-3">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="h-4 bg-muted rounded animate-pulse" />
                ))}
              </div>
            </div>
          );

        case 'card':
          return (
            <div className={className}>
              <CardSkeleton
                showImage={showSkeletonImage}
                className="max-w-sm mx-auto"
              />
            </div>
          );

        case 'grid':
          return (
            <div className={className}>
              <GridSkeleton
                items={skeletonItems}
                columns={skeletonColumns}
              />
            </div>
          );

        case 'list':
          return (
            <div className={className}>
              <ListSkeleton
                items={skeletonItems}
                showAvatar={showSkeletonAvatar}
              />
            </div>
          );

        default:
          return (
            <div className={cn('flex justify-centre py-8', className)}>
              <Loading
                text={loadingText}
                size={deviceInfo.isMobile ? 'sm' : 'md'}
              />
            </div>
          );
      }
    })();

    return loadingContent;
  }

  // Error state
  if (state === 'error' && error) {
    const errorMessage = error.message || '';
    const isNetworkError = errorMessage.toLowerCase().includes('network') ||
      errorMessage.toLowerCase().includes('fetch') ||
      errorMessage.toLowerCase().includes('connection');

    if (isNetworkError) {
      return (
        <div className={className}>
          <NetworkErrorState
            title={errorTitle || 'Connection Error'}
            description={errorDescription || error.message}
            onRetry={onRetry}
            size={emptyStateSize}
            showCard={true}
          />
        </div>
      );
    }

    return (
      <div className={className}>
        <LoadingErrorState
          title={errorTitle || 'Loading Error'}
          description={errorDescription || error.message}
          onRetry={onRetry}
          size={emptyStateSize}
          showCard={true}
        />
      </div>
    );
  }

  // Empty state
  if (state === 'empty' || isEmpty) {
    const actions = [...emptyStateActions];

    if (onRefresh && showRefreshButton) {
      actions.push({
        label: 'Refresh',
        onClick: onRefresh,
        variant: 'outline' as const
      });
    }

    return (
      <div className={className}>
        <EmptyState
          type={emptyStateType}
          title={emptyTitle}
          description={emptyDescription}
          size={emptyStateSize}
          showCard={true}
          actions={actions.map(action => ({
            label: action.label,
            onClick: action.onClick,
            variant: action.variant,
            icon: action.label === 'Refresh' ? RefreshCw : undefined
          }))}
        />
      </div>
    );
  }

  // Success state - render children
  if (state === 'success') {
    return (
      <div className={className}>
        {showRefreshButton && onRefresh && (
          <div className="flex justify-end mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              className="gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        )}
        {children}
      </div>
    );
  }

  // Idle state - render children without refresh button
  return <div className={className}>{children}</div>;
};

/**
 * Specialised async content for medical data
 */
export const AsyncMedicalContent: React.FC<Omit<AsyncContentProps, 'emptyStateType'> & {
  dataType?: 'procedures' | 'conditions' | 'testimonials' | 'reviews';
}> = ({ dataType = 'procedures', loadingVariant = 'grid', ...props }) => {
  const getEmptyStateType = () => {
    switch (dataType) {
      case 'procedures': return 'no-procedures' as const;
      case 'conditions': return 'no-conditions' as const;
      case 'testimonials': return 'no-testimonials' as const;
      case 'reviews': return 'no-reviews' as const;
      default: return 'no-data' as const;
    }
  };

  return (
    <AsyncContent
      {...props}
      emptyStateType={getEmptyStateType()}
      loadingVariant={loadingVariant}
    />
  );
};

/**
 * Async content for search results
 */
export const AsyncSearchContent: React.FC<AsyncContentProps & {
  searchQuery?: string;
  onClearSearch?: () => void;
}> = ({ searchQuery, onClearSearch, ...props }) => {
  const emptyActions = [...(props.emptyStateActions || [])];

  if (onClearSearch && searchQuery) {
    emptyActions.unshift({
      label: 'Clear Search',
      onClick: onClearSearch,
      variant: 'outline' as const
    });
  }

  return (
    <AsyncContent
      {...props}
      emptyStateType="no-results"
      emptyTitle={searchQuery ? `No results for "${searchQuery}"` : props.emptyTitle}
      emptyStateActions={emptyActions}
    />
  );
};

/**
 * Async content for paginated data
 */
export const AsyncPaginatedContent: React.FC<AsyncContentProps & {
  hasMore?: boolean;
  onLoadMore?: () => void;
  isLoadingMore?: boolean;
}> = ({ hasMore, onLoadMore, isLoadingMore, children, ...props }) => {
  return (
    <AsyncContent {...props}>
      {children}
      {hasMore && onLoadMore && (
        <div className="flex justify-centre mt-8">
          <Button
            variant="outline"
            onClick={onLoadMore}
            disabled={isLoadingMore}
            className="gap-2"
          >
            {isLoadingMore ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              'Load More'
            )}
          </Button>
        </div>
      )}
    </AsyncContent>
  );
};

/**
 * Simple loading wrapper for quick implementations
 */
export const LoadingWrapper: React.FC<{
  isLoading: boolean;
  error?: Error | null;
  isEmpty?: boolean;
  onRetry?: () => void;
  children: React.ReactNode;
  className?: string;
}> = ({ isLoading, error, isEmpty, onRetry, children, className }) => {
  const state: AsyncDataState = isLoading ? 'loading' :
    error ? 'error' :
      isEmpty ? 'empty' : 'success';

  return (
    <AsyncContent
      state={state}
      error={error}
      isEmpty={isEmpty}
      onRetry={onRetry}
      className={className}
    >
      {children}
    </AsyncContent>
  );
};

AsyncContent.displayName = 'AsyncContent';
AsyncMedicalContent.displayName = 'AsyncMedicalContent';
AsyncSearchContent.displayName = 'AsyncSearchContent';
AsyncPaginatedContent.displayName = 'AsyncPaginatedContent';
LoadingWrapper.displayName = 'LoadingWrapper';

export default AsyncContent;
