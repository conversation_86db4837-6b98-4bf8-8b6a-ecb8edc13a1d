import { Loader2, <PERSON>, <PERSON> } from 'lucide-react';
import React from 'react';

import { cn } from '@/lib/utils';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'spinner' | 'pulse' | 'medical' | 'skeleton';
  text?: string;
  className?: string;
  fullScreen?: boolean;
}

/**
 * Loading Component with multiple variants and accessibility features
 */
const Loading: React.FC<LoadingProps> = ({
  size = 'md',
  variant = 'spinner',
  text = 'Loading...',
  className,
  fullScreen = false
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  };

  const renderSpinner = () => (
    <div className="flex flex-col items-center justify-center gap-3">
      <Loader2 
        className={cn(
          'animate-spin text-primary',
          sizeClasses[size]
        )}
        aria-hidden="true"
      />
      {text && (
        <p className={cn(
          'text-muted-foreground font-medium',
          textSizeClasses[size]
        )}>
          {text}
        </p>
      )}
    </div>
  );

  const renderPulse = () => (
    <div className="flex flex-col items-center justify-center gap-3">
      <div 
        className={cn(
          'bg-primary rounded-full animate-pulse',
          sizeClasses[size]
        )}
        aria-hidden="true"
      />
      {text && (
        <p className={cn(
          'text-muted-foreground font-medium animate-pulse',
          textSizeClasses[size]
        )}>
          {text}
        </p>
      )}
    </div>
  );

  const renderMedical = () => (
    <div className="flex flex-col items-center justify-center gap-3">
      <div className="relative">
        <Brain 
          className={cn(
            'text-primary animate-pulse',
            sizeClasses[size]
          )}
          aria-hidden="true"
        />
        <Heart 
          className={cn(
            'absolute -top-1 -right-1 text-foreground animate-bounce',
            size === 'sm' ? 'h-2 w-2' :
            size === 'md' ? 'h-3 w-3' :
            size === 'lg' ? 'h-4 w-4' : 'h-6 w-6'
          )}
          aria-hidden="true"
        />
      </div>
      {text && (
        <p className={cn(
          'text-muted-foreground font-medium',
          textSizeClasses[size]
        )}>
          {text}
        </p>
      )}
    </div>
  );

  const renderSkeleton = () => (
    <div className="space-y-3 w-full max-w-sm">
      <div className="h-4 bg-muted rounded animate-pulse" />
      <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
      <div className="h-4 bg-muted rounded animate-pulse w-1/2" />
    </div>
  );

  const renderContent = () => {
    switch (variant) {
      case 'pulse':
        return renderPulse();
      case 'medical':
        return renderMedical();
      case 'skeleton':
        return renderSkeleton();
      default:
        return renderSpinner();
    }
  };

  const content = (
    <div 
      className={cn(
        'flex items-center justify-center',
        fullScreen ? 'min-h-screen' : 'p-8',
        className
      )}
      role="status"
      aria-live="polite"
      aria-label={text}
    >
      {renderContent()}
      <span className="sr-only">{text}</span>
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
        {content}
      </div>
    );
  }

  return content;
}
/**
 * Inline Loading Spinner for buttons and small spaces
 */
export function InlineLoading({
  size = 'sm',
  className
}: {
  size?: 'sm' | 'md';
  className?: string;
}) {
  return (
    <Loader2
      className={cn(
        'animate-spin',
        size === 'sm' ? 'h-4 w-4' : 'h-5 w-5',
        className
      )}
      aria-hidden="true"
    />
  );
}
/**
 * Page Loading Component with medical theme
 */
export function PageLoading({ text = 'Loading page...' }: { text?: string }) {
  return (
    <Loading
      variant="medical"
      size="lg"
      text={text}
      fullScreen
    />
  );
}
/**
 * Content Loading Skeleton
 */
export function ContentSkeleton({
  lines = 3,
  className
}: {
  lines?: number;
  className?: string;
}) {
  return (
    <div className={cn('space-y-3', className)}>
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={cn(
            'h-4 bg-muted rounded animate-pulse',
            index === lines - 1 && 'w-3/4',
            index === lines - 2 && lines > 2 && 'w-5/6'
          )}
        />
      ))}
    </div>
  );
}

/**
 * Card Loading Skeleton
 */
export function CardSkeleton({
  showImage = true,
  showTitle = true,
  showDescription = true,
  showActions = true,
  className
}: {
  showImage?: boolean;
  showTitle?: boolean;
  showDescription?: boolean;
  showActions?: boolean;
  className?: string;
}) {
  return (
    <div className={cn('border rounded-lg overflow-hidden', className)}>
      {showImage && (
        <div className="h-48 bg-muted animate-pulse" />
      )}
      <div className="p-6 space-y-3">
        {showTitle && (
          <div className="h-6 bg-muted rounded animate-pulse w-3/4" />
        )}
        {showDescription && (
          <div className="space-y-2">
            <div className="h-4 bg-muted rounded animate-pulse" />
            <div className="h-4 bg-muted rounded animate-pulse w-5/6" />
          </div>
        )}
        {showActions && (
          <div className="flex gap-2 pt-2">
            <div className="h-9 bg-muted rounded animate-pulse w-24" />
            <div className="h-9 bg-muted rounded animate-pulse w-20" />
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Grid Loading Skeleton
 */
export function GridSkeleton({
  items = 6,
  columns = 3,
  itemComponent: ItemComponent = CardSkeleton,
  className
}: {
  items?: number;
  columns?: number;
  itemComponent?: React.ComponentType<{ className?: string }>;
  className?: string;
}) {
  return (
    <div className={cn(
      'grid gap-6',
      columns === 1 && 'grid-cols-1',
      columns === 2 && 'grid-cols-1 md:grid-cols-2',
      columns === 3 && 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      columns === 4 && 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
      className
    )}>
      {Array.from({ length: items }).map((_, index) => (
        <ItemComponent key={index} />
      ))}
    </div>
  );
}

/**
 * List Loading Skeleton
 */
export function ListSkeleton({
  items = 5,
  showAvatar = false,
  className
}: {
  items?: number;
  showAvatar?: boolean;
  className?: string;
}) {
  return (
    <div className={cn('space-y-4', className)}>
      {Array.from({ length: items }).map((_, index) => (
        <div key={index} className="flex items-start gap-4">
          {showAvatar && (
            <div className="w-10 h-10 bg-muted rounded-full animate-pulse flex-shrink-0" />
          )}
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-muted rounded animate-pulse w-1/4" />
            <div className="h-4 bg-muted rounded animate-pulse" />
            <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
          </div>
        </div>
      ))}
    </div>
  );
}

/**
 * Image Loading Skeleton
 */
export function ImageSkeleton({
  aspectRatio = 'aspect-video',
  className
}: {
  aspectRatio?: string;
  className?: string;
}) {
  return (
    <div
      className={cn(
        'bg-muted rounded animate-pulse',
        aspectRatio,
        className
      )}
      aria-hidden="true"
    />
  );
}

Loading.displayName = 'Loading';

export default Loading;
