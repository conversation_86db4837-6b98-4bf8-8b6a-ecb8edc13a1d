import { ChevronLeft, ChevronR<PERSON>, Star } from 'lucide-react';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { AsyncMedicalContent } from '@/components/AsyncContent';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAsyncData } from '@/hooks/useAsyncData';
import { validateString } from '@/lib/content-validation';
import { cn } from '@/lib/utils';
import en from '@/locales/en';

interface Testimonial {
  id: number;
  name: string;
  location: string;
  avatar: string;
  content: string;
  rating: number;
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: "<PERSON>",
    location: "Melbourne, Australia",
    avatar: "/images/testimonials/patient-1.jpg",
    content: "Dr. <PERSON>'s expertise in minimally invasive spine surgery changed my life. After years of chronic back pain and failed treatments, his precise diagnosis and surgical approach led to a remarkable recovery. I'm now pain-free and back to my active lifestyle.",
    rating: 5
  },
  {
    id: 2,
    name: "<PERSON>",
    location: "Sydney, Australia",
    avatar: "/images/testimonials/patient-2.jpg",
    content: "The cervical disc replacement procedure Dr<PERSON> performed was truly life-changing. His attention to detail, clear explanations, and exceptional surgical skills resulted in immediate relief from my neck pain and arm numbness. The recovery was much faster than I expected.",
    rating: 5
  },
  {
    id: 3,
    name: "Emily <PERSON>",
    location: "Brisbane, Australia",
    avatar: "/images/testimonials/patient-3.jpg",
    content: "After being told by multiple surgeons that my complex spinal condition was inoperable, Dr. <PERSON>ashkevich offered hope with his innovative approach. His use of advanced imaging and robotic technology made a significant difference. Six months post-surgery, I'm enjoying activities I thought I'd never do again.",
    rating: 5
  },
];

const TestimonialsSection: React.FC = React.memo(() => {
  const { t } = useLanguage();

  // Safe fallback for translations
  const safeT = t || en;
  const finalT = safeT || {
    // Add minimal fallback structure based on component needs
    navigation: { home: "Home", expertise: "Expertise", contact: "Contact", about: "About", locations: "Locations", patientResources: "Patient Resources", bookAppointment: "Book Appointment", language: "Language", menu: "Menu", close: "Close", skipToContent: "Skip to Content" },
    hero: { title: "Welcome", subtitle: "Professional Care", primaryCTA: "Book Now", secondaryCTA: "Learn More", scrollDown: "Scroll Down" },
    footer: { description: "Professional medical practice", quickLinks: "Quick Links", contactInfo: "Contact Info", followUs: "Follow Us", copyright: "All rights reserved", privacyPolicy: "Privacy Policy", termsOfService: "Terms of Service", accessibility: "Accessibility", sitemap: "Sitemap" },
    testimonials: { title: "Testimonials", description: "Patient reviews", readMore: "Read More", previous: "Previous", next: "Next" }
  };

  // Simulate async data loading for testimonials
  const loadTestimonials = async (signal?: AbortSignal) => {
    // Simulate network delay with cancellation support
    await new Promise<void>((resolve, reject) => {
      const timeoutId = setTimeout(resolve, 800);

      if (signal) {
        signal.addEventListener('abort', () => {
          clearTimeout(timeoutId);
          reject(new Error('Aborted'));
        });
      }
    });

    // In a real app, this would be an API call
    return testimonials;
  };

  // Use async data hook
  const {
    data: loadedTestimonials,
    state,
    error,
    retry
  } = useAsyncData(loadTestimonials, {
    validateData: (data) => Array.isArray(data) && data.length > 0,
    transformData: (data: unknown) => {
      const testimonialArray = data as typeof testimonials;
      return testimonialArray.filter(testimonial =>
        typeof testimonial.id === 'number' &&
        typeof testimonial.name === 'string' &&
        typeof testimonial.content === 'string' &&
        typeof testimonial.rating === 'number'
      );
    }
  });

  // Validate translation strings
  const title = validateString(finalT.testimonials?.title, {
    fallback: "Patient Testimonials",
    allowEmpty: false
  });

  const description = validateString(finalT.testimonials?.description, {
    fallback: "Read what our patients have to say about their experience with Dr. Aliashkevich.",
    allowEmpty: false
  });

  const [activeIndex, setActiveIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const nextTestimonial = useCallback(() => {
    if (isAnimating || !loadedTestimonials) return;

    // Clear any existing animation timeout
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
    }

    setIsAnimating(true);
    setActiveIndex((prev: number) => (prev + 1) % loadedTestimonials.length);

    animationTimeoutRef.current = setTimeout(() => {
      setIsAnimating(false);
      animationTimeoutRef.current = null;
    }, 500);
  }, [isAnimating, loadedTestimonials]);

  const prevTestimonial = useCallback(() => {
    if (isAnimating || !loadedTestimonials) return;

    // Clear any existing animation timeout
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
    }

    setIsAnimating(true);
    setActiveIndex((prev: number) => (prev - 1 + loadedTestimonials.length) % loadedTestimonials.length);

    animationTimeoutRef.current = setTimeout(() => {
      setIsAnimating(false);
      animationTimeoutRef.current = null;
    }, 500);
  }, [isAnimating, loadedTestimonials]);

  useEffect(() => {
    const interval = setInterval(nextTestimonial, 8000);
    return () => clearInterval(interval);
  }, [nextTestimonial]);

  // Cleanup animation timeout on unmount
  useEffect(() => {
    return () => {
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
    };
  }, []);

  return (
    <section className="section bg-muted py-20">
      <div className="container">
        <div className="text-center max-w-3xl mx-auto mb-12 animate-fade-in">
          <h2 className="text-enhanced-heading text-3xl md:text-enhanced-heading text-4xl font-bold mb-4">
            {title.data}
          </h2>
          <p className="text-muted-foreground">
            {description.data}
          </p>
        </div>

        <AsyncMedicalContent
          state={state}
          error={error}
          onRetry={retry}
          dataType="testimonials"
          loadingVariant="card"
          emptyTitle="Testimonials Coming Soon"
          emptyDescription="Patient testimonials will be available here once we receive them. We're committed to providing excellent care and building trust with our patients."
          emptyStateActions={[
            {
              label: "Schedule Consultation",
              onClick: () => window.location.href = '/appointments',
              variant: 'default'
            }
          ]}
          className="relative max-w-4xl mx-auto"
        >
          <div
            className="relative h-[400px] md:h-[300px]"
            role="region"
            aria-label="Patient testimonials carousel"
            aria-live="polite"
          >
            {loadedTestimonials?.map((testimonial, index) => (
              <div
                key={testimonial.id}
                className={cn(
                  "absolute inset-0 glass-card p-8 md:p-10 transition-all duration-500",
                  activeIndex === index
                    ? "opacity-100 translate-x-0 z-10"
                    : index < activeIndex
                      ? "opacity-0 -translate-x-full z-0"
                      : "opacity-0 translate-x-full z-0"
                )}
              >
                <div className="flex flex-col md:flex-row gap-6 h-full">
                  <div className="flex flex-col items-center md:items-start">
                    <div className="rounded-full overflow-hidden w-20 h-20 mb-4 border-2 border-primary">
                      <img
                        src={testimonial.avatar}
                        alt={testimonial.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex mb-2" role="img" aria-label={`${testimonial.rating} out of 5 stars`}>
                      {[...Array(5)].map((_, i: number) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${i < testimonial.rating ? "fill-primary text-primary" : "text-muted-foreground"}`}
                          aria-hidden="true"
                        />
                      ))}
                    </div>
                    <h4 className="text-lg font-semibold text-center md:text-left">{testimonial.name}</h4>
                    <p className="text-sm text-muted-foreground text-center md:text-left">{testimonial.location}</p>
                  </div>

                  <div className="flex-1 flex items-center">
                    <blockquote className="italic text-muted-foreground">
                      "{testimonial.content}"
                    </blockquote>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="flex justify-between mt-8">
            <button
              onClick={prevTestimonial}
              className="p-2 rounded-full medical-card hover:bg-muted border border-border transition-colors"
              disabled={isAnimating}
            >
              <ChevronLeft className="h-5 w-5" />
              <span className="sr-only">Previous testimonial</span>
            </button>

            <div className="flex space-x-2">
              {loadedTestimonials?.map((_, index: number) => (
                <button
                  key={index}
                  onClick={() => {
                    if (isAnimating) return;
                    setIsAnimating(true);
                    setActiveIndex(index);
                    setTimeout(() => setIsAnimating(false), 500);
                  }}
                  className={`w-3 h-3 rounded-full transition-all ${
                    activeIndex === index
                      ? "bg-primary w-6"
                      : "bg-muted-foreground/30 hover:bg-muted-foreground/50"
                  }`}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>

            <button
              onClick={nextTestimonial}
              className="p-2 rounded-full medical-card hover:bg-muted border border-border transition-colors"
              disabled={isAnimating}
            >
              <ChevronRight className="h-5 w-5" />
              <span className="sr-only">Next testimonial</span>
            </button>
          </div>
        </AsyncMedicalContent>
      </div>
    </section>
  );
});

TestimonialsSection.displayName = 'TestimonialsSection';

export default TestimonialsSection;
