import { <PERSON>, Sun } from 'lucide-react';
import React, { useState, useEffect, useCallback } from 'react';

import ErrorBoundary from '@/components/ErrorBoundary';
import { Button } from '@/components/ui/button';

const ThemeToggle: React.FC = () => {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    try {
      const storedTheme = localStorage.getItem("theme");
      const isDarkMode = storedTheme === "dark" ||
        (!storedTheme && window.matchMedia("(prefers-colour-scheme: dark)").matches);

      setIsDark(isDarkMode);

      if (isDarkMode) {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }
    } catch {
      // Fallback to system preference if localStorage is not available
      const isDarkMode = window.matchMedia("(prefers-colour-scheme: dark)").matches;
      setIsDark(isDarkMode);

      if (isDarkMode) {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }
    }
  }, []);

  const toggleTheme = useCallback(() => {
    setIsDark(!isDark);

    if (isDark) {
      document.documentElement.classList.remove("dark");
      try {
        localStorage.setItem("theme", "light");
      } catch {
        // Silently fail if localStorage is not available
      }
    } else {
      document.documentElement.classList.add("dark");
      try {
        localStorage.setItem("theme", "dark");
      } catch {
        // Silently fail if localStorage is not available
      }
    }
  }, [isDark]);

  return (
    <ErrorBoundary>
      <Button
        variant="ghost"
        size="icon"
        onClick={toggleTheme}
        className="rounded-full w-10 h-10 transition-all duration-300 hover:bg-muted"
        aria-pressed={isDark}
        aria-label={isDark ? "Switch to light theme" : "Switch to dark theme"}
        title={isDark ? "Switch to light theme" : "Switch to dark theme"}
        onKeyDown={useCallback((e: React.KeyboardEvent<HTMLButtonElement>) => {
          // Ensure Space and Enter keys trigger the toggle
          if (e.key === ' ' || e.key === 'Enter') {
            e.preventDefault();
            toggleTheme();
          }
        }, [toggleTheme])}
      >
      {isDark ? (
        <Sun className="h-5 w-5 transition-transform duration-500 rotate-0" aria-hidden="true" />
      ) : (
        <Moon className="h-5 w-5 transition-transform duration-500 rotate-0" aria-hidden="true" />
      )}
      <span className="sr-only">{isDark ? "Switch to light theme" : "Switch to dark theme"}</span>
    </Button>
    </ErrorBoundary>
  );
};

ThemeToggle.displayName = 'ThemeToggle';

export default ThemeToggle;
