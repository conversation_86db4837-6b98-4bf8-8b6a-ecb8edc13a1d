
// TODO: Extract inline object types to interfaces
// Consider creating interfaces for complex object types
import React from 'react';

import SafeImage from '@/components/SafeImage';

interface InvestigationsSectionProps {
  title: string;
  description: string[];
  items: string[];
  image: {
    src: string;
    alt: string;
  };
}

const InvestigationsSection: React.FC<InvestigationsSectionProps> = ({
  title,
  description,
  items,
  image
}) => {
  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-centre">
          <div>
            <h2 className="text-enhanced-heading text-3xl font-bold mb-6">{title}</h2>
            
            <p className="text-muted-foreground mb-6">
              {description[0]}
            </p>
            
            <p className="text-muted-foreground mb-6">
              {description[1]}
            </p>
            
            <ul className="list-disc list-inside text-muted-foreground space-y-2 mb-6">
              {items.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
            
            <p className="text-muted-foreground">
              {description[2]}
            </p>
          </div>
          
          <div className="relative rounded-xl overflow-hidden shadow-lg">
            <SafeImage
              src={image.src}
              alt={image.alt}
              className="w-full h-auto"
              fallbackSrc="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default InvestigationsSection;
