import React from 'react';
import { Link } from 'react-router-dom';

import { But<PERSON> } from '@/components/ui/button';

interface ContactCTAProps {
  title: string;
  description: string;
  buttonText: string;
  buttonLink: string;
}

const ContactCTA: React.FC<ContactCTAProps> = ({
  title,
  description,
  buttonText,
  buttonLink
}) => {
  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="text-centre max-w-3xl mx-auto">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-6">{title}</h2>
          <p className="text-muted-foreground mb-8">{description}</p>
          <Button asChild size="lg">
            <Link to={buttonLink}>{buttonText}</Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

ContactCTA.displayName = 'ContactCTA';

export default ContactCTA;
