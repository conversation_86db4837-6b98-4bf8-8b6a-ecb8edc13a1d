import React from 'react';

import { GalleryImage } from '@/data/consultingRooms/consultingRoomsData';

interface GallerySectionProps {
  title: string;
  description: string;
  images: GalleryImage[];
}

const GallerySection: React.FC<GallerySectionProps> = ({
  title,
  description,
  images
}) => {
  return (
    <section className="py-16">
      <div className="container">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-muted-foreground">{description}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {images.map((image) => (
            <div key={image.id} className="relative h-64 rounded-lg overflow-hidden shadow-md">
              <img
                src={image.src}
                alt={image.alt}
                className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

GallerySection.displayName = 'GallerySection';

export default GallerySection;
