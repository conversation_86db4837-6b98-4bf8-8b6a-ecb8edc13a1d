import { ChevronDown, ChevronUp, HelpCircle } from 'lucide-react';
import React, { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface FAQ {
  question: string;
  answer: string;
}

interface ContactFAQSectionProps {
  title: string;
  subtitle: string;
  questions: FAQ[];
}

const ContactFAQSection: React.FC<ContactFAQSectionProps> = ({
  title,
  subtitle,
  questions
}) => {
  const deviceInfo = useDeviceDetection();
  const [openItems, setOpenItems] = useState<Set<number>>(new Set());

  const toggleItem = (index: number) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(index)) {
      newOpenItems.delete(index);
    } else {
      newOpenItems.add(index);
    }
    setOpenItems(newOpenItems);
  };

  return (
    <section className="py-16 bg-muted/30">
      <div className="container">
        <div className="text-centre mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            {subtitle}
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {questions.map((faq, index) => (
              <Card key={index} className="shadow-md hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <Button
                    variant="ghost"
                    className="w-full justify-between p-0 h-auto text-left"
                    onClick={() => toggleItem(index)}
                  >
                    <div className="flex items-start gap-3 flex-1">
                      <div className="p-2 bg-primary/10 rounded-lg mt-1">
                        <HelpCircle className="h-5 w-5 text-primary" />
                      </div>
                      <CardTitle className="text-lg text-foreground text-left flex-1">
                        {faq.question}
                      </CardTitle>
                    </div>
                    <div className="ml-4">
                      {openItems.has(index) ? (
                        <ChevronUp className="h-5 w-5 text-muted-foreground" />
                      ) : (
                        <ChevronDown className="h-5 w-5 text-muted-foreground" />
                      )}
                    </div>
                  </Button>
                </CardHeader>
                
                {openItems.has(index) && (
                  <CardContent className="pt-0">
                    <div className="ml-14">
                      <p className="text-muted-foreground leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        </div>

        {/* Additional Help */}
        <div className="mt-12 text-centre">
          <Card className="max-w-2xl mx-auto bg-gradient-to-r from-primary/5 to-info-light border-primary/20">
            <CardContent className="p-8">
              <h3 className="text-xl font-bold mb-4 text-primary">
                💬 Still Have Questions?
              </h3>
              <p className="text-muted-foreground mb-6">
                Our friendly staff are here to help with any questions about our services, 
                appointments, or procedures. Don't hesitate to get in touch.
              </p>
              
              <div className={cn(
                "flex gap-4",
                deviceInfo.isMobile 
                  ? "flex-col" 
                  : "flex-row justify-centre"
              )}>
                <Button asChild>
                  <a href="tel:0390084200">
                    📞 Call Us: (03) 9008 4200
                  </a>
                </Button>
                
                <Button asChild variant="outline">
                  <a href="mailto:<EMAIL>">
                    📧 Email: <EMAIL>
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Contact Summary */}
        <div className="mt-8">
          <Card className="max-w-4xl mx-auto shadow-lg">
            <CardContent className="p-6">
              <h3 className="text-lg font-bold mb-4 text-centre">
                📋 Quick Contact Reference
              </h3>
              
              <div className={cn(
                "grid gap-4 text-sm",
                deviceInfo.isMobile 
                  ? "grid-cols-1" 
                  : "grid-cols-2 md:grid-cols-4"
              )}>
                <div className="text-centre">
                  <h4 className="font-semibold text-primary mb-2">📞 General Inquiries</h4>
                  <p className="text-muted-foreground">
                    (03) 9008 4200<br />
                    Mon-Fri 8:30-5:30
                  </p>
                </div>
                
                <div className="text-centre">
                  <h4 className="font-semibold text-primary mb-2">🚨 Emergencies</h4>
                  <p className="text-muted-foreground">
                    Call 000<br />
                    Life-threatening only
                  </p>
                </div>
                
                <div className="text-centre">
                  <h4 className="font-semibold text-primary mb-2">📧 Email</h4>
                  <p className="text-muted-foreground">
                    <EMAIL><br />
                    24hr response
                  </p>
                </div>
                
                <div className="text-centre">
                  <h4 className="font-semibold text-primary mb-2">📍 Location</h4>
                  <p className="text-muted-foreground">
                    Surrey Hills<br />
                    2 min from station
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default ContactFAQSection;
