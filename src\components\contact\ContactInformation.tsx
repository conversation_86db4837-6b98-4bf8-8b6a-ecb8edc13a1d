import { LucideIcon } from 'lucide-react';
import React from 'react';

import SafeImage from '@/components/SafeImage';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ContactDetail {
  icon: LucideIcon;
  title: string;
  content: string[];
  note?: string;
  link?: string;
}

interface ContactInformationProps {
  title: string;
  image: string;
  details: ContactDetail[];
}

const ContactInformation: React.FC<ContactInformationProps> = ({
  title,
  image,
  details
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <div className={cn(
      deviceInfo.isMobile ? "mobile-fade-in" : "animate-fade-in [animation-delay:100ms]"
    )}>
      <h2 className={cn(
        "font-bold mb-mobile-lg",
        deviceInfo.isMobile ? "mobile-heading" : "text-2xl mb-6"
      )}>
        {title}
      </h2>

      <div className="relative rounded-xl overflow-hidden mb-8 shadow-lg">
        <SafeImage
          src={image}
          alt="miNEURO Consulting Suites Reception"
          className="w-full h-auto"
          fallbackSrc="/images/medical-consulting.jpg"
        />
      </div>

      <div className={cn(
        "glass-card space-y-mobile-lg mb-mobile-xl",
        deviceInfo.isMobile ? "p-mobile-lg" : "p-6 space-y-6 mb-8"
      )}>
        {details.map((detail, index) => {
          const IconComponent = detail.icon;
          return (
            <div key={index} className="flex items-start">
              <div className={cn(
                "flex-shrink-0 rounded-full bg-primary/10 flex items-centre justify-centre",
                deviceInfo.isMobile
                  ? "h-12 w-12 mr-mobile-md"
                  : "h-10 w-10 mr-4"
              )}>
                <IconComponent className={cn(
                  "text-primary",
                  deviceInfo.isMobile ? "h-6 w-6" : "h-5 w-5"
                )} />
              </div>
              <div>
                <h3 className={cn(
                  "font-semibold mb-1",
                  deviceInfo.isMobile ? "mobile-subheading" : ""
                )}>
                  {detail.title}
                </h3>
                {detail.content.map((line, lineIndex) => (
                  <p key={lineIndex} className={cn(
                    "text-muted-foreground",
                    deviceInfo.isMobile ? "mobile-text" : ""
                  )}>
                    {detail.link && lineIndex === 0 ? (
                      <a
                        href={detail.link}
                        className={cn(
                          "transition-colors touch-feedback",
                          deviceInfo.isMobile
                            ? "mobile-text block"
                            : "hover:text-primary"
                        )}
                      >
                        {line}
                      </a>
                    ) : (
                      line
                    )}
                    {lineIndex < detail.content.length - 1 && <br />}
                  </p>
                ))}
                {detail.note && (
                  <p className={cn(
                    "text-muted-foreground mt-2",
                    deviceInfo.isMobile ? "mobile-text" : ""
                  )}>
                    {detail.note}
                  </p>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ContactInformation;
