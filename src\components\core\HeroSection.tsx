
// TODO: Extract inline object types to interfaces
// Consider creating interfaces for complex object types
import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';

interface HeroSectionProps {
  title: string;
  subtitle?: string;
  description: string;
  primaryCTA?: {
    text: string;
    link: string;
  };
  secondaryCTA?: {
    text: string;
    link: string;
  };
  backgroundImage?: string;
  backgroundVariant?: 'gradient' | 'image' | 'solid';
  className?: string;
}

const HeroSection: React.FC<HeroSectionProps> = ({
  title,
  subtitle,
  description,
  primaryCTA,
  secondaryCTA,
  backgroundImage,
  backgroundVariant = 'gradient',
  className = ''
}) => {
  const getBackgroundClass = () => {
    switch (backgroundVariant) {
      case 'image':
        return backgroundImage
          ? `bg-cover bg-center bg-no-repeat`
          : 'section-background-alt';
      case 'solid':
        return 'bg-primary/5';
      default:
        return 'section-background-alt';
    }
  };

  const backgroundStyle = backgroundVariant === 'image' && backgroundImage 
    ? { backgroundImage: `url(${backgroundImage})` }
    : {};

  return (
    <section 
      className={`relative py-20 ${getBackgroundClass()} ${className}`}
      style={backgroundStyle}
    >
      {backgroundVariant === 'image' && (
        <div className="absolute inset-0 bg-foreground/40" />
      )}
      
      <div className="container relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className={`text-enhanced-heading text-4xl md:text-5xl lg:text-6xl font-bold mb-6 ${
            backgroundVariant === 'image' ? 'text-primary-foreground' : ''
          }`}>
            {title}
          </h1>

          {subtitle && (
            <h2 className={`text-enhanced-heading text-xl md:text-2xl mb-6 ${
              backgroundVariant === 'image' ? 'text-primary-foreground/90' : 'text-primary'
            }`}>
              {subtitle}
            </h2>
          )}

          <p className={`text-enhanced-body text-lg md:text-xl mb-8 max-w-2xl mx-auto ${
            backgroundVariant === 'image' ? 'text-primary-foreground/80' : 'text-muted-foreground'
          }`}>
            {description}
          </p>
          
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            {primaryCTA && (
              <Button asChild size="lg" className="text-lg px-8 py-3">
                <Link to={primaryCTA.link}>{primaryCTA.text}</Link>
              </Button>
            )}
            
            {secondaryCTA && (
              <Button 
                asChild 
                variant={backgroundVariant === 'image' ? 'secondary' : 'outline'} 
                size="lg" 
                className="text-lg px-8 py-3"
              >
                <Link to={secondaryCTA.link}>{secondaryCTA.text}</Link>
              </Button>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
