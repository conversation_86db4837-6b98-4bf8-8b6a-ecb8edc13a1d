import React from 'react';

import { BenefitCard } from '@/data/expertise/cervicalDiscReplacementData';

interface BenefitsGridProps {
  title: string;
  benefits: BenefitCard[];
}

const BenefitsGrid: React.FC<BenefitsGridProps> = ({ title, benefits }) => {
  return (
    <div className="mb-8">
      <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{title}</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {benefits.map((benefit) => (
          <div key={benefit.id} className="card p-6 rounded-lg shadow-md medical-card">
            <h3 className="text-xl font-semibold mb-3 text-primary">{benefit.title}</h3>
            <p className="text-muted-foreground">
              {benefit.description}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

BenefitsGrid.displayName = 'BenefitsGrid';

export default BenefitsGrid;
