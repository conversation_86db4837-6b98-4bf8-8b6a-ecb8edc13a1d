import React from 'react';
import { Link } from 'react-router-dom';

import { But<PERSON> } from '@/components/ui/button';

interface CallToActionButton {
  text: string;
  link: string;
}

interface ExpertiseCallToActionProps {
  title: string;
  description: string;
  primaryButton: CallToActionButton;
  secondaryButton: CallToActionButton;
}

const ExpertiseCallToAction: React.FC<ExpertiseCallToActionProps> = ({
  title,
  description,
  primaryButton,
  secondaryButton
}) => {
  return (
    <section className="py-20 bg-gradient-to-br from-primary/10 via-primary/5 to-background border-y border-border/50">
      <div className="container">
        <div className="text-centre max-w-4xl mx-auto">
          <h2 className="text-enhanced-heading text-3xl lg:text-enhanced-heading text-4xl font-bold mb-8 text-foreground">{title}</h2>
          <p className="text-foreground/80 mb-12 text-lg leading-relaxed font-medium">
            {description}
          </p>
          <div className="flex flex-col sm:flex-row justify-centre gap-6">
            <Button asChild size="lg" className="font-bold py-3 px-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
              <Link to={primaryButton.link}>{primaryButton.text}</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="font-bold py-3 px-8 border-2 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
              <Link to={secondaryButton.link}>{secondaryButton.text}</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

ExpertiseCallToAction.displayName = 'ExpertiseCallToAction';

export default ExpertiseCallToAction;
