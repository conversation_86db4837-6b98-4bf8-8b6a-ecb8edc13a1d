import React from 'react';

import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface IntroductionSectionProps {
  introduction: {
    paragraph1: string;
    paragraph2: string;
    paragraph3: string;
  };
}

const IntroductionSection: React.FC<IntroductionSectionProps> = ({ introduction }) => {
  const deviceInfo = useDeviceDetection();

  return (
    <div className={cn(
      "mobile-safe-area",
      deviceInfo.isMobile ? "py-mobile-xl" : "py-20"
    )}>
      <div className={cn(
        deviceInfo.isMobile ? "mobile-container" : "container"
      )}>
        <div className="max-w-4xl mx-auto text-centre">
          <div className={cn(
            "text-muted-foreground mb-mobile-md",
            deviceInfo.isMobile ? "mobile-text" : "text-lg mb-8"
          )}>
            <p className="mb-4">
              <a
                href="https://mpscentre.com.au/dtTeam/dr-ales-<PERSON><PERSON><PERSON><PERSON>/"
                className={cn(
                  "text-primary transition-colors touch-feedback",
                  deviceInfo.isMobile ? "" : "hover:underline"
                )}
                target="_blank"
                rel="noopener noreferrer"
              >
                Dr Ales Aliashkevich
              </a> {introduction.paragraph1}
            </p>
            <p className={cn(
              "text-muted-foreground mb-mobile-md",
              deviceInfo.isMobile ? "mobile-text" : "mb-4"
            )}>
              <a
                href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/"
                className={cn(
                  "text-primary transition-colors touch-feedback",
                  deviceInfo.isMobile ? "" : "hover:underline"
                )}
                target="_blank"
                rel="noopener noreferrer"
              >
                Dr Aliashkevich
              </a> {introduction.paragraph2}
            </p>
            <p>
              {introduction.paragraph3}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

IntroductionSection.displayName = 'IntroductionSection';

export default IntroductionSection;
