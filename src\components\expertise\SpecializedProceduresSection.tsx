import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';

interface SpecialisedProceduresSectionProps {
  title: string;
  procedures: {
    cervicalDisc: { description: string };
    lumbarDisc: { description: string };
    imageGuided: { description: string };
    roboticSpine: { description: string };
  };
  navigation: {
    cervicalDisc: string;
    lumbarDisc: string;
    imageGuided: string;
    roboticSpine: string;
  };
  learnMoreText: string;
}

const SpecialisedProceduresSection: React.FC<SpecialisedProceduresSectionProps> = ({
  title,
  procedures,
  navigation,
  learnMoreText
}) => {
  return (
    <div className="mt-16">
      <h2 className="text-enhanced-heading text-3xl font-bold mb-8 text-centre">{title}</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="card p-6 rounded-lg shadow-md medical-card">
          <h3 className="text-xl font-semibold mb-3 text-primary">{navigation.cervicalDisc}</h3>
          <p className="text-muted-foreground mb-4">
            {procedures.cervicalDisc.description}
          </p>
          <Button asChild>
            <Link to="/expertise/cervical-disc-replacement">{learnMoreText}</Link>
          </Button>
        </div>

        <div className="card p-6 rounded-lg shadow-md medical-card">
          <h3 className="text-xl font-semibold mb-3 text-primary">{navigation.lumbarDisc}</h3>
          <p className="text-muted-foreground mb-4">
            {procedures.lumbarDisc.description}
          </p>
          <Button asChild>
            <Link to="/expertise/lumbar-disc-replacement">{learnMoreText}</Link>
          </Button>
        </div>

        <div className="card p-6 rounded-lg shadow-md medical-card">
          <h3 className="text-xl font-semibold mb-3 text-primary">{navigation.imageGuided}</h3>
          <p className="text-muted-foreground mb-4">
            {procedures.imageGuided.description}
          </p>
          <Button asChild>
            <Link to="/expertise/image-guided-surgery">{learnMoreText}</Link>
          </Button>
        </div>

        <div className="card p-6 rounded-lg shadow-md medical-card">
          <h3 className="text-xl font-semibold mb-3 text-primary">{navigation.roboticSpine}</h3>
          <p className="text-muted-foreground mb-4">
            {procedures.roboticSpine.description}
          </p>
          <Button asChild>
            <Link to="/expertise/robotic-spine-surgery">{learnMoreText}</Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

SpecialisedProceduresSection.displayName = 'SpecialisedProceduresSection';

export default SpecialisedProceduresSection;
