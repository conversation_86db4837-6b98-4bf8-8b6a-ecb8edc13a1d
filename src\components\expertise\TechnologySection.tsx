import React from 'react';

import SafeImage from '@/components/SafeImage';

interface TechnologyItem {
  title: string;
  description: string;
}

interface TechnologySectionProps {
  title: string;
  description: string;
  items: TechnologyItem[];
  images: string[];
  backgroundColor?: string;
  imageOrder?: 'left' | 'right';
}

const TechnologySection: React.FC<TechnologySectionProps> = ({
  title,
  description,
  items,
  images,
  backgroundColor = "",
  imageOrder = 'left'
}) => {
  const sectionClass = backgroundColor ? `py-16 ${backgroundColor}` : "py-16";

  return (
    <section className={sectionClass}>
      <div className="container">
        <h2 className="text-enhanced-heading text-3xl font-bold mb-8 text-center">{title}</h2>
        <p className="text-center text-muted-foreground mb-12 max-w-3xl mx-auto">
          {description}
        </p>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-12">
          {imageOrder === 'left' ? (
            <>
              <div className="relative rounded-xl overflow-hidden shadow-lg">
                {images[0] && (
                  images[0].includes('robotic-operating-theatre') ? (
                    <SafeImage
                      src={images[0]}
                      alt={`${title} - Image 1`}
                      className="w-full h-auto"
                      fallbackSrc="/images/medical-consulting.jpg"
                    />
                  ) : (
                    <img
                      src={images[0]}
                      alt={`${title} - Image 1`}
                      className="w-full h-auto"
                    />
                  )
                )}
              </div>
              <div>
                <div className="grid grid-cols-1 gap-6">
                  {items.slice(0, 2).map((item: TechnologyItem, index: number) => (
                    <div key={index} className="card p-6 rounded-lg shadow-md medical-card">
                      <h3 className="text-xl font-semibold mb-3 text-primary">{item.title}</h3>
                      <p className="text-muted-foreground">
                        {item.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </>
          ) : (
            <>
              <div className="order-2 lg:order-1">
                <div className="grid grid-cols-1 gap-6">
                  {items.slice(0, 2).map((item: TechnologyItem, index: number) => (
                    <div key={index} className="card p-6 rounded-lg shadow-md medical-card">
                      <h3 className="text-xl font-semibold mb-3 text-primary">{item.title}</h3>
                      <p className="text-muted-foreground">
                        {item.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
              <div className="order-1 lg:order-2 relative rounded-xl overflow-hidden shadow-lg">
                {images[0] && (
                  <img
                    src={images[0]}
                    alt={`${title} - Image 1`}
                    className="w-full h-auto"
                  />
                )}
              </div>
            </>
          )}
        </div>

        {items.length > 2 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {imageOrder === 'left' ? (
              <>
                <div className="order-2 lg:order-1">
                  <div className="grid grid-cols-1 gap-6">
                    {items.slice(2, 4).map((item: TechnologyItem, index: number) => (
                      <div key={index} className="card p-6 rounded-lg shadow-md medical-card">
                        <h3 className="text-xl font-semibold mb-3 text-primary">{item.title}</h3>
                        <p className="text-muted-foreground">
                          {item.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="order-1 lg:order-2 relative rounded-xl overflow-hidden shadow-lg">
                  {images[1] && (
                    <img
                      src={images[1]}
                      alt={`${title} - Image 2`}
                      className="w-full h-auto"
                    />
                  )}
                </div>
              </>
            ) : (
              <>
                <div className="relative rounded-xl overflow-hidden shadow-lg">
                  {images[1] && (
                    <img
                      src={images[1]}
                      alt={`${title} - Image 2`}
                      className="w-full h-auto"
                    />
                  )}
                </div>
                <div>
                  <div className="grid grid-cols-1 gap-6">
                    {items.slice(2, 4).map((item: TechnologyItem, index: number) => (
                      <div key={index} className="card p-6 rounded-lg shadow-md medical-card">
                        <h3 className="text-xl font-semibold mb-3 text-primary">{item.title}</h3>
                        <p className="text-muted-foreground">
                          {item.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </section>
  );
};

TechnologySection.displayName = 'TechnologySection';

export default TechnologySection;
