import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import type { TreatmentDecisionsProps } from '@/types/homepage';

/**
 * Treatment Decisions Section Component
 * Displays conservative treatment approach and philosophy
 * Preserves all original content and styling from Index.tsx lines 856-913
 */
const TreatmentDecisionsSection: React.FC<TreatmentDecisionsProps> = ({ content }) => {
  return (
    <section className="section medical-card">
      <div className="container">
        <div className="text-centre mb-12">
          <span className="text-primary font-medium uppercase tracking-wider text-sm">
            CONSERVATIVE APPROACH
          </span>
          <h2 className="text-enhanced-heading text-3xl md:text-enhanced-heading text-4xl font-bold mt-2 mb-6">Treatment Decisions</h2>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="bg-primary/5 p-8 rounded-lg mb-8">
            <h3 className="text-xl font-semibold mb-4 text-centre">{content.primaryGoal.title}</h3>
            <p className="text-muted-foreground text-centre">
              {content.primaryGoal.description}
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="text-xl font-semibold mb-4">{content.buildingTrust.title}</h3>
              <p className="text-muted-foreground mb-4">
                {content.buildingTrust.description}
              </p>
              {content.buildingTrust.items && (
                <ul className="space-y-2 text-muted-foreground">
                  {content.buildingTrust.items.map((item, index) => (
                    <li key={index}>– {item}</li>
                  ))}
                </ul>
              )}
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-4">{content.secondOpinions.title}</h3>
              <p className="text-muted-foreground mb-4">
                {content.secondOpinions.description}
              </p>
              {content.secondOpinions.items && (
                <ul className="space-y-2 text-muted-foreground">
                  {content.secondOpinions.items.map((item, index) => (
                    <li key={index}>– {item}</li>
                  ))}
                </ul>
              )}
            </div>
          </div>

          <div className="text-centre">
            <p className="text-muted-foreground mb-6">
              Our conservative approach means we exhaust all non-surgical options before considering surgery. When surgery is necessary, we use the least invasive techniques possible to achieve the best outcomes for our patients.
            </p>
            <Button asChild>
              <Link to="/appointments">
                Schedule a Consultation
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

TreatmentDecisionsSection.displayName = 'TreatmentDecisionsSection';

export default TreatmentDecisionsSection;
