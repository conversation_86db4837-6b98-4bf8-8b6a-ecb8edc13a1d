import React from 'react';

import SafeImage from '@/components/SafeImage';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ProcedureCategory } from '@/data/lifestyle-modifications/interventionalProceduresData';

interface InterventionalProceduresSectionProps {
  categories: ProcedureCategory[];
  title: string;
  description: string;
}

const InterventionalProceduresSection: React.FC<InterventionalProceduresSectionProps> = ({
  categories, 
  title, 
  description 
}) => {
  return (
    <section className="py-16 bg-muted/30">
      <div className="container max-w-7xl">
        <div className="text-centre mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{title}</h2>
          <p className="text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            {description}
          </p>
        </div>

        <Tabs defaultValue={categories[0]?.id} className="w-full max-w-5xl mx-auto">
          <TabsList className="grid grid-cols-2 mb-8 bg-background/50 backdrop-blur-sm">
            {categories.map((category) => (
              <TabsTrigger 
                key={category.id} 
                value={category.id} 
                className="text-centre py-3 text-sm"
              >
                {category.title}
              </TabsTrigger>
            ))}
          </TabsList>

          {categories.map((category) => (
            <TabsContent 
              key={category.id} 
              value={category.id} 
              className="medical-card/50 backdrop-blur-sm border border-border/50 p-6 rounded-lg shadow-md"
            >
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                <div className="lg:col-span-2 space-y-4">
                  <h3 className="text-enhanced-heading text-2xl font-bold text-foreground">{category.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {category.description}
                  </p>
                </div>
                <div className="relative rounded-lg overflow-hidden shadow-md">
                  <SafeImage
                    src={category.imageSrc}
                    alt={category.imageAlt}
                    className="w-full h-full object-cover"
                    fallbackSrc="/images/patient-resources/medical-procedure.jpg"
                  />
                </div>
              </div>

              <Accordion type="single" collapsible className="w-full space-y-4">
                {category.procedures.map((procedure) => (
                  <AccordionItem 
                    key={procedure.id} 
                    value={procedure.id}
                    className="bg-background/50 backdrop-blur-sm border border-border/30 rounded-lg px-4"
                  >
                    <AccordionTrigger className="text-lg font-semibold text-foreground hover:no-underline">
                      {procedure.title}
                    </AccordionTrigger>
                    <AccordionContent className="text-muted-foreground space-y-4 pt-2">
                      <p className="leading-relaxed">
                        {procedure.description}
                      </p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="text-base font-semibold mb-3 text-foreground">Best for:</h4>
                          <ul className="space-y-2">
                            {procedure.bestFor.map((item, index) => (
                              <li key={index} className="flex items-start">
                                <span className="text-success mr-2 mt-0.5">✓</span>
                                <span className="text-sm leading-relaxed">{item}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                        
                        <div>
                          <h4 className="text-base font-semibold mb-3 text-foreground">Considerations:</h4>
                          <ul className="space-y-2">
                            {procedure.considerations.map((item, index) => (
                              <li key={index} className="flex items-start">
                                <span className="text-info mr-2 mt-0.5">•</span>
                                <span className="text-sm leading-relaxed">{item}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>

              {/* Case Example */}
              {category.caseExample && (
                <div className="mt-8 p-6 bg-muted/50 backdrop-blur-sm border border-border/30 rounded-lg">
                  <h4 className="font-semibold mb-3 text-foreground">{category.caseExample.title}</h4>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    {category.caseExample.description}
                  </p>
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </section>
  );
};

InterventionalProceduresSection.displayName = 'InterventionalProceduresSection';

export default InterventionalProceduresSection;
