import React from 'react';

import { ConditionsTreatedData } from '@/types/location';

interface ConditionsTreatedProps extends ConditionsTreatedData {
  // Extends ConditionsTreatedData with all required props
  className?: string;
}

/**
 * Conditions Treated Component
 * Displays the various medical conditions treated at the location
 * Preserves all original conditions content from location pages
 */
const ConditionsTreated: React.FC<ConditionsTreatedProps> = ({
  title,
  subtitle,
  description,
  categories,
  className
}) => {
  return (
    <section className={`py-16 ${className || ''}`}>
      <div className="container">
        <div className="mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-muted-foreground">
            {subtitle}
          </p>
        </div>

        <div className="mt-8 max-w-3xl mx-auto mb-12">
          <p className="text-muted-foreground text-center">
            {description}
          </p>
        </div>

        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category, index) => (
            <div key={index} className="card p-6 rounded-lg shadow-md medical-card">
              <h3 className="text-xl font-semibold mb-3 text-primary">{category.title}</h3>
              <ul className="text-muted-foreground list-none space-y-2">
                {category.conditions.map((condition, conditionIndex) => (
                  <li key={conditionIndex} className="flex items-start">
                    <span className="text-primary mr-2">•</span>
                    <span>{condition}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ConditionsTreated;
