import React from 'react';

interface InsuranceAndFundingProps {
  title: string;
  subtitle: string;
  categories?: Array<{
    title: string;
    description1?: string;
    description2?: string;
    description3?: string;
    tac?: string;
    workCover?: string;
    veteranAffairs?: string;
    dr<PERSON><PERSON><PERSON><PERSON>vichProfileLink?: string;
  }>;
  ctaTitle?: string;
  ctaDescription?: string;
  ctaButtons?: Array<{
    text: string;
    link: string;
    variant?: 'default' | 'outline';
  }>;
}

/**
 * Insurance and Funding Component
 * Displays insurance options, funding information, and patient privacy details
 * Preserves all content from the original Mornington location page
 */
const InsuranceAndFunding: React.FC<InsuranceAndFundingProps> = ({
  title,
  subtitle,
  categories = [],
  ctaTitle,
  ctaDescription,
  ctaButtons
}) => {
  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-muted-foreground">
            {subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {categories.map((category, index) => (
            <div key={index} className="card p-6 rounded-lg shadow-md medical-card">
              <h3 className="text-xl font-semibold mb-3 text-primary">{category.title}</h3>
              
              {category.description1 && (
                <p className="text-muted-foreground mb-4">
                  {category.description1}
                </p>
              )}
              
              {category.description2 && (
                <p className="text-muted-foreground mb-4">
                  {category.title === 'Patients Privacy' && category.drAliashkevichProfileLink ? (
                    <>
                      Any files and all personal information are kept secure. Patients can give consent to share their health information, for example, when attending other medical practitioners.{' '}
                      <a
                        href={category.drAliashkevichProfileLink}
                        className="text-primary hover:underline"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Dr Aliashkevich
                      </a>{' '}
                      will never release any information to insurers or other parties without consent.
                    </>
                  ) : (
                    category.description2
                  )}
                </p>
              )}

              {category.description3 && (
                <p className="text-muted-foreground mb-4">
                  {category.description3}
                </p>
              )}

              {category.tac && (
                <p className="text-muted-foreground mb-4">
                  <strong>TAC:</strong> {category.tac}
                </p>
              )}

              {category.workCover && (
                <p className="text-muted-foreground mb-4">
                  <strong>WorkCover:</strong> {category.workCover}
                </p>
              )}

              {category.veteranAffairs && (
                <p className="text-muted-foreground">
                  <strong>Veteran Affairs/Military:</strong> {category.veteranAffairs}
                </p>
              )}
            </div>
          ))}
        </div>

        {ctaTitle && ctaDescription && ctaButtons && (
          <div className="text-center">
            <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{ctaTitle}</h2>
            <p className="text-lg mb-8 max-w-2xl mx-auto text-muted-foreground">
              {ctaDescription}
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              {ctaButtons.map((button, index) => (
                <button
                  key={index}
                  className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                    button.variant === 'outline'
                      ? 'border border-primary text-primary hover:bg-primary hover:text-primary-foreground'
                      : 'bg-primary text-primary-foreground hover:bg-primary/90'
                  }`}
                >
                  {button.text}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default InsuranceAndFunding;
