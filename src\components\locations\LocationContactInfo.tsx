import { MapPin, Phone, Mail, Clock } from 'lucide-react';
import React from 'react';

interface ContactDetail {
  icon: React.ReactNode;
  title: string;
  content: string | React.ReactNode;
}

interface LocationContactInfoProps {
  address: {
    street: string;
    suburb: string;
    state: string;
    postcode: string;
  };
  phone: string;
  email: string;
  hours: {
    weekdays: string;
    note?: string;
  };
  consultingHours?: {
    title: string;
    details: string;
    appointmentNote: string;
    urgentNote?: string;
  };
  appointmentProcess?: {
    title: string;
    details1: string;
    details2: string;
  };
}

const LocationContactInfo: React.FC<LocationContactInfoProps> = ({
  address,
  phone,
  email,
  hours,
  consultingHours,
  appointmentProcess
}) => {
  const contactDetails: ContactDetail[] = [
    {
      icon: <MapPin className="h-5 w-5 text-primary" />,
      title: "Address",
      content: (
        <div className="space-y-1">
          <p className="font-medium text-foreground">miNEURO Consulting Suites</p>
          <p className="text-muted-foreground">{address.street}</p>
          <p className="text-muted-foreground">{address.suburb} {address.state} {address.postcode}</p>
        </div>
      )
    },
    {
      icon: <Phone className="h-5 w-5 text-primary" />,
      title: "Phone",
      content: (
        <div className="space-y-1">
          <p className="text-muted-foreground">{phone}</p>
          <p className="text-sm text-muted-foreground">Medicolegal Office: 03 99997433</p>
          <p className="text-sm text-muted-foreground">Fax: 03 99236688</p>
        </div>
      )
    },
    {
      icon: <Mail className="h-5 w-5 text-primary" />,
      title: "Email",
      content: <p className="text-muted-foreground">{email}</p>
    },
    {
      icon: <Clock className="h-5 w-5 text-primary" />,
      title: "Hours",
      content: (
        <div className="space-y-1">
          <p className="text-muted-foreground">{hours.weekdays}</p>
          {hours.note && <p className="text-sm text-muted-foreground">{hours.note}</p>}
        </div>
      )
    }
  ];

  return (
    <div className="space-y-8">
      {/* Contact Information Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
        {contactDetails.map((detail, index) => (
          <div key={index} className="card p-6 rounded-lg shadow-md medical-card border border-border hover:shadow-md transition-shadow">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 h-10 w-10 rounded-full bg-primary/10 flex items-centre justify-centre">
                {detail.icon}
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold mb-2 text-primary">{detail.title}</h3>
                <div className="text-muted-foreground">{detail.content}</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Additional Information Cards */}
      {consultingHours && (
        <div className="card p-6 rounded-lg shadow-md medical-card border border-border">
          <h3 className="text-xl font-semibold mb-4 text-primary">{consultingHours.title}</h3>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <span className="font-semibold text-foreground min-w-fit">Monday to Friday:</span>
              <span className="text-muted-foreground">{consultingHours.details}</span>
            </div>

            <div className="bg-primary/5 p-4 rounded-lg border-l-4 border-primary">
              <p className="text-muted-foreground leading-relaxed text-sm">
                {consultingHours.appointmentNote}
              </p>
            </div>

            {consultingHours.urgentNote && (
              <div className="bg-info dark:bg-info/20 p-4 rounded-lg border-l-4 border-info dark:border-info">
                <p className="text-muted-foreground leading-relaxed text-sm">
                  <span className="font-semibold text-info dark:text-info">Urgent appointments:</span> {consultingHours.urgentNote}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {appointmentProcess && (
        <div className="card p-6 rounded-lg shadow-md medical-card border border-border">
          <h3 className="text-xl font-semibold mb-4 text-primary">{appointmentProcess.title}</h3>
          <div className="space-y-4">
            <div className="flex gap-3">
              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-centre justify-centre text-sm font-semibold mt-1">
                1
              </div>
              <p className="text-muted-foreground leading-relaxed">
                {appointmentProcess.details1}
              </p>
            </div>
            <div className="flex gap-3">
              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-centre justify-centre text-sm font-semibold mt-1">
                2
              </div>
              <p className="text-muted-foreground leading-relaxed">
                {appointmentProcess.details2}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LocationContactInfo;
