import React from 'react';

import LocationGallery from './LocationGallery';

interface Facility {
  icon: React.ReactNode;
  title: string;
  description: string;
}

interface AdditionalFacility {
  title: string;
  description: string;
  additionalInfo?: string;
}

interface GalleryImage {
  src: string;
  alt: string;
}

interface LocationFacilitiesProps {
  title: string;
  subtitle: string;
  description: string;
  facilities: Facility[];
  additionalFacilities?: AdditionalFacility[];
  gallery?: {
    title: string;
    description: string;
    images: GalleryImage[];
  };
}

const LocationFacilities: React.FC<LocationFacilitiesProps> = ({
  title,
  subtitle,
  description,
  facilities,
  additionalFacilities,
  gallery
}) => {
  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-muted-foreground">{subtitle}</p>
        </div>

        <div className="mt-8 max-w-3xl mx-auto mb-12">
          <p className="text-muted-foreground text-center">{description}</p>
        </div>

        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
          {facilities.map((facility, index) => (
            <div key={index} className="card p-6 rounded-lg shadow-md medical-card text-center">
              <div className="flex justify-center mb-4">
                <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center">
                  {facility.icon}
                </div>
              </div>
              <h3 className="text-xl font-semibold mb-3 text-primary">{facility.title}</h3>
              <p className="text-muted-foreground">{facility.description}</p>
            </div>
          ))}
        </div>

        {additionalFacilities && additionalFacilities.length > 0 && (
          <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">
            {additionalFacilities.map((facility, index) => (
              <div key={index} className="card p-6 rounded-lg shadow-md medical-card">
                <h3 className="text-xl font-semibold mb-3 text-primary">{facility.title}</h3>
                <p className="text-muted-foreground mb-4">
                  {facility.description}
                </p>
                {facility.additionalInfo && (
                  <p className="text-muted-foreground">
                    {facility.additionalInfo}
                  </p>
                )}
              </div>
            ))}
          </div>
        )}

        {gallery && (
          <LocationGallery
            title={gallery.title}
            description={gallery.description}
            images={gallery.images}
          />
        )}
      </div>
    </section>
  );
};

export default LocationFacilities;
