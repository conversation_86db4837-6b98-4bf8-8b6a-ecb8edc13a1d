import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';

interface LocationHeroProps {
  title: string;
  subtitle: string;
  introduction1: string;
  introduction2?: string;
  introduction3?: string;
  imageUrl?: string;
  ctaText?: string;
  ctaLink?: string;
}

const LocationHero: React.FC<LocationHeroProps> = ({
  title,
  subtitle,
  introduction1,
  introduction2,
  introduction3,
  imageUrl,
  ctaText = "Book an Appointment",
  ctaLink = "/appointments"
}) => {
  return (
    <section className="relative py-20 bg-gradient-to-r from-primary/10 to-white dark:from-primary/20 dark:to-background">
      <div className="container relative z-10">
        <div className="flex flex-col md:flex-row items-centre gap-8">
          <div className="md:w-1/2">
            <h1 className="text-enhanced-heading text-3xl font-bold mb-6">{title}</h1>
            <h2 className="text-xl text-primary mb-4">{subtitle}</h2>
            <p className="text-muted-foreground mb-4">{introduction1}</p>
            {introduction2 && (
              <p className="text-muted-foreground mb-4">{introduction2}</p>
            )}
            {introduction3 && (
              <p className="text-muted-foreground mb-6">{introduction3}</p>
            )}
            <Button asChild size="lg">
              <Link to={ctaLink}>{ctaText}</Link>
            </Button>
          </div>
          <div className="md:w-1/2">
            {imageUrl ? (
              <img
                src={imageUrl}
                alt={title}
                className="w-full h-80 object-cover rounded-lg shadow-xl"
              />
            ) : (
              <div className="w-full h-80 bg-gradient-to-br from-primary/20 to-primary/5 rounded-lg shadow-xl flex items-centre justify-centre">
                <div className="text-centre">
                  <h3 className="text-2xl font-semibold text-primary mb-2">Professional Care</h3>
                  <p className="text-muted-foreground">Expert medical services</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default LocationHero;
