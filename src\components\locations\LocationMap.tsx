import { Car, Train } from 'lucide-react';
import React from 'react';

interface TransportOption {
  icon: React.ReactNode;
  title: string;
  description: string;
}

interface LocationMapProps {
  embedUrl: string;
  title: string;
  transportOptions: {
    publicTransport: string;
    car: string;
  };
  gettingHereTitle?: string;
  publicTransportTitle?: string;
  carTitle?: string;
  parkingTitle?: string; // Add the missing property
}

const LocationMap: React.FC<LocationMapProps> = ({
  embedUrl,
  title,
  transportOptions,
  gettingHereTitle = "Getting Here",
  publicTransportTitle = "By Public Transport",
  carTitle = "By Car",
  parkingTitle: _parkingTitle = "Parking" // Add default value
}) => {
  const transportDetails: TransportOption[] = [
    {
      icon: <Train className="h-6 w-6 text-primary" />,
      title: publicTransportTitle,
      description: transportOptions.publicTransport
    },
    {
      icon: <Car className="h-6 w-6 text-primary" />,
      title: carTitle,
      description: transportOptions.car
    }
  ];

  return (
    <div className="space-y-8">
      {/* Interactive Map */}
      <div className="relative rounded-lg overflow-hidden shadow-lg border border-border">
        <iframe
          src={embedUrl}
          width="100%"
          height="400"
          style={{ border: 0 }}
          allowFullScreen
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
          title={`${title} Location Map`}
          className="w-full"
        />
      </div>

      {/* Getting Here Information */}
      <div className="card p-6 rounded-lg shadow-md medical-card border border-border">
        <h3 className="text-xl font-semibold mb-6 text-primary flex items-centre gap-2">
          <Train className="h-5 w-5" />
          {gettingHereTitle}
        </h3>
        <div className="space-y-6">
          {transportDetails.map((transport, index) => (
            <div key={index} className="border-l-4 border-primary/20 pl-4">
              <div className="flex items-centre gap-3 mb-3">
                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-centre justify-centre">
                  {transport.icon}
                </div>
                <h4 className="text-lg font-semibold text-foreground">{transport.title}</h4>
              </div>
              <p className="text-muted-foreground leading-relaxed ml-11">
                {transport.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LocationMap;
