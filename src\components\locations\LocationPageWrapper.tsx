import React from 'react';

import GenericLocationTemplate from './GenericLocationTemplate';

import { StandardErrorBoundary, SimpleErrorFallback } from '@/components/shared';
import { useEnhancedLocationData, useLocationPageState } from '@/hooks/useEnhancedLocationData';

/**
 * Location Page Wrapper
 * Content injection system that loads location data and renders the appropriate template
 * Handles loading states, errors, and data validation
 */

export interface LocationPageWrapperProps {
  locationSlug: string;
  customSections?: React.ReactNode[];
}

const LocationPageWrapper: React.FC<LocationPageWrapperProps> = ({
  locationSlug,
  customSections = []
}) => {
  const {
    locationData,
    config,
    specialSections,
    isLoading,
    error,
    isReady,
    retry
  } = useEnhancedLocationData(locationSlug);

  const { trackInteraction } = useLocationPageState(locationSlug);

  // Track page load - MUST be called before any conditional returns
  // Use a ref to track if we've already logged the page load
  const hasTrackedPageLoad = React.useRef(false);

  React.useEffect(() => {
    if (isReady && locationData && config && !hasTrackedPageLoad.current) {
      trackInteraction('page_load', 'location_page');
      hasTrackedPageLoad.current = true;
    }
  }, [trackInteraction, isReady, locationData, config]);

  // Reset tracking when location changes
  React.useEffect(() => {
    hasTrackedPageLoad.current = false;
  }, [locationSlug]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading {locationSlug} location...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !config) {
    return (
      <StandardErrorBoundary componentName="LocationPageWrapper">
        <SimpleErrorFallback
          error={new Error(error || `Location not found: ${locationSlug}`)}
          resetError={retry}
          message={`Failed to load ${locationSlug} location page. Please try again.`}
        />
      </StandardErrorBoundary>
    );
  }

  // Data not ready
  if (!isReady || !locationData) {
    return (
      <StandardErrorBoundary componentName="LocationPageWrapper">
        <SimpleErrorFallback
          error={new Error('Location data is incomplete')}
          resetError={retry}
          message="Location data is incomplete. Please try again."
        />
      </StandardErrorBoundary>
    );
  }

  return (
    <StandardErrorBoundary
      componentName={`LocationPage-${locationSlug}`}
      showErrorDetails={false}
    >
      <GenericLocationTemplate
        locationData={locationData}
        specialSections={specialSections}
        seoData={config.seoData}
        layoutVariant={config.layoutVariant}
        customSections={customSections}
      />
    </StandardErrorBoundary>
  );
};

LocationPageWrapper.displayName = 'LocationPageWrapper';

export default LocationPageWrapper;
