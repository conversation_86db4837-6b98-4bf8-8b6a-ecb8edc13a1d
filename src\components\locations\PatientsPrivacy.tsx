import React from 'react';

interface PatientsPrivacyProps {
  title: string;
  subtitle: string;
  description1: string;
  description2: string;
  description3: string;
  drAliashkevichLink?: string;
}

/**
 * Patients Privacy Component
 * Displays patient privacy information and policies
 * Used specifically for location pages that have dedicated privacy sections
 */
const PatientsPrivacy: React.FC<PatientsPrivacyProps> = ({
  title,
  subtitle,
  description1,
  description2,
  description3,
  drAliashkevichLink
}) => {
  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-muted-foreground">
            {subtitle}
          </p>
        </div>

        <div className="max-w-3xl mx-auto mb-12">
          <p className="text-muted-foreground mb-4">
            {description1}
          </p>
          <p className="text-muted-foreground mb-4">
            {description2.split('<PERSON>')[0]}
            {drAliashkevichLink && (
              <a 
                href={drAliashkevichLink} 
                className="text-primary hover:underline" 
                target="_blank" 
                rel="noopener noreferrer"
              >
                Dr Aliashkevich
              </a>
            )}
            {description2.split('Dr Aliashkevich')[1] || ' will never release any information to insurers or other parties without consent.'}
          </p>
          <p className="text-muted-foreground">
            {description3}
          </p>
        </div>
      </div>
    </section>
  );
};

export default PatientsPrivacy;
