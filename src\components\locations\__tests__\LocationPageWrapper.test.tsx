import { render, screen } from '@testing-library/react';
import React from 'react';
import { describe, it, expect, vi } from 'vitest';

import LocationPageWrapper from '../LocationPageWrapper';

// Mock the hooks to avoid actual data loading in tests
vi.mock('@/hooks/useEnhancedLocationData', () => ({
  useEnhancedLocationData: vi.fn(() => ({
    locationData: null,
    config: null,
    specialSections: null,
    isLoading: true,
    error: null,
    isReady: false,
    retry: vi.fn(),
  })),
  useLocationPageState: vi.fn(() => ({
    visitCount: 0,
    lastVisited: null,
    interactionCount: 0,
    trackInteraction: vi.fn(),
  })),
}));

// Mock the GenericLocationTemplate
vi.mock('../GenericLocationTemplate', () => ({
  default: () => <div data-testid="generic-location-template">Generic Location Template</div>,
}));

// Mock shared components
vi.mock('@/components/shared', () => ({
  StandardErrorBoundary: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  SimpleErrorFallback: ({ message }: { message: string }) => <div data-testid="error-fallback">{message}</div>,
}));

describe('LocationPageWrapper', () => {
  it('renders loading state without hooks order violation', () => {
    render(<LocationPageWrapper locationSlug="sunbury" />);
    
    expect(screen.getByText(/Loading sunbury location/)).toBeInTheDocument();
  });

  it('maintains consistent hook order across renders', () => {
    const { rerender } = render(<LocationPageWrapper locationSlug="sunbury" />);
    
    // Re-render with different props to test hook order consistency
    rerender(<LocationPageWrapper locationSlug="heidelberg" />);
    
    expect(screen.getByText(/Loading heidelberg location/)).toBeInTheDocument();
  });
});
