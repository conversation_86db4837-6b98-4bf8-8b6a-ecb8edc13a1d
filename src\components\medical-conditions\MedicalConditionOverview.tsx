import { CheckCircle } from 'lucide-react';
import React from 'react';

import SafeImage from '@/components/SafeImage';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

export interface MedicalConditionOverviewProps {
  title: string;
  description: string[];
  keyPoints?: string[];
  image?: string;
  fallbackImage?: string;
  imageCaption?: string;
  className?: string;
}

const MedicalConditionOverview: React.FC<MedicalConditionOverviewProps> = ({
  title,
  description,
  keyPoints = [],
  image,
  fallbackImage = "https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
  imageCaption,
  className
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <div className={cn(
      deviceInfo.isMobile ? "py-8" : "py-16",
      className
    )}><div className={cn("container", deviceInfo.isMobile ? "px-4" : "")}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className={cn(
              "font-bold mb-6",
              deviceInfo.isMobile ? "text-2xl" : "text-3xl"
            )}>
              {title}
            </h2>
            {description.map((paragraph, index) => (
              <p key={index} className="text-muted-foreground mb-4">
                {paragraph}
              </p>
            ))}
            {keyPoints.length > 0 && (
              <div className="space-y-3 mt-6">
                {keyPoints.map((point, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-success mt-0.5 flex-shrink-0" />
                    <p className="text-sm">{point}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
          {image && (
            <div className="space-y-4">
              <SafeImage
                src={image}
                alt={`${title} illustration`}
                className="w-full h-auto rounded-lg shadow-lg"
                fallbackSrc={fallbackImage}
              />
              {imageCaption && (
                <div className="p-3 rounded-lg bg-muted/50 border border-border/50">
                  <p className="text-sm font-medium text-muted-foreground leading-relaxed">
                    {imageCaption}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>        </div>
  );
};

export default MedicalConditionOverview;
