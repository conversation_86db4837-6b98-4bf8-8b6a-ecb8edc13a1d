import React from 'react';

import { Tabs, TabsContent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

export interface TabContent {
  id: string;
  label: string;
  content: React.ReactNode;
}

export interface MedicalConditionTabsProps {
  title?: string;
  tabs: TabContent[];
  defaultTab?: string;
  className?: string;
}

export const MedicalConditionTabs: React.FC<MedicalConditionTabsProps> = ({
  title,
  tabs,
  defaultTab,
  className
}) => {
  const deviceInfo = useDeviceDetection();

  if (tabs.length === 0) {
    return null;
  }

  const defaultValue = defaultTab || tabs[0]?.id;

  return (
    <div className={cn(
      "bg-muted/30",
      deviceInfo.isMobile ? "py-8" : "py-16",
      className
    )}><div className={cn("container", deviceInfo.isMobile ? "px-4" : "")}>
        {title && (
          <h2 className={cn(
            "font-bold text-center mb-12",
            deviceInfo.isMobile ? "text-2xl mb-8" : "text-3xl"
          )}>
            {title}
          </h2>
        )}

        <Tabs defaultValue={defaultValue} className="w-full max-w-4xl mx-auto">
          <TabsList className={cn(
            "grid mb-8",
            deviceInfo.isMobile ? "grid-cols-1 h-auto" : 
            tabs.length === 2 ? "grid-cols-2" :
            tabs.length === 3 ? "grid-cols-3" :
            tabs.length === 4 ? "grid-cols-4" :
            "grid-cols-2 lg:grid-cols-4"
          )}>
            {tabs.map((tab) => (
              <TabsTrigger 
                key={tab.id} 
                value={tab.id} 
                className={cn(
                  "text-center",
                  deviceInfo.isMobile ? "py-3" : "py-3"
                )}
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>

          {tabs.map((tab) => (
            <TabsContent 
              key={tab.id} 
              value={tab.id} 
              className="medical-card p-6 rounded-lg shadow-md"
            >
              {tab.content}
            </TabsContent>
          ))}
        </Tabs>
      </div>        </div>
  );
};

export default MedicalConditionTabs;
