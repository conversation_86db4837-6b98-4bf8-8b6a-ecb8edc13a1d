import React from 'react';

import { MedicalConditionTabs, TabContent } from './MedicalConditionTabs';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const SpinalStenosisTypesSection: React.FC = () => {
  const typesContent = (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Central Canal Stenosis</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-3">
            Narrowing of the central spinal canal where the spinal cord and cauda equina 
            are located. Most common type affecting the entire neural bundle.
          </p>
          <Badge variant="muted">Most Severe</Badge>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Lateral Recess Stenosis</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-3">
            Narrowing of the lateral recesses where nerve roots exit the central canal. 
            Often caused by hypertrophied ligamentum flavum or facet joint arthritis.
          </p>
          <Badge variant="outline">Moderate</Badge>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Foraminal Stenosis</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-3">
            Narrowing of the neural foramina where individual nerve roots exit the spine. 
            Can cause specific dermatomal symptoms.
          </p>
          <Badge variant="secondary">Localized</Badge>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Extraforaminal Stenosis</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-3">
            Narrowing outside the neural foramen, often caused by disc herniation 
            or osteophyte formation. Less common but can be challenging to treat.
          </p>
          <Badge variant="outline">Rare</Badge>
        </CardContent>
      </Card>
    </div>
  );

  const anatomyContent = (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div>
        <h3 className="text-xl font-bold mb-4">Spinal Canal Components</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-semibold text-primary mb-2">Central Canal</h4>
            <p className="text-sm text-muted-foreground">
              Houses the spinal cord (cervical/thoracic) or cauda equina (lumbar). 
              Normal diameter: 15-25mm in lumbar spine, 14-23mm in cervical spine.
            </p>
          </div>
          <div>
            <h4 className="font-semibold text-primary mb-2">Lateral Recesses</h4>
            <p className="text-sm text-muted-foreground">
              Triangular spaces where nerve roots travel before entering foramina. 
              Bounded by disc, pedicle, and ligamentum flavum.
            </p>
          </div>
          <div>
            <h4 className="font-semibold text-primary mb-2">Neural Foramina</h4>
            <p className="text-sm text-muted-foreground">
              Openings between vertebrae where nerve roots exit. Bounded by 
              pedicles, facet joints, and intervertebral discs.
            </p>
          </div>
        </div>
      </div>
      <div className="relative">
        <SafeImage
          src="/images/spine-anatomy/spinal-canal.jpg"
          alt="Spinal canal anatomy"
          className="w-full h-auto rounded-lg"
          fallbackSrc="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
        />
      </div>
    </div>
  );

  const locationsContent = (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg text-centre">Cervical Stenosis</CardTitle>
          </CardHeader>
          <CardContent className="text-centre">
            <p className="text-sm text-muted-foreground mb-3">
              C3-C7 most commonly affected
            </p>
            <p className="text-xs">
              Symptoms: Myelopathy, neck pain, arm symptoms, balance issues
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-lg text-centre">Thoracic Stenosis</CardTitle>
          </CardHeader>
          <CardContent className="text-centre">
            <p className="text-sm text-muted-foreground mb-3">
              Least common location
            </p>
            <p className="text-xs">
              Symptoms: Myelopathy, gait disturbance, bowel/bladder dysfunction
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-lg text-centre">Lumbar Stenosis</CardTitle>
          </CardHeader>
          <CardContent className="text-centre">
            <p className="text-sm text-muted-foreground mb-3">
              L3-L5 most commonly affected
            </p>
            <p className="text-xs">
              Symptoms: Neurogenic claudication, back pain, leg symptoms
            </p>
          </CardContent>
        </Card>
      </div>
      
      <div className="text-centre">
        <SafeImage
          src="/images/spine-anatomy/vertebrae.jpg"
          alt="Spine anatomy showing common stenosis locations"
          className="w-full max-w-md mx-auto h-auto rounded-lg"
          fallbackSrc="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
        />
      </div>
    </div>
  );

  const tabs: TabContent[] = [
    {
      id: 'types',
      label: 'Types of Stenosis',
      content: typesContent
    },
    {
      id: 'anatomy',
      label: 'Spinal Anatomy',
      content: anatomyContent
    },
    {
      id: 'locations',
      label: 'Common Locations',
      content: locationsContent
    }
  ];

  return (
    <MedicalConditionTabs
      title="Types of Spinal Stenosis & Anatomy"
      tabs={tabs}
      defaultTab="types"
    />
  );
};

export default SpinalStenosisTypesSection;
