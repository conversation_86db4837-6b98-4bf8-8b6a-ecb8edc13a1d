import React, { useEffect, useRef } from 'react';

import { announceToScreenReader } from '@/lib/accessibility';

interface AccessibilityAnnouncerProps {
  message: string;
  priority?: 'polite' | 'assertive';
  delay?: number;
}

/**
 * Component that announces messages to screen readers
 * Used for dynamic content updates in the brain tumour guide
 */
export function AccessibilityAnnouncer({ 
  message, 
  priority = 'polite', 
  delay = 100 
}: AccessibilityAnnouncerProps) {
  const previousMessage = useRef<string>('');

  useEffect(() => {
    if (message && message !== previousMessage.current) {
      const timer = setTimeout(() => {
        announceToScreenReader(message, priority);
        previousMessage.current = message;
      }, delay);

      return () => clearTimeout(timer);
    }
  }, [message, priority, delay]);

  return null; // This component doesn't render anything visible
}

interface SkipLinkProps {
  targetId: string;
  children: React.ReactNode;
  className?: string;
}

/**
 * Skip link component for keyboard navigation
 */
export function SkipLink({ targetId, children, className = '' }: SkipLinkProps) {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    const target = document.getElementById(targetId);
    if (target) {
      target.focus();
      target.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <a
      href={`#${targetId}`}
      onClick={handleClick}
      className={`
        sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 
        bg-primary text-primary-foreground px-4 py-2 rounded-md z-50
        focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2
        ${className}
      `}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleClick(e as any);
        }
      }}
    >
      {children}
    </a>
  );
}

interface FocusableRegionProps {
  children: React.ReactNode;
  label: string;
  description?: string;
  className?: string;
  tabIndex?: number;
}

/**
 * Creates a focusable region with proper ARIA labeling
 */
export function FocusableRegion({ 
  children, 
  label, 
  description, 
  className = '',
  tabIndex = -1 
}: FocusableRegionProps) {
  const regionRef = useRef<HTMLDivElement>(null);

  return (
    <div
      ref={regionRef}
      role="region"
      aria-label={label}
      aria-describedby={description ? `${label.replace(/\s+/g, '-').toLowerCase()}-desc` : undefined}
      tabIndex={tabIndex}
      className={`focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-md ${className}`}
      onKeyDown={(e) => {
        if (e.key === 'Escape' && regionRef.current) {
          regionRef.current.blur();
        }
      }}
    >
      {description && (
        <div 
          id={`${label.replace(/\s+/g, '-').toLowerCase()}-desc`}
          className="sr-only"
        >
          {description}
        </div>
      )}
      {children}
    </div>
  );
}

interface ProgressIndicatorProps {
  current: number;
  total: number;
  label: string;
  className?: string;
}

/**
 * Accessible progress indicator for assessments
 */
export function ProgressIndicator({ 
  current, 
  total, 
  label, 
  className = '' 
}: ProgressIndicatorProps) {
  const percentage = Math.round((current / total) * 100);

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex justify-between items-center">
        <span className="text-sm font-medium">{label}</span>
        <span className="text-sm text-muted-foreground">
          {current} of {total}
        </span>
      </div>
      <div
        role="progressbar"
        aria-valuenow={current}
        aria-valuemin={0}
        aria-valuemax={total}
        aria-label={`${label}: ${current} of ${total} completed`}
        className="w-full bg-secondary rounded-full h-2"
      >
        <div
          className="bg-primary h-2 rounded-full transition-all duration-300 ease-in-out"
          style={{ width: `${percentage}%` }}
        />
      </div>
      <div className="sr-only">
        Progress: {percentage}% complete. {current} out of {total} items.
      </div>
    </div>
  );
}

interface KeyboardShortcutsProps {
  shortcuts: Array<{
    key: string;
    description: string;
    action: () => void;
  }>;
}

/**
 * Keyboard shortcuts handler for enhanced navigation
 */
export function KeyboardShortcuts({ shortcuts }: KeyboardShortcutsProps) {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle shortcuts when not in input fields
      if (e.target instanceof HTMLInputElement || 
          e.target instanceof HTMLTextAreaElement || 
          e.target instanceof HTMLSelectElement) {
        return;
      }

      const shortcut = shortcuts.find(s => {
        const keys = s.key.toLowerCase().split('+');
        const hasCtrl = keys.includes('ctrl') && e.ctrlKey;
        const hasAlt = keys.includes('alt') && e.altKey;
        const hasShift = keys.includes('shift') && e.shiftKey;
        const mainKey = keys[keys.length - 1];
        
        return e.key.toLowerCase() === mainKey && 
               (!keys.includes('ctrl') || hasCtrl) &&
               (!keys.includes('alt') || hasAlt) &&
               (!keys.includes('shift') || hasShift);
      });

      if (shortcut) {
        e.preventDefault();
        shortcut.action();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts]);

  return null;
}

interface LiveRegionProps {
  children: React.ReactNode;
  priority?: 'polite' | 'assertive';
  atomic?: boolean;
  className?: string;
}

/**
 * Live region for dynamic content announcements
 */
export function LiveRegion({ 
  children, 
  priority = 'polite', 
  atomic = true,
  className = '' 
}: LiveRegionProps) {
  return (
    <div
      aria-live={priority}
      aria-atomic={atomic}
      className={`sr-only ${className}`}
    >
      {children}
    </div>
  );
}

export default {
  AccessibilityAnnouncer,
  SkipLink,
  FocusableRegion,
  ProgressIndicator,
  KeyboardShortcuts,
  LiveRegion
};
