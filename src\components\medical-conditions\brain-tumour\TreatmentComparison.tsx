import { <PERSON><PERSON>ircle, XCircle, AlertTriangle, Clock, Target, TrendingUp } from 'lucide-react';
import React, { useState, useId } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface TreatmentOption {
  name: string;
  description: string;
  indications: string[];
  advantages: string[];
  disadvantages: string[];
  successRate: string;
  recoveryTime: string;
  sideEffects: string[];
}

interface TreatmentComparisonProps {
  treatments: TreatmentOption[];
  title?: string;
  description?: string;
}

export function TreatmentComparison({
  treatments,
  title = "Treatment Options Comparison",
  description = "Compare different treatment modalities to understand the best approach for your specific situation."
}: TreatmentComparisonProps) {
  const deviceInfo = useDeviceDetection();
  const [selectedTreatment, setSelectedTreatment] = useState<string>(treatments[0]?.name || '');
  const comparisonId = useId();
  const selectedOption = treatments.find(t => t.name === selectedTreatment);

  return (
    <section
      className={cn("py-16", deviceInfo.isMobile ? "px-4" : "")}
      aria-labelledby={`${comparisonId}-title`}
      role="region"
    >
      <div className="container">
        <div className="text-center mb-12">
          <h2
            id={`${comparisonId}-title`}
            className={cn(
              "font-bold mb-4",
              deviceInfo.isMobile ? "text-2xl" : "text-3xl"
            )}
          >
            {title}
          </h2>
          <p
            className={cn(
              "text-muted-foreground max-w-3xl mx-auto",
              deviceInfo.isMobile ? "text-sm" : "text-lg"
            )}
            id={`${comparisonId}-description`}
          >
            {description}
          </p>
        </div>

        <Tabs value={selectedTreatment} onValueChange={setSelectedTreatment} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-8",
            deviceInfo.isMobile ? "grid-cols-1 h-auto" : `grid-cols-${Math.min(treatments.length, 3)}`
          )}>
            {treatments.map((treatment) => (
              <TabsTrigger 
                key={treatment.name} 
                value={treatment.name}
                className={cn(
                  "text-center",
                  deviceInfo.isMobile ? "py-3 text-sm" : "py-4"
                )}
              >
                {treatment.name}
              </TabsTrigger>
            ))}
          </TabsList>

          {treatments.map((treatment) => (
            <TabsContent key={treatment.name} value={treatment.name} className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5 text-primary" />
                    {treatment.name}
                  </CardTitle>
                  <CardDescription>{treatment.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Quick Stats */}
                  <div className={cn(
                    "grid gap-4",
                    deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                  )}>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-success" />
                      <span className="font-medium">Success Rate:</span>
                      <Badge variant="secondary">{treatment.successRate}</Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-info" />
                      <span className="font-medium">Recovery Time:</span>
                      <Badge variant="outline">{treatment.recoveryTime}</Badge>
                    </div>
                  </div>

                  {/* Indications */}
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <Target className="h-4 w-4 text-primary" />
                      Best For
                    </h4>
                    <ul className="space-y-2">
                      {treatment.indications.map((indication, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <CheckCircle className="h-4 w-4 text-success mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{indication}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Advantages vs Disadvantages */}
                  <div className={cn(
                    "grid gap-6",
                    deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                  )}>
                    <div>
                      <h4 className="font-semibold mb-3 flex items-center gap-2 text-success">
                        <CheckCircle className="h-4 w-4" />
                        Advantages
                      </h4>
                      <ul className="space-y-2">
                        {treatment.advantages.map((advantage, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                            <span className="text-sm">{advantage}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-3 flex items-center gap-2 text-info">
                        <AlertTriangle className="h-4 w-4" />
                        Considerations
                      </h4>
                      <ul className="space-y-2">
                        {treatment.disadvantages.map((disadvantage, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                            <span className="text-sm">{disadvantage}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* Side Effects */}
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center gap-2 text-foreground">
                      <XCircle className="h-4 w-4" />
                      Potential Side Effects
                    </h4>
                    <div className={cn(
                      "grid gap-2",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                    )}>
                      {treatment.sideEffects.map((effect, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-muted rounded-full flex-shrink-0" />
                          <span className="text-sm">{effect}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <Card className="bg-primary/5 border-primary/20">
            <CardContent className="pt-6">
              <h3 className="font-semibold mb-2">Need Help Choosing?</h3>
              <p className="text-muted-foreground mb-4">
                Our neurosurgical team will work with you to determine the best treatment approach based on your specific condition and circumstances.
              </p>
              <Button size={deviceInfo.isMobile ? "default" : "lg"}>
                Schedule a Consultation
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default TreatmentComparison;
