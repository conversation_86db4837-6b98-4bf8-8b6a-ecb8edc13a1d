import { <PERSON><PERSON><PERSON><PERSON>, ArrowRight, ArrowLeft, Target, Brain, Activity, AlertTriangle } from 'lucide-react';
import React, { useState, useId } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface Question {
  id: string;
  question: string;
  description?: string;
  options: Array<{
    value: string;
    label: string;
    description?: string;
    weight: number;
  }>;
}

interface TreatmentRecommendation {
  treatment: string;
  confidence: number;
  reasons: string[];
  considerations: string[];
  nextSteps: string[];
}

const treatmentQuestions: Question[] = [
  {
    id: 'tumor-type',
    question: 'What type of brain tumor has been diagnosed?',
    description: 'The type of tumor significantly influences treatment options',
    options: [
      { value: 'glioma-low', label: 'Low-grade Glioma (Grade I-II)', weight: 1 },
      { value: 'glioma-high', label: 'High-grade Glioma (Grade III-IV)', weight: 3 },
      { value: 'meningioma', label: 'Meningioma', weight: 2 },
      { value: 'metastatic', label: 'Metastatic (Secondary)', weight: 4 },
      { value: 'unknown', label: 'Not yet determined', weight: 0 }
    ]
  },
  {
    id: 'tumor-location',
    question: 'Where is the tumor located?',
    description: 'Location affects surgical accessibility and treatment approach',
    options: [
      { value: 'accessible', label: 'Easily accessible area', weight: 1 },
      { value: 'eloquent', label: 'Near critical brain areas (speech, motor)', weight: 3 },
      { value: 'deep', label: 'Deep brain structures', weight: 4 },
      { value: 'brainstem', label: 'Brainstem', weight: 5 },
      { value: 'multiple', label: 'Multiple locations', weight: 4 }
    ]
  },
  {
    id: 'tumor-size',
    question: 'What is the approximate size of the tumor?',
    description: 'Size influences treatment urgency and approach',
    options: [
      { value: 'small', label: 'Small (< 2cm)', weight: 1 },
      { value: 'medium', label: 'Medium (2-4cm)', weight: 2 },
      { value: 'large', label: 'Large (> 4cm)', weight: 3 },
      { value: 'unknown', label: 'Size not specified', weight: 0 }
    ]
  },
  {
    id: 'symptoms',
    question: 'What symptoms are you experiencing?',
    description: 'Symptoms help determine treatment urgency',
    options: [
      { value: 'none', label: 'No symptoms (incidental finding)', weight: 1 },
      { value: 'mild', label: 'Mild symptoms (headaches, minor changes)', weight: 2 },
      { value: 'moderate', label: 'Moderate symptoms affecting daily life', weight: 3 },
      { value: 'severe', label: 'Severe symptoms or neurological deficits', weight: 4 },
      { value: 'emergency', label: 'Emergency symptoms (seizures, severe headache)', weight: 5 }
    ]
  },
  {
    id: 'age-health',
    question: 'What is your age and overall health status?',
    description: 'Age and health affect treatment tolerance and options',
    options: [
      { value: 'young-healthy', label: 'Under 50, excellent health', weight: 1 },
      { value: 'middle-healthy', label: '50-70, good health', weight: 2 },
      { value: 'older-healthy', label: 'Over 70, good health', weight: 3 },
      { value: 'health-issues', label: 'Any age with significant health issues', weight: 4 }
    ]
  }
];

export function TreatmentDecisionHelper() {
  const deviceInfo = useDeviceDetection();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showResults, setShowResults] = useState(false);
  const helperId = useId();

  const progress = ((currentQuestion + 1) / treatmentQuestions.length) * 100;
  const isLastQuestion = currentQuestion === treatmentQuestions.length - 1;
  const canProceed = answers[treatmentQuestions[currentQuestion]?.id];

  const handleAnswer = (value: string) => {
    setAnswers(prev => ({
      ...prev,
      [treatmentQuestions[currentQuestion].id]: value
    }));
  };

  const handleNext = () => {
    if (isLastQuestion) {
      setShowResults(true);
    } else {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const calculateRecommendation = (): TreatmentRecommendation => {
    const weights = Object.entries(answers).reduce((acc, [questionId, answer]) => {
      const question = treatmentQuestions.find(q => q.id === questionId);
      const option = question?.options.find(o => o.value === answer);
      return acc + (option?.weight || 0);
    }, 0);

    // Simple algorithm based on total weight
    if (weights <= 6) {
      return {
        treatment: 'Conservative Management or Observation',
        confidence: 85,
        reasons: [
          'Low-risk tumor characteristics',
          'Minimal or no symptoms',
          'Favorable location and size'
        ],
        considerations: [
          'Regular monitoring with MRI scans',
          'Symptom management as needed',
          'Quality of life preservation'
        ],
        nextSteps: [
          'Schedule regular follow-up appointments',
          'Discuss monitoring schedule with your doctor',
          'Learn about warning signs to watch for'
        ]
      };
    } else if (weights <= 12) {
      return {
        treatment: 'Surgical Resection',
        confidence: 90,
        reasons: [
          'Tumor characteristics favor surgical approach',
          'Symptoms warrant intervention',
          'Good surgical candidacy'
        ],
        considerations: [
          'Image-guided surgery for precision',
          'Possible awake craniotomy if near eloquent areas',
          'Post-operative recovery planning'
        ],
        nextSteps: [
          'Consult with neurosurgeon',
          'Pre-operative planning and imaging',
          'Discuss surgical options and risks'
        ]
      };
    } else {
      return {
        treatment: 'Multimodal Treatment Approach',
        confidence: 95,
        reasons: [
          'Complex tumor characteristics',
          'High-grade or aggressive features',
          'Multiple treatment modalities needed'
        ],
        considerations: [
          'Surgery followed by radiation therapy',
          'Possible chemotherapy',
          'Multidisciplinary team approach'
        ],
        nextSteps: [
          'Urgent consultation with neuro-oncology team',
          'Comprehensive treatment planning',
          'Consider clinical trial options'
        ]
      };
    }
  };

  const recommendation = showResults ? calculateRecommendation() : null;

  if (showResults && recommendation) {
    return (
      <section className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}>
        <div className="container max-w-4xl">
          <Card className="border-success">
            <CardHeader className="text-center">
              <div className="flex items-center justify-center gap-2 mb-4">
                <Target className="h-8 w-8 text-success" />
                <CardTitle className="text-2xl text-success">Treatment Recommendation</CardTitle>
              </div>
              <CardDescription>
                Based on your responses, here's a personalized treatment guidance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Main Recommendation */}
              <div className="text-center p-6 bg-success rounded-lg border border-success">
                <h3 className="text-xl font-semibold text-success mb-2">
                  Recommended Approach: {recommendation.treatment}
                </h3>
                <div className="flex items-center justify-center gap-2">
                  <span className="text-sm text-success">Confidence Level:</span>
                  <Badge variant="secondary" className="bg-success-light text-foreground">
                    {recommendation.confidence}%
                  </Badge>
                </div>
              </div>

              {/* Detailed Sections */}
              <div className={cn("grid gap-6", deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3")}>
                <div>
                  <h4 className="font-semibold mb-3 flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-success" />
                    Key Reasons
                  </h4>
                  <ul className="space-y-2">
                    {recommendation.reasons.map((reason, index) => (
                      <li key={index} className="text-sm flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-success rounded-full mt-2 flex-shrink-0" />
                        {reason}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold mb-3 flex items-center gap-2">
                    <Brain className="h-4 w-4 text-info" />
                    Considerations
                  </h4>
                  <ul className="space-y-2">
                    {recommendation.considerations.map((consideration, index) => (
                      <li key={index} className="text-sm flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-info rounded-full mt-2 flex-shrink-0" />
                        {consideration}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold mb-3 flex items-center gap-2">
                    <ArrowRight className="h-4 w-4 text-medical-blue" />
                    Next Steps
                  </h4>
                  <ul className="space-y-2">
                    {recommendation.nextSteps.map((step, index) => (
                      <li key={index} className="text-sm flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-medical-blue rounded-full mt-2 flex-shrink-0" />
                        {step}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Important Disclaimer */}
              <div className="bg-info border border-info rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-info mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-info mb-1">Important Disclaimer</h4>
                    <p className="text-sm text-info">
                      This tool provides general guidance only and does not replace professional medical advice. 
                      Always consult with your healthcare team for personalized treatment decisions.
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className={cn("flex gap-3", deviceInfo.isMobile ? "flex-col" : "flex-row justify-center")}>
                <Button size="lg">
                  Schedule Consultation
                </Button>
                <Button variant="outline" size="lg" onClick={() => {
                  setShowResults(false);
                  setCurrentQuestion(0);
                  setAnswers({});
                }}>
                  Start Over
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    );
  }

  return (
    <section className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container max-w-3xl">
        <div className="text-center mb-8">
          <h2 className={cn("font-bold mb-4", deviceInfo.isMobile ? "text-2xl" : "text-3xl")}>
            Treatment Decision Helper
          </h2>
          <p className={cn("text-muted-foreground", deviceInfo.isMobile ? "text-sm" : "text-lg")}>
            Answer a few questions to receive personalized treatment guidance
          </p>
        </div>

        {/* Progress */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Progress</span>
            <span className="text-sm text-muted-foreground">
              {currentQuestion + 1} of {treatmentQuestions.length}
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Question Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-primary" />
              Question {currentQuestion + 1}
            </CardTitle>
            <CardDescription className="text-lg font-medium">
              {treatmentQuestions[currentQuestion]?.question}
            </CardDescription>
            {treatmentQuestions[currentQuestion]?.description && (
              <p className="text-sm text-muted-foreground">
                {treatmentQuestions[currentQuestion].description}
              </p>
            )}
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={answers[treatmentQuestions[currentQuestion]?.id] || ''}
              onValueChange={handleAnswer}
              className="space-y-3"
            >
              {treatmentQuestions[currentQuestion]?.options.map((option) => (
                <div key={option.value} className="flex items-start space-x-3 p-3 rounded-lg border hover:bg-muted/50">
                  <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
                  <div className="flex-1">
                    <Label htmlFor={option.value} className="font-medium cursor-pointer">
                      {option.label}
                    </Label>
                    {option.description && (
                      <p className="text-sm text-muted-foreground mt-1">{option.description}</p>
                    )}
                  </div>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className={cn("flex justify-between mt-8", deviceInfo.isMobile ? "flex-col gap-3" : "")}>
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentQuestion === 0}
            className={deviceInfo.isMobile ? "order-2" : ""}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button
            onClick={handleNext}
            disabled={!canProceed}
            className={deviceInfo.isMobile ? "order-1" : ""}
          >
            {isLastQuestion ? 'Get Recommendation' : 'Next'}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </section>
  );
}

export default TreatmentDecisionHelper;
