/**
 * Brain Tumour Components Barrel Export
 * Provides clean imports for all brain tumour related components
 */

export { default as BrainTumourTypes } from './BrainTumourTypes';
export { default as TreatmentComparison } from './TreatmentComparison';
export { default as WarningSigns } from './WarningSigns';
export { default as SymptomAssessment } from './SymptomAssessment';
export { default as TreatmentDecisionHelper } from './TreatmentDecisionHelper';
export { default as JourneyTracker } from './JourneyTracker';
export { default as AccessibilityEnhanced } from './AccessibilityEnhanced';

// Re-export types for convenience
export type { BrainTumourData } from '@/data/conditions/brainTumour';
