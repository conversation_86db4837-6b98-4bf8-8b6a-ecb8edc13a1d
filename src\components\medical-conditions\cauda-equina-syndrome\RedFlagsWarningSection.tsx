import { Alert<PERSON>riangle, Phone, Clock, Droplets, Activity, Zap, ChevronDown, ChevronUp } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface RedFlagSymptom {
  symptom: string;
  description: string;
  mechanism: string;
  urgency: 'immediate' | 'urgent' | 'prompt';
}

interface RedFlagsWarningSectionProps {
  title: string;
  description: string;
  emergencySymptoms: RedFlagSymptom[];
}

export function RedFlagsWarningSection({ 
  title, 
  description, 
  emergencySymptoms 
}: RedFlagsWarningSectionProps) {
  const deviceInfo = useDeviceDetection();
  const [expandedSymptom, setExpandedSymptom] = useState<string | null>(null);

  const toggleExpanded = (symptom: string) => {
    setExpandedSymptom(expandedSymptom === symptom ? null : symptom);
  };

  const getSymptomIcon = (symptom: string) => {
    if (symptom.includes('Bladder')) return Droplets;
    if (symptom.includes('Bowel')) return Activity;
    if (symptom.includes('Saddle')) return Zap;
    if (symptom.includes('Leg')) return Activity;
    return AlertTriangle;
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return 'text-foreground bg-muted-light border-border/70';
      case 'urgent': return 'text-foreground bg-info-light border-info/30';
      default: return 'text-foreground bg-info-light border-info/30';
    }
  };

  const getUrgencyBadge = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return { label: 'IMMEDIATE', color: 'badge-emergency' };
      case 'urgent': return { label: 'URGENT', color: 'badge-info' };
      default: return { label: 'PROMPT', color: 'badge-info' };
    }
  };

  const getTimeframe = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return 'Go to emergency department now';
      case 'urgent': return 'Seek medical attention within 2-4 hours';
      default: return 'Contact healthcare provider within 24 hours';
    }
  };

  return (
    <section className={cn("py-16 bg-muted/30 dark:bg-muted/20", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-center mb-12">
          <h2 className={cn(
            "font-bold mb-4 text-foreground",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* Emergency Alert */}
        <div className="mb-12">
          <Card className="bg-muted/50 dark:bg-muted/30 border-border dark:border-border/70">
            <CardContent className="pt-6">
              <div className="flex items-start gap-4">
                <div className="p-3 rounded-full bg-muted">
                  <AlertTriangle className="h-6 w-6 text-foreground-foreground" />
                </div>
                <div className="flex-1">
                  <h3 className="font-bold text-foreground mb-2">MEDICAL EMERGENCY</h3>
                  <p className="text-foreground mb-4 font-medium">
                    Cauda equina syndrome is a surgical emergency. If you experience any combination of these symptoms,
                    seek immediate medical attention. Delayed treatment can result in permanent disability.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <Button variant="default">
                      <Phone className="mr-2 h-4 w-4" />
                      Call 000 Now
                    </Button>
                    <Button variant="outline" className="border-border text-foreground hover:bg-muted/30">
                      Find Emergency Department
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Red Flag Symptoms */}
        <div className="space-y-6">
          {emergencySymptoms.map((symptom, index) => {
            const Icon = getSymptomIcon(symptom.symptom);
            const isExpanded = expandedSymptom === symptom.symptom;
            const urgencyBadge = getUrgencyBadge(symptom.urgency);
            
            return (
              <Card key={index} className={cn("transition-all duration-200", getUrgencyColor(symptom.urgency))}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-full medical-card/80 border border-border/50">
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{symptom.symptom}</CardTitle>
                        <CardDescription className="text-sm">{symptom.description}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={urgencyBadge.color}>
                        {urgencyBadge.label}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(symptom.symptom)}
                        className="flex items-center gap-1"
                      >
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <div className="space-y-4">
                        {/* Mechanism */}
                        <div className="bg-background/50 rounded-lg p-4">
                          <h4 className="font-semibold mb-2">Why This Happens</h4>
                          <p className="text-sm text-muted-foreground">{symptom.mechanism}</p>
                        </div>

                        {/* Action Required */}
                        <div className="bg-background/70 rounded-lg p-4 border">
                          <h4 className="font-semibold mb-2 flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            Action Required
                          </h4>
                          <p className="text-sm font-medium mb-2">{getTimeframe(symptom.urgency)}</p>
                          
                          {symptom.urgency === 'immediate' && (
                            <div className="bg-muted/30 border border-border rounded p-3 mt-3">
                              <p className="text-sm text-foreground">
                                <strong>Emergency Action:</strong> Call 000 immediately or go to the nearest emergency department.
                                Tell them you may have cauda equina syndrome - this is a surgical emergency.
                              </p>
                            </div>
                          )}
                          
                          {symptom.urgency === 'urgent' && (
                            <div className="bg-info border border-info rounded p-3 mt-3">
                              <p className="text-sm text-info">
                                <strong>Urgent Action:</strong> Contact your GP immediately or go to an urgent care centre. 
                                Do not wait - early treatment is crucial for recovery.
                              </p>
                            </div>
                          )}
                        </div>

                        {/* What to Tell Medical Staff */}
                        <div className="bg-info-light border border-info/30 rounded-lg p-4">
                          <h4 className="font-semibold text-foreground mb-2">What to Tell Medical Staff</h4>
                          <ul className="text-sm text-info space-y-1">
                            <li>• "I may have cauda equina syndrome"</li>
                            <li>• Describe your specific symptoms clearly</li>
                            <li>• Mention when symptoms started</li>
                            <li>• Emphasise this is a surgical emergency</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* Symptom Combinations */}
        <div className="mt-12">
          <Card className="bg-muted/50 dark:bg-muted/30 border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-foreground">
                <AlertTriangle className="h-5 w-5 text-foreground" />
                Dangerous Symptom Combinations
              </CardTitle>
              <CardDescription className="text-muted-foreground">
                These combinations of symptoms are particularly concerning and require immediate attention
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-4",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div className="medical-card p-4 rounded-lg border border-border/70">
                  <h4 className="font-semibold text-foreground mb-2">Classic Triad</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Severe back pain</li>
                    <li>• Bladder dysfunction</li>
                    <li>• Saddle anaesthesia</li>
                  </ul>
                  <Badge variant="muted" className="mt-2">EMERGENCY</Badge>
                </div>

                <div className="medical-card p-4 rounded-lg border border-border/70">
                  <h4 className="font-semibold text-foreground mb-2">Progressive Pattern</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Worsening leg weakness</li>
                    <li>• Spreading numbness</li>
                    <li>• New bladder symptoms</li>
                  </ul>
                  <Badge variant="muted" className="mt-2">EMERGENCY</Badge>
                </div>
                
                <div className="bg-background p-4 rounded-lg border border-info">
                  <h4 className="font-semibold text-info mb-2">Early Warning</h4>
                  <ul className="text-sm text-info space-y-1">
                    <li>• Severe back pain</li>
                    <li>• Bilateral leg symptoms</li>
                    <li>• Perineal tingling</li>
                  </ul>
                  <Badge className="mt-2 bg-primary text-primary-foreground">URGENT</Badge>
                </div>
                
                <div className="bg-background p-4 rounded-lg border border-info">
                  <h4 className="font-semibold text-info mb-2">Incomplete Syndrome</h4>
                  <ul className="text-sm text-info space-y-1">
                    <li>• Partial bladder control</li>
                    <li>• Some sensation preserved</li>
                    <li>• Variable leg weakness</li>
                  </ul>
                  <Badge className="mt-2 bg-primary text-primary-foreground">URGENT</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Time-Critical Information */}
        <div className="mt-12">
          <Card className="bg-muted">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-foreground">
                <Clock className="h-5 w-5" />
                Why Time is Critical
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div className="text-center">
                  <div className="w-12 h-12 bg-success text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    24
                  </div>
                  <h4 className="font-semibold mb-2">0-24 Hours</h4>
                  <p className="text-sm text-muted-foreground">
                    Optimal window for surgery with best recovery outcomes
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    48
                  </div>
                  <h4 className="font-semibold mb-2">24-48 Hours</h4>
                  <p className="text-sm text-muted-foreground">
                    Good outcomes still possible with urgent intervention
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-muted text-foreground-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    72
                  </div>
                  <h4 className="font-semibold mb-2">48-72 Hours</h4>
                  <p className="text-sm text-muted-foreground">
                    Limited recovery potential - permanent damage likely
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Emergency Contacts */}
        <div className="mt-12">
          <Card className="bg-muted/50 dark:bg-muted/30 border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-foreground">
                <Phone className="h-5 w-5" />
                Emergency Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-4",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div className="medical-card p-4 rounded-lg border border-border/70 text-center">
                  <h4 className="font-bold text-foreground mb-2">Emergency Services</h4>
                  <div className="text-enhanced-heading text-2xl font-bold text-foreground mb-2">000</div>
                  <p className="text-sm text-foreground/80">For immediate ambulance</p>
                </div>

                <div className="medical-card p-4 rounded-lg border border-border/70 text-center">
                  <h4 className="font-bold text-foreground mb-2">Emergency Department</h4>
                  <div className="text-lg font-bold text-foreground mb-2">Go Immediately</div>
                  <p className="text-sm text-foreground/80">Nearest hospital with neurosurgery</p>
                </div>

                <div className="medical-card p-4 rounded-lg border border-border/70 text-center">
                  <h4 className="font-bold text-foreground mb-2">What to Say</h4>
                  <div className="text-sm font-bold text-foreground mb-2">"Cauda Equina Syndrome"</div>
                  <p className="text-sm text-foreground/80">Surgical emergency</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default RedFlagsWarningSection;
