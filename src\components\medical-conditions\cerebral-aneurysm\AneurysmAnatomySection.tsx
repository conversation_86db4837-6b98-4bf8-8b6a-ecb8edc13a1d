import { Brain, Droplets, Target, Info, ChevronDown, ChevronUp, Activity } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface BloodVessel {
  vessel: string;
  description: string;
  commonSites: string[];
  characteristics: string[];
}

interface AneurysmAnatomySectionProps {
  title: string;
  description: string;
  bloodVessels: BloodVessel[];
}

export function AneurysmAnatomySection({ 
  title, 
  description, 
  bloodVessels 
}: AneurysmAnatomySectionProps) {
  const deviceInfo = useDeviceDetection();
  const [expandedVessel, setExpandedVessel] = useState<string | null>(null);

  const toggleExpanded = (vessel: string) => {
    setExpandedVessel(expandedVessel === vessel ? null : vessel);
  };

  const getVesselIcon = (vessel: string) => {
    if (vessel.includes('Anterior')) return Target;
    if (vessel.includes('Middle')) return Brain;
    if (vessel.includes('Posterior')) return Activity;
    return Droplets;
  };

  const getVesselColor = (vessel: string) => {
    if (vessel.includes('Anterior')) return 'text-foreground bg-info border-info';
    if (vessel.includes('Middle')) return 'text-foreground bg-success border-success';
    if (vessel.includes('Posterior')) return 'text-medical-blue bg-medical-blue border-medical-blue';
    return 'text-foreground bg-muted border-border';
  };

  const getRiskLevel = (characteristics: string[]) => {
    const riskIndicators = characteristics.join(' ').toLowerCase();
    if (riskIndicators.includes('most common') || riskIndicators.includes('30-35%')) {
      return { level: 'High Frequency', color: 'bg-muted text-foreground' };
    }
    if (riskIndicators.includes('second most') || riskIndicators.includes('20-25%')) {
      return { level: 'Moderate Frequency', color: 'bg-info-light/30 text-foreground' };
    }
    return { level: 'Lower Frequency', color: 'bg-success-light/30 text-foreground' };
  };

  return (
    <section className={cn("py-16", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-center mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* Anatomical Overview */}
        <div className="mb-12">
          <Card className="bg-muted">
            <CardContent className="pt-6">
              <div className={cn(
                "grid gap-8 items-center",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h3 className="text-xl font-semibold mb-4">Circle of Willis Overview</h3>
                  <p className="text-muted-foreground mb-4">
                    The Circle of Willis is a circular arrangement of arteries at the base of the brain 
                    that provides critical blood supply. Most cerebral aneurysms occur at branching points 
                    within this vascular network, where blood flow creates areas of increased stress on vessel walls.
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-info rounded-full"></div>
                      <span className="text-sm">Anterior Circulation (70-80%)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-success rounded-full"></div>
                      <span className="text-sm">Middle Cerebral Territory</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-medical-blue rounded-full"></div>
                      <span className="text-sm">Posterior Circulation (15-20%)</span>
                    </div>
                  </div>
                </div>
                <div className="flex justify-center">
                  <div className="relative">
                    <img 
                      src="/images/neurological-conditions/circle-of-willis-diagram.jpg" 
                      alt="Circle of Willis anatomy diagram"
                      className="rounded-lg shadow-lg max-w-full h-auto"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Blood Vessel Details */}
        <div className="space-y-6">
          {bloodVessels.map((vessel, index) => {
            const Icon = getVesselIcon(vessel.vessel);
            const isExpanded = expandedVessel === vessel.vessel;
            const riskLevel = getRiskLevel(vessel.characteristics);
            
            return (
              <Card key={index} className={cn("transition-all duration-200", getVesselColor(vessel.vessel))}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-full bg-background/80">
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{vessel.vessel}</CardTitle>
                        <CardDescription className="text-sm">{vessel.description}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={riskLevel.color}>
                        {riskLevel.level}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(vessel.vessel)}
                        className="flex items-center gap-1"
                      >
                        <Info className="h-4 w-4" />
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        {/* Common Aneurysm Sites */}
                        <div>
                          <h4 className="font-semibold mb-3">Common Aneurysm Sites</h4>
                          <ul className="space-y-2">
                            {vessel.commonSites.map((site, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{site}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Clinical Characteristics */}
                        <div>
                          <h4 className="font-semibold mb-3">Clinical Characteristics</h4>
                          <ul className="space-y-2">
                            {vessel.characteristics.map((characteristic, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{characteristic}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Additional Information */}
                      <div className="mt-6 p-4 bg-background/50 rounded-lg">
                        <h4 className="font-semibold mb-2">Treatment Considerations</h4>
                        <p className="text-sm text-muted-foreground">
                          {vessel.vessel.includes('Anterior Communicating') && 
                            "Anterior communicating artery aneurysms often require careful surgical planning due to their complex anatomy and proximity to critical structures. Both microsurgical clipping and endovascular coiling can be effective."
                          }
                          {vessel.vessel.includes('Middle Cerebral') && 
                            "Middle cerebral artery aneurysms are often well-suited for microsurgical clipping due to their accessible location. The proximity to speech and motor areas requires careful surgical technique."
                          }
                          {vessel.vessel.includes('Posterior Communicating') && 
                            "Posterior communicating artery aneurysms may present with third nerve palsy due to compression. Both surgical and endovascular approaches can be effective depending on aneurysm characteristics."
                          }
                        </p>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* Aneurysm Formation Process */}
        <div className="mt-12">
          <Card className="bg-muted">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Droplets className="h-5 w-5 text-foreground" />
                How Aneurysms Form
              </CardTitle>
              <CardDescription>
                Understanding the process of aneurysm formation and growth
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div className="text-center">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    1
                  </div>
                  <h4 className="font-semibold mb-2">Wall Weakness</h4>
                  <p className="text-sm text-muted-foreground">
                    Blood vessel wall becomes weakened due to genetic factors, hypertension, or other causes
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    2
                  </div>
                  <h4 className="font-semibold mb-2">Bulge Formation</h4>
                  <p className="text-sm text-muted-foreground">
                    Constant blood pressure causes the weakened area to bulge outward, forming an aneurysm
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    3
                  </div>
                  <h4 className="font-semibold mb-2">Growth Risk</h4>
                  <p className="text-sm text-muted-foreground">
                    The aneurysm may grow over time, increasing the risk of rupture if left untreated
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Clinical Significance */}
        <div className="mt-12">
          <Card className="bg-info-light border-info/30">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-info">
                <Info className="h-5 w-5" />
                Clinical Significance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-info text-sm">
                Understanding cerebral vascular anatomy is crucial for aneurysm treatment planning. 
                The location of an aneurysm determines the surgical approach, treatment options, and potential risks. 
                Anterior circulation aneurysms are more common and often more accessible for treatment, while 
                posterior circulation aneurysms may require specialised endovascular techniques.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default AneurysmAnatomySection;
