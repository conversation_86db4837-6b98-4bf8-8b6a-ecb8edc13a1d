import { Target, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, TrendingUp, ArrowRight, ArrowLeft, Activity, Heart } from 'lucide-react';
import React, { useState, useId } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface AssessmentQuestion {
  id: string;
  question: string;
  description?: string;
  options: Array<{
    value: string;
    label: string;
    score: number;
  }>;
}

interface AssessmentResult {
  totalScore: number;
  riskLevel: 'low' | 'moderate' | 'high';
  recommendation: string;
  urgency: 'routine' | 'urgent' | 'immediate';
  nextSteps: string[];
  riskFactors: string[];
}

const assessmentQuestions: AssessmentQuestion[] = [
  {
    id: 'family-history',
    question: 'Do you have a family history of brain aneurysms or stroke?',
    description: 'Family history significantly increases aneurysm risk',
    options: [
      { value: 'first-degree', label: 'Yes, in immediate family (parent, sibling, child)', score: 3 },
      { value: 'second-degree', label: 'Yes, in extended family (grandparent, aunt, uncle)', score: 2 },
      { value: 'distant', label: 'Yes, but distant relatives only', score: 1 },
      { value: 'none', label: 'No known family history', score: 0 }
    ]
  },
  {
    id: 'age-gender',
    question: 'What is your age and gender?',
    description: 'Age and gender affect aneurysm risk',
    options: [
      { value: 'female-over-50', label: 'Female, over 50 years old', score: 3 },
      { value: 'female-under-50', label: 'Female, under 50 years old', score: 2 },
      { value: 'male-over-50', label: 'Male, over 50 years old', score: 2 },
      { value: 'male-under-50', label: 'Male, under 50 years old', score: 1 }
    ]
  },
  {
    id: 'hypertension',
    question: 'Do you have high blood pressure?',
    description: 'Hypertension is a major risk factor for aneurysm formation and rupture',
    options: [
      { value: 'uncontrolled', label: 'Yes, poorly controlled despite medication', score: 3 },
      { value: 'controlled', label: 'Yes, well controlled with medication', score: 2 },
      { value: 'borderline', label: 'Borderline high blood pressure', score: 1 },
      { value: 'normal', label: 'Normal blood pressure', score: 0 }
    ]
  },
  {
    id: 'smoking',
    question: 'What is your smoking history?',
    description: 'Smoking significantly increases aneurysm rupture risk',
    options: [
      { value: 'current-heavy', label: 'Current smoker (>1 pack per day)', score: 4 },
      { value: 'current-light', label: 'Current smoker (<1 pack per day)', score: 3 },
      { value: 'recent-quit', label: 'Quit within the last 5 years', score: 2 },
      { value: 'former', label: 'Former smoker (quit >5 years ago)', score: 1 },
      { value: 'never', label: 'Never smoked', score: 0 }
    ]
  },
  {
    id: 'connective-tissue',
    question: 'Do you have any connective tissue disorders?',
    description: 'Genetic conditions affecting blood vessel structure increase risk',
    options: [
      { value: 'diagnosed', label: 'Yes, diagnosed connective tissue disorder', score: 4 },
      { value: 'suspected', label: 'Suspected but not confirmed', score: 2 },
      { value: 'family-history', label: 'Family history of connective tissue disorders', score: 1 },
      { value: 'none', label: 'No known connective tissue disorders', score: 0 }
    ]
  },
  {
    id: 'symptoms',
    question: 'Have you experienced any concerning symptoms?',
    description: 'Certain symptoms may indicate the presence of an aneurysm',
    options: [
      { value: 'severe-headaches', label: 'Severe, unusual headaches', score: 3 },
      { value: 'vision-changes', label: 'Vision changes or double vision', score: 3 },
      { value: 'mild-headaches', label: 'Mild, persistent headaches', score: 1 },
      { value: 'none', label: 'No concerning symptoms', score: 0 }
    ]
  }
];

export function RiskAssessmentTool() {
  const deviceInfo = useDeviceDetection();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showResults, setShowResults] = useState(false);
  const assessmentId = useId();

  const progress = ((currentQuestion + 1) / assessmentQuestions.length) * 100;
  const isLastQuestion = currentQuestion === assessmentQuestions.length - 1;
  const canProceed = answers[assessmentQuestions[currentQuestion]?.id];

  const handleAnswer = (value: string) => {
    setAnswers(prev => ({
      ...prev,
      [assessmentQuestions[currentQuestion].id]: value
    }));
  };

  const handleNext = () => {
    if (isLastQuestion) {
      setShowResults(true);
    } else {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const calculateResults = (): AssessmentResult => {
    const totalScore = Object.entries(answers).reduce((total, [questionId, answer]) => {
      const question = assessmentQuestions.find(q => q.id === questionId);
      const option = question?.options.find(o => o.value === answer);
      return total + (option?.score || 0);
    }, 0);

    let riskLevel: 'low' | 'moderate' | 'high';
    let recommendation: string;
    let urgency: 'routine' | 'urgent' | 'immediate';
    let nextSteps: string[];
    const riskFactors: string[] = [];

    // Identify specific risk factors
    if (answers['family-history'] && answers['family-history'] !== 'none') {
      riskFactors.push('Family history of aneurysms');
    }
    if (answers['smoking'] && answers['smoking'].includes('current')) {
      riskFactors.push('Current smoking');
    }
    if (answers['hypertension'] && answers['hypertension'] !== 'normal') {
      riskFactors.push('High blood pressure');
    }
    if (answers['connective-tissue'] && answers['connective-tissue'] !== 'none') {
      riskFactors.push('Connective tissue disorder');
    }
    if (answers['symptoms'] && answers['symptoms'] !== 'none') {
      riskFactors.push('Concerning symptoms');
    }

    if (totalScore >= 12) {
      riskLevel = 'high';
      urgency = 'urgent';
      recommendation = 'Your risk factors suggest you should have urgent medical evaluation for possible aneurysm screening. Multiple risk factors significantly increase your likelihood of having a cerebral aneurysm.';
      nextSteps = [
        'Contact a neurologist or neurosurgeon within 1-2 weeks',
        'Request brain imaging (CTA or MRA) for aneurysm screening',
        'Bring complete family history to appointment',
        'Begin aggressive risk factor modification immediately'
      ];
    } else if (totalScore >= 7) {
      riskLevel = 'moderate';
      urgency = 'urgent';
      recommendation = 'You have moderate risk factors for cerebral aneurysms. Medical evaluation and possible screening should be considered, especially given your risk profile.';
      nextSteps = [
        'Discuss aneurysm screening with your GP or specialist',
        'Consider brain imaging if family history is strong',
        'Focus on modifiable risk factor reduction',
        'Monitor blood pressure regularly'
      ];
    } else if (totalScore >= 3) {
      riskLevel = 'low';
      urgency = 'routine';
      recommendation = 'You have some risk factors for cerebral aneurysms, but your overall risk is relatively low. Focus on risk factor modification and routine health maintenance.';
      nextSteps = [
        'Discuss risk factors with your GP at next routine visit',
        'Focus on lifestyle modifications',
        'Monitor and control blood pressure',
        'Consider screening if symptoms develop'
      ];
    } else {
      riskLevel = 'low';
      urgency = 'routine';
      recommendation = 'Your risk factors for cerebral aneurysms are minimal. Continue with routine health maintenance and healthy lifestyle choices.';
      nextSteps = [
        'Continue routine health check-ups',
        'Maintain healthy lifestyle',
        'Be aware of warning signs',
        'Seek medical attention if symptoms develop'
      ];
    }

    return { totalScore, riskLevel, recommendation, urgency, nextSteps, riskFactors };
  };

  const results = showResults ? calculateResults() : null;

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'high': return 'bg-muted-light text-foreground border-border/70';
      case 'moderate': return 'bg-info-light text-info border-info/30';
      default: return 'bg-success-light text-success border-success/30';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return <AlertTriangle className="h-5 w-5 text-foreground" />;
      case 'urgent': return <Target className="h-5 w-5 text-info" />;
      default: return <CheckCircle className="h-5 w-5 text-success" />;
    }
  };

  if (showResults && results) {
    return (
      <section className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}>
        <div className="container max-w-4xl">
          <Card className={cn("border-2", getRiskLevelColor(results.riskLevel))}>
            <CardHeader className="text-center">
              <div className="flex items-center justify-center gap-2 mb-4">
                {getUrgencyIcon(results.urgency)}
                <CardTitle className="text-2xl">Risk Assessment Results</CardTitle>
              </div>
              <CardDescription>
                Based on your responses, here's your personalised risk assessment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Score and Risk Level */}
              <div className="text-center p-6 bg-background/50 rounded-lg">
                <div className="flex items-center justify-center gap-4 mb-4">
                  <div>
                    <div className="text-enhanced-heading text-3xl font-bold text-primary">{results.totalScore}</div>
                    <div className="text-sm text-muted-foreground">Risk Score</div>
                  </div>
                  <div>
                    <Badge className={getRiskLevelColor(results.riskLevel)}>
                      {results.riskLevel.toUpperCase()} RISK
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Risk Factors */}
              {results.riskFactors.length > 0 && (
                <div className="bg-background rounded-lg p-6 border">
                  <h3 className="font-semibold mb-3 flex items-center gap-2">
                    <Heart className="h-5 w-5 text-foreground" />
                    Your Risk Factors
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {results.riskFactors.map((factor, index) => (
                      <Badge key={index} variant="secondary" className="bg-muted-light text-foreground">
                        {factor}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Recommendation */}
              <div className="bg-background rounded-lg p-6 border">
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <Activity className="h-5 w-5 text-primary" />
                  Recommendation
                </h3>
                <p className="text-sm">{results.recommendation}</p>
              </div>

              {/* Next Steps */}
              <div className="bg-background rounded-lg p-6 border">
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <ArrowRight className="h-5 w-5 text-primary" />
                  Recommended Next Steps
                </h3>
                <ul className="space-y-2">
                  {results.nextSteps.map((step, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold mt-0.5 flex-shrink-0">
                        {index + 1}
                      </div>
                      <span className="text-sm">{step}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Risk Reduction Tips */}
              <div className="bg-success border border-success rounded-lg p-4">
                <h4 className="font-semibold text-success mb-2">Risk Reduction Strategies</h4>
                <ul className="text-sm text-success space-y-1">
                  <li>• Quit smoking completely (most important modifiable factor)</li>
                  <li>• Control blood pressure through medication and lifestyle</li>
                  <li>• Limit alcohol consumption</li>
                  <li>• Avoid illicit drugs, especially stimulants</li>
                  <li>• Manage stress through relaxation techniques</li>
                  <li>• Maintain regular exercise routine</li>
                </ul>
              </div>

              {/* Important Disclaimer */}
              <div className="bg-info border border-info rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-info mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-info mb-1">Important Disclaimer</h4>
                    <p className="text-sm text-info">
                      This assessment tool is for educational purposes only and does not replace professional medical evaluation. 
                      Risk factors do not guarantee the presence of an aneurysm, and absence of risk factors does not eliminate the possibility.
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className={cn("flex gap-3", deviceInfo.isMobile ? "flex-col" : "flex-row justify-center")}>
                <Button size="lg">
                  <Target className="mr-2 h-4 w-4" />
                  Find a Specialist
                </Button>
                <Button variant="outline" size="lg" onClick={() => {
                  setShowResults(false);
                  setCurrentQuestion(0);
                  setAnswers({});
                }}>
                  Retake Assessment
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    );
  }

  return (
    <section 
      className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}
      aria-labelledby={`${assessmentId}-title`}
    >
      <div className="container max-w-3xl">
        <div className="text-center mb-8">
          <h2 
            id={`${assessmentId}-title`}
            className={cn("font-bold mb-4", deviceInfo.isMobile ? "text-2xl" : "text-3xl")}
          >
            Cerebral Aneurysm Risk Assessment
          </h2>
          <p className={cn("text-muted-foreground", deviceInfo.isMobile ? "text-sm" : "text-lg")}>
            Answer these questions to assess your risk factors for cerebral aneurysms and receive personalised guidance
          </p>
        </div>

        {/* Progress */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Progress</span>
            <span className="text-sm text-muted-foreground">
              {currentQuestion + 1} of {assessmentQuestions.length}
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Question Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              Question {currentQuestion + 1}
            </CardTitle>
            <CardDescription className="text-lg font-medium">
              {assessmentQuestions[currentQuestion]?.question}
            </CardDescription>
            {assessmentQuestions[currentQuestion]?.description && (
              <p className="text-sm text-muted-foreground">
                {assessmentQuestions[currentQuestion].description}
              </p>
            )}
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={answers[assessmentQuestions[currentQuestion]?.id] || ''}
              onValueChange={handleAnswer}
              className="space-y-3"
            >
              {assessmentQuestions[currentQuestion]?.options.map((option) => (
                <div key={option.value} className="flex items-start space-x-3 p-3 rounded-lg border hover:bg-muted/50">
                  <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
                  <Label htmlFor={option.value} className="font-medium cursor-pointer flex-1">
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className={cn("flex justify-between mt-8", deviceInfo.isMobile ? "flex-col gap-3" : "")}>
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentQuestion === 0}
            className={deviceInfo.isMobile ? "order-2" : ""}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button
            onClick={handleNext}
            disabled={!canProceed}
            className={deviceInfo.isMobile ? "order-1" : ""}
          >
            {isLastQuestion ? 'Get Results' : 'Next'}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </section>
  );
}

export default RiskAssessmentTool;
