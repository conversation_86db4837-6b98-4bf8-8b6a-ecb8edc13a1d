import { Target, Gauge, Brain, Info, ChevronDown, ChevronUp, AlertTriangle } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { Progress } from '@/components/ui/progress';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface GradingCriteria {
  grade: string;
  criteria: string;
  surgicalRisk: string;
  characteristics: string[];
  treatmentRecommendation: string;
}

interface AVMGradingSystemProps {
  title: string;
  description: string;
  spetzlerMartin: GradingCriteria[];
}

export function AVMGradingSystem({ 
  title, 
  description, 
  spetzlerMartin 
}: AVMGradingSystemProps) {
  const deviceInfo = useDeviceDetection();
  const [expandedGrade, setExpandedGrade] = useState<string | null>(null);

  const toggleExpanded = (grade: string) => {
    setExpandedGrade(expandedGrade === grade ? null : grade);
  };

  const getGradeIcon = (grade: string) => {
    if (grade.includes('I')) return Target;
    if (grade.includes('II')) return Gauge;
    if (grade.includes('III')) return Brain;
    return AlertTriangle;
  };

  const getGradeColor = (grade: string) => {
    if (grade.includes('Grade I')) return 'text-foreground bg-success-light border-success/30';
    if (grade.includes('Grade II')) return 'text-foreground bg-info-light border-info/30';
    if (grade.includes('Grade III')) return 'text-foreground bg-info-light border-info/30';
    return 'text-foreground bg-muted-light border-border/70';
  };

  const getRiskLevel = (surgicalRisk: string) => {
    if (surgicalRisk.includes('Low risk')) {
      return { level: 'Low Risk', colour: 'bg-success-light/30 text-foreground', value: 25 };
    }
    if (surgicalRisk.includes('Low-moderate')) {
      return { level: 'Low-Moderate Risk', colour: 'bg-info-light/30 text-foreground', value: 50 };
    }
    if (surgicalRisk.includes('Moderate')) {
      return { level: 'Moderate Risk', colour: 'bg-info-light/30 text-foreground', value: 75 };
    }
    return { level: 'High Risk', colour: 'bg-muted text-foreground', value: 90 };
  };

  const getGradeNumber = (grade: string) => {
    if (grade.includes('I')) return 1;
    if (grade.includes('II')) return 2;
    if (grade.includes('III')) return 3;
    return 4;
  };

  return (
    <section className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-centre mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* Grading Overview */}
        <div className="mb-12">
          <Card className="bg-muted">
            <CardContent className="pt-6">
              <div className={cn(
                "grid gap-8 items-centre",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h3 className="text-xl font-semibold mb-4">Spetzler-Martin Grading Factors</h3>
                  <p className="text-muted-foreground mb-4">
                    The Spetzler-Martin grading system evaluates three key factors to predict surgical risk 
                    and guide treatment decisions for cerebral AVMs.
                  </p>
                  <div className="space-y-3">
                    <div className="flex items-centre gap-3">
                      <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre text-sm font-bold">
                        1
                      </div>
                      <div>
                        <h4 className="font-medium">Size</h4>
                        <p className="text-sm text-muted-foreground">Small (&lt;3cm), Medium (3-6cm), Large (&gt;6cm)</p>
                      </div>
                    </div>
                    <div className="flex items-centre gap-3">
                      <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre text-sm font-bold">
                        2
                      </div>
                      <div>
                        <h4 className="font-medium">Eloquence</h4>
                        <p className="text-sm text-muted-foreground">Non-eloquent vs Eloquent brain areas</p>
                      </div>
                    </div>
                    <div className="flex items-centre gap-3">
                      <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre text-sm font-bold">
                        3
                      </div>
                      <div>
                        <h4 className="font-medium">Venous Drainage</h4>
                        <p className="text-sm text-muted-foreground">Superficial vs Deep venous drainage</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex justify-centre">
                  <div className="relative">
                    <img 
                      src="/images/brain-conditions/brain-anatomy-detailed.jpg" 
                      alt="Spetzler-Martin grading system illustration"
                      className="rounded-lg shadow-lg max-w-full h-auto"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Grade Details */}
        <div className="space-y-6">
          {spetzlerMartin.map((grade, index) => {
            const Icon = getGradeIcon(grade.grade);
            const isExpanded = expandedGrade === grade.grade;
            const riskLevel = getRiskLevel(grade.surgicalRisk);
            const gradeNumber = getGradeNumber(grade.grade);
            
            return (
              <Card key={index} className={cn("transition-all duration-200", getGradeColor(grade.grade))}>
                <CardHeader>
                  <div className="flex items-centre justify-between">
                    <div className="flex items-centre gap-3">
                      <div className="p-2 rounded-full medical-card/80 border border-border/50">
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-lg flex items-centre gap-2">
                          {grade.grade}
                          <span className="text-sm font-normal text-muted-foreground">
                            ({grade.criteria})
                          </span>
                        </CardTitle>
                        <CardDescription className="text-sm">{grade.surgicalRisk}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-centre gap-2">
                      <Badge className={riskLevel.colour}>
                        {riskLevel.level}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(grade.grade)}
                        className="flex items-centre gap-1"
                      >
                        <Info className="h-4 w-4" />
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      {/* Risk Visualization */}
                      <div className="mb-6 p-4 bg-background/50 rounded-lg">
                        <h4 className="font-semibold mb-3">Surgical Risk Assessment</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between items-centre">
                            <span className="text-sm">Risk Level</span>
                            <span className="text-sm font-medium">{riskLevel.level}</span>
                          </div>
                          <Progress value={riskLevel.value} className="h-2" />
                          <p className="text-xs text-muted-foreground">{grade.surgicalRisk}</p>
                        </div>
                      </div>

                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        {/* Characteristics */}
                        <div>
                          <h4 className="font-semibold mb-3">Key Characteristics</h4>
                          <ul className="space-y-2">
                            {grade.characteristics.map((characteristic, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{characteristic}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Treatment Recommendation */}
                        <div>
                          <h4 className="font-semibold mb-3">Treatment Recommendation</h4>
                          <div className="p-3 bg-background/70 rounded-lg border">
                            <p className="text-sm">{grade.treatmentRecommendation}</p>
                          </div>
                        </div>
                      </div>

                      {/* Grade-Specific Information */}
                      <div className="mt-6 p-4 bg-background/50 rounded-lg">
                        <h4 className="font-semibold mb-2">Clinical Considerations</h4>
                        <p className="text-sm text-muted-foreground">
                          {grade.grade.includes('Grade I') && 
                            "Grade I AVMs have the best surgical outcomes with minimal risk. Surgery is usually the first-line treatment, offering immediate cure with excellent functional preservation."
                          }
                          {grade.grade.includes('Grade II') && 
                            "Grade II AVMs still have favourable surgical outcomes. Treatment decisions should consider patient age, symptoms, and individual risk factors. Surgery remains a good option for most patients."
                          }
                          {grade.grade.includes('Grade III') && 
                            "Grade III AVMs require careful consideration of treatment options. The choice between surgery, radiosurgery, and endovascular treatment depends on specific AVM characteristics and patient factors."
                          }
                          {grade.grade.includes('Grade IV-V') && 
                            "High-grade AVMs carry significant surgical risk. Conservative management or radiosurgery are often preferred. Surgery is reserved for specific circumstances with experienced surgical teams."
                          }
                        </p>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* Treatment Decision Guide */}
        <div className="mt-12">
          <Card className="bg-primary/5 border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2">
                <Target className="h-5 w-5 text-primary" />
                Treatment Decision Guide
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-4",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h4 className="font-semibold mb-2">Consider Surgery for:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Grade I-II AVMs</li>
                    <li>• Superficial, accessible locations</li>
                    <li>• Young patients</li>
                    <li>• Ruptured AVMs with mass effect</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Consider Radiosurgery for:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Small AVMs (&lt;3cm)</li>
                    <li>• Deep or eloquent locations</li>
                    <li>• High surgical risk patients</li>
                    <li>• Grade III AVMs in selected cases</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default AVMGradingSystem;
