import { Brain, Layers, Shield, Info, ChevronDown, ChevronUp, Activity } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface MeningesLayer {
  layer: string;
  description: string;
  function: string[];
  characteristics: string[];
}

interface MeningiomaAnatomySectionProps {
  title: string;
  description: string;
  meninges: MeningesLayer[];
}

export function MeningiomaAnatomySection({ 
  title, 
  description, 
  meninges 
}: MeningiomaAnatomySectionProps) {
  const deviceInfo = useDeviceDetection();
  const [expandedLayer, setExpandedLayer] = useState<string | null>(null);

  const toggleExpanded = (layer: string) => {
    setExpandedLayer(expandedLayer === layer ? null : layer);
  };

  const getLayerIcon = (layer: string) => {
    if (layer.includes('Dura')) return Shield;
    if (layer.includes('Arachnoid')) return Layers;
    if (layer.includes('Pia')) return Brain;
    return Activity;
  };

  const getLayerColor = (layer: string) => {
    if (layer.includes('Dura')) return 'text-foreground bg-muted border-border';
    if (layer.includes('Arachnoid')) return 'text-foreground bg-info border-info';
    if (layer.includes('Pia')) return 'text-foreground bg-success border-success';
    return 'text-muted-foreground bg-muted border-border';
  };

  const getTumourOrigin = (layer: string) => {
    if (layer.includes('Arachnoid')) {
      return { origin: 'Primary Origin', colour: 'bg-muted text-foreground' };
    }
    if (layer.includes('Dura')) {
      return { origin: 'Attachment Site', colour: 'bg-info-light/30 text-foreground' };
    }
    return { origin: 'Rarely Involved', colour: 'bg-success-light/30 text-foreground' };
  };

  return (
    <section className={cn("py-16", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-centre mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* Anatomical Overview */}
        <div className="mb-12">
          <Card className="bg-muted">
            <CardContent className="pt-6">
              <div className={cn(
                "grid gap-8 items-centre",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h3 className="text-xl font-semibold mb-4">Meninges Overview</h3>
                  <p className="text-muted-foreground mb-4">
                    The meninges are three protective membrane layers that surround the brain and spinal cord. 
                    Meningiomas arise from arachnoid cap cells within the arachnoid layer, making them the most 
                    common primary brain tumour. Understanding this anatomy helps explain tumour behaviour and treatment approaches.
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-centre gap-2">
                      <div className="w-3 h-3 bg-muted rounded-full"></div>
                      <span className="text-sm">Dura Mater (Outermost)</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <div className="w-3 h-3 bg-info rounded-full"></div>
                      <span className="text-sm">Arachnoid Mater (Middle - Tumour Origin)</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <div className="w-3 h-3 bg-success rounded-full"></div>
                      <span className="text-sm">Pia Mater (Innermost)</span>
                    </div>
                  </div>
                </div>
                <div className="flex justify-centre">
                  <div className="relative">
                    <img
                      src="/images/brain-conditions/brain-anatomy-detailed.jpg"
                      alt="Meninges anatomy diagram showing three layers"
                      className="rounded-lg shadow-lg max-w-full h-auto"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Meninges Layer Details */}
        <div className="space-y-6">
          {meninges.map((layer, index) => {
            const Icon = getLayerIcon(layer.layer);
            const isExpanded = expandedLayer === layer.layer;
            const tumourOrigin = getTumourOrigin(layer.layer);
            
            return (
              <Card key={index} className={cn("transition-all duration-200", getLayerColor(layer.layer))}>
                <CardHeader>
                  <div className="flex items-centre justify-between">
                    <div className="flex items-centre gap-3">
                      <div className="p-2 rounded-full bg-background/80">
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{layer.layer}</CardTitle>
                        <CardDescription className="text-sm">{layer.description}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-centre gap-2">
                      <Badge className={tumourOrigin.colour}>
                        {tumourOrigin.origin}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(layer.layer)}
                        className="flex items-centre gap-1"
                      >
                        <Info className="h-4 w-4" />
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        {/* Functions */}
                        <div>
                          <h4 className="font-semibold mb-3">Primary Functions</h4>
                          <ul className="space-y-2">
                            {layer.function.map((func, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{func}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Characteristics */}
                        <div>
                          <h4 className="font-semibold mb-3">Meningioma Characteristics</h4>
                          <ul className="space-y-2">
                            {layer.characteristics.map((characteristic, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{characteristic}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Additional Information */}
                      <div className="mt-6 p-4 bg-background/50 rounded-lg">
                        <h4 className="font-semibold mb-2">Clinical Significance</h4>
                        <p className="text-sm text-muted-foreground">
                          {layer.layer.includes('Dura') && 
                            "The dura mater provides the attachment point for most meningiomas. Understanding dural involvement is crucial for surgical planning and determining the extent of resection needed."
                          }
                          {layer.layer.includes('Arachnoid') && 
                            "The arachnoid mater contains the arachnoid cap cells from which meningiomas arise. These tumours typically grow slowly and push against rather than invade brain tissue."
                          }
                          {layer.layer.includes('Pia') && 
                            "The pia mater is rarely the primary site of meningioma formation but may be involved in aggressive tumours that invade brain tissue."
                          }
                        </p>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* Meningioma Formation Process */}
        <div className="mt-12">
          <Card className="bg-muted">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2">
                <Layers className="h-5 w-5 text-foreground" />
                How Meningiomas Form
              </CardTitle>
              <CardDescription>
                Understanding the process of meningioma formation and growth
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    1
                  </div>
                  <h4 className="font-semibold mb-2">Cell Transformation</h4>
                  <p className="text-sm text-muted-foreground">
                    Arachnoid cap cells undergo genetic changes leading to uncontrolled growth
                  </p>
                </div>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    2
                  </div>
                  <h4 className="font-semibold mb-2">Tumour Growth</h4>
                  <p className="text-sm text-muted-foreground">
                    Slow, progressive growth typically pushing against rather than invading brain tissue
                  </p>
                </div>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    3
                  </div>
                  <h4 className="font-semibold mb-2">Mass Effect</h4>
                  <p className="text-sm text-muted-foreground">
                    As tumour enlarges, it may compress brain tissue and cause symptoms
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Clinical Significance */}
        <div className="mt-12">
          <Card className="bg-info-light border-info/30">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2 text-info">
                <Info className="h-5 w-5" />
                Clinical Significance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-info text-sm">
                Understanding meninges anatomy is crucial for meningioma treatment planning. 
                The location and relationship to the meninges determines surgical approach, 
                the extent of resection needed, and potential complications. Most meningiomas 
                have a characteristic "dural tail" on imaging, representing reactive dural changes 
                rather than tumour invasion.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default MeningiomaAnatomySection;
