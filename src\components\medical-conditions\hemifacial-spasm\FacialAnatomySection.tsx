import { Brain, Network, Zap, Info, ChevronDown, ChevronUp, Activity, Hexagon } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface FacialNerveComponent {
  component: string;
  description: string;
  normalFunction: string[];
  spasmCharacteristics: string[];
}

interface FacialAnatomySectionProps {
  title: string;
  description: string;
  facialNerveComponents: FacialNerveComponent[];
}

export function FacialAnatomySection({ 
  title, 
  description, 
  facialNerveComponents 
}: FacialAnatomySectionProps) {
  const deviceInfo = useDeviceDetection();
  const [expandedComponent, setExpandedComponent] = useState<string | null>(null);

  const toggleExpanded = (component: string) => {
    setExpandedComponent(expandedComponent === component ? null : component);
  };

  const getComponentIcon = (component: string) => {
    if (component.includes('Root Exit Zone')) return Brain;
    if (component.includes('Cerebellopontine')) return Hexagon;
    if (component.includes('Facial Muscles')) return Network;
    return Activity;
  };

  const getComponentColor = (component: string) => {
    if (component.includes('Root Exit Zone')) return 'text-info dark:text-info bg-muted dark:from-info-light/50 dark:to-info-light/30 border-info/70 dark:border-info/50';
    if (component.includes('Cerebellopontine')) return 'text-foreground dark:text-foreground bg-muted dark:from-info-light/50 dark:to-info-light/30 border-border/70 dark:border-border';
    if (component.includes('Facial Muscles')) return 'text-success dark:text-success bg-muted dark:from-info-light/50 dark:to-info-light/30 border-success/70 dark:border-success/50';
    return 'text-muted-foreground dark:text-muted-foreground bg-muted dark:from-info-light/50 dark:to-info-light/30 border-border/70 dark:border-border/50';
  };

  const getAbnormalityLevel = (component: string) => {
    if (component.includes('Root Exit Zone')) {
      return { level: 'Compression Site', color: 'bg-muted/80 text-foreground dark:bg-muted/50 dark:text-foreground border border-border dark:border-border' };
    }
    if (component.includes('Cerebellopontine')) {
      return { level: 'Anatomical Space', color: 'bg-info/80 text-foreground dark:bg-info/50 dark:text-info border border-info dark:border-info' };
    }
    return { level: 'Target Muscles', color: 'bg-success/80 text-foreground dark:bg-success/50 dark:text-success border border-success dark:border-success' };
  };

  return (
    <section className={cn(
      "py-16 bg-gradient-to-br from-background via-background/95 to-muted/30",
      deviceInfo.isMobile ? "px-4" : ""
    )}>
      <div className="container">
        <div className="text-center mb-16">
          <h2 className={cn(
            "font-bold text-foreground mb-6 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-4xl mx-auto leading-relaxed",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* Anatomical Overview */}
        <div className="mb-16">
          <Card className="bg-gradient-to-br from-card/90 via-card to-muted/20 backdrop-blur-sm border-border/50 shadow-xl">
            <CardContent className="pt-8 pb-8">
              <div className={cn(
                "grid gap-12 items-center",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div className="space-y-6">
                  <h3 className={cn(
                    "font-bold text-foreground mb-6 leading-tight",
                    deviceInfo.isMobile ? "text-xl" : "text-2xl"
                  )}>
                    Facial Nerve Anatomy and Hemifacial Spasm
                  </h3>
                  <p className={cn(
                    "text-muted-foreground mb-6 leading-relaxed",
                    deviceInfo.isMobile ? "text-sm" : "text-base"
                  )}>
                    Hemifacial spasm occurs when the facial nerve (cranial nerve VII) is compressed or irritated,
                    usually by a blood vessel at the nerve's exit from the brainstem. This compression causes
                    abnormal electrical activity that spreads to facial muscles, resulting in involuntary spasms.
                  </p>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-2 rounded-md hover:bg-primary/5 transition-colors">
                      <div className="w-4 h-4 bg-primary rounded-full border-2 border-primary/30"></div>
                      <span className={cn(
                        "font-medium text-foreground",
                        deviceInfo.isMobile ? "text-sm" : "text-base"
                      )}>
                        Facial Nerve Root Exit Zone
                      </span>
                    </div>
                    <div className="flex items-center gap-3 p-2 rounded-md hover:bg-primary/5 transition-colors">
                      <div className="w-4 h-4 bg-muted rounded-full border-2 border-border/70"></div>
                      <span className={cn(
                        "font-medium text-foreground",
                        deviceInfo.isMobile ? "text-sm" : "text-base"
                      )}>
                        Cerebellopontine Angle
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-success rounded-full"></div>
                      <span className="text-sm">Facial Muscles</span>
                    </div>
                  </div>
                </div>
                <div className="flex justify-center">
                  <div className="relative">
                    <img 
                      src="/images/brain-conditions/brain-anatomy-detailed.jpg" 
                      alt="Facial nerve anatomy diagram showing compression sites"
                      className="rounded-lg shadow-lg max-w-full h-auto"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Facial Nerve Component Details */}
        <div className="space-y-6">
          {facialNerveComponents.map((component, index) => {
            const Icon = getComponentIcon(component.component);
            const isExpanded = expandedComponent === component.component;
            const abnormalityLevel = getAbnormalityLevel(component.component);
            
            return (
              <Card key={index} className={cn("transition-all duration-200", getComponentColor(component.component))}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-full bg-background/80">
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{component.component}</CardTitle>
                        <CardDescription className="text-sm">{component.description}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={abnormalityLevel.color}>
                        {abnormalityLevel.level}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(component.component)}
                        className="flex items-center gap-1"
                      >
                        <Info className="h-4 w-4" />
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        {/* Normal Functions */}
                        <div>
                          <h4 className="font-semibold mb-3">Normal Functions</h4>
                          <ul className="space-y-2">
                            {component.normalFunction.map((func, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{func}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Spasm Characteristics */}
                        <div>
                          <h4 className="font-semibold mb-3">Spasm-Related Features</h4>
                          <ul className="space-y-2">
                            {component.spasmCharacteristics.map((characteristic, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{characteristic}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Additional Information */}
                      <div className="mt-6 p-4 bg-background/50 rounded-lg">
                        <h4 className="font-semibold mb-2">Clinical Significance</h4>
                        <p className="text-sm text-muted-foreground">
                          {component.component.includes('Root Exit Zone') && 
                            "The facial nerve root exit zone is the most vulnerable area for vascular compression. This region lacks myelin protection, making it susceptible to mechanical irritation that can trigger hemifacial spasm."
                          }
                          {component.component.includes('Cerebellopontine') && 
                            "The cerebellopontine angle is where blood vessels commonly compress the facial nerve. Understanding this anatomy is crucial for surgical planning in microvascular decompression procedures."
                          }
                          {component.component.includes('Facial Muscles') && 
                            "Facial muscles receive abnormal nerve signals in hemifacial spasm, causing involuntary contractions. The pattern of muscle involvement helps determine the extent of nerve dysfunction and guides treatment planning."
                          }
                        </p>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* Spasm Development Process */}
        <div className="mt-12">
          <Card className="bg-muted">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-foreground" />
                How Hemifacial Spasm Develops
              </CardTitle>
              <CardDescription>
                Understanding the mechanism of spasm development
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div className="text-center">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    1
                  </div>
                  <h4 className="font-semibold mb-2">Vascular Compression</h4>
                  <p className="text-sm text-muted-foreground">
                    Blood vessel compresses facial nerve at root exit zone, causing mechanical irritation
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    2
                  </div>
                  <h4 className="font-semibold mb-2">Nerve Hyperexcitability</h4>
                  <p className="text-sm text-muted-foreground">
                    Chronic compression leads to abnormal electrical activity and nerve hyperexcitability
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    3
                  </div>
                  <h4 className="font-semibold mb-2">Muscle Spasms</h4>
                  <p className="text-sm text-muted-foreground">
                    Abnormal nerve signals cause involuntary facial muscle contractions and spasms
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Clinical Significance */}
        <div className="mt-12">
          <Card className="bg-info-light border-info/30">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-info">
                <Info className="h-5 w-5" />
                Clinical Significance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-info text-sm">
                Understanding facial nerve anatomy is essential for hemifacial spasm treatment. The location of 
                vascular compression determines surgical approach, while the pattern of muscle involvement guides 
                botulinum toxin injection sites. Knowledge of nerve anatomy helps predict treatment outcomes and 
                potential complications.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default FacialAnatomySection;
