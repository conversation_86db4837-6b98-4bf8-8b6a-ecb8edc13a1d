import { Scissors, Target, TrendingUp, Clock, AlertTriangle, CheckCircle, Activity, Pill, Zap } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface Treatment {
  name: string;
  description: string;
  indications: string[];
  advantages: string[];
  disadvantages: string[];
  successRate: string;
  duration: string;
  sideEffects: string[];
}

interface HemifacialTreatmentComparisonProps {
  title: string;
  description: string;
  treatments: Treatment[];
}

export function HemifacialTreatmentComparison({ 
  title, 
  description, 
  treatments 
}: HemifacialTreatmentComparisonProps) {
  const deviceInfo = useDeviceDetection();
  const [selectedTreatment, setSelectedTreatment] = useState<string>(treatments[0]?.name || '');
  const [comparisonMode, setComparisonMode] = useState(false);

  const selectedTreatment_obj = treatments.find(treatment => treatment.name === selectedTreatment);

  const getSuccessRateNumber = (successRate: string) => {
    const match = successRate.match(/(\d+)-?(\d+)?%/);
    if (match) {
      return match[2] ? parseInt(match[2]) : parseInt(match[1]);
    }
    return 0;
  };

  const getInvasivenessLevel = (treatmentName: string) => {
    if (treatmentName.includes('Botulinum')) return { level: 'Minimal', value: 20, color: 'bg-success' };
    if (treatmentName.includes('Microvascular')) return { level: 'High', value: 90, color: 'bg-muted' };
    return { level: 'None', value: 0, color: 'bg-info' };
  };

  const getDurationValue = (duration: string) => {
    if (duration.includes('3-6 months')) return { time: '3-6 months', value: 30, color: 'bg-info' };
    if (duration.includes('Permanent')) return { time: 'Permanent', value: 100, color: 'bg-success' };
    if (duration.includes('Ongoing')) return { time: 'Ongoing', value: 50, color: 'bg-info' };
    return { time: 'Variable', value: 25, color: 'bg-muted' };
  };

  const getTreatmentIcon = (treatmentName: string) => {
    if (treatmentName.includes('Botulinum')) return Zap;
    if (treatmentName.includes('Microvascular')) return Scissors;
    return Pill;
  };

  return (
    <section className={cn(
      "bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20",
      "border-y border-border/50",
      deviceInfo.isMobile ? "py-12" : "py-20"
    )}>
      <div className="container">
        <div className="text-center mb-16">
          <h2 className={cn(
            "font-bold text-foreground mb-6 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-4xl mx-auto leading-relaxed",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* View Toggle */}
        <div className="flex justify-center mb-12">
          <div className="flex bg-muted/50 backdrop-blur-sm rounded-xl p-2 border border-border/50 shadow-lg">
            <Button
              variant={!comparisonMode ? "default" : "ghost"}
              size={deviceInfo.isMobile ? "sm" : "default"}
              onClick={() => setComparisonMode(false)}
              className={cn(
                "px-6 py-2 font-semibold transition-all duration-300",
                !comparisonMode && "shadow-md hover:shadow-lg"
              )}
            >
              Detailed View
            </Button>
            <Button
              variant={comparisonMode ? "default" : "ghost"}
              size={deviceInfo.isMobile ? "sm" : "default"}
              onClick={() => setComparisonMode(true)}
              className={cn(
                "px-6 py-2 font-semibold transition-all duration-300",
                comparisonMode && "shadow-md hover:shadow-lg"
              )}
            >
              Comparison View
            </Button>
          </div>
        </div>

        {!comparisonMode ? (
          /* Detailed View */
          <Tabs value={selectedTreatment} onValueChange={setSelectedTreatment} className="w-full">
            <TabsList className={cn(
              "grid w-full mb-8",
              deviceInfo.isMobile ? "grid-cols-1 h-auto" : `grid-cols-${Math.min(treatments.length, 3)}`
            )}>
              {treatments.map((treatment) => (
                <TabsTrigger 
                  key={treatment.name} 
                  value={treatment.name}
                  className={cn(
                    "text-center",
                    deviceInfo.isMobile ? "py-3 text-sm" : "py-4"
                  )}
                >
                  {treatment.name.replace('Botulinum Toxin Injections', 'Botulinum Toxin')}
                </TabsTrigger>
              ))}
            </TabsList>

            {treatments.map((treatment) => {
              const Icon = getTreatmentIcon(treatment.name);
              
              return (
                <TabsContent key={treatment.name} value={treatment.name} className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Icon className="h-5 w-5 text-primary" />
                        {treatment.name}
                      </CardTitle>
                      <CardDescription>{treatment.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Key Metrics */}
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                      )}>
                        <div>
                          <h4 className="font-semibold mb-2 flex items-center gap-2">
                            <TrendingUp className="h-4 w-4 text-success" />
                            Success Rate
                          </h4>
                          <div className="space-y-2">
                            <Progress value={getSuccessRateNumber(treatment.successRate)} className="h-2" />
                            <p className="text-sm text-muted-foreground">{treatment.successRate}</p>
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="font-semibold mb-2 flex items-center gap-2">
                            <Activity className="h-4 w-4 text-info" />
                            Invasiveness
                          </h4>
                          <div className="space-y-2">
                            <Progress 
                              value={getInvasivenessLevel(treatment.name).value} 
                              className="h-2"
                            />
                            <p className="text-sm text-muted-foreground">
                              {getInvasivenessLevel(treatment.name).level}
                            </p>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold mb-2 flex items-center gap-2">
                            <Clock className="h-4 w-4 text-medical-blue" />
                            Duration
                          </h4>
                          <div className="space-y-2">
                            <Progress 
                              value={getDurationValue(treatment.duration).value} 
                              className="h-2"
                            />
                            <p className="text-sm text-muted-foreground">{treatment.duration}</p>
                          </div>
                        </div>
                      </div>

                      {/* Indications, Advantages, Disadvantages */}
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                      )}>
                        <div>
                          <h4 className="font-semibold mb-3 flex items-center gap-2 text-info">
                            <Target className="h-4 w-4" />
                            Best For
                          </h4>
                          <ul className="space-y-2">
                            {treatment.indications.map((indication, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                                <span className="text-sm">{indication}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-semibold mb-3 flex items-center gap-2 text-success">
                            <CheckCircle className="h-4 w-4" />
                            Advantages
                          </h4>
                          <ul className="space-y-2">
                            {treatment.advantages.map((advantage, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                                <span className="text-sm">{advantage}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-semibold mb-3 flex items-center gap-2 text-foreground">
                            <AlertTriangle className="h-4 w-4" />
                            Considerations
                          </h4>
                          <ul className="space-y-2">
                            {treatment.disadvantages.map((disadvantage, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-muted rounded-full mt-2 flex-shrink-0" />
                                <span className="text-sm">{disadvantage}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Side Effects */}
                      <div>
                        <h4 className="font-semibold mb-3 flex items-center gap-2 text-info">
                          <AlertTriangle className="h-4 w-4" />
                          Potential Side Effects
                        </h4>
                        <div className={cn(
                          "grid gap-2",
                          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                        )}>
                          {treatment.sideEffects.map((effect, index) => (
                            <div key={index} className="flex items-center gap-2">
                              <div className="w-1.5 h-1.5 bg-info rounded-full flex-shrink-0" />
                              <span className="text-sm">{effect}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Clinical Notes */}
                      <div className="bg-info-light border border-info/30 rounded-lg p-4">
                        <h4 className="font-semibold text-foreground mb-2">Clinical Considerations</h4>
                        <p className="text-sm text-foreground">
                          {treatment.name.includes('Botulinum') && 
                            "Botulinum toxin is the gold standard first-line treatment for hemifacial spasm. It provides excellent symptom control with minimal invasiveness and can be repeated as needed."
                          }
                          {treatment.name.includes('Microvascular') && 
                            "Microvascular decompression offers the potential for permanent cure but requires major surgery. It's best suited for young, healthy patients who desire a permanent solution."
                          }
                          {treatment.name.includes('Oral') && 
                            "Oral medications have limited effectiveness but may be useful as adjunctive therapy or for patients who cannot receive injections or surgery."
                          }
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              );
            })}
          </Tabs>
        ) : (
          /* Comparison View */
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Treatment Comparison</CardTitle>
                <CardDescription>
                  Compare key metrics across treatment options
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Treatment</th>
                        <th className="text-left p-2">Success Rate</th>
                        <th className="text-left p-2">Invasiveness</th>
                        <th className="text-left p-2">Duration</th>
                        <th className="text-left p-2">Best For</th>
                      </tr>
                    </thead>
                    <tbody>
                      {treatments.map((treatment, index) => (
                        <tr key={index} className="border-b">
                          <td className="p-2 font-medium">
                            {treatment.name.replace('Botulinum Toxin Injections', 'Botulinum Toxin')}
                          </td>
                          <td className="p-2">
                            <Badge variant="secondary">{treatment.successRate}</Badge>
                          </td>
                          <td className="p-2">
                            <Badge className={cn(
                              "text-primary-foreground",
                              getInvasivenessLevel(treatment.name).color
                            )}>
                              {getInvasivenessLevel(treatment.name).level}
                            </Badge>
                          </td>
                          <td className="p-2">
                            <span className="text-sm">{treatment.duration}</span>
                          </td>
                          <td className="p-2">
                            <span className="text-sm">{treatment.indications[0]}</span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Decision Support */}
        <div className="mt-12">
          <Card className="bg-primary/5 border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-primary" />
                Choosing the Right Treatment
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-4",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div>
                  <h4 className="font-semibold mb-2">Consider Botulinum Toxin if:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• First-line treatment</li>
                    <li>• Any stage of disease</li>
                    <li>• Prefer non-surgical option</li>
                    <li>• Want reversible treatment</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Consider Surgery if:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Young, healthy patient</li>
                    <li>• Desire permanent cure</li>
                    <li>• Confirmed vascular compression</li>
                    <li>• Failed botulinum toxin</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Consider Medications if:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Mild symptoms</li>
                    <li>• Adjunct to other treatments</li>
                    <li>• Cannot receive injections</li>
                    <li>• Temporary symptom control</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <Card className="bg-info-light border border-info/30">
            <CardContent className="pt-6">
              <h3 className="font-semibold mb-2">Expert Hemifacial Spasm Treatment</h3>
              <p className="text-muted-foreground mb-4">
                Our neurosurgical team specialises in hemifacial spasm treatment and will help 
                you choose the best treatment option based on your specific symptoms and personal preferences.
              </p>
              <Button size={deviceInfo.isMobile ? "default" : "lg"}>
                Schedule Consultation
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default HemifacialTreatmentComparison;
