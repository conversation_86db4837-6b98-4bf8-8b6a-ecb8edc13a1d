import { TrendingUp, Clock, Eye, Activity, AlertTriangle, Info, ChevronDown, ChevronUp } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { Progress } from '@/components/ui/progress';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ProgressionStage {
  stage: string;
  description: string;
  characteristics: string[];
  duration: string;
  impact: string;
}

interface SpasmProgressionSectionProps {
  title: string;
  description: string;
  stages: ProgressionStage[];
}

export function SpasmProgressionSection({ 
  title, 
  description, 
  stages 
}: SpasmProgressionSectionProps) {
  const deviceInfo = useDeviceDetection();
  const [expandedStage, setExpandedStage] = useState<string | null>(null);

  const toggleExpanded = (stage: string) => {
    setExpandedStage(expandedStage === stage ? null : stage);
  };

  const getStageIcon = (stage: string) => {
    if (stage.includes('Stage 1')) return Eye;
    if (stage.includes('Stage 2')) return Activity;
    if (stage.includes('Stage 3')) return TrendingUp;
    return AlertTriangle;
  };

  const getStageColor = (stage: string) => {
    if (stage.includes('Stage 1')) return 'text-success dark:text-success bg-muted dark:from-info-light/50 dark:to-info-light/30 border-success/70 dark:border-success/50';
    if (stage.includes('Stage 2')) return 'text-info dark:text-info bg-muted dark:from-info-light/50 dark:to-info-light/30 border-info/70 dark:border-info/50';
    if (stage.includes('Stage 3')) return 'text-info dark:text-info bg-muted dark:from-info-light/50 dark:to-info-light/30 border-info/70 dark:border-info/50';
    return 'text-foreground dark:text-foreground bg-muted dark:from-info-light/50 dark:to-info-light/30 border-border/70 dark:border-border';
  };

  const getSeverityLevel = (stage: string) => {
    if (stage.includes('Stage 1')) {
      return { level: 'Mild', colour: 'bg-success-light/30 text-foreground', value: 25 };
    }
    if (stage.includes('Stage 2')) {
      return { level: 'Moderate', colour: 'bg-info-light/30 text-foreground', value: 50 };
    }
    if (stage.includes('Stage 3')) {
      return { level: 'Significant', colour: 'bg-info-light/30 text-foreground', value: 75 };
    }
    return { level: 'Severe', colour: 'bg-muted text-foreground', value: 100 };
  };

  const getStageNumber = (stage: string) => {
    if (stage.includes('Stage 1')) return 1;
    if (stage.includes('Stage 2')) return 2;
    if (stage.includes('Stage 3')) return 3;
    return 4;
  };

  return (
    <section className={cn(
      "bg-background",
      deviceInfo.isMobile ? "py-12" : "py-20"
    )}>
      <div className="container">
        <div className="text-centre mb-16">
          <h2 className={cn(
            "font-bold text-foreground mb-6 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-4xl mx-auto leading-relaxed",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* Progression Overview */}
        <div className="mb-16">
          <Card className={cn(
            "medical-card backdrop-blur-sm",
            "shadow-xl hover:shadow-2xl transition-all duration-300"
          )}>
            <CardContent className="pt-8 pb-8">
              <div className={cn(
                "grid gap-12 items-centre",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h3 className={cn(
                    "font-bold text-foreground mb-6 leading-tight",
                    deviceInfo.isMobile ? "text-xl" : "text-2xl"
                  )}>
                    Natural History of Hemifacial Spasm
                  </h3>
                  <p className={cn(
                    "text-muted-foreground mb-6 leading-relaxed",
                    deviceInfo.isMobile ? "text-sm" : "text-base"
                  )}>
                    Hemifacial spasm typically follows a predictable progression pattern, starting with mild 
                    eyelid twitching and gradually spreading to involve the entire side of the face. 
                    Understanding this progression helps in treatment planning and patient education.
                  </p>
                  <div className="space-y-3">
                    <div className="flex items-centre gap-3">
                      <div className="w-8 h-8 bg-success text-primary-foreground rounded-full flex items-centre justify-centre text-sm font-bold">
                        1
                      </div>
                      <div>
                        <h4 className="font-medium">Eyelid Involvement</h4>
                        <p className="text-sm text-muted-foreground">Initial twitching around the eye</p>
                      </div>
                    </div>
                    <div className="flex items-centre gap-3">
                      <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre text-sm font-bold">
                        2
                      </div>
                      <div>
                        <h4 className="font-medium">Periocular Spread</h4>
                        <p className="text-sm text-muted-foreground">Extension to muscles around the eye</p>
                      </div>
                    </div>
                    <div className="flex items-centre gap-3">
                      <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre text-sm font-bold">
                        3
                      </div>
                      <div>
                        <h4 className="font-medium">Mid-face Involvement</h4>
                        <p className="text-sm text-muted-foreground">Cheek and mouth muscle spasms</p>
                      </div>
                    </div>
                    <div className="flex items-centre gap-3">
                      <div className="w-8 h-8 bg-muted text-primary-foreground rounded-full flex items-centre justify-centre text-sm font-bold">
                        4
                      </div>
                      <div>
                        <h4 className="font-medium">Complete Hemifacial</h4>
                        <p className="text-sm text-muted-foreground">Entire side of face and neck</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex justify-centre">
                  <div className="relative">
                    <img 
                      src="/images/brain-conditions/brain-anatomy-detailed.jpg" 
                      alt="Hemifacial spasm progression illustration"
                      className="rounded-lg shadow-lg max-w-full h-auto"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Stage Details */}
        <div className="space-y-6">
          {stages.map((stage, index) => {
            const Icon = getStageIcon(stage.stage);
            const isExpanded = expandedStage === stage.stage;
            const severityLevel = getSeverityLevel(stage.stage);
            const stageNumber = getStageNumber(stage.stage);
            
            return (
              <Card key={index} className={cn("transition-all duration-200", getStageColor(stage.stage))}>
                <CardHeader>
                  <div className="flex items-centre justify-between">
                    <div className="flex items-centre gap-3">
                      <div className="p-2 rounded-full bg-background/80">
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-lg flex items-centre gap-2">
                          {stage.stage}
                          <span className="text-sm font-normal text-muted-foreground">
                            ({stage.duration})
                          </span>
                        </CardTitle>
                        <CardDescription className="text-sm">{stage.description}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-centre gap-2">
                      <Badge className={severityLevel.colour}>
                        {severityLevel.level}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(stage.stage)}
                        className="flex items-centre gap-1"
                      >
                        <Info className="h-4 w-4" />
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      {/* Severity Visualization */}
                      <div className="mb-6 p-4 bg-background/50 rounded-lg">
                        <h4 className="font-semibold mb-3">Severity Assessment</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between items-centre">
                            <span className="text-sm">Impact Level</span>
                            <span className="text-sm font-medium">{severityLevel.level}</span>
                          </div>
                          <Progress value={severityLevel.value} className="h-2" />
                          <p className="text-xs text-muted-foreground">{stage.impact}</p>
                        </div>
                      </div>

                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        {/* Characteristics */}
                        <div>
                          <h4 className="font-semibold mb-3">Key Characteristics</h4>
                          <ul className="space-y-2">
                            {stage.characteristics.map((characteristic, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{characteristic}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Timeline and Impact */}
                        <div>
                          <h4 className="font-semibold mb-3">Timeline and Impact</h4>
                          <div className="space-y-3">
                            <div className="p-3 bg-background/70 rounded-lg border">
                              <div className="flex items-centre gap-2 mb-1">
                                <Clock className="h-4 w-4 text-info" />
                                <span className="font-medium text-sm">Duration</span>
                              </div>
                              <p className="text-sm">{stage.duration}</p>
                            </div>
                            <div className="p-3 bg-background/70 rounded-lg border">
                              <div className="flex items-centre gap-2 mb-1">
                                <TrendingUp className="h-4 w-4 text-info" />
                                <span className="font-medium text-sm">Functional Impact</span>
                              </div>
                              <p className="text-sm">{stage.impact}</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Stage-Specific Information */}
                      <div className="mt-6 p-4 bg-background/50 rounded-lg">
                        <h4 className="font-semibold mb-2">Clinical Considerations</h4>
                        <p className="text-sm text-muted-foreground">
                          {stage.stage.includes('Stage 1') && 
                            "Early intervention with botulinum toxin can prevent progression and maintain quality of life. Many patients at this stage have excellent treatment outcomes with minimal side effects."
                          }
                          {stage.stage.includes('Stage 2') && 
                            "This stage often marks the point where patients seek medical attention due to functional impairment. Treatment response remains excellent, and early intervention is still highly beneficial."
                          }
                          {stage.stage.includes('Stage 3') && 
                            "Significant functional and cosmetic concerns develop at this stage. Treatment becomes more complex, requiring careful injection technique and potentially higher doses of botulinum toxin."
                          }
                          {stage.stage.includes('Stage 4') && 
                            "Advanced disease with severe functional impairment. Treatment may be more challenging, and surgical options should be considered for suitable candidates seeking permanent cure."
                          }
                        </p>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* Treatment Timing Guide */}
        <div className="mt-12">
          <Card className="bg-primary/5 border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2">
                <Clock className="h-5 w-5 text-primary" />
                Optimal Treatment Timing
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-4",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h4 className="font-semibold mb-2">Early Treatment Benefits:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Prevents disease progression</li>
                    <li>• Better treatment response</li>
                    <li>• Fewer side effects</li>
                    <li>• Maintains quality of life</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Delayed Treatment Risks:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Progressive muscle involvement</li>
                    <li>• Increased functional impairment</li>
                    <li>• More complex treatment needs</li>
                    <li>• Potential social isolation</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Progression Timeline */}
        <div className="mt-12">
          <Card className="bg-muted">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2">
                <TrendingUp className="h-5 w-5 text-foreground" />
                Typical Progression Timeline
              </CardTitle>
              <CardDescription>
                Understanding the natural history helps guide treatment decisions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-4"
              )}>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-success text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    0-1
                  </div>
                  <h4 className="font-semibold mb-2">Initial Year</h4>
                  <p className="text-sm text-muted-foreground">
                    Eyelid twitching begins, often intermittent and stress-related
                  </p>
                </div>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    1-2
                  </div>
                  <h4 className="font-semibold mb-2">Years 1-2</h4>
                  <p className="text-sm text-muted-foreground">
                    Spread to periocular muscles, more frequent spasms
                  </p>
                </div>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    2-5
                  </div>
                  <h4 className="font-semibold mb-2">Years 2-5</h4>
                  <p className="text-sm text-muted-foreground">
                    Mid-face involvement, significant functional impact
                  </p>
                </div>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    5+
                  </div>
                  <h4 className="font-semibold mb-2">Beyond 5 Years</h4>
                  <p className="text-sm text-muted-foreground">
                    Complete hemifacial involvement, severe disability
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default SpasmProgressionSection;
