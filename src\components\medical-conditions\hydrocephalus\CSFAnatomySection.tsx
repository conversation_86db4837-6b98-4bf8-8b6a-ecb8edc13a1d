import { Brain, Droplets, Network, Layers, Info, ChevronDown, ChevronUp, Activity, Zap, Waves } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface CSFStructure {
  structure: string;
  description: string;
  normalFunction: string[];
  hydrocephalusEffects: string[];
}

interface CSFAnatomySectionProps {
  title: string;
  description: string;
  csfSystem: CSFStructure[];
}

export function CSFAnatomySection({ 
  title, 
  description, 
  csfSystem 
}: CSFAnatomySectionProps) {
  const deviceInfo = useDeviceDetection();
  const [expandedStructure, setExpandedStructure] = useState<string | null>(null);

  const toggleExpanded = (structure: string) => {
    setExpandedStructure(expandedStructure === structure ? null : structure);
  };

  const getStructureIcon = (structure: string) => {
    if (structure.includes('Lateral Ventricles')) return Brain;
    if (structure.includes('Third and Fourth')) return Network;
    if (structure.includes('Choroid Plexus')) return Droplets;
    if (structure.includes('Arachnoid')) return Layers;
    return Activity;
  };

  const getStructureColor = (structure: string) => {
    if (structure.includes('Lateral Ventricles')) return 'text-foreground bg-info-light border-info/30';
    if (structure.includes('Third and Fourth')) return 'text-foreground bg-success-light border-success/30';
    if (structure.includes('Choroid Plexus')) return 'text-medical-blue bg-medical-blue-light border-medical-blue/30';
    if (structure.includes('Arachnoid')) return 'text-foreground bg-info-light border-info/30';
    return 'text-muted-foreground bg-muted border-border';
  };

  const getStructureImportance = (structure: string) => {
    if (structure.includes('Lateral Ventricles')) {
      return { level: 'Primary Site', colour: 'badge-info' };
    }
    if (structure.includes('Choroid Plexus')) {
      return { level: 'CSF Production', colour: 'badge-medical' };
    }
    if (structure.includes('Arachnoid')) {
      return { level: 'CSF Absorption', colour: 'badge-info' };
    }
    return { level: 'CSF Pathway', colour: 'badge-routine' };
  };

  return (
    <section className={cn(
      "py-16 hydrocephalus-section",
      deviceInfo.isMobile ? "px-4" : ""
    )}>
      <div className="container">
        <div className="text-centre mb-12">
          <h2 className={cn(
            "text-enhanced-heading font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-enhanced-body max-w-3xl mx-auto leading-relaxed",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* CSF System Overview */}
        <div className="mb-12">
          <Card className="medical-card">
            <CardContent className="pt-6">
              <div className={cn(
                "grid gap-8 items-centre",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h3 className="text-enhanced-heading text-xl font-semibold mb-4">Cerebrospinal Fluid System</h3>
                  <p className="text-enhanced-body mb-4 leading-relaxed">
                    The cerebrospinal fluid system is a complex network that produces, circulates, and absorbs
                    CSF to protect and nourish the brain. In hydrocephalus, this delicate balance is disrupted,
                    leading to fluid accumulation and increased pressure.
                  </p>
                  <div className="space-y-3">
                    <div className="flex items-centre gap-3">
                      <div className="w-4 h-4 bg-info rounded-full shadow-md"></div>
                      <span className="text-enhanced-body text-sm font-medium">Lateral Ventricles (Primary CSF Storage)</span>
                    </div>
                    <div className="flex items-centre gap-3">
                      <div className="w-4 h-4 bg-success rounded-full shadow-md"></div>
                      <span className="text-enhanced-body text-sm font-medium">Third & Fourth Ventricles (CSF Pathways)</span>
                    </div>
                    <div className="flex items-centre gap-3">
                      <div className="w-4 h-4 bg-medical-blue rounded-full shadow-md"></div>
                      <span className="text-enhanced-body text-sm font-medium">Choroid Plexus (CSF Production)</span>
                    </div>
                    <div className="flex items-centre gap-3">
                      <div className="w-4 h-4 bg-info rounded-full shadow-md"></div>
                      <span className="text-enhanced-body text-sm font-medium">Arachnoid Granulations (CSF Absorption)</span>
                    </div>
                  </div>
                </div>
                <div className="flex justify-centre">
                  <div className="relative">
                    <img 
                      src="/images/brain-conditions/hydrocephalus-anatomy.jpg" 
                      alt="Hydrocephalus anatomy showing enlarged ventricles and CSF flow"
                      className="rounded-lg shadow-lg max-w-full h-auto"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-background/20 to-transparent rounded-lg"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* CSF System Details */}
        <div className="space-y-6">
          {csfSystem.map((structure, index) => {
            const Icon = getStructureIcon(structure.structure);
            const isExpanded = expandedStructure === structure.structure;
            const structureImportance = getStructureImportance(structure.structure);
            
            return (
              <Card key={index} className={cn(
                "transition-all duration-300 hover:shadow-xl backdrop-blur-sm",
                getStructureColor(structure.structure),
                isExpanded && "ring-2 ring-primary/20"
              )}>
                <CardHeader className="pb-4">
                  <div className="flex items-centre justify-between">
                    <div className="flex items-centre gap-4">
                      <div className="p-3 rounded-xl medical-card-inner shadow-lg backdrop-blur-sm">
                        <Icon className="h-6 w-6 text-current" />
                      </div>
                      <div className="space-y-1">
                        <CardTitle className="text-lg font-bold text-current leading-tight">
                          {structure.structure}
                        </CardTitle>
                        <CardDescription className="text-sm text-current/80 font-medium leading-relaxed">
                          {structure.description}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-centre gap-3">
                      <Badge className={structureImportance.colour}>
                        {structureImportance.level}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(structure.structure)}
                        className="flex items-centre gap-1 enhanced-hover"
                      >
                        <Info className="h-4 w-4" />
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        {/* Normal Functions */}
                        <div className="space-y-3">
                          <h4 className="text-enhanced-strong font-semibold mb-3 text-base">Normal Functions</h4>
                          <ul className="space-y-3">
                            {structure.normalFunction.map((func, idx) => (
                              <li key={idx} className="flex items-start gap-3">
                                <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm leading-relaxed">{func}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Hydrocephalus Effects */}
                        <div className="space-y-3">
                          <h4 className="text-enhanced-strong font-semibold mb-3 text-base">Effects in Hydrocephalus</h4>
                          <ul className="space-y-3">
                            {structure.hydrocephalusEffects.map((effect, idx) => (
                              <li key={idx} className="flex items-start gap-3">
                                <div className="w-2 h-2 bg-muted rounded-full mt-2 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm leading-relaxed">{effect}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Additional Information */}
                      <div className="mt-6 p-4 medical-card-inner rounded-lg">
                        <h4 className="text-enhanced-heading font-semibold mb-2">Clinical Significance</h4>
                        <p className="text-enhanced-body text-sm">
                          {structure.structure.includes('Lateral Ventricles') && 
                            "The lateral ventricles are the primary site of CSF accumulation in hydrocephalus. Their enlargement is the hallmark of the condition and can be easily detected on brain imaging. The degree of enlargement often correlates with the severity of symptoms and the urgency of treatment."
                          }
                          {structure.structure.includes('Choroid Plexus') && 
                            "The choroid plexus produces approximately 500ml of CSF daily in adults. In hydrocephalus, this production continues despite impaired drainage, contributing to fluid accumulation. Understanding CSF production is crucial for medical management strategies."
                          }
                          {structure.structure.includes('Third and Fourth') && 
                            "The third and fourth ventricles form critical pathways in the CSF circulation system. Obstruction at these levels can cause upstream ventricular enlargement and may be amenable to endoscopic treatment options like third ventriculostomy."
                          }
                          {structure.structure.includes('Arachnoid') && 
                            "Arachnoid granulations are the primary site of CSF absorption into the venous system. Impaired function of these structures, often due to inflammation or scarring, is a common cause of communicating hydrocephalus and typically requires shunt placement for treatment."
                          }
                        </p>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* CSF Flow Dynamics */}
        <div className="mt-12">
          <Card className="medical-card bg-info-light border border-info/30">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2 text-enhanced-heading">
                <Waves className="h-5 w-5 text-primary" />
                CSF Flow Dynamics in Health and Disease
              </CardTitle>
              <CardDescription className="text-enhanced-body">
                Understanding normal CSF flow and how it becomes disrupted in hydrocephalus
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-8",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div className="text-centre space-y-4">
                  <div className="w-16 h-16 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre font-bold text-xl mx-auto shadow-lg">
                    1
                  </div>
                  <h4 className="text-enhanced-strong font-semibold text-base">CSF Production</h4>
                  <p className="text-enhanced-body text-sm leading-relaxed">
                    Choroid plexus produces 20ml/hour of CSF in the lateral ventricles
                  </p>
                </div>
                <div className="text-centre space-y-4">
                  <div className="w-16 h-16 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre font-bold text-xl mx-auto shadow-lg">
                    2
                  </div>
                  <h4 className="text-enhanced-strong font-semibold text-base">CSF Circulation</h4>
                  <p className="text-enhanced-body text-sm leading-relaxed">
                    CSF flows through ventricles and around brain and spinal cord
                  </p>
                </div>
                <div className="text-centre space-y-4">
                  <div className="w-16 h-16 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre font-bold text-xl mx-auto shadow-lg">
                    3
                  </div>
                  <h4 className="text-enhanced-strong font-semibold text-base">CSF Absorption</h4>
                  <p className="text-enhanced-body text-sm leading-relaxed">
                    Arachnoid granulations absorb CSF back into venous circulation
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Hydrocephalus Mechanism */}
        <div className="mt-12">
          <Card className="medical-card bg-muted-light border border-border/70">
            <CardHeader>
              <CardTitle className="text-enhanced-heading flex items-centre gap-2 text-foreground">
                <Zap className="h-5 w-5" />
                How Hydrocephalus Develops
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-enhanced-body text-foreground text-sm leading-relaxed">
                Hydrocephalus occurs when the delicate balance between CSF production and absorption is disrupted.
                Whether due to overproduction, impaired circulation, or reduced absorption, the result is the same:
                CSF accumulates in the ventricles, causing them to enlarge and creating increased pressure that can
                damage brain tissue. Early recognition and treatment are essential to prevent irreversible damage.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default CSFAnatomySection;
