import { <PERSON>, Zap, MapPin, Target, Eye, Activity } from "lucide-react";
import React from 'react';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface AnatomyPoint {
  id: string;
  title: string;
  description: string;
  location: string;
  significance: string;
}

interface MeralgiaAnatomySectionProps {
  className?: string;
}

const anatomyData = {
  title: "Lateral Femoral Cutaneous Nerve Anatomy",
  subtitle: "Understanding the anatomical pathway and compression points of the LFCN",
  description: [
    "The lateral femoral cutaneous nerve (LFCN) is a purely sensory nerve that provides sensation to the outer thigh. Understanding its anatomical course is crucial for comprehending how meralgia paresthetica develops.",
    "This nerve originates from the lumbar plexus (L2-L3 nerve roots) and travels through several anatomical structures where compression can occur, leading to the characteristic symptoms of meralgia paresthetica."
  ],
  
  anatomyPoints: [
    {
      id: "origin",
      title: "Nerve Origin",
      description: "The LFCN arises from the lateral border of the psoas muscle, formed by nerve roots L2 and L3",
      location: "Lumbar spine region (L2-L3)",
      significance: "Pure sensory nerve with no motor function"
    },
    {
      id: "course",
      title: "Anatomical Course",
      description: "Travels laterally across the iliacus muscle toward the anterior superior iliac spine",
      location: "Pelvis - across iliacus muscle",
      significance: "Protected by muscle until reaching compression point"
    },
    {
      id: "compression-point",
      title: "Primary Compression Site",
      description: "Passes under or through the inguinal ligament near the anterior superior iliac spine",
      location: "Inguinal ligament at ASIS",
      significance: "Most common site of nerve entrapment"
    },
    {
      id: "distribution",
      title: "Sensory Distribution",
      description: "Provides sensation to the lateral thigh from hip to knee level",
      location: "Lateral thigh surface",
      significance: "Defines the symptom distribution pattern"
    }
  ],

  compressionMechanisms: [
    {
      title: "Inguinal Ligament Entrapment",
      description: "Tight inguinal ligament compresses nerve as it passes underneath",
      severity: "Most Common",
      factors: ["Obesity", "Pregnancy", "Tight clothing"]
    },
    {
      title: "Fascial Compression",
      description: "Thickened fascia around the nerve creates compression",
      severity: "Common",
      factors: ["Scar tissue", "Inflammation", "Repetitive trauma"]
    },
    {
      title: "Anatomical Variations",
      description: "Individual anatomical differences affect nerve pathway",
      severity: "Variable",
      factors: ["Genetic variations", "Developmental differences"]
    }
  ],

  clinicalCorrelation: [
    {
      finding: "Lateral Thigh Numbness",
      anatomicalBasis: "LFCN sensory distribution",
      significance: "Confirms nerve involvement"
    },
    {
      finding: "No Motor Weakness",
      anatomicalBasis: "Pure sensory nerve function",
      significance: "Distinguishes from other conditions"
    },
    {
      finding: "Inguinal Tenderness",
      anatomicalBasis: "Compression at inguinal ligament",
      significance: "Localizes compression site"
    }
  ]
};

const MeralgiaAnatomySection: React.FC<MeralgiaAnatomySectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "section-background border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-centre mb-20">
          <Badge variant="info" className="mb-6">
            <Brain className="w-4 h-4 mr-2" />
            Nerve Anatomy
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            {anatomyData.title}
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            {anatomyData.subtitle}
          </p>
        </div>

        {/* Main Content Grid */}
        <div className={cn(
          "grid gap-12 mb-16",
          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-2"
        )}>
          {/* Anatomy Illustration */}
          <div className="space-y-6">
            <Card className="medical-card overflow-hidden">
              <CardContent className="p-0">
                <SafeImage
                  src="https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Lateral femoral cutaneous nerve anatomy illustration"
                  fallbackSrc="https://images.pexels.com/photos/7659564/pexels-photo-7659564.jpeg?auto=compress&cs=tinysrgb&w=800"
                  className="w-full h-80 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-enhanced-heading text-lg font-semibold mb-2">
                    LFCN Anatomical Pathway
                  </h3>
                  <p className="text-enhanced-body text-sm">
                    Detailed visualization of the lateral femoral cutaneous nerve course from lumbar spine to thigh distribution
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Description */}
            <div className="space-y-4">
              {anatomyData.description.map((paragraph, index) => (
                <p key={index} className="text-enhanced-body leading-relaxed">
                  {paragraph}
                </p>
              ))}
            </div>
          </div>

          {/* Anatomy Points */}
          <div className="space-y-6">
            <h3 className={cn(
              "font-bold text-foreground mb-6",
              deviceInfo.isMobile ? "text-xl" : "text-2xl"
            )}>
              Key Anatomical Structures
            </h3>
            
            <div className="space-y-4">
              {anatomyData.anatomyPoints.map((point, index) => (
                <Card key={point.id} className="medical-card hover:shadow-lg transition-all duration-300">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-enhanced-heading flex items-centre gap-3">
                      <div className="p-2 rounded-lg bg-primary/10 border border-primary/20">
                        <MapPin className="w-4 h-4 text-primary" />
                      </div>
                      {point.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <p className="text-enhanced-body">{point.description}</p>
                    <div className="grid grid-cols-1 gap-2">
                      <div className="flex items-start gap-2">
                        <Target className="w-4 h-4 text-info mt-0.5 flex-shrink-0" />
                        <div>
                          <span className="text-enhanced-caption font-medium">Location: </span>
                          <span className="text-enhanced-body">{point.location}</span>
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <Zap className="w-4 h-4 text-info mt-0.5 flex-shrink-0" />
                        <div>
                          <span className="text-enhanced-caption font-medium">Significance: </span>
                          <span className="text-enhanced-body">{point.significance}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Compression Mechanisms */}
        <div className="mb-16">
          <h3 className={cn(
            "font-bold text-foreground mb-8 text-centre",
            deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
          )}>
            Compression Mechanisms
          </h3>
          
          <div className={cn(
            "grid gap-6",
            deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
          )}>
            {anatomyData.compressionMechanisms.map((mechanism, index) => (
              <Card key={index} className="medical-card h-full hover:shadow-xl transition-all duration-300">
                <CardHeader>
                  <CardTitle className="text-enhanced-heading flex items-centre gap-3">
                    <Activity className="w-5 h-5 text-primary" />
                    {mechanism.title}
                  </CardTitle>
                  <Badge variant="info" className="w-fit">
                    {mechanism.severity}
                  </Badge>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-enhanced-body">{mechanism.description}</p>
                  <div>
                    <h4 className="text-enhanced-caption font-medium mb-2">Contributing Factors:</h4>
                    <ul className="space-y-1">
                      {mechanism.factors.map((factor, factorIndex) => (
                        <li key={factorIndex} className="flex items-centre gap-2">
                          <div className="w-1.5 h-1.5 rounded-full bg-primary flex-shrink-0" />
                          <span className="text-enhanced-body text-sm">{factor}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Clinical Correlation */}
        <Card className="medical-card">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-centre gap-3">
              <Eye className="w-5 h-5 text-primary" />
              Clinical-Anatomical Correlation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              {anatomyData.clinicalCorrelation.map((correlation, index) => (
                <div key={index} className="space-y-3">
                  <h4 className="text-enhanced-subheading font-semibold">{correlation.finding}</h4>
                  <div className="space-y-2">
                    <div>
                      <span className="text-enhanced-caption font-medium">Anatomical Basis: </span>
                      <span className="text-enhanced-body text-sm">{correlation.anatomicalBasis}</span>
                    </div>
                    <div>
                      <span className="text-enhanced-caption font-medium">Clinical Significance: </span>
                      <span className="text-enhanced-body text-sm">{correlation.significance}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default MeralgiaAnatomySection;
