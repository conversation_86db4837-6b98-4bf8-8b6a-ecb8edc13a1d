import { 
  Du<PERSON>bell, 
  Activity, 
  Target, 
  Clock, 
  TrendingUp, 
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON>,
  <PERSON>otate<PERSON>c<PERSON>,
  Heart,
  Gauge
} from "lucide-react";
import React, { useState } from 'react';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ExerciseStep {
  step: number;
  instruction: string;
  duration?: string;
  repetitions?: string;
  image?: string;
}

interface Exercise {
  id: string;
  title: string;
  description: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  duration: string;
  frequency: string;
  benefits: string[];
  precautions: string[];
  steps: ExerciseStep[];
  videoUrl?: string;
  imageUrl: string;
  category: 'stretching' | 'strengthening' | 'nerve-gliding' | 'postural';
}

interface MeralgiaExerciseSectionProps {
  className?: string;
}

const exercises: Exercise[] = [
  {
    id: 'hip-flexor-stretch',
    title: 'Hip Flexor Stretch',
    description: 'Gentle stretching to reduce tension on the lateral femoral cutaneous nerve by releasing tight hip flexors',
    difficulty: 'Beginner',
    duration: '5-10 minutes',
    frequency: '2-3 times daily',
    benefits: [
      'Reduces nerve compression at inguinal ligament',
      'Improves hip mobility and flexibility',
      'Decreases anterior pelvic tilt',
      'Relieves tension in surrounding tissues'
    ],
    precautions: [
      'Stop if pain increases',
      'Avoid bouncing or forcing the stretch',
      'Maintain gentle, sustained pressure',
      'Breathe normally throughout'
    ],
    steps: [
      {
        step: 1,
        instruction: 'Stand in a lunge position with affected leg behind you',
        duration: '10 seconds'
      },
      {
        step: 2,
        instruction: 'Place hands on front thigh for support',
        duration: '5 seconds'
      },
      {
        step: 3,
        instruction: 'Gently push hips forward while keeping back leg straight',
        duration: '30 seconds',
        repetitions: '3 times'
      },
      {
        step: 4,
        instruction: 'Feel stretch in front of hip and thigh',
        duration: '30 seconds'
      },
      {
        step: 5,
        instruction: 'Return to starting position slowly',
        duration: '10 seconds'
      }
    ],
    imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'stretching'
  },
  {
    id: 'pelvic-tilts',
    title: 'Pelvic Tilts',
    description: 'Core strengthening exercise to improve posture and reduce anterior pelvic tilt that contributes to nerve compression',
    difficulty: 'Beginner',
    duration: '5-8 minutes',
    frequency: 'Daily',
    benefits: [
      'Strengthens deep core muscles',
      'Improves pelvic alignment',
      'Reduces lumbar lordosis',
      'Decreases pressure on LFCN'
    ],
    precautions: [
      'Keep movements slow and controlled',
      'Avoid holding breath',
      'Stop if lower back pain occurs',
      'Start with small movements'
    ],
    steps: [
      {
        step: 1,
        instruction: 'Lie on back with knees bent, feet flat on floor',
        duration: '10 seconds'
      },
      {
        step: 2,
        instruction: 'Place hands on lower ribs and hip bones',
        duration: '5 seconds'
      },
      {
        step: 3,
        instruction: 'Gently tilt pelvis to flatten lower back against floor',
        duration: '5 seconds',
        repetitions: '10-15 times'
      },
      {
        step: 4,
        instruction: 'Hold the position while breathing normally',
        duration: '5 seconds'
      },
      {
        step: 5,
        instruction: 'Slowly return to neutral position',
        duration: '3 seconds'
      }
    ],
    imageUrl: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'strengthening'
  },
  {
    id: 'nerve-gliding',
    title: 'Lateral Femoral Cutaneous Nerve Gliding',
    description: 'Specific nerve mobilization exercises to improve nerve mobility and reduce adhesions around the LFCN',
    difficulty: 'Intermediate',
    duration: '8-10 minutes',
    frequency: '2 times daily',
    benefits: [
      'Improves nerve mobility and gliding',
      'Reduces nerve adhesions',
      'Enhances blood flow to nerve',
      'Decreases nerve sensitivity'
    ],
    precautions: [
      'Perform movements slowly and gently',
      'Stop if symptoms worsen',
      'Avoid aggressive stretching',
      'Progress gradually'
    ],
    steps: [
      {
        step: 1,
        instruction: 'Lie on back with affected leg straight',
        duration: '10 seconds'
      },
      {
        step: 2,
        instruction: 'Slowly bring knee toward chest',
        duration: '10 seconds'
      },
      {
        step: 3,
        instruction: 'Straighten leg while maintaining hip flexion',
        duration: '5 seconds',
        repetitions: '10 times'
      },
      {
        step: 4,
        instruction: 'Gently move ankle up and down',
        duration: '10 seconds'
      },
      {
        step: 5,
        instruction: 'Lower leg slowly to starting position',
        duration: '10 seconds'
      }
    ],
    imageUrl: 'https://images.unsplash.com/photo-1506629905607-c28b47e8b6b1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'nerve-gliding'
  },
  {
    id: 'postural-correction',
    title: 'Postural Correction Exercises',
    description: 'Comprehensive postural training to address underlying biomechanical factors contributing to nerve compression',
    difficulty: 'Intermediate',
    duration: '10-15 minutes',
    frequency: 'Daily',
    benefits: [
      'Improves overall posture',
      'Reduces compensatory patterns',
      'Strengthens postural muscles',
      'Prevents symptom recurrence'
    ],
    precautions: [
      'Focus on quality over quantity',
      'Maintain proper alignment',
      'Progress exercises gradually',
      'Seek guidance if unsure'
    ],
    steps: [
      {
        step: 1,
        instruction: 'Stand against wall with back flat',
        duration: '30 seconds'
      },
      {
        step: 2,
        instruction: 'Perform wall slides with arms',
        duration: '10 repetitions'
      },
      {
        step: 3,
        instruction: 'Practice chin tucks for neck alignment',
        duration: '10 repetitions'
      },
      {
        step: 4,
        instruction: 'Shoulder blade squeezes',
        duration: '10 repetitions'
      },
      {
        step: 5,
        instruction: 'Hold proper standing posture',
        duration: '60 seconds'
      }
    ],
    imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'postural'
  }
];

const MeralgiaExerciseSection: React.FC<MeralgiaExerciseSectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();
  const [selectedCategory, setSelectedCategory] = useState<string>('stretching');
  const [activeExercise, setActiveExercise] = useState<string>('hip-flexor-stretch');

  const categories = [
    { id: 'stretching', label: 'Stretching', icon: Activity },
    { id: 'strengthening', label: 'Strengthening', icon: Dumbbell },
    { id: 'nerve-gliding', label: 'Nerve Gliding', icon: Target },
    { id: 'postural', label: 'Postural', icon: TrendingUp }
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-success-light text-success border border-success/30';
      case 'Intermediate': return 'bg-info-light text-info border border-info/30';
      case 'Advanced': return 'bg-muted-light text-foreground border border-border/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const filteredExercises = exercises.filter(exercise => exercise.category === selectedCategory);

  return (
    <section className={cn(
      "section-background border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Badge variant="info" className="mb-6">
            <Dumbbell className="w-4 h-4 mr-2" />
            Exercise & Therapy
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            Exercise and Physical Therapy Guide
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            Evidence-based exercises designed to reduce nerve compression, improve mobility, and prevent recurrence
          </p>
        </div>

        {/* Exercise Categories */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-12",
            deviceInfo.isMobile ? "grid-cols-2 h-auto" : "grid-cols-4 h-14"
          )}>
            {categories.map((category) => {
              const IconComponent = category.icon;
              return (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className={cn(
                    "flex items-center gap-2 font-medium",
                    deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
                  )}
                >
                  <IconComponent className={cn(
                    deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5"
                  )} />
                  <span className={deviceInfo.isMobile ? "text-center" : ""}>
                    {category.label}
                  </span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* Exercise Content */}
          {categories.map((category) => (
            <TabsContent key={category.id} value={category.id} className="space-y-8">
              <div className={cn(
                "grid gap-8",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
              )}>
                {filteredExercises.map((exercise) => (
                  <Card 
                    key={exercise.id} 
                    className={cn(
                      "medical-card cursor-pointer transition-all duration-300 hover:shadow-xl",
                      activeExercise === exercise.id ? "ring-2 ring-primary/50 border-primary/30" : ""
                    )}
                    onClick={() => setActiveExercise(exercise.id)}
                  >
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-enhanced-heading">{exercise.title}</CardTitle>
                        <Badge className={getDifficultyColor(exercise.difficulty)}>
                          {exercise.difficulty}
                        </Badge>
                      </div>
                      <p className="text-enhanced-body text-sm">{exercise.description}</p>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <SafeImage
                        src={exercise.imageUrl}
                        alt={`${exercise.title} demonstration`}
                        fallbackSrc="https://images.pexels.com/photos/4056723/pexels-photo-4056723.jpeg?auto=compress&cs=tinysrgb&w=800"
                        className="w-full h-48 object-cover rounded-lg"
                      />
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4 text-info" />
                          <span className="text-enhanced-caption">{exercise.duration}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <RotateCcw className="w-4 h-4 text-info" />
                          <span className="text-enhanced-caption">{exercise.frequency}</span>
                        </div>
                      </div>

                      <Button 
                        variant={activeExercise === exercise.id ? "default" : "outline"}
                        className="w-full"
                        onClick={() => setActiveExercise(exercise.id)}
                      >
                        <Play className="w-4 h-4 mr-2" />
                        View Instructions
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Detailed Exercise Instructions */}
        {activeExercise && (
          <Card className="medical-card mt-12">
            <CardHeader>
              <CardTitle className="text-enhanced-heading flex items-center gap-3">
                <Target className="w-5 h-5 text-primary" />
                {exercises.find(ex => ex.id === activeExercise)?.title} - Step by Step
              </CardTitle>
            </CardHeader>
            <CardContent>
              {(() => {
                const exercise = exercises.find(ex => ex.id === activeExercise);
                if (!exercise) return null;

                return (
                  <div className="space-y-8">
                    {/* Exercise Steps */}
                    <div className="space-y-4">
                      <h4 className="text-enhanced-subheading font-semibold">Instructions:</h4>
                      <div className="space-y-4">
                        {exercise.steps.map((step, index) => (
                          <div key={index} className="flex gap-4">
                            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 border border-primary/20 flex items-center justify-center">
                              <span className="text-primary font-semibold text-sm">{step.step}</span>
                            </div>
                            <div className="flex-1">
                              <p className="text-enhanced-body">{step.instruction}</p>
                              {(step.duration || step.repetitions) && (
                                <div className="flex gap-4 mt-1">
                                  {step.duration && (
                                    <span className="text-enhanced-caption">Duration: {step.duration}</span>
                                  )}
                                  {step.repetitions && (
                                    <span className="text-enhanced-caption">Repetitions: {step.repetitions}</span>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Benefits and Precautions */}
                    <div className={cn(
                      "grid gap-6",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
                    )}>
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-success" />
                          Benefits
                        </h4>
                        <ul className="space-y-2">
                          {exercise.benefits.map((benefit, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 rounded-full bg-success mt-2 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{benefit}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                          <AlertTriangle className="w-4 h-4 text-foreground" />
                          Precautions
                        </h4>
                        <ul className="space-y-2">
                          {exercise.precautions.map((precaution, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{precaution}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </CardContent>
          </Card>
        )}

        {/* Exercise Program Guidelines */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Heart className="w-5 h-5 text-primary" />
              Exercise Program Guidelines
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Gauge className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Start Slowly</h4>
                <p className="text-enhanced-body text-sm">Begin with gentle exercises and gradually increase intensity as tolerated</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Clock className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Consistency</h4>
                <p className="text-enhanced-body text-sm">Regular daily practice is more effective than occasional intensive sessions</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-muted/50 border border-border/50 mb-4">
                  <Target className="w-8 h-8 text-foreground mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Listen to Your Body</h4>
                <p className="text-enhanced-body text-sm">Stop if symptoms worsen and consult healthcare provider if needed</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default MeralgiaExerciseSection;
