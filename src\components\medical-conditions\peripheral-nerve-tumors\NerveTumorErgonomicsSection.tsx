import { 
  Shield, 
  Home, 
  Briefcase, 
  Activity, 
  CheckCircle,
  AlertTriangle,
  Target,
  Heart,
  TrendingUp,
  <PERSON>tings,
  Eye
} from "lucide-react";
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ErgonomicRecommendation {
  id: string;
  title: string;
  description: string;
  importance: 'High' | 'Medium' | 'Low';
  recommendations: string[];
  avoidActions: string[];
  scientificRationale: string;
  implementationTips: string[];
}

interface LifestyleCategory {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
  recommendations: ErgonomicRecommendation[];
}

interface NerveTumorErgonomicsSectionProps {
  className?: string;
}

const lifestyleCategories: LifestyleCategory[] = [
  {
    id: 'workplace-ergonomics',
    title: 'Workplace Ergonomics',
    icon: Briefcase,
    description: 'Workplace modifications to reduce nerve stress and prevent tumor growth or symptoms',
    recommendations: [
      {
        id: 'workstation-setup',
        title: 'Ergonomic Workstation Design',
        description: 'Optimising workspace to minimise nerve compression and repetitive stress',
        importance: 'High',
        recommendations: [
          'Adjust monitor height to eye level to reduce neck strain',
          'Use ergonomic keyboard and mouse to reduce wrist pressure',
          'Maintain neutral wrist position during typing',
          'Take regular breaks every 30-60 minutes',
          'Use document holders to reduce neck rotation'
        ],
        avoidActions: [
          'Prolonged typing with bent wrists',
          'Resting wrists on hard surfaces',
          'Working in awkward or twisted positions',
          'Ignoring early warning signs of discomfort'
        ],
        scientificRationale: 'Proper ergonomics reduces mechanical stress on peripheral nerves, potentially slowing tumor growth and preventing symptom development.',
        implementationTips: [
          'Gradually implement changes to allow adaptation',
          'Use ergonomic assessment tools or professional evaluation',
          'Invest in quality adjustable equipment',
          'Train colleagues and supervisors about your needs'
        ]
      },
      {
        id: 'activity-modification',
        title: 'Work Activity Modifications',
        description: 'Adapting work tasks to accommodate nerve tumor limitations',
        importance: 'High',
        recommendations: [
          'Alternate between different types of tasks',
          'Use voice recognition software when appropriate',
          'Delegate tasks that aggravate symptoms',
          'Modify tools and equipment for easier use',
          'Plan demanding tasks for times when symptoms are minimal'
        ],
        avoidActions: [
          'Pushing through pain or increasing symptoms',
          'Performing repetitive tasks for extended periods',
          'Using excessive force or pressure',
          'Ignoring workplace accommodation options'
        ],
        scientificRationale: 'Activity modification reduces mechanical stress on affected nerves and can prevent symptom progression while maintaining productivity.',
        implementationTips: [
          'Communicate openly with supervisors about limitations',
          'Document accommodation needs with medical support',
          'Explore assistive technology options',
          'Consider flexible work arrangements'
        ]
      }
    ]
  },
  {
    id: 'home-environment',
    title: 'Home Environment',
    icon: Home,
    description: 'Home modifications to support daily activities and reduce nerve stress',
    recommendations: [
      {
        id: 'daily-activities',
        title: 'Activities of Daily Living Adaptations',
        description: 'Modifying home tasks to accommodate nerve tumor symptoms',
        importance: 'Medium',
        recommendations: [
          'Use ergonomic tools for cooking and cleaning',
          'Organise frequently used items at comfortable heights',
          'Install lever-style door handles and faucets',
          'Use lightweight tools and utensils',
          'Consider adaptive equipment for challenging tasks'
        ],
        avoidActions: [
          'Lifting heavy objects overhead',
          'Prolonged gripping of tools or utensils',
          'Repetitive twisting or turning motions',
          'Working in cramped or awkward positions'
        ],
        scientificRationale: 'Home modifications reduce daily stress on peripheral nerves and help maintain independence while managing tumor-related symptoms.',
        implementationTips: [
          'Start with the most challenging daily tasks',
          'Consult with occupational therapist for recommendations',
          'Gradually introduce adaptive equipment',
          'Involve family members in understanding modifications'
        ]
      },
      {
        id: 'sleep-positioning',
        title: 'Sleep and Rest Positioning',
        description: 'Optimal positioning to reduce nerve compression during rest',
        importance: 'Medium',
        recommendations: [
          'Use supportive pillows to maintain neutral spine alignment',
          'Avoid sleeping on the affected side if tumor is superficial',
          'Consider splinting affected limb during sleep if recommended',
          'Use mattress that provides adequate support',
          'Maintain comfortable room temperature'
        ],
        avoidActions: [
          'Sleeping in positions that compress the tumor',
          'Using too many or too few pillows',
          'Ignoring numbness or tingling upon waking',
          'Sleeping on stomach if it affects neck or arm nerves'
        ],
        scientificRationale: 'Proper positioning during sleep prevents prolonged nerve compression and allows for optimal recovery and symptom management.',
        implementationTips: [
          'Experiment with different pillow configurations',
          'Consider memory foam or contoured pillows',
          'Use body pillows for additional support',
          'Track sleep quality and symptom changes'
        ]
      }
    ]
  },
  {
    id: 'activity-management',
    title: 'Activity Management',
    icon: Activity,
    description: 'Strategies for managing physical activities and exercise with nerve tumors',
    recommendations: [
      {
        id: 'exercise-modifications',
        title: 'Exercise and Physical Activity Guidelines',
        description: 'Safe exercise practices for individuals with peripheral nerve tumors',
        importance: 'High',
        recommendations: [
          'Focus on low-impact activities that don\'t stress the tumor',
          'Avoid exercises that directly compress or stretch the tumor',
          'Maintain cardiovascular fitness with appropriate activities',
          'Include gentle stretching and range of motion exercises',
          'Work with qualified exercise professionals'
        ],
        avoidActions: [
          'High-impact activities that jar or compress nerves',
          'Exercises that cause pain or neurological symptoms',
          'Lifting heavy weights that strain affected areas',
          'Ignoring body signals and pushing through discomfort'
        ],
        scientificRationale: 'Appropriate exercise maintains overall health while avoiding activities that could accelerate tumor growth or worsen symptoms.',
        implementationTips: [
          'Start with gentle activities and progress gradually',
          'Monitor symptoms before, during, and after exercise',
          'Consider water-based exercises for low-impact options',
          'Maintain exercise log to track tolerance and progress'
        ]
      }
    ]
  },
  {
    id: 'stress-management',
    title: 'Stress Management',
    icon: Heart,
    description: 'Psychological and emotional support strategies for living with nerve tumors',
    recommendations: [
      {
        id: 'coping-strategies',
        title: 'Psychological Coping and Support',
        description: 'Mental health strategies for managing the emotional impact of nerve tumors',
        importance: 'High',
        recommendations: [
          'Develop stress reduction techniques (meditation, deep breathing)',
          'Maintain social connections and support networks',
          'Consider counselling or support groups',
          'Practice mindfulness and relaxation techniques',
          'Maintain realistic expectations about recovery'
        ],
        avoidActions: [
          'Isolating yourself from friends and family',
          'Catastrophic thinking about tumor outcomes',
          'Ignoring mental health needs',
          'Comparing your situation to others'
        ],
        scientificRationale: 'Effective stress management improves overall well-being and may positively influence immune function and healing processes.',
        implementationTips: [
          'Start with simple relaxation techniques',
          'Join online or in-person support groups',
          'Consider professional counselling if needed',
          'Maintain hobbies and activities you enjoy'
        ]
      }
    ]
  }
];

const NerveTumorErgonomicsSection: React.FC<NerveTumorErgonomicsSectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();
  const [selectedCategory, setSelectedCategory] = useState<string>('workplace-ergonomics');

  const getImportanceColour = (importance: string) => {
    switch (importance) {
      case 'High': return 'bg-muted-light text-foreground border border-border/30';
      case 'Medium': return 'bg-info-light text-info border border-info/30';
      case 'Low': return 'bg-success-light text-success border border-success/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <section className={cn(
      "section-background-alt border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-centre mb-20">
          <Badge variant="info" className="mb-6">
            <Shield className="w-4 h-4 mr-2" />
            Ergonomics & Lifestyle
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            Ergonomic and Lifestyle Adaptations
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            Comprehensive lifestyle modifications and adaptive strategies to manage peripheral nerve tumors, 
            reduce symptoms, and maintain quality of life
          </p>
        </div>

        {/* Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-12",
            deviceInfo.isMobile ? "grid-cols-2 h-auto" : "grid-cols-4 h-14"
          )}>
            {lifestyleCategories.map((category) => {
              const IconComponent = category.icon;
              return (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className={cn(
                    "flex items-centre gap-2 font-medium",
                    deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
                  )}
                >
                  <IconComponent className={cn(
                    deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5"
                  )} />
                  <span className={deviceInfo.isMobile ? "text-centre" : ""}>
                    {category.title}
                  </span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* Category Content */}
          {lifestyleCategories.map((category) => (
            <TabsContent key={category.id} value={category.id} className="space-y-8">
              {/* Category Description */}
              <Card className="medical-card">
                <CardHeader>
                  <CardTitle className="text-enhanced-heading flex items-centre gap-3">
                    <category.icon className="w-5 h-5 text-primary" />
                    {category.title}
                  </CardTitle>
                  <p className="text-enhanced-body">{category.description}</p>
                </CardHeader>
              </Card>

              {/* Recommendations */}
              <div className="space-y-8">
                {category.recommendations.map((recommendation) => (
                  <Card key={recommendation.id} className="medical-card">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-enhanced-heading">{recommendation.title}</CardTitle>
                        <Badge className={getImportanceColour(recommendation.importance)}>
                          {recommendation.importance} Priority
                        </Badge>
                      </div>
                      <p className="text-enhanced-body">{recommendation.description}</p>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Recommendations and Avoid Actions */}
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
                      )}>
                        <div>
                          <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                            <CheckCircle className="w-4 h-4 text-success" />
                            Recommended Actions
                          </h4>
                          <ul className="space-y-2">
                            {recommendation.recommendations.map((item, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <CheckCircle className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm">{item}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                            <AlertTriangle className="w-4 h-4 text-foreground" />
                            Actions to Avoid
                          </h4>
                          <ul className="space-y-2">
                            {recommendation.avoidActions.map((item, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm">{item}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Scientific Rationale */}
                      <div className="bg-info/5 border border-info/20 rounded-lg p-4">
                        <h4 className="text-enhanced-subheading font-semibold mb-2 flex items-centre gap-2">
                          <Target className="w-4 h-4 text-info" />
                          Scientific Rationale
                        </h4>
                        <p className="text-enhanced-body text-sm">{recommendation.scientificRationale}</p>
                      </div>

                      {/* Implementation Tips */}
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                          <Settings className="w-4 h-4 text-primary" />
                          Implementation Tips
                        </h4>
                        <ul className="space-y-2">
                          {recommendation.implementationTips.map((tip, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{tip}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Quick Reference Guide */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-centre gap-3">
              <TrendingUp className="w-5 h-5 text-primary" />
              Quick Reference Guide
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Shield className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Gentle Approach</h4>
                <p className="text-enhanced-body text-sm">Avoid activities that directly compress or stress the tumor site</p>
              </div>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Eye className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Monitor Symptoms</h4>
                <p className="text-enhanced-body text-sm">Watch for changes in symptoms and adjust activities accordingly</p>
              </div>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-muted/50 border border-border/50 mb-4">
                  <Heart className="w-8 h-8 text-foreground mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Holistic Care</h4>
                <p className="text-enhanced-body text-sm">Address both physical and emotional aspects of living with nerve tumors</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default NerveTumorErgonomicsSection;
