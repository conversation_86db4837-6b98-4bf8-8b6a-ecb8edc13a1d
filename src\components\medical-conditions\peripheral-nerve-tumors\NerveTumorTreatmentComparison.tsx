import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Pill, 
  Zap, 
  TrendingUp, 
  Clock, 
  Activity,
  CheckCircle,
  XCircle,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Gauge,
  Heart,
  Brain,
  Target
} from "lucide-react";
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface TreatmentOption {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
  effectiveness: number;
  timeToImprovement: string;
  duration: string;
  cost: 'Low' | 'Medium' | 'High';
  invasiveness: 'Non-invasive' | 'Minimally invasive' | 'Invasive';
  pros: string[];
  cons: string[];
  scientificEvidence: string;
  successRate: string;
  recommendedFor: string[];
  contraindications: string[];
}

interface NerveTumorTreatmentComparisonProps {
  className?: string;
}

const treatmentOptions: TreatmentOption[] = [
  {
    id: 'observation',
    title: 'Observation and Monitoring',
    icon: Eye,
    description: 'Active surveillance with regular clinical and imaging follow-up for asymptomatic or slowly growing tumors',
    effectiveness: 85,
    timeToImprovement: 'N/A - Monitoring',
    duration: 'Ongoing surveillance',
    cost: 'Low',
    invasiveness: 'Non-invasive',
    pros: [
      'No surgical risks or complications',
      'Preserves nerve function completely',
      'Cost-effective for stable tumors',
      'Allows natural history observation',
      'Can intervene if symptoms develop'
    ],
    cons: [
      'Requires ongoing monitoring and imaging',
      'Potential for tumor growth',
      'Patient anxiety about tumor presence',
      'May miss optimal surgical timing'
    ],
    scientificEvidence: 'Strong evidence supports observation for asymptomatic schwannomas with stable imaging',
    successRate: '85-90% remain stable without intervention',
    recommendedFor: [
      'Asymptomatic schwannomas <3cm',
      'Elderly patients with comorbidities',
      'Tumors in surgically challenging locations',
      'Patients declining surgical intervention'
    ],
    contraindications: [
      'Progressive neurological symptoms',
      'Rapid tumor growth',
      'Suspicion of malignancy',
      'Significant functional impairment'
    ]
  },
  {
    id: 'microsurgery',
    title: 'Microsurgical Resection',
    icon: Scissors,
    description: 'Precise surgical removal using microsurgical techniques with nerve preservation',
    effectiveness: 95,
    timeToImprovement: '2-4 weeks',
    duration: 'Permanent (if complete)',
    cost: 'High',
    invasiveness: 'Invasive',
    pros: [
      'Definitive treatment with high cure rates',
      'Provides tissue diagnosis',
      'Immediate symptom relief in most cases',
      'Prevents future growth and complications',
      'Excellent long-term outcomes'
    ],
    cons: [
      'Surgical risks and potential complications',
      'Temporary or permanent nerve dysfunction',
      'Requires specialized microsurgical expertise',
      'Recovery time and rehabilitation needed',
      'Small risk of tumor recurrence'
    ],
    scientificEvidence: 'Extensive evidence shows >95% cure rates for schwannomas with microsurgical enucleation',
    successRate: '90-98% depending on tumor type',
    recommendedFor: [
      'Symptomatic tumors causing dysfunction',
      'Growing tumors on serial imaging',
      'Tumors causing significant pain',
      'Diagnostic uncertainty requiring tissue'
    ],
    contraindications: [
      'Poor surgical candidates',
      'Severe medical comorbidities',
      'Tumors in critical nerve locations',
      'Patient preference for non-surgical management'
    ]
  },
  {
    id: 'medication',
    title: 'Medical Management',
    icon: Pill,
    description: 'Pharmacological treatment for symptom control and specific tumor types',
    effectiveness: 60,
    timeToImprovement: '2-6 weeks',
    duration: 'Ongoing as needed',
    cost: 'Medium',
    invasiveness: 'Non-invasive',
    pros: [
      'Non-invasive symptom management',
      'Can improve quality of life',
      'Multiple medication options available',
      'Reversible treatment approach',
      'Can be combined with other treatments'
    ],
    cons: [
      'Does not treat underlying tumor',
      'Potential medication side effects',
      'May require ongoing use',
      'Variable response between patients',
      'Does not prevent tumor growth'
    ],
    scientificEvidence: 'Good evidence for neuropathic pain medications in tumor-related nerve pain',
    successRate: '60-75% symptom improvement',
    recommendedFor: [
      'Patients with neuropathic pain',
      'Those unsuitable for surgery',
      'Adjunct to other treatments',
      'Symptomatic relief while planning surgery'
    ],
    contraindications: [
      'Allergy to specific medications',
      'Significant drug interactions',
      'Severe liver or kidney disease',
      'Pregnancy (for certain medications)'
    ]
  },
  {
    id: 'radiation',
    title: 'Radiation Therapy',
    icon: Zap,
    description: 'Targeted radiation for tumor control, particularly for malignant or recurrent lesions',
    effectiveness: 75,
    timeToImprovement: '3-6 months',
    duration: 'Long-term control',
    cost: 'High',
    invasiveness: 'Minimally invasive',
    pros: [
      'Non-surgical tumor control',
      'Effective for malignant tumors',
      'Can treat multiple lesions',
      'Preserves surrounding tissue',
      'Outpatient treatment option'
    ],
    cons: [
      'Risk of radiation-induced nerve damage',
      'Potential for secondary malignancy',
      'May not provide immediate relief',
      'Requires multiple treatment sessions',
      'Long-term effects uncertain'
    ],
    scientificEvidence: 'Moderate evidence for stereotactic radiosurgery in acoustic neuromas and MPNSTs',
    successRate: '70-85% tumor control rates',
    recommendedFor: [
      'Malignant peripheral nerve sheath tumors',
      'Recurrent tumors after surgery',
      'Acoustic neuromas in poor surgical candidates',
      'Adjuvant treatment after incomplete resection'
    ],
    contraindications: [
      'Previous radiation to the area',
      'Pregnancy',
      'Certain genetic syndromes',
      'Benign tumors amenable to surgery'
    ]
  }
];

const NerveTumorTreatmentComparison: React.FC<NerveTumorTreatmentComparisonProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();
  const [selectedTreatment, setSelectedTreatment] = useState<string>('observation');

  const getCostColour = (cost: string) => {
    switch (cost) {
      case 'Low': return 'bg-success-light text-success border border-success/30';
      case 'Medium': return 'bg-info-light text-info border border-info/30';
      case 'High': return 'bg-muted-light text-foreground border border-border/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getInvasivenessColour = (invasiveness: string) => {
    switch (invasiveness) {
      case 'Non-invasive': return 'bg-success-light text-success border border-success/30';
      case 'Minimally invasive': return 'bg-info-light text-info border border-info/30';
      case 'Invasive': return 'bg-muted-light text-foreground border border-border/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <section className={cn(
      "section-background border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Badge variant="info" className="mb-6">
            <TrendingUp className="w-4 h-4 mr-2" />
            Treatment Comparison
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            Comprehensive Treatment Analysis
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            Evidence-based comparison of all treatment options for peripheral nerve tumors, 
            helping you understand the pros, cons, and scientific evidence for each approach
          </p>
        </div>

        {/* Treatment Overview Cards */}
        <div className={cn(
          "grid gap-6 mb-12",
          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2 lg:grid-cols-4"
        )}>
          {treatmentOptions.map((treatment) => {
            const IconComponent = treatment.icon;
            return (
              <Card 
                key={treatment.id} 
                className={cn(
                  "medical-card cursor-pointer transition-all duration-300 hover:shadow-lg",
                  selectedTreatment === treatment.id ? "ring-2 ring-primary" : ""
                )}
                onClick={() => setSelectedTreatment(treatment.id)}
              >
                <CardHeader className="text-center">
                  <div className="p-4 rounded-xl bg-primary/10 border border-primary/20 mb-4 mx-auto w-fit">
                    <IconComponent className="w-8 h-8 text-primary" />
                  </div>
                  <CardTitle className="text-enhanced-subheading">{treatment.title}</CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-3">
                  <div>
                    <p className="text-enhanced-caption mb-1">Effectiveness</p>
                    <Progress value={treatment.effectiveness} className="h-2" />
                    <p className="text-enhanced-caption mt-1">{treatment.effectiveness}%</p>
                  </div>
                  <div className="flex justify-center gap-2">
                    <Badge className={getCostColour(treatment.cost)}>
                      {treatment.cost} Cost
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Detailed Treatment Information */}
        {(() => {
          const treatment = treatmentOptions.find(t => t.id === selectedTreatment);
          return treatment ? (
            <Card className="medical-card">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-enhanced-heading flex items-center gap-3">
                      <treatment.icon className="w-6 h-6 text-primary" />
                      {treatment.title}
                    </CardTitle>
                    <p className="text-enhanced-body mt-2">{treatment.description}</p>
                  </div>
                  <div className="flex gap-2">
                    <Badge className={getCostColour(treatment.cost)}>
                      {treatment.cost} Cost
                    </Badge>
                    <Badge className={getInvasivenessColour(treatment.invasiveness)}>
                      {treatment.invasiveness}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="overview" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="evidence">Evidence</TabsTrigger>
                    <TabsTrigger value="selection">Selection</TabsTrigger>
                    <TabsTrigger value="outcomes">Outcomes</TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="space-y-6 mt-6">
                    <div className={cn(
                      "grid gap-6",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                    )}>
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-success" />
                          Advantages
                        </h4>
                        <ul className="space-y-2">
                          {treatment.pros.map((pro, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <CheckCircle className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{pro}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                          <XCircle className="w-4 h-4 text-foreground" />
                          Disadvantages
                        </h4>
                        <ul className="space-y-2">
                          {treatment.cons.map((con, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <XCircle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{con}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    <div className={cn(
                      "grid gap-4",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                    )}>
                      <div className="bg-info/5 border border-info/20 rounded-lg p-4">
                        <Clock className="w-5 h-5 text-info mb-2" />
                        <h5 className="text-enhanced-caption font-medium">Time to Improvement</h5>
                        <p className="text-enhanced-body text-sm">{treatment.timeToImprovement}</p>
                      </div>
                      <div className="bg-success/5 border border-success/20 rounded-lg p-4">
                        <Activity className="w-5 h-5 text-success mb-2" />
                        <h5 className="text-enhanced-caption font-medium">Treatment Duration</h5>
                        <p className="text-enhanced-body text-sm">{treatment.duration}</p>
                      </div>
                      <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                        <Gauge className="w-5 h-5 text-primary mb-2" />
                        <h5 className="text-enhanced-caption font-medium">Success Rate</h5>
                        <p className="text-enhanced-body text-sm">{treatment.successRate}</p>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="evidence" className="space-y-6 mt-6">
                    <div className="bg-info/5 border border-info/20 rounded-lg p-6">
                      <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                        <Award className="w-5 h-5 text-info" />
                        Scientific Evidence
                      </h4>
                      <p className="text-enhanced-body">{treatment.scientificEvidence}</p>
                    </div>
                  </TabsContent>

                  <TabsContent value="selection" className="space-y-6 mt-6">
                    <div className={cn(
                      "grid gap-6",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                    )}>
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                          <Star className="w-4 h-4 text-success" />
                          Recommended For
                        </h4>
                        <ul className="space-y-2">
                          {treatment.recommendedFor.map((indication, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <Star className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{indication}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                          <AlertTriangle className="w-4 h-4 text-foreground" />
                          Contraindications
                        </h4>
                        <ul className="space-y-2">
                          {treatment.contraindications.map((contraindication, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{contraindication}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="outcomes" className="space-y-6 mt-6">
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3">Treatment Effectiveness</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-enhanced-body text-sm">Overall Effectiveness</span>
                            <span className="text-enhanced-body text-sm font-medium">{treatment.effectiveness}%</span>
                          </div>
                          <Progress value={treatment.effectiveness} className="h-3" />
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          ) : null;
        })()}

        {/* Treatment Decision Framework */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Brain className="w-5 h-5 text-primary" />
              Treatment Decision Framework
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Eye className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Asymptomatic Tumors</h4>
                <p className="text-enhanced-body text-sm">Consider observation with regular monitoring for stable, asymptomatic lesions</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Target className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Symptomatic Tumors</h4>
                <p className="text-enhanced-body text-sm">Microsurgical resection is often the treatment of choice for symptomatic lesions</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-muted/50 border border-border/50 mb-4">
                  <Heart className="w-8 h-8 text-foreground mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Individual Factors</h4>
                <p className="text-enhanced-body text-sm">Consider patient age, comorbidities, tumor characteristics, and preferences</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default NerveTumorTreatmentComparison;
