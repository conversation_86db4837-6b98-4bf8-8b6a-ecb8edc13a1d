import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  Phone, 
  Clock, 
  Shield,
  Target,
  Eye,
  Heart,
  Search
} from "lucide-react";
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface WarningSign {
  id: string;
  sign: string;
  description: string;
  urgency: 'immediate' | 'urgent' | 'concerning';
  action: string;
  timeframe: string;
}

interface TumorProgression {
  stage: string;
  title: string;
  description: string;
  symptoms: string[];
  malignancyRisk: 'low' | 'moderate' | 'high';
  timeframe: string;
}

interface NerveTumorWarningSignsSectionProps {
  className?: string;
}

const warningSignsData = {
  title: "Warning Signs and When to Seek Help",
  subtitle: "Recognise concerning symptoms and understand when immediate medical attention is needed for peripheral nerve tumors",
  
  warningSignsByUrgency: {
    immediate: [
      {
        id: 'rapid-growth',
        sign: 'Rapid Tumor Growth',
        description: 'Sudden increase in tumor size over days to weeks',
        urgency: 'immediate' as const,
        action: 'Seek emergency medical evaluation immediately',
        timeframe: 'Within 24 hours'
      },
      {
        id: 'severe-neurological-deficit',
        sign: 'Severe Neurological Deficit',
        description: 'Sudden onset of significant weakness, numbness, or loss of function',
        urgency: 'immediate' as const,
        action: 'Emergency department evaluation',
        timeframe: 'Immediately'
      },
      {
        id: 'signs-of-malignancy',
        sign: 'Signs Suggesting Malignancy',
        description: 'Hard, fixed mass with skin changes or systemic symptoms',
        urgency: 'immediate' as const,
        action: 'Urgent oncological assessment',
        timeframe: 'Within 24-48 hours'
      }
    ],
    urgent: [
      {
        id: 'progressive-symptoms',
        sign: 'Progressive Neurological Symptoms',
        description: 'Gradual worsening of weakness, numbness, or pain over weeks',
        urgency: 'urgent' as const,
        action: 'Contact healthcare provider for urgent appointment',
        timeframe: 'Within 1-2 days'
      },
      {
        id: 'new-tumor-characteristics',
        sign: 'Change in Tumor Characteristics',
        description: 'Previously soft tumor becomes hard, fixed, or irregular',
        urgency: 'urgent' as const,
        action: 'Schedule urgent medical consultation',
        timeframe: 'Within 2-3 days'
      },
      {
        id: 'severe-pain',
        sign: 'Severe or Increasing Pain',
        description: 'Intense pain that interferes with sleep or daily activities',
        urgency: 'urgent' as const,
        action: 'Medical evaluation needed',
        timeframe: 'Within 24-48 hours'
      },
      {
        id: 'functional-impairment',
        sign: 'Significant Functional Loss',
        description: 'Inability to perform important daily activities due to nerve dysfunction',
        urgency: 'urgent' as const,
        action: 'Urgent specialist referral',
        timeframe: 'Within 1-2 days'
      }
    ],
    concerning: [
      {
        id: 'gradual-growth',
        sign: 'Gradual Tumor Growth',
        description: 'Slow but consistent increase in tumor size over months',
        urgency: 'concerning' as const,
        action: 'Schedule follow-up with specialist',
        timeframe: 'Within 2-4 weeks'
      },
      {
        id: 'new-symptoms',
        sign: 'Development of New Symptoms',
        description: 'New onset of tingling, numbness, or mild weakness',
        urgency: 'concerning' as const,
        action: 'Medical evaluation recommended',
        timeframe: 'Within 1-2 weeks'
      },
      {
        id: 'multiple-tumors',
        sign: 'Multiple Tumors',
        description: 'Discovery of additional tumors or masses along nerves',
        urgency: 'concerning' as const,
        action: 'Genetic counselling and comprehensive evaluation',
        timeframe: 'Within 2-3 weeks'
      },
      {
        id: 'family-history',
        sign: 'Positive Family History',
        description: 'Family history of neurofibromatosis or multiple nerve tumors',
        urgency: 'concerning' as const,
        action: 'Genetic counselling consultation',
        timeframe: 'Within 4-6 weeks'
      }
    ]
  },

  tumorProgression: [
    {
      stage: 'Benign Stable',
      title: 'Benign Stable Tumor',
      description: 'Well-defined, slow-growing or stable benign tumor',
      symptoms: [
        'Palpable mass that moves with nerve',
        'Minimal or no neurological symptoms',
        'Stable size on serial imaging',
        'Soft, mobile consistency'
      ],
      malignancyRisk: 'low' as const,
      timeframe: 'Stable over years'
    },
    {
      stage: 'Benign Growing',
      title: 'Benign Growing Tumor',
      description: 'Benign tumor showing gradual growth with developing symptoms',
      symptoms: [
        'Gradual increase in size',
        'Progressive neurological symptoms',
        'Increasing pain or discomfort',
        'Functional impairment developing'
      ],
      malignancyRisk: 'low' as const,
      timeframe: 'Growth over months to years'
    },
    {
      stage: 'Suspicious',
      title: 'Suspicious Characteristics',
      description: 'Tumor with features raising concern for malignant transformation',
      symptoms: [
        'Rapid growth or size change',
        'Hard, fixed consistency',
        'Irregular borders on imaging',
        'Severe or progressive pain'
      ],
      malignancyRisk: 'moderate' as const,
      timeframe: 'Changes over weeks to months'
    },
    {
      stage: 'Malignant',
      title: 'Malignant Features',
      description: 'Tumor with characteristics suggesting malignant peripheral nerve sheath tumor',
      symptoms: [
        'Rapid, aggressive growth',
        'Fixed to surrounding tissues',
        'Severe neurological deficits',
        'Systemic symptoms (weight loss, fatigue)'
      ],
      malignancyRisk: 'high' as const,
      timeframe: 'Rapid progression over weeks'
    }
  ],

  whenToSeekHelp: {
    immediate: [
      'Sudden rapid growth of existing tumor',
      'Severe neurological deficits developing quickly',
      'Signs suggesting malignant transformation',
      'Severe pain not controlled by medications'
    ],
    urgent: [
      'Progressive worsening of neurological symptoms',
      'Change in tumor characteristics or consistency',
      'Significant functional impairment',
      'New onset of severe symptoms'
    ],
    routine: [
      'Gradual tumor growth on imaging',
      'Mild new symptoms developing',
      'Questions about treatment options',
      'Family history concerns'
    ]
  }
};

const NerveTumorWarningSignsSection: React.FC<NerveTumorWarningSignsSectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();

  const getMalignancyRiskColour = (risk: string) => {
    switch (risk) {
      case 'low': return 'bg-success-light text-success border border-success/30';
      case 'moderate': return 'bg-info-light text-info border border-info/30';
      case 'high': return 'bg-muted-light text-foreground border border-border/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <section className={cn(
      "section-background border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-centre mb-20">
          <Badge variant="emergency" className="mb-6">
            <AlertTriangle className="w-4 h-4 mr-2" />
            Warning Signs
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            {warningSignsData.title}
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            {warningSignsData.subtitle}
          </p>
        </div>

        {/* Emergency Contact Card */}
        <Card className="medical-card border-l-4 border-l-primary mb-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-centre gap-3">
              <Phone className="w-5 h-5 text-primary" />
              Emergency Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-centre">
                <Phone className="w-8 h-8 text-foreground mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Emergency</h4>
                <p className="text-enhanced-body font-bold">000</p>
                <p className="text-enhanced-caption">Severe symptoms</p>
              </div>
              <div className="text-centre">
                <Clock className="w-8 h-8 text-info mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Urgent Care</h4>
                <p className="text-enhanced-body font-bold">(02) 9999 0000</p>
                <p className="text-enhanced-caption">Same-day appointment</p>
              </div>
              <div className="text-centre">
                <Shield className="w-8 h-8 text-info mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Clinic</h4>
                <p className="text-enhanced-body font-bold">(02) 8888 0000</p>
                <p className="text-enhanced-caption">Regular consultation</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Warning Signs by Urgency */}
        <div className="mb-16">
          <h3 className={cn(
            "font-bold text-foreground mb-8 text-centre",
            deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
          )}>
            Warning Signs by Urgency Level
          </h3>
          
          <div className="space-y-8">
            {Object.entries(warningSignsData.warningSignsByUrgency).map(([urgency, signs]) => (
              <Card key={urgency} className="medical-card">
                <CardHeader>
                  <CardTitle className="text-enhanced-heading flex items-centre gap-3">
                    <AlertTriangle className="w-5 h-5 text-foreground" />
                    {urgency.charAt(0).toUpperCase() + urgency.slice(1)} Attention Required
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {signs.map((sign) => (
                      <div key={sign.id} className="border border-border/50 rounded-lg p-4">
                        <div className="flex items-start justify-between mb-3">
                          <h4 className="text-enhanced-subheading font-semibold">{sign.sign}</h4>
                          <Badge variant={sign.urgency === 'immediate' ? 'emergency' : sign.urgency === 'urgent' ? 'urgent' : 'routine'}>
                            {sign.urgency}
                          </Badge>
                        </div>
                        <p className="text-enhanced-body mb-3">{sign.description}</p>
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <span className="text-enhanced-caption font-medium">Action Required: </span>
                            <span className="text-enhanced-body text-sm">{sign.action}</span>
                          </div>
                          <div>
                            <span className="text-enhanced-caption font-medium">Timeframe: </span>
                            <span className="text-enhanced-body text-sm">{sign.timeframe}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Tumor Progression Stages */}
        <div className="mb-16">
          <h3 className={cn(
            "font-bold text-foreground mb-8 text-centre",
            deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
          )}>
            Tumor Progression and Risk Assessment
          </h3>
          
          <div className={cn(
            "grid gap-6",
            deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
          )}>
            {warningSignsData.tumorProgression.map((stage, index) => (
              <Card key={stage.stage} className="medical-card h-full">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-enhanced-heading">{stage.title}</CardTitle>
                    <Badge className={getMalignancyRiskColour(stage.malignancyRisk)}>
                      {stage.malignancyRisk} risk
                    </Badge>
                  </div>
                  <p className="text-enhanced-body text-sm">{stage.description}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="text-enhanced-caption font-medium mb-2">Typical Features:</h4>
                    <ul className="space-y-1">
                      {stage.symptoms.map((symptom, symptomIndex) => (
                        <li key={symptomIndex} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                          <span className="text-enhanced-body text-sm">{symptom}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="pt-2 border-t border-border/50">
                    <span className="text-enhanced-caption font-medium">Timeline: </span>
                    <span className="text-enhanced-body text-sm">{stage.timeframe}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* When to Seek Help */}
        <div className="mb-12">
          <h3 className={cn(
            "font-bold text-foreground mb-8 text-centre",
            deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
          )}>
            When to Seek Medical Help
          </h3>
          
          <div className={cn(
            "grid gap-8",
            deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
          )}>
            <Card className="medical-card border-l-4 border-l-muted">
              <CardHeader>
                <CardTitle className="text-enhanced-heading flex items-centre gap-3">
                  <Phone className="w-5 h-5 text-foreground" />
                  Immediate Care
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {warningSignsData.whenToSeekHelp.immediate.map((item, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                      <span className="text-enhanced-body text-sm">{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            <Card className="medical-card border-l-4 border-l-info">
              <CardHeader>
                <CardTitle className="text-enhanced-heading flex items-centre gap-3">
                  <Clock className="w-5 h-5 text-info" />
                  Urgent Care
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {warningSignsData.whenToSeekHelp.urgent.map((item, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Clock className="w-4 h-4 text-info mt-0.5 flex-shrink-0" />
                      <span className="text-enhanced-body text-sm">{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Key Takeaways */}
        <Card className="medical-card">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-centre gap-3">
              <Target className="w-5 h-5 text-primary" />
              Key Takeaways
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Eye className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Regular Monitoring</h4>
                <p className="text-enhanced-body text-sm">Regular self-examination and medical follow-up are essential for early detection of changes</p>
              </div>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Search className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Know the Signs</h4>
                <p className="text-enhanced-body text-sm">Understanding warning signs enables prompt medical attention when needed</p>
              </div>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-muted/50 border border-border/50 mb-4">
                  <Heart className="w-8 h-8 text-foreground mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Early Intervention</h4>
                <p className="text-enhanced-body text-sm">Early detection and treatment lead to better outcomes and preserved nerve function</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default NerveTumorWarningSignsSection;
