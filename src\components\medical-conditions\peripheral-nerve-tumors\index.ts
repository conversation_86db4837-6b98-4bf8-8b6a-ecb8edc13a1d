/**
 * Peripheral Nerve Tumors Components Barrel Export
 * Provides clean imports for all peripheral nerve tumor related components
 */

export { default as NerveTumorAnatomySection } from './NerveTumorAnatomySection';
export { default as NerveTumorTreatmentComparison } from './NerveTumorTreatmentComparison';
export { default as NerveTumorExerciseSection } from './NerveTumorExerciseSection';
export { default as NerveTumorErgonomicsSection } from './NerveTumorErgonomicsSection';
export { default as NerveTumorWarningSignsSection } from './NerveTumorWarningSignsSection';

// Re-export types for convenience
export type { ConditionData as PeripheralNerveTumorsData } from '@/components/medical-conditions/types';
