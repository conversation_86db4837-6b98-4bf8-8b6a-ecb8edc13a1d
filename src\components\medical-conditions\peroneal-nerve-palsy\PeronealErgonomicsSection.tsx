import { 
  Shield, 
  Home, 
  Briefcase, 
  Car, 
  Footprints, 
  CheckCircle,
  AlertTriangle,
  Target,
  Heart,
  TrendingUp,
  <PERSON>tings,
  Zap
} from "lucide-react";
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ErgonomicRecommendation {
  id: string;
  title: string;
  description: string;
  importance: 'High' | 'Medium' | 'Low';
  recommendations: string[];
  avoidActions: string[];
  scientificRationale: string;
  implementationTips: string[];
}

interface LifestyleCategory {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
  recommendations: ErgonomicRecommendation[];
}

interface PeronealErgonomicsSectionProps {
  className?: string;
}

const lifestyleCategories: LifestyleCategory[] = [
  {
    id: 'mobility-aids',
    title: 'Mobility & Assistive Devices',
    icon: Footprints,
    description: 'Essential devices and equipment to improve safety and function with foot drop',
    recommendations: [
      {
        id: 'afo-selection',
        title: 'Ankle-Foot Orthosis (AFO) Selection and Use',
        description: 'Choosing and properly using AFO devices for optimal function and comfort',
        importance: 'High',
        recommendations: [
          'Work with orthotist for proper AFO fitting and selection',
          'Choose lightweight, durable materials for daily use',
          'Ensure proper shoe fit with AFO accommodation',
          'Maintain regular AFO maintenance and adjustments',
          'Gradually increase wearing time to allow adaptation'
        ],
        avoidActions: [
          'Using ill-fitting or damaged AFO devices',
          'Ignoring skin irritation or pressure points',
          'Attempting to modify AFO without professional guidance',
          'Wearing AFO with inappropriate footwear'
        ],
        scientificRationale: 'AFO devices significantly improve walking safety, reduce fall risk, and enhance functional mobility by providing mechanical foot clearance during swing phase of gait.',
        implementationTips: [
          'Start with shorter wearing periods and gradually increase',
          'Check skin regularly for pressure points or irritation',
          'Keep spare AFO for backup if possible',
          'Learn proper donning and doffing techniques'
        ]
      },
      {
        id: 'footwear-modifications',
        title: 'Footwear Selection and Modifications',
        description: 'Choosing appropriate footwear to accommodate AFO and enhance safety',
        importance: 'High',
        recommendations: [
          'Select shoes with wide toe box and adjustable closures',
          'Choose low-heeled, stable shoes with good traction',
          'Ensure adequate depth to accommodate AFO',
          'Consider shoes with removable insoles for better fit',
          'Use non-slip soles for improved traction'
        ],
        avoidActions: [
          'Wearing high heels or unstable footwear',
          'Using shoes that are too tight with AFO',
          'Choosing shoes with smooth or slippery soles',
          'Ignoring proper shoe maintenance'
        ],
        scientificRationale: 'Proper footwear enhances the effectiveness of AFO devices, improves stability, and reduces the risk of trips and falls associated with foot drop.',
        implementationTips: [
          'Shop for shoes in the afternoon when feet are slightly swollen',
          'Bring AFO when trying on new shoes',
          'Consider having shoes professionally fitted',
          'Replace worn shoes promptly to maintain safety'
        ]
      }
    ]
  },
  {
    id: 'home-safety',
    title: 'Home Environment',
    icon: Home,
    description: 'Modify your home environment to reduce fall risk and improve accessibility',
    recommendations: [
      {
        id: 'fall-prevention',
        title: 'Fall Prevention Modifications',
        description: 'Home modifications to reduce tripping hazards and improve safety',
        importance: 'High',
        recommendations: [
          'Remove or secure loose rugs and floor mats',
          'Ensure adequate lighting in all areas, especially stairs',
          'Install handrails on both sides of staircases',
          'Use non-slip mats in bathrooms and wet areas',
          'Keep walkways clear of clutter and obstacles'
        ],
        avoidActions: [
          'Leaving electrical cords across walkways',
          'Using loose rugs without non-slip backing',
          'Ignoring burnt-out light bulbs',
          'Rushing through dimly lit areas'
        ],
        scientificRationale: 'Individuals with foot drop have significantly increased fall risk due to reduced foot clearance. Environmental modifications can reduce this risk by up to 50%.',
        implementationTips: [
          'Conduct a home safety assessment with occupational therapist',
          'Install motion-sensor lights for nighttime navigation',
          'Use contrasting colours to highlight step edges',
          'Consider grab bars in strategic locations'
        ]
      },
      {
        id: 'stair-safety',
        title: 'Stair Navigation and Safety',
        description: 'Strategies for safe stair use with foot drop',
        importance: 'High',
        recommendations: [
          'Always use handrails when available',
          'Take stairs one step at a time, never rush',
          'Ensure good lighting on all staircases',
          'Consider stair lifts for frequent multi-level access',
          'Mark step edges with contrasting tape if needed'
        ],
        avoidActions: [
          'Carrying items that obstruct view of steps',
          'Using stairs in poor lighting conditions',
          'Attempting to take multiple steps at once',
          'Ignoring fatigue when using stairs'
        ],
        scientificRationale: 'Stairs present the highest fall risk for individuals with foot drop due to the need for precise foot placement and clearance.',
        implementationTips: [
          'Practice stair techniques with physiotherapist',
          'Consider installing additional handrails',
          'Use good lighting and take your time',
          'Have emergency plan for stair-related difficulties'
        ]
      }
    ]
  },
  {
    id: 'workplace',
    title: 'Workplace Adaptations',
    icon: Briefcase,
    description: 'Workplace modifications to maintain productivity and safety',
    recommendations: [
      {
        id: 'workstation-setup',
        title: 'Workstation Ergonomics',
        description: 'Optimising workspace for comfort and safety with foot drop',
        importance: 'Medium',
        recommendations: [
          'Ensure clear pathways to and from workstation',
          'Use anti-fatigue mats if standing work is required',
          'Position frequently used items within easy reach',
          'Consider sit-stand desk options for flexibility',
          'Ensure adequate lighting in work areas'
        ],
        avoidActions: [
          'Cluttering workspace with obstacles',
          'Ignoring proper ergonomic principles',
          'Standing for prolonged periods without breaks',
          'Using inadequate lighting'
        ],
        scientificRationale: 'Proper workplace ergonomics reduces fatigue and fall risk while maintaining productivity and job satisfaction.',
        implementationTips: [
          'Work with occupational health services',
          'Request reasonable workplace accommodations',
          'Take regular breaks to prevent fatigue',
          'Communicate needs clearly with supervisors'
        ]
      }
    ]
  },
  {
    id: 'transportation',
    title: 'Transportation & Mobility',
    icon: Car,
    description: 'Safe transportation options and driving considerations',
    recommendations: [
      {
        id: 'driving-safety',
        title: 'Driving Adaptations and Safety',
        description: 'Modifications and considerations for safe driving with foot drop',
        importance: 'High',
        recommendations: [
          'Consult with driving rehabilitation specialist',
          'Consider left-foot accelerator if right foot affected',
          'Ensure AFO doesn\'t interfere with pedal operation',
          'Practice emergency braking in safe environment',
          'Keep emergency contact information in vehicle'
        ],
        avoidActions: [
          'Driving without proper assessment',
          'Ignoring pedal operation difficulties',
          'Driving when fatigued or unsteady',
          'Failing to inform insurance company of condition'
        ],
        scientificRationale: 'Foot drop can significantly affect driving safety, particularly emergency braking response. Proper assessment and adaptations are essential.',
        implementationTips: [
          'Get professional driving assessment',
          'Practice with adaptations before regular use',
          'Consider automatic transmission if not already using',
          'Plan routes to avoid challenging driving conditions'
        ]
      },
      {
        id: 'public-transport',
        title: 'Public Transportation Strategies',
        description: 'Safe use of public transportation with mobility limitations',
        importance: 'Medium',
        recommendations: [
          'Allow extra time for boarding and alighting',
          'Use priority seating when available',
          'Hold handrails securely during travel',
          'Plan routes with minimal transfers',
          'Consider off-peak travel when possible'
        ],
        avoidActions: [
          'Rushing to catch transport',
          'Standing without support during travel',
          'Carrying excessive luggage',
          'Ignoring fatigue during long journeys'
        ],
        scientificRationale: 'Public transportation presents unique challenges for individuals with foot drop, including uneven surfaces, crowding, and time pressures.',
        implementationTips: [
          'Research accessible transport options',
          'Practice boarding techniques',
          'Carry mobility aid identification if needed',
          'Plan alternative routes in case of service disruptions'
        ]
      }
    ]
  }
];

const PeronealErgonomicsSection: React.FC<PeronealErgonomicsSectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();
  const [selectedCategory, setSelectedCategory] = useState<string>('mobility-aids');

  const getImportanceColour = (importance: string) => {
    switch (importance) {
      case 'High': return 'bg-muted-light text-foreground border border-border/30';
      case 'Medium': return 'bg-info-light text-info border border-info/30';
      case 'Low': return 'bg-success-light text-success border border-success/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <section className={cn(
      "section-background-alt border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Badge variant="info" className="mb-6">
            <Shield className="w-4 h-4 mr-2" />
            Ergonomics & Lifestyle
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            Ergonomic and Lifestyle Adaptations
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            Comprehensive lifestyle modifications and adaptive strategies to enhance safety, 
            independence, and quality of life with peroneal nerve palsy
          </p>
        </div>

        {/* Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-12",
            deviceInfo.isMobile ? "grid-cols-2 h-auto" : "grid-cols-4 h-14"
          )}>
            {lifestyleCategories.map((category) => {
              const IconComponent = category.icon;
              return (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className={cn(
                    "flex items-center gap-2 font-medium",
                    deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
                  )}
                >
                  <IconComponent className={cn(
                    deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5"
                  )} />
                  <span className={deviceInfo.isMobile ? "text-center" : ""}>
                    {category.title}
                  </span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* Category Content */}
          {lifestyleCategories.map((category) => (
            <TabsContent key={category.id} value={category.id} className="space-y-8">
              {/* Category Description */}
              <Card className="medical-card">
                <CardHeader>
                  <CardTitle className="text-enhanced-heading flex items-center gap-3">
                    <category.icon className="w-5 h-5 text-primary" />
                    {category.title}
                  </CardTitle>
                  <p className="text-enhanced-body">{category.description}</p>
                </CardHeader>
              </Card>

              {/* Recommendations */}
              <div className="space-y-8">
                {category.recommendations.map((recommendation) => (
                  <Card key={recommendation.id} className="medical-card">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-enhanced-heading">{recommendation.title}</CardTitle>
                        <Badge className={getImportanceColour(recommendation.importance)}>
                          {recommendation.importance} Priority
                        </Badge>
                      </div>
                      <p className="text-enhanced-body">{recommendation.description}</p>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Recommendations and Avoid Actions */}
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
                      )}>
                        <div>
                          <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                            <CheckCircle className="w-4 h-4 text-success" />
                            Recommended Actions
                          </h4>
                          <ul className="space-y-2">
                            {recommendation.recommendations.map((item, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <CheckCircle className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm">{item}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                            <AlertTriangle className="w-4 h-4 text-foreground" />
                            Actions to Avoid
                          </h4>
                          <ul className="space-y-2">
                            {recommendation.avoidActions.map((item, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm">{item}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Scientific Rationale */}
                      <div className="bg-info/5 border border-info/20 rounded-lg p-4">
                        <h4 className="text-enhanced-subheading font-semibold mb-2 flex items-center gap-2">
                          <Target className="w-4 h-4 text-info" />
                          Scientific Rationale
                        </h4>
                        <p className="text-enhanced-body text-sm">{recommendation.scientificRationale}</p>
                      </div>

                      {/* Implementation Tips */}
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                          <Settings className="w-4 h-4 text-primary" />
                          Implementation Tips
                        </h4>
                        <ul className="space-y-2">
                          {recommendation.implementationTips.map((tip, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{tip}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Quick Reference Guide */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <TrendingUp className="w-5 h-5 text-primary" />
              Quick Reference Guide
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Shield className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Safety First</h4>
                <p className="text-enhanced-body text-sm">Prioritise fall prevention and safety modifications in all environments</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Zap className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Adaptive Equipment</h4>
                <p className="text-enhanced-body text-sm">Use appropriate assistive devices to maintain independence and function</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-muted/50 border border-border/50 mb-4">
                  <Heart className="w-8 h-8 text-foreground mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Gradual Adaptation</h4>
                <p className="text-enhanced-body text-sm">Allow time to adjust to new equipment and strategies</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default PeronealErgonomicsSection;
