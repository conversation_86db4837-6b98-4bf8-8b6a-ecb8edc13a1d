import { LucideIcon } from 'lucide-react';

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface CauseItem {
  icon: LucideIcon;
  title: string;
  description: string;
  details: string[];
  severity?: 'low' | 'medium' | 'high';
}

interface RiskFactor {
  factor: string;
  description: string;
  modifiable: boolean;
}

interface ConditionCausesProps {
  title?: string;
  subtitle?: string;
  causes: CauseItem[];
  riskFactors?: RiskFactor[];
  className?: string;
}

const severityColors = {
  low: 'border-success/30 bg-success-light',
  medium: 'border-info/30 bg-info-light',
  high: 'border-border/30 bg-muted'
};

export function ConditionCauses({
  title = "Causes and Risk Factors",
  subtitle,
  causes,
  riskFactors = [],
  className
}: ConditionCausesProps) {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "section-background border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        <div className="text-centre mb-20">
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            {title || "Causes and Risk Factors"}
          </h2>
          {subtitle && (
            <p className={cn(
              "text-foreground/80 max-w-5xl mx-auto leading-relaxed font-medium",
              deviceInfo.isMobile ? "text-base" : "text-lg"
            )}>
              {subtitle}
            </p>
          )}
        </div>

        {/* Causes Section */}
        <div className="mb-16">
          <h3 className={cn(
            "font-bold text-foreground mb-8 text-centre",
            deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
          )}>
            Primary Causes
          </h3>
          <div className={cn(
            "grid gap-8",
            deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
          )}>
            {causes.map((cause, index) => {
              const IconComponent = cause.icon;
              return (
                <Card
                  key={index}
                  className={cn(
                    "medical-card h-full transition-all duration-300 hover:shadow-xl hover:scale-105",
                    "hover:border-primary/30",
                    cause.severity ? severityColors[cause.severity] : ""
                  )}
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-centre gap-4">
                      <div className={cn(
                        "w-14 h-14 rounded-full flex items-centre justify-centre",
                        "bg-primary/10 border-2 border-primary/20"
                      )}>
                        <IconComponent className="h-7 w-7 text-primary" />
                      </div>
                      <CardTitle className={cn(
                        "text-foreground leading-tight",
                        deviceInfo.isMobile ? "text-lg" : "text-xl"
                      )}>
                        {cause.title}
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className={cn(
                      "text-foreground/80 mb-8 leading-relaxed font-medium",
                      deviceInfo.isMobile ? "text-base" : "text-lg"
                    )}>
                      {cause.description}
                    </p>
                    <ul className="space-y-4">
                      {cause.details.map((detail, detailIndex) => (
                        <li
                          key={detailIndex}
                          className={cn(
                            "flex items-start gap-4 p-4 rounded-xl",
                            "medical-card-inner backdrop-blur-sm border border-border/30 shadow-md",
                            "enhanced-hover hover:border-primary/40 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]",
                            deviceInfo.isMobile ? "text-base" : "text-lg"
                          )}
                        >
                          <div className="w-3 h-3 rounded-full bg-primary mt-2 flex-shrink-0" />
                          <span className="text-foreground font-semibold leading-relaxed">
                            {detail}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Risk Factors Section */}
        {riskFactors.length > 0 && (
          <div>
            <h3 className={cn(
              "font-bold text-foreground mb-10 text-centre",
              deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
            )}>
              Risk Factors
            </h3>
            <div className={cn(
              "grid gap-10",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
            )}>
              <Card className="medical-card shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-success/50">
                <CardHeader className="pb-6">
                  <CardTitle className="text-enhanced-heading text-success text-xl font-bold flex items-centre gap-3">
                    <div className="w-3 h-3 rounded-full bg-success"></div>
                    Modifiable Risk Factors
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="content-spacing">
                    {riskFactors
                      .filter(factor => factor.modifiable)
                      .map((factor, index) => (
                        <li key={index} className="border-l-4 border-success pl-6 py-3 bg-success-light rounded-r-lg">
                          <h4 className="text-enhanced-strong font-bold text-base mb-2">{factor.factor}</h4>
                          <p className="text-enhanced-body text-base leading-relaxed">{factor.description}</p>
                        </li>
                      ))}
                  </ul>
                </CardContent>
              </Card>

              <Card className="medical-card shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-info/50">
                <CardHeader className="pb-6">
                  <CardTitle className="text-info dark:text-info text-xl font-bold flex items-centre gap-3">
                    <div className="w-3 h-3 rounded-full bg-info"></div>
                    Non-Modifiable Risk Factors
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-5">
                    {riskFactors
                      .filter(factor => !factor.modifiable)
                      .map((factor, index) => (
                        <li key={index} className="border-l-4 border-info/30 pl-6 py-3 bg-info-light rounded-r-lg">
                          <h4 className="font-bold text-base text-foreground mb-2">{factor.factor}</h4>
                          <p className="text-base text-foreground/80 leading-relaxed">{factor.description}</p>
                        </li>
                      ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}