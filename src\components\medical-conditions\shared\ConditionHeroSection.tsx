import { Target, Calendar } from "lucide-react";
import React from 'react';
import { Link } from 'react-router-dom';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

// Props interface for ConditionHeroSection component
interface ConditionHeroSectionProps {
  /** Main title for the condition */
  title: string;
  /** Optional subtitle for additional context */
  subtitle?: string;
  /** Detailed description of the condition */
  description: string;
  /** Badge text to display above the title */
  badgeText?: string;
  /** Source URL for the hero background image */
  heroImageSrc?: string;
  /** Alt text for the hero image for accessibility */
  heroImageAlt?: string;
  /** Fallback image URL if main image fails to load */
  fallbackImageSrc?: string;
  /** Whether to show the assessment button */
  showAssessmentButton?: boolean;
  /** Whether to show the booking consultation button */
  showBookingButton?: boolean;
  /** Link destination for the assessment button */
  assessmentLink?: string;
  /** Link destination for the booking button */
  bookingLink?: string;
  /** Additional CSS classes for customization */
  className?: string;
}

const ConditionHeroSection: React.FC<ConditionHeroSectionProps> = ({
  title,
  subtitle,
  description,
  badgeText = "Spine Conditions Library",
  heroImageSrc,
  heroImageAlt = "Medical condition illustration",
  fallbackImageSrc = "https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
  showAssessmentButton = true,
  showBookingButton = true,
  assessmentLink = "#assessment",
  bookingLink = "/contact",
  className
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "relative bg-gradient-to-br from-primary/5 via-white to-primary/10 dark:from-primary/10 dark:via-background dark:to-primary/20",
      "border-b border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      {heroImageSrc && (
        <div className="absolute inset-0 overflow-hidden opacity-5 dark:opacity-10">
          <SafeImage
            src={heroImageSrc}
            alt={heroImageAlt}
            className="w-full h-full object-cover"
            fallbackSrc={fallbackImageSrc}
          />
        </div>
      )}
      <div className="container relative z-10">
        <div className="text-centre max-w-5xl mx-auto">
          <Badge
            variant="secondary"
            className={cn(
              "mb-6 px-4 py-2 text-sm font-medium",
              "bg-primary/10 text-primary border-primary/20",
              "dark:bg-primary/20 dark:text-primary-foreground dark:border-primary/30"
            )}
          >
            {badgeText}
          </Badge>
          <h1 className={cn(
            "font-bold text-foreground mb-6 leading-tight",
            deviceInfo.isMobile ? "text-3xl" : "text-4xl md:text-5xl lg:text-6xl"
          )}>
            {title}
          </h1>
          {subtitle && (
            <h2 className={cn(
              "text-foreground/80 mb-6 font-medium leading-relaxed",
              deviceInfo.isMobile ? "text-lg" : "text-xl md:text-2xl"
            )}>
              {subtitle}
            </h2>
          )}
          <p className={cn(
            "text-muted-foreground mb-10 leading-relaxed max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-base" : "text-lg md:text-xl"
          )}>
            {description}
          </p>
          {(showAssessmentButton || showBookingButton) && (
            <div className={cn(
              "flex gap-4 justify-centre",
              deviceInfo.isMobile ? "flex-col items-centre" : "flex-row items-centre"
            )}>
              {showAssessmentButton && (
                <Button
                  asChild
                  size={deviceInfo.isMobile ? "lg" : "lg"}
                  className={cn(
                    "px-8 py-3 font-semibold shadow-lg hover:shadow-xl",
                    "bg-primary hover:bg-primary/90 text-primary-foreground",
                    "transition-all duration-300 hover:scale-105",
                    "border-2 border-primary hover:border-primary/80",
                    deviceInfo.isMobile ? "w-full max-w-sm" : "min-w-[200px]"
                  )}
                >
                  <Link to={assessmentLink}>
                    <Target className="mr-2 h-5 w-5" />
                    Take Assessment
                  </Link>
                </Button>
              )}
              {showBookingButton && (
                <Button
                  asChild
                  variant="outline"
                  size={deviceInfo.isMobile ? "lg" : "lg"}
                  className={cn(
                    "px-8 py-3 font-semibold shadow-lg hover:shadow-xl",
                    "bg-background hover:bg-muted text-foreground",
                    "border-2 border-primary hover:border-primary/80",
                    "transition-all duration-300 hover:scale-105",
                    deviceInfo.isMobile ? "w-full max-w-sm" : "min-w-[200px]"
                  )}
                >
                  <Link to={bookingLink}>
                    <Calendar className="mr-2 h-5 w-5" />
                    Book Consultation
                  </Link>
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

ConditionHeroSection.displayName = 'ConditionHeroSection';

export default ConditionHeroSection;
