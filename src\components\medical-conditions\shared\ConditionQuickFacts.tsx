import { LucideIcon } from 'lucide-react';
import React from 'react';

import { Card, CardContent } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

// Interface for individual quick fact items
interface QuickFact {
  /** Lucide icon component to display */
  icon: LucideIcon;
  /** Title/label for the fact */
  title: string;
  /** Main value or statistic */
  value: string;
  /** Optional additional description */
  description?: string;
  /** Optional color theme for the fact card */
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
}

// Props interface for ConditionQuickFacts component
interface ConditionQuickFactsProps {
  /** Array of quick facts to display */
  facts: QuickFact[];
  /** Additional CSS classes for customization */
  className?: string;

}

const ConditionQuickFacts: React.FC<ConditionQuickFactsProps> = ({
  facts,
  className
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "section-background-alt",
      deviceInfo.isMobile ? "py-12" : "section-spacing",
      className
    )}>
      <div className="container">
        <div className={cn(
          "grid gap-6",
          facts.length <= 2
            ? "grid-cols-1 md:grid-cols-2"
            : facts.length === 3
            ? "grid-cols-1 md:grid-cols-3"
            : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
        )}>
          {facts.map((fact, index) => {
            const IconComponent = fact.icon;
            return (
              <Card
                key={index}
                className={cn(
                  "medical-card text-center transition-all duration-300 hover:shadow-lg hover:scale-105",
                  "hover:border-primary/30"
                )}
              >
                <CardContent className="pt-8 pb-6 px-6">
                  <div className={cn(
                    "w-16 h-16 mx-auto mb-4 rounded-full",
                    "bg-primary/10",
                    "flex items-center justify-center",
                    "border-2 border-primary/20"
                  )}>
                    <IconComponent className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-enhanced-heading font-bold mb-2 text-lg">
                    {fact.title}
                  </h3>
                  <p className="text-enhanced-strong text-base font-semibold text-primary mb-2">
                    {fact.value}
                  </p>
                  {fact.description && (
                    <p className="text-enhanced-muted text-sm leading-relaxed">
                      {fact.description}
                    </p>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};

ConditionQuickFacts.displayName = 'ConditionQuickFacts';

export default ConditionQuickFacts;
