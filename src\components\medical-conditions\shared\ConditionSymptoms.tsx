import { LucideIcon, AlertTriangle } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface SymptomDetail {
  name: string;
  description: string;
  severity: 'mild' | 'moderate' | 'severe';
  frequency: string;
}

interface SymptomItem {
  icon: LucideIcon;
  title?: string;
  category?: string;
  description?: string;
  symptoms: (string | SymptomDetail)[];
  severity?: 'mild' | 'moderate' | 'severe';
  frequency?: 'common' | 'occasional' | 'rare';
}

interface WarningSign {
  sign: string;
  description: string;
  urgency: 'immediate' | 'urgent' | 'monitor';
}

interface ConditionSymptomsProps {
  title?: string;
  subtitle?: string;
  symptomCategories: SymptomItem[];
  warningSigns?: WarningSign[];
  className?: string;
}

const severityColors = {
  mild: 'bg-success-light text-success border border-success/20',
  moderate: 'bg-info-light text-info border border-info/20',
  severe: 'bg-muted-light text-foreground border border-border/20'
};

const frequencyColors = {
  common: 'bg-primary/10 text-primary border border-primary/20',
  occasional: 'bg-info-light text-info border border-info/20',
  rare: 'bg-muted text-muted-foreground border border-border'
};

const urgencyColors = {
  immediate: 'border-border bg-muted-light',
  urgent: 'border-info/50 bg-info-light',
  monitor: 'border-info/50 bg-info-light'
};

export function ConditionSymptoms({
  title = "Signs and Symptoms",
  subtitle,
  symptomCategories,
  warningSigns = [],
  className
}: ConditionSymptomsProps) {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "section-background-alt",
      "border-y border-border/50",
      deviceInfo.isMobile ? "py-12" : "py-20",
      className
    )}>
      <div className="container">
        <div className="text-center mb-16">
          <h2 className={cn(
            "font-bold text-foreground mb-6 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            {title || "Symptoms and Warning Signs"}
          </h2>
          {subtitle && (
            <p className={cn(
              "text-muted-foreground max-w-4xl mx-auto leading-relaxed",
              deviceInfo.isMobile ? "text-base" : "text-lg"
            )}>
              {subtitle}
            </p>
          )}
        </div>

        {/* Symptom Categories */}
        <div className="mb-16">
          <div className={cn(
            "grid gap-8",
            deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
          )}>
            {symptomCategories.map((category, index) => {
              const IconComponent = category.icon;
              return (
                <Card
                  key={index}
                  className={cn(
                    "medical-card h-full transition-all duration-300 hover:shadow-xl hover:scale-105",
                    "hover:border-primary/30"
                  )}
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex items-center gap-3 flex-1">
                        <div className={cn(
                          "w-12 h-12 rounded-full flex items-center justify-center",
                          "bg-primary/10 border-2 border-primary/20"
                        )}>
                          <IconComponent className="h-6 w-6 text-primary" />
                        </div>
                        <CardTitle className={cn(
                          "text-foreground leading-tight",
                          deviceInfo.isMobile ? "text-lg" : "text-xl"
                        )}>
                          {category.title}
                        </CardTitle>
                      </div>
                      <div className="flex flex-col gap-2">
                        {category.severity && (
                          <Badge className={cn(
                            severityColors[category.severity],
                            "text-xs font-medium"
                          )}>
                            {category.severity}
                          </Badge>
                        )}
                        {category.frequency && (
                          <Badge className={cn(
                            frequencyColors[category.frequency],
                            "text-xs font-medium"
                          )}>
                            {category.frequency}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className={cn(
                      "text-muted-foreground mb-6 leading-relaxed",
                      deviceInfo.isMobile ? "text-sm" : "text-base"
                    )}>
                      {category.description}
                    </p>
                    <ul className="space-y-3">
                      {category.symptoms.map((symptom, symptomIndex) => (
                        <li
                          key={symptomIndex}
                          className={cn(
                            "flex items-start gap-3 p-2 rounded-md",
                            "hover:bg-primary/5 transition-colors duration-200",
                            deviceInfo.isMobile ? "text-sm" : "text-base"
                          )}
                        >
                          <div className="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0" />
                          <div className="flex-1">
                            <span className="text-foreground font-medium leading-relaxed">
                              {typeof symptom === 'string' ? symptom : symptom.name}
                            </span>
                            {typeof symptom === 'object' && symptom.description && (
                              <p className="text-muted-foreground text-sm mt-1 leading-relaxed">
                                {symptom.description}
                              </p>
                            )}
                            {typeof symptom === 'object' && symptom.frequency && (
                              <p className="text-muted-foreground text-xs mt-1">
                                {symptom.frequency}
                              </p>
                            )}
                          </div>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Warning Signs */}
        {warningSigns.length > 0 && (
          <div>
            <div className="flex items-center gap-3 mb-6">
              <AlertTriangle className="h-6 w-6 text-foreground" />
              <h3 className={cn(
                "font-semibold text-foreground dark:text-foreground",
                deviceInfo.isMobile ? "text-xl" : "text-2xl"
              )}>
                Warning Signs - Seek Medical Attention
              </h3>
            </div>
            
            <div className={cn(
              "grid gap-4",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
            )}>
              {warningSigns.map((warning, index) => (
                <Card 
                  key={index} 
                  className={cn(
                    "border-2",
                    urgencyColors[warning.urgency]
                  )}
                >
                  <CardContent className="pt-4">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="h-5 w-5 text-foreground mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold text-sm mb-1">{warning.sign}</h4>
                        <p className="text-sm text-muted-foreground">{warning.description}</p>
                        <Badge 
                          className={cn(
                            "mt-2",
                            warning.urgency === 'immediate' ? 'bg-muted text-primary-foreground' :
                            warning.urgency === 'urgent' ? 'bg-primary text-primary-foreground' :
                            'bg-info text-foreground'
                          )}
                        >
                          {warning.urgency === 'immediate' ? 'Call 000' :
                           warning.urgency === 'urgent' ? 'Seek urgent care' :
                           'Monitor closely'}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    </section>
  );
}