import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

interface Recommendation {
  title: string;
  description: string;
  importance: 'high' | 'medium' | 'low';
  implementation: string[];
}

interface WorkplaceArea {
  area: string;
  recommendations: Recommendation[];
}

interface DailyHabit {
  habit: string;
  description: string;
  frequency: string;
  benefits: string[];
}

interface ErgonomicGuidanceSectionProps {
  title: string;
  description: string;
  workplaceSetup: WorkplaceArea[];
  dailyHabits: DailyHabit[];
}

/**
 * ErgonomicGuidanceSection Component
 * Comprehensive ergonomic guidance for workplace setup and daily habits
 * to prevent and manage carpal tunnel syndrome
 */
const ErgonomicGuidanceSection: React.FC<ErgonomicGuidanceSectionProps> = ({
  title,
  description,
  workplaceSetup,
  dailyHabits
}) => {
  const [selectedArea, setSelectedArea] = useState<number>(0);

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'high': return 'medical-card border-l-4 border-l-destructive text-enhanced-heading';
      case 'medium': return 'medical-card border-l-4 border-l-warning text-enhanced-heading';
      case 'low': return 'medical-card border-l-4 border-l-success text-enhanced-heading';
      default: return 'medical-card border-border text-enhanced-heading';
    }
  };

  const getImportanceIcon = (importance: string) => {
    switch (importance) {
      case 'high': return '🔴';
      case 'medium': return '🟡';
      case 'low': return '🟢';
      default: return '⚪';
    }
  };

  return (
    <section className="py-16 bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-centre mb-12">
          <h2 className="text-4xl font-bold text-enhanced-heading mb-6">
            {title}
          </h2>
          <p className="text-xl text-enhanced-muted max-w-4xl mx-auto leading-relaxed">
            {description}
          </p>
        </div>

        {/* Ergonomic Principles Overview */}
        <div className="mb-12 p-6 bg-primary/5 rounded-xl border border-primary/20">
          <h3 className="text-xl font-semibold text-enhanced-heading mb-4">
            Key Ergonomic Principles
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-start gap-3">
              <span className="text-2xl">🤲</span>
              <div>
                <h4 className="font-semibold">Neutral Positioning</h4>
                <p className="text-sm text-enhanced-muted">Keep wrists straight and hands in natural positions</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <span className="text-2xl">⏰</span>
              <div>
                <h4 className="font-semibold">Regular Breaks</h4>
                <p className="text-sm text-enhanced-muted">Take frequent breaks to allow tissue recovery</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <span className="text-2xl">🔄</span>
              <div>
                <h4 className="font-semibold">Task Variation</h4>
                <p className="text-sm text-enhanced-muted">Alternate between different types of activities</p>
              </div>
            </div>
          </div>
        </div>

        <Tabs defaultValue="workplace" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="workplace">Workplace Setup</TabsTrigger>
            <TabsTrigger value="habits">Daily Habits</TabsTrigger>
          </TabsList>

          <TabsContent value="workplace" className="mt-6">
            {/* Workplace Area Selection */}
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-enhanced-heading mb-4">
                Select Workplace Area
              </h3>
              <div className="flex flex-wrap gap-3">
                {workplaceSetup.map((area, index) => (
                  <Button
                    key={index}
                    variant={selectedArea === index ? "default" : "outline"}
                    onClick={() => setSelectedArea(index)}
                  >
                    {area.area}
                  </Button>
                ))}
              </div>
            </div>

            {/* Selected Area Recommendations */}
            {workplaceSetup[selectedArea] && (
              <div>
                <h3 className="text-2xl font-semibold text-enhanced-heading mb-6">
                  {workplaceSetup[selectedArea].area} Recommendations
                </h3>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {workplaceSetup[selectedArea].recommendations.map((recommendation, index) => (
                    <Card key={index} className="h-full">
                      <CardHeader>
                        <div className="flex items-start justify-between">
                          <CardTitle className="text-lg flex items-centre gap-2">
                            <span>{getImportanceIcon(recommendation.importance)}</span>
                            {recommendation.title}
                          </CardTitle>
                          <Badge className={getImportanceColor(recommendation.importance)}>
                            {recommendation.importance} priority
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-enhanced-muted leading-relaxed">
                          {recommendation.description}
                        </p>
                        
                        <div>
                          <h4 className="font-semibold mb-3">Implementation Steps:</h4>
                          <ul className="space-y-2">
                            {recommendation.implementation.map((step, stepIndex) => (
                              <li key={stepIndex} className="flex items-start gap-2">
                                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                                <span className="text-sm text-enhanced-muted">{step}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="habits" className="mt-6">
            <div>
              <h3 className="text-2xl font-semibold text-enhanced-heading mb-6">
                Daily Habits for Prevention and Management
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {dailyHabits.map((habit, index) => (
                  <Card key={index} className="h-full">
                    <CardHeader>
                      <CardTitle className="text-lg">{habit.habit}</CardTitle>
                      <Badge variant="outline">{habit.frequency}</Badge>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <p className="text-enhanced-muted leading-relaxed">
                        {habit.description}
                      </p>
                      
                      <div>
                        <h4 className="font-semibold mb-3">Benefits:</h4>
                        <ul className="space-y-2">
                          {habit.benefits.map((benefit, benefitIndex) => (
                            <li key={benefitIndex} className="flex items-start gap-2">
                              <span className="w-1.5 h-1.5 bg-success rounded-full mt-2 flex-shrink-0"></span>
                              <span className="text-sm text-enhanced-muted">{benefit}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Quick Reference Checklist */}
        <div className="mt-12">
          <h3 className="text-2xl font-semibold text-enhanced-heading mb-6 text-centre">
            Quick Reference Checklist
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Workstation Setup</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm">
                  <li>✓ Keyboard at elbow height</li>
                  <li>✓ Wrists straight while typing</li>
                  <li>✓ Monitor at eye level</li>
                  <li>✓ Feet flat on floor</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Break Schedule</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm">
                  <li>✓ 30-60 seconds every 10-15 min</li>
                  <li>✓ Hand exercises during breaks</li>
                  <li>✓ Alternate task types</li>
                  <li>✓ Avoid prolonged typing</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Hand Position</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm">
                  <li>✓ Wrists floating above keyboard</li>
                  <li>✓ Light touch typing</li>
                  <li>✓ Avoid resting on wrist pads</li>
                  <li>✓ Gentle grip on mouse</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Sleep & Recovery</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm">
                  <li>✓ Avoid sleeping on hands</li>
                  <li>✓ Use night splints if needed</li>
                  <li>✓ Keep wrists straight</li>
                  <li>✓ Regular exercise routine</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Implementation Timeline */}
        <div className="mt-12 p-6 bg-muted/20 rounded-xl">
          <h3 className="text-xl font-semibold text-enhanced-heading mb-4">
            Implementation Timeline
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-semibold text-enhanced-heading mb-2">Week 1-2: Assessment</h4>
              <ul className="space-y-1 text-sm text-enhanced-muted">
                <li>• Evaluate current workstation</li>
                <li>• Identify problem areas</li>
                <li>• Begin basic adjustments</li>
                <li>• Start break schedule</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-enhanced-heading mb-2">Week 3-4: Implementation</h4>
              <ul className="space-y-1 text-sm text-enhanced-muted">
                <li>• Make equipment changes</li>
                <li>• Establish new habits</li>
                <li>• Monitor symptoms</li>
                <li>• Adjust as needed</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-enhanced-heading mb-2">Week 5+: Maintenance</h4>
              <ul className="space-y-1 text-sm text-enhanced-muted">
                <li>• Maintain good habits</li>
                <li>• Regular reassessment</li>
                <li>• Continue monitoring</li>
                <li>• Prevent regression</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

ErgonomicGuidanceSection.displayName = 'ErgonomicGuidanceSection';

export default ErgonomicGuidanceSection;
