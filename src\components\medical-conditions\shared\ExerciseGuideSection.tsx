import React, { useState } from 'react';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

interface Exercise {
  name: string;
  description: string;
  instructions: string[];
  frequency: string;
  duration: string;
  benefits: string[];
  precautions: string[];
  imageSrc: string;
}

interface ExerciseCategory {
  category: string;
  description: string;
  exercises: Exercise[];
}

interface ExerciseGuideSectionProps {
  title: string;
  description: string;
  exerciseCategories: ExerciseCategory[];
}

/**
 * ExerciseGuideSection Component
 * Comprehensive exercise guide with step-by-step instructions,
 * benefits, precautions, and visual demonstrations
 */
const ExerciseGuideSection: React.FC<ExerciseGuideSectionProps> = ({
  title,
  description,
  exerciseCategories
}) => {
  const [selectedCategory, setSelectedCategory] = useState<number>(0);
  const [selectedExercise, setSelectedExercise] = useState<number>(0);

  const currentCategory = exerciseCategories[selectedCategory];
  const currentExercise = currentCategory?.exercises[selectedExercise];

  return (
    <section className="py-16 bg-gradient-to-br from-muted/10 to-background">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-enhanced-heading mb-6">
            {title}
          </h2>
          <p className="text-xl text-enhanced-muted max-w-4xl mx-auto leading-relaxed">
            {description}
          </p>
        </div>

        {/* Important Safety Notice */}
        <div className="mb-8 p-6 medical-card rounded-xl border-l-4 border-l-warning">
          <div className="flex items-start gap-4">
            <div className="w-6 h-6 bg-warning rounded-full flex items-center justify-center flex-shrink-0 mt-1">
              <span className="text-warning-foreground text-sm font-bold">!</span>
            </div>
            <div>
              <h4 className="font-semibold text-enhanced-heading mb-2">
                Important Safety Information
              </h4>
              <p className="text-enhanced-body leading-relaxed">
                These exercises should be performed under professional guidance, especially when starting.
                Stop immediately if symptoms worsen and consult your healthcare provider. Individual
                modifications may be necessary based on your specific condition and severity.
              </p>
            </div>
          </div>
        </div>

        {/* Exercise Categories */}
        <div className="mb-8">
          <h3 className="text-2xl font-semibold text-enhanced-heading mb-4">
            Exercise Categories
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {exerciseCategories.map((category, index) => (
              <Button
                key={index}
                variant={selectedCategory === index ? "default" : "outline"}
                className="h-auto p-4 text-left justify-start"
                onClick={() => {
                  setSelectedCategory(index);
                  setSelectedExercise(0);
                }}
              >
                <div>
                  <div className="font-semibold mb-1">{category.category}</div>
                  <div className="text-sm opacity-80">{category.description}</div>
                </div>
              </Button>
            ))}
          </div>
        </div>

        {/* Exercise Selection and Details */}
        {currentCategory && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Exercise List */}
            <div className="lg:col-span-1">
              <h4 className="font-semibold text-enhanced-heading mb-4">
                {currentCategory.category}
              </h4>
              <div className="space-y-2">
                {currentCategory.exercises.map((exercise, index) => (
                  <Button
                    key={index}
                    variant={selectedExercise === index ? "default" : "ghost"}
                    className="w-full text-left justify-start h-auto p-3"
                    onClick={() => setSelectedExercise(index)}
                  >
                    <div>
                      <div className="font-medium">{exercise.name}</div>
                      <div className="text-xs opacity-70 mt-1">
                        {exercise.frequency} • {exercise.duration}
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            {/* Exercise Details */}
            <div className="lg:col-span-3">
              {currentExercise && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-2xl">{currentExercise.name}</CardTitle>
                    <p className="text-enhanced-muted">{currentExercise.description}</p>
                  </CardHeader>
                  <CardContent>
                    <Tabs defaultValue="instructions" className="w-full">
                      <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="instructions">Instructions</TabsTrigger>
                        <TabsTrigger value="benefits">Benefits</TabsTrigger>
                        <TabsTrigger value="precautions">Precautions</TabsTrigger>
                        <TabsTrigger value="schedule">Schedule</TabsTrigger>
                      </TabsList>

                      <TabsContent value="instructions" className="mt-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                          <div>
                            <h4 className="font-semibold mb-4">Step-by-Step Instructions</h4>
                            <ol className="space-y-3">
                              {currentExercise.instructions.map((instruction, index) => (
                                <li key={index} className="flex items-start gap-3">
                                  <span className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-semibold flex-shrink-0 mt-0.5">
                                    {index + 1}
                                  </span>
                                  <span className="text-enhanced-muted leading-relaxed">
                                    {instruction}
                                  </span>
                                </li>
                              ))}
                            </ol>
                          </div>
                          <div>
                            <SafeImage
                              src={currentExercise.imageSrc}
                              alt={currentExercise.name}
                              className="w-full h-64 object-cover rounded-lg"
                              fallbackSrc="/images/exercises/default-exercise.jpg"
                            />
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="benefits" className="mt-6">
                        <div>
                          <h4 className="font-semibold mb-4">Exercise Benefits</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {currentExercise.benefits.map((benefit, index) => (
                              <div key={index} className="flex items-start gap-3 p-3 bg-success/10 rounded-lg border border-success/20">
                                <span className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0"></span>
                                <span className="text-success-foreground">{benefit}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="precautions" className="mt-6">
                        <div>
                          <h4 className="font-semibold mb-4">Important Precautions</h4>
                          <div className="space-y-3">
                            {currentExercise.precautions.map((precaution, index) => (
                              <div key={index} className="flex items-start gap-3 p-3 bg-warning/10 rounded-lg border border-warning/20">
                                <span className="w-6 h-6 bg-warning rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                  <span className="text-warning-foreground text-xs font-bold">!</span>
                                </span>
                                <span className="text-warning-foreground">{precaution}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="schedule" className="mt-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">Exercise Schedule</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                              <div>
                                <div className="font-medium text-enhanced-heading">Frequency</div>
                                <Badge variant="outline" className="mt-1">
                                  {currentExercise.frequency}
                                </Badge>
                              </div>
                              <div>
                                <div className="font-medium text-enhanced-heading">Duration</div>
                                <Badge variant="outline" className="mt-1">
                                  {currentExercise.duration}
                                </Badge>
                              </div>
                            </CardContent>
                          </Card>

                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">Progression Tips</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <ul className="space-y-2 text-sm">
                                <li className="flex items-start gap-2">
                                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                                  Start slowly and gradually increase intensity
                                </li>
                                <li className="flex items-start gap-2">
                                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                                  Consistency is more important than intensity
                                </li>
                                <li className="flex items-start gap-2">
                                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                                  Track your progress and symptoms
                                </li>
                                <li className="flex items-start gap-2">
                                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                                  Adjust based on your response
                                </li>
                              </ul>
                            </CardContent>
                          </Card>
                        </div>
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )}

        {/* Professional Guidance Note */}
        <div className="mt-12 p-6 bg-primary/5 rounded-xl border border-primary/20">
          <div className="flex items-start gap-4">
            <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-1">
              <span className="text-primary-foreground text-sm font-bold">i</span>
            </div>
            <div>
              <h4 className="font-semibold text-enhanced-heading mb-2">
                Professional Guidance Recommended
              </h4>
              <p className="text-enhanced-muted leading-relaxed">
                While these exercises are evidence-based and generally safe, individual needs vary. 
                Consider working with a qualified hand therapist or physical therapist who can 
                assess your specific condition, modify exercises as needed, and monitor your progress. 
                They can also teach proper technique and provide additional treatment modalities.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

ExerciseGuideSection.displayName = 'ExerciseGuideSection';

export default ExerciseGuideSection;
