import React, { useState } from 'react';

import SafeImage from '@/components/SafeImage';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface AnatomyPoint {
  title: string;
  description: string;
  clinicalSignificance: string;
}

interface InteractiveFeature {
  title: string;
  description: string;
  imageSrc: string;
}

interface NerveAnatomySectionProps {
  title: string;
  description: string;
  anatomyPoints: AnatomyPoint[];
  interactiveFeatures: InteractiveFeature[];
}

/**
 * NerveAnatomySection Component
 * Specialized component for displaying detailed nerve anatomy information
 * with interactive features and clinical significance explanations
 */
const NerveAnatomySection: React.FC<NerveAnatomySectionProps> = ({
  title,
  description,
  anatomyPoints,
  interactiveFeatures
}) => {
  const [selectedFeature, setSelectedFeature] = useState<number>(0);

  return (
    <section className="py-16 bg-gradient-to-br from-muted/10 to-background">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-enhanced-heading mb-6">
            {title}
          </h2>
          <p className="text-xl text-enhanced-muted max-w-4xl mx-auto leading-relaxed">
            {description}
          </p>
        </div>

        {/* Interactive Anatomy Visualization */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-enhanced-heading mb-8 text-center">
            Interactive Anatomy Visualization
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Feature Selection */}
            <div className="lg:col-span-1">
              <div className="space-y-4">
                {interactiveFeatures.map((feature, index) => (
                  <Button
                    key={index}
                    variant={selectedFeature === index ? "default" : "outline"}
                    className="w-full text-left justify-start h-auto p-4"
                    onClick={() => setSelectedFeature(index)}
                  >
                    <div>
                      <div className="font-semibold mb-1">{feature.title}</div>
                      <div className="text-sm opacity-80">{feature.description}</div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            {/* Selected Feature Display */}
            <div className="lg:col-span-2">
              <Card className="overflow-hidden">
                <CardHeader>
                  <CardTitle className="text-xl">
                    {interactiveFeatures[selectedFeature].title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <SafeImage
                    src={interactiveFeatures[selectedFeature].imageSrc}
                    alt={interactiveFeatures[selectedFeature].title}
                    className="w-full h-96 object-cover rounded-lg mb-4"
                    fallbackSrc="/images/peripheral-nerve-conditions/default-anatomy.jpg"
                  />
                  <p className="text-enhanced-muted leading-relaxed">
                    {interactiveFeatures[selectedFeature].description}
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Detailed Anatomy Points */}
        <div>
          <h3 className="text-2xl font-bold text-enhanced-heading mb-8 text-center">
            Detailed Anatomical Understanding
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {anatomyPoints.map((point, index) => (
              <Card key={index} className="h-full">
                <CardHeader>
                  <CardTitle className="text-xl text-enhanced-heading">
                    {point.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-enhanced-heading mb-2">
                      Anatomical Description
                    </h4>
                    <p className="text-enhanced-muted leading-relaxed">
                      {point.description}
                    </p>
                  </div>
                  
                  <div className="border-t pt-4">
                    <h4 className="font-semibold text-enhanced-heading mb-2 flex items-center gap-2">
                      <span className="w-2 h-2 bg-primary rounded-full"></span>
                      Clinical Significance
                    </h4>
                    <p className="text-enhanced-muted leading-relaxed">
                      {point.clinicalSignificance}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Educational Note */}
        <div className="mt-12 p-6 bg-primary/5 rounded-xl border border-primary/20">
          <div className="flex items-start gap-4">
            <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-1">
              <span className="text-primary-foreground text-sm font-bold">!</span>
            </div>
            <div>
              <h4 className="font-semibold text-enhanced-heading mb-2">
                Educational Purpose
              </h4>
              <p className="text-enhanced-muted leading-relaxed">
                This anatomical information is provided for educational purposes to help patients 
                understand their condition. It should not replace professional medical consultation 
                or be used for self-diagnosis. Always consult with qualified healthcare providers 
                for accurate diagnosis and treatment recommendations.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

NerveAnatomySection.displayName = 'NerveAnatomySection';

export default NerveAnatomySection;
