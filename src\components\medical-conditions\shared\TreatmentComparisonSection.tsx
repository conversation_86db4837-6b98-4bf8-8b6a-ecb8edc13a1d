import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

interface TreatmentOption {
  treatment: string;
  effectiveness: string;
  duration: string;
  invasiveness: 'low' | 'medium' | 'high';
  cost: 'low' | 'medium' | 'high';
  recovery: string;
  pros: string[];
  cons: string[];
  scientificEvidence: string;
  recommendedFor: string[];
}

interface TreatmentComparisonSectionProps {
  title: string;
  description: string;
  comparisonTable: TreatmentOption[];
}

/**
 * TreatmentComparisonSection Component
 * Interactive component for comparing different treatment options
 * with detailed pros/cons, effectiveness, and scientific evidence
 */
const TreatmentComparisonSection: React.FC<TreatmentComparisonSectionProps> = ({
  title,
  description,
  comparisonTable
}) => {
  const [selectedTreatments, setSelectedTreatments] = useState<number[]>([0, 1]);

  const getInvasivenessColor = (level: string) => {
    switch (level) {
      case 'low': return 'medical-card border-l-4 border-l-success text-enhanced-heading';
      case 'medium': return 'medical-card border-l-4 border-l-warning text-enhanced-heading';
      case 'high': return 'medical-card border-l-4 border-l-destructive text-enhanced-heading';
      default: return 'medical-card border-border text-enhanced-heading';
    }
  };

  const getCostColor = (level: string) => {
    switch (level) {
      case 'low': return 'medical-card border-l-4 border-l-success text-enhanced-heading';
      case 'medium': return 'medical-card border-l-4 border-l-warning text-enhanced-heading';
      case 'high': return 'medical-card border-l-4 border-l-destructive text-enhanced-heading';
      default: return 'medical-card border-border text-enhanced-heading';
    }
  };

  const toggleTreatmentSelection = (index: number) => {
    if (selectedTreatments.includes(index)) {
      if (selectedTreatments.length > 1) {
        setSelectedTreatments(selectedTreatments.filter(i => i !== index));
      }
    } else {
      if (selectedTreatments.length < 3) {
        setSelectedTreatments([...selectedTreatments, index]);
      }
    }
  };

  return (
    <section className="py-16 bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-centre mb-12">
          <h2 className="text-4xl font-bold text-enhanced-heading mb-6">
            {title}
          </h2>
          <p className="text-xl text-enhanced-muted max-w-4xl mx-auto leading-relaxed">
            {description}
          </p>
        </div>

        {/* Treatment Selection */}
        <div className="mb-8">
          <h3 className="text-xl font-semibold text-enhanced-heading mb-4">
            Select treatments to compare (up to 3):
          </h3>
          <div className="flex flex-wrap gap-3">
            {comparisonTable.map((treatment, index) => (
              <Button
                key={index}
                variant={selectedTreatments.includes(index) ? "default" : "outline"}
                onClick={() => toggleTreatmentSelection(index)}
                className="h-auto p-3"
              >
                {treatment.treatment}
              </Button>
            ))}
          </div>
        </div>

        {/* Comparison Table */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-12">
          {selectedTreatments.map((treatmentIndex) => {
            const treatment = comparisonTable[treatmentIndex];
            return (
              <Card key={treatmentIndex} className="h-full">
                <CardHeader>
                  <CardTitle className="text-xl text-enhanced-heading">
                    {treatment.treatment}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Quick Stats */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm font-medium text-enhanced-muted">Effectiveness</div>
                      <div className="font-semibold">{treatment.effectiveness}</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-enhanced-muted">Duration</div>
                      <div className="font-semibold">{treatment.duration}</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-enhanced-muted">Invasiveness</div>
                      <Badge className={getInvasivenessColor(treatment.invasiveness)}>
                        {treatment.invasiveness}
                      </Badge>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-enhanced-muted">Cost</div>
                      <Badge className={getCostColor(treatment.cost)}>
                        {treatment.cost}
                      </Badge>
                    </div>
                  </div>

                  <div>
                    <div className="text-sm font-medium text-enhanced-muted">Recovery Time</div>
                    <div className="font-semibold">{treatment.recovery}</div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Detailed Comparison */}
        <Tabs defaultValue="pros-cons" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="pros-cons">Pros & Cons</TabsTrigger>
            <TabsTrigger value="evidence">Scientific Evidence</TabsTrigger>
            <TabsTrigger value="candidates">Ideal Candidates</TabsTrigger>
            <TabsTrigger value="summary">Summary</TabsTrigger>
          </TabsList>

          <TabsContent value="pros-cons" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {selectedTreatments.map((treatmentIndex) => {
                const treatment = comparisonTable[treatmentIndex];
                return (
                  <Card key={treatmentIndex}>
                    <CardHeader>
                      <CardTitle className="text-lg">{treatment.treatment}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="font-semibold text-success-foreground mb-2">Advantages</h4>
                        <ul className="space-y-1">
                          {treatment.pros.map((pro, index) => (
                            <li key={index} className="flex items-start gap-2 text-sm">
                              <span className="w-1.5 h-1.5 bg-success rounded-full mt-2 flex-shrink-0"></span>
                              {pro}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold text-destructive-foreground mb-2">Disadvantages</h4>
                        <ul className="space-y-1">
                          {treatment.cons.map((con, index) => (
                            <li key={index} className="flex items-start gap-2 text-sm">
                              <span className="w-1.5 h-1.5 bg-destructive rounded-full mt-2 flex-shrink-0"></span>
                              {con}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          <TabsContent value="evidence" className="mt-6">
            <div className="space-y-6">
              {selectedTreatments.map((treatmentIndex) => {
                const treatment = comparisonTable[treatmentIndex];
                return (
                  <Card key={treatmentIndex}>
                    <CardHeader>
                      <CardTitle className="text-lg">{treatment.treatment}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-enhanced-muted leading-relaxed">
                        {treatment.scientificEvidence}
                      </p>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          <TabsContent value="candidates" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {selectedTreatments.map((treatmentIndex) => {
                const treatment = comparisonTable[treatmentIndex];
                return (
                  <Card key={treatmentIndex}>
                    <CardHeader>
                      <CardTitle className="text-lg">{treatment.treatment}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <h4 className="font-semibold mb-3">Recommended for:</h4>
                      <ul className="space-y-2">
                        {treatment.recommendedFor.map((candidate, index) => (
                          <li key={index} className="flex items-start gap-2 text-sm">
                            <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                            {candidate}
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          <TabsContent value="summary" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Treatment Selection Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-enhanced-muted leading-relaxed mb-4">
                  The choice of treatment for carpal tunnel syndrome should be individualised based on 
                  symptom severity, functional impact, patient preferences, and response to previous treatments. 
                  This comparison tool helps understand the trade-offs between different approaches.
                </p>
                <div className="bg-primary/5 p-4 rounded-lg">
                  <h4 className="font-semibold mb-2">Important Considerations:</h4>
                  <ul className="space-y-1 text-sm">
                    <li>• Conservative treatments are typically tried first for mild to moderate symptoms</li>
                    <li>• Surgical options are considered when conservative treatments fail or symptoms are severe</li>
                    <li>• Patient factors (age, occupation, medical history) influence treatment selection</li>
                    <li>• Combination approaches may be most effective for some patients</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
};

TreatmentComparisonSection.displayName = 'TreatmentComparisonSection';

export default TreatmentComparisonSection;
