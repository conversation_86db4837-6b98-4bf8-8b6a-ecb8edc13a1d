import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface EmergencySign {
  sign: string;
  description: string;
  action: string;
  urgency: 'immediate' | 'urgent' | 'prompt';
}

interface WarningSignsSectionProps {
  title: string;
  description: string;
  emergencySigns: EmergencySign[];
}

/**
 * WarningSignsSection Component
 * Comprehensive warning signs section with urgency levels
 * and clear action recommendations for patients
 */
const WarningSignsSection: React.FC<WarningSignsSectionProps> = ({
  title,
  description,
  emergencySigns
}) => {
  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return 'medical-card border-border/70 bg-card';
      case 'urgent': return 'medical-card border-border/70 bg-card';
      case 'prompt': return 'medical-card border-border/70 bg-card';
      default: return 'medical-card border-border/70 bg-card';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return '🚨';
      case 'urgent': return '⚠️';
      case 'prompt': return '⏰';
      default: return 'ℹ️';
    }
  };

  const getUrgencyLabel = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return 'IMMEDIATE ATTENTION';
      case 'urgent': return 'URGENT CARE';
      case 'prompt': return 'PROMPT EVALUATION';
      default: return 'ROUTINE CARE';
    }
  };

  const immediateSignsCount = emergencySigns.filter(sign => sign.urgency === 'immediate').length;
  const urgentSignsCount = emergencySigns.filter(sign => sign.urgency === 'urgent').length;
  const promptSignsCount = emergencySigns.filter(sign => sign.urgency === 'prompt').length;

  return (
    <section className="section-spacing section-background">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-enhanced-heading mb-6">
            {title}
          </h2>
          <p className="text-xl text-enhanced-body max-w-4xl mx-auto leading-relaxed">
            {description}
          </p>
        </div>

        {/* Urgency Level Overview */}
        <div className="mb-12">
          <h3 className="text-2xl font-semibold text-enhanced-heading mb-6 text-center">
            Understanding Urgency Levels
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="medical-card border-border/70">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2 text-enhanced-heading">
                  🚨 Immediate Attention
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-enhanced-body text-sm mb-2">
                  Seek emergency medical care immediately. These signs may indicate severe nerve damage.
                </p>
                <Badge variant="outline" className="badge-urgent">
                  {immediateSignsCount} warning sign{immediateSignsCount !== 1 ? 's' : ''}
                </Badge>
              </CardContent>
            </Card>

            <Card className="medical-card border-border/70">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2 text-enhanced-heading">
                  ⚠️ Urgent Care
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-enhanced-body text-sm mb-2">
                  Contact your healthcare provider within 24-48 hours for evaluation.
                </p>
                <Badge variant="outline" className="badge-warning">
                  {urgentSignsCount} warning sign{urgentSignsCount !== 1 ? 's' : ''}
                </Badge>
              </CardContent>
            </Card>

            <Card className="medical-card border-border/70">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2 text-enhanced-heading">
                  ⏰ Prompt Evaluation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-enhanced-body text-sm mb-2">
                  Schedule an appointment within 1-2 weeks to prevent progression.
                </p>
                <Badge variant="outline" className="badge-info">
                  {promptSignsCount} warning sign{promptSignsCount !== 1 ? 's' : ''}
                </Badge>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Warning Signs by Urgency */}
        <div className="space-y-8">
          {['immediate', 'urgent', 'prompt'].map((urgencyLevel) => {
            const signsForLevel = emergencySigns.filter(sign => sign.urgency === urgencyLevel);
            
            if (signsForLevel.length === 0) return null;

            return (
              <div key={urgencyLevel}>
                <h3 className="text-xl font-semibold text-enhanced-heading mb-4 flex items-center gap-2">
                  <span>{getUrgencyIcon(urgencyLevel)}</span>
                  {getUrgencyLabel(urgencyLevel)}
                </h3>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {signsForLevel.map((sign, index) => (
                    <Card key={index} className={`${getUrgencyColor(sign.urgency)} hover:shadow-lg transition-shadow`}>
                      <CardHeader>
                        <div className="flex items-start justify-between">
                          <CardTitle className="text-lg flex items-center gap-2 text-enhanced-heading">
                            <span>{getUrgencyIcon(sign.urgency)}</span>
                            {sign.sign}
                          </CardTitle>
                          <Badge variant="outline" className="badge-secondary">
                            {sign.urgency}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <h4 className="font-semibold mb-2 text-enhanced-heading">Description</h4>
                          <p className="text-enhanced-body leading-relaxed">
                            {sign.description}
                          </p>
                        </div>

                        <div className="border-t border-border/50 pt-4">
                          <h4 className="font-semibold mb-2 text-enhanced-heading">Recommended Action</h4>
                          <p className="text-enhanced-body leading-relaxed mb-3">
                            {sign.action}
                          </p>

                          {sign.urgency === 'immediate' && (
                            <Button variant="default" className="w-full">
                              Seek Emergency Care Now
                            </Button>
                          )}

                          {sign.urgency === 'urgent' && (
                            <Button variant="outline" className="w-full">
                              Contact Healthcare Provider
                            </Button>
                          )}

                          {sign.urgency === 'prompt' && (
                            <Button variant="outline" className="w-full">
                              Schedule Appointment
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            );
          })}
        </div>

        {/* Emergency Contact Information */}
        <div className="mt-12 medical-card p-6 rounded-xl">
          <h3 className="text-xl font-semibold text-enhanced-heading mb-4 flex items-center gap-2">
            🚨 Emergency Contact Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-enhanced-heading mb-2">When to Call Emergency Services</h4>
              <ul className="space-y-1 text-enhanced-body text-sm">
                <li>• Complete loss of hand function</li>
                <li>• Severe, sudden onset of symptoms</li>
                <li>• Signs of infection (fever, redness, swelling)</li>
                <li>• Inability to move fingers or thumb</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-enhanced-heading mb-2">Emergency Numbers</h4>
              <ul className="space-y-1 text-enhanced-body text-sm">
                <li>• Emergency Services: 000</li>
                <li>• Poison Information: 13 11 26</li>
                <li>• Health Direct: 1800 022 222</li>
                <li>• Your Healthcare Provider: [Contact Details]</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Prevention Reminder */}
        <div className="mt-8 medical-card p-6 rounded-xl">
          <h3 className="text-xl font-semibold text-enhanced-heading mb-4 flex items-center gap-2">
            🛡️ Prevention is Key
          </h3>
          <p className="text-enhanced-body leading-relaxed">
            Many of these warning signs can be prevented through early intervention, proper ergonomics,
            and appropriate treatment. Regular monitoring of symptoms and adherence to treatment
            recommendations can help prevent progression to more serious complications. Don't wait
            for symptoms to worsen – early action leads to better outcomes.
          </p>
        </div>

        {/* Symptom Tracking Reminder */}
        <div className="mt-8 medical-card p-6 rounded-xl">
          <h3 className="text-xl font-semibold text-enhanced-heading mb-4 flex items-center gap-2">
            📊 Track Your Symptoms
          </h3>
          <p className="text-enhanced-body leading-relaxed mb-4">
            Keep a record of your symptoms, including when they occur, their severity, and what
            activities trigger them. This information is valuable for your healthcare provider
            in determining the best treatment approach.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <h4 className="font-semibold text-enhanced-heading mb-1">What to Track</h4>
              <ul className="text-enhanced-body space-y-1">
                <li>• Symptom severity (1-10 scale)</li>
                <li>• Time of day symptoms occur</li>
                <li>• Activities that trigger symptoms</li>
                <li>• Duration of symptoms</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-enhanced-heading mb-1">When to Track</h4>
              <ul className="text-enhanced-body space-y-1">
                <li>• Daily for 1-2 weeks</li>
                <li>• Before medical appointments</li>
                <li>• When starting new treatments</li>
                <li>• If symptoms change</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-enhanced-heading mb-1">How to Use</h4>
              <ul className="text-enhanced-body space-y-1">
                <li>• Share with healthcare provider</li>
                <li>• Identify patterns and triggers</li>
                <li>• Monitor treatment effectiveness</li>
                <li>• Guide activity modifications</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

WarningSignsSection.displayName = 'WarningSignsSection';

export default WarningSignsSection;
