import { 
  Shield, 
  Home, 
  Briefcase, 
  Footprints, 
  Activity, 
  CheckCircle,
  AlertTriangle,
  Target,
  Heart,
  TrendingUp,
  Settings
} from "lucide-react";
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ErgonomicRecommendation {
  id: string;
  title: string;
  description: string;
  importance: 'High' | 'Medium' | 'Low';
  recommendations: string[];
  avoidActions: string[];
  scientificRationale: string;
  implementationTips: string[];
}

interface LifestyleCategory {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
  recommendations: ErgonomicRecommendation[];
}

interface TarsalErgonomicsSectionProps {
  className?: string;
}

const lifestyleCategories: LifestyleCategory[] = [
  {
    id: 'footwear',
    title: 'Footwear & Orthotics',
    icon: Footprints,
    description: 'Proper footwear selection and orthotic devices to support foot mechanics and reduce nerve compression',
    recommendations: [
      {
        id: 'shoe-selection',
        title: 'Proper Shoe Selection',
        description: 'Choosing appropriate footwear to support foot arch and reduce nerve compression',
        importance: 'High',
        recommendations: [
          'Choose shoes with good arch support and cushioning',
          'Ensure adequate toe box space and proper width',
          'Select shoes with firm heel counter for stability',
          'Avoid high heels and completely flat shoes',
          'Replace worn-out shoes regularly (every 300-500 miles for athletic shoes)'
        ],
        avoidActions: [
          'Wearing shoes without arch support',
          'Using worn-out or poorly fitting shoes',
          'Walking barefoot on hard surfaces for extended periods',
          'Wearing high heels or shoes with poor support'
        ],
        scientificRationale: 'Proper footwear maintains optimal foot biomechanics, reduces overpronation, and decreases stress on the posterior tibial nerve by supporting the medial longitudinal arch.',
        implementationTips: [
          'Shop for shoes in the afternoon when feet are slightly swollen',
          'Have both feet measured as they may be different sizes',
          'Consider custom orthotics if over-the-counter options are insufficient',
          'Gradually break in new shoes to avoid sudden changes'
        ]
      },
      {
        id: 'orthotic-devices',
        title: 'Orthotic Devices and Arch Support',
        description: 'Using orthotic devices to improve foot mechanics and reduce nerve compression',
        importance: 'High',
        recommendations: [
          'Use over-the-counter arch supports for mild cases',
          'Consider custom orthotics for severe flat feet or biomechanical issues',
          'Ensure orthotics fit properly in your shoes',
          'Gradually increase wearing time to allow adaptation',
          'Replace orthotics when they show signs of wear'
        ],
        avoidActions: [
          'Using orthotics that are too rigid or uncomfortable',
          'Ignoring proper fitting and adjustment',
          'Expecting immediate results without gradual adaptation',
          'Using damaged or worn-out orthotic devices'
        ],
        scientificRationale: 'Orthotic devices help control excessive pronation, support the medial arch, and reduce tension on the posterior tibial nerve by improving foot alignment and biomechanics.',
        implementationTips: [
          'Start with 2-3 hours daily and gradually increase',
          'Ensure orthotics don\'t create pressure points',
          'Consider different orthotics for different activities',
          'Work with a podiatrist for custom orthotic fitting'
        ]
      }
    ]
  },
  {
    id: 'workplace',
    title: 'Workplace Ergonomics',
    icon: Briefcase,
    description: 'Optimize your work environment to reduce prolonged standing and foot stress',
    recommendations: [
      {
        id: 'standing-work',
        title: 'Standing Work Modifications',
        description: 'Strategies for jobs requiring prolonged standing to reduce foot stress',
        importance: 'High',
        recommendations: [
          'Use anti-fatigue mats to reduce pressure on feet',
          'Alternate between standing and sitting when possible',
          'Take regular breaks to rest and elevate feet',
          'Wear supportive, well-cushioned work shoes',
          'Use footrests or rails to change positions frequently'
        ],
        avoidActions: [
          'Standing in one position for hours without breaks',
          'Working on hard concrete floors without mats',
          'Wearing unsupportive or worn-out work shoes',
          'Ignoring foot discomfort during work'
        ],
        scientificRationale: 'Prolonged standing increases pressure on the feet and can worsen nerve compression. Regular position changes and proper support reduce cumulative stress on the posterior tibial nerve.',
        implementationTips: [
          'Set hourly reminders to change positions',
          'Invest in quality anti-fatigue mats',
          'Consider adjustable height workstations',
          'Rotate between different standing positions'
        ]
      },
      {
        id: 'desk-work',
        title: 'Desk Work and Sitting Positions',
        description: 'Proper sitting ergonomics to maintain good foot positioning',
        importance: 'Medium',
        recommendations: [
          'Keep feet flat on floor or footrest',
          'Avoid crossing legs for extended periods',
          'Use a footrest if feet don\'t reach the floor comfortably',
          'Take regular breaks to walk and stretch',
          'Maintain proper chair height and support'
        ],
        avoidActions: [
          'Sitting with feet dangling or unsupported',
          'Crossing legs or ankles for long periods',
          'Remaining sedentary for hours without breaks',
          'Using chairs that don\'t support proper posture'
        ],
        scientificRationale: 'Proper sitting posture maintains good circulation and prevents prolonged pressure on nerve pathways. Regular movement prevents stiffness and promotes nerve health.',
        implementationTips: [
          'Adjust chair height so thighs are parallel to floor',
          'Use a footrest if needed to support feet',
          'Set reminders to stand and move every 30-45 minutes',
          'Perform ankle exercises while sitting'
        ]
      }
    ]
  },
  {
    id: 'daily-activities',
    title: 'Daily Activities',
    icon: Home,
    description: 'Modify daily activities and habits to support foot health and prevent symptom aggravation',
    recommendations: [
      {
        id: 'walking-running',
        title: 'Walking and Exercise Modifications',
        description: 'Adapting physical activities to reduce stress on the posterior tibial nerve',
        importance: 'High',
        recommendations: [
          'Choose softer surfaces for walking and running when possible',
          'Gradually increase activity levels rather than sudden changes',
          'Use proper warm-up and cool-down routines',
          'Listen to your body and rest when symptoms worsen',
          'Consider low-impact activities like swimming or cycling'
        ],
        avoidActions: [
          'Sudden increases in activity intensity or duration',
          'Exercising through significant pain or numbness',
          'Walking or running on hard surfaces exclusively',
          'Ignoring proper warm-up and stretching'
        ],
        scientificRationale: 'Gradual activity progression allows tissues to adapt and prevents overuse injuries. Softer surfaces reduce impact forces that can worsen nerve compression.',
        implementationTips: [
          'Follow the 10% rule - increase activity by no more than 10% per week',
          'Keep an activity log to track symptoms',
          'Choose grass, tracks, or treadmills over concrete',
          'Consider activity modification rather than complete rest'
        ]
      },
      {
        id: 'home-environment',
        title: 'Home Environment Modifications',
        description: 'Creating a foot-friendly home environment',
        importance: 'Medium',
        recommendations: [
          'Use supportive slippers or shoes at home',
          'Place cushioned mats in areas where you stand frequently',
          'Ensure good lighting to prevent falls and awkward steps',
          'Keep frequently used items at accessible heights',
          'Use proper step stools rather than tiptoeing'
        ],
        avoidActions: [
          'Walking barefoot on hard floors for extended periods',
          'Using unsupportive flip-flops or slippers',
          'Standing on hard surfaces while cooking or cleaning',
          'Rushing or taking awkward steps'
        ],
        scientificRationale: 'A supportive home environment reduces cumulative stress on feet throughout the day and prevents sudden movements that could worsen symptoms.',
        implementationTips: [
          'Place mats in kitchen and bathroom areas',
          'Choose supportive house shoes with arch support',
          'Organize home to minimize excessive walking',
          'Consider ergonomic tools for household tasks'
        ]
      }
    ]
  },
  {
    id: 'health-management',
    title: 'Health & Wellness',
    icon: Heart,
    description: 'Manage overall health factors that can influence tarsal tunnel syndrome',
    recommendations: [
      {
        id: 'weight-management',
        title: 'Weight Management and Nutrition',
        description: 'Maintaining healthy weight to reduce pressure on feet and nerves',
        importance: 'High',
        recommendations: [
          'Maintain a healthy BMI to reduce foot pressure',
          'Follow an anti-inflammatory diet rich in omega-3 fatty acids',
          'Stay hydrated to support nerve health and reduce swelling',
          'Include foods rich in B vitamins for nerve health',
          'Limit processed foods and excess sodium'
        ],
        avoidActions: [
          'Rapid weight gain that increases foot pressure',
          'Crash dieting or extreme dietary restrictions',
          'Consuming excessive inflammatory foods',
          'Ignoring the connection between weight and foot health'
        ],
        scientificRationale: 'Excess weight increases pressure on the feet and can worsen nerve compression. Anti-inflammatory nutrition supports nerve health and reduces systemic inflammation.',
        implementationTips: [
          'Set realistic weight loss goals (1-2 pounds per week)',
          'Focus on sustainable lifestyle changes',
          'Include fish, nuts, and leafy greens in diet',
          'Work with healthcare providers for guidance'
        ]
      },
      {
        id: 'medical-conditions',
        title: 'Managing Underlying Medical Conditions',
        description: 'Controlling medical conditions that can worsen tarsal tunnel syndrome',
        importance: 'High',
        recommendations: [
          'Maintain good blood sugar control if diabetic',
          'Manage thyroid conditions with proper medication',
          'Control inflammatory conditions like rheumatoid arthritis',
          'Address circulation problems promptly',
          'Follow up regularly with healthcare providers'
        ],
        avoidActions: [
          'Ignoring underlying medical conditions',
          'Poor compliance with prescribed medications',
          'Skipping regular medical check-ups',
          'Self-medicating without professional guidance'
        ],
        scientificRationale: 'Underlying medical conditions like diabetes and thyroid disorders can affect nerve health and increase susceptibility to compression syndromes.',
        implementationTips: [
          'Keep a symptom diary to track patterns',
          'Coordinate care between different specialists',
          'Monitor blood sugar levels if diabetic',
          'Take medications as prescribed'
        ]
      }
    ]
  }
];

const TarsalErgonomicsSection: React.FC<TarsalErgonomicsSectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();
  const [selectedCategory, setSelectedCategory] = useState<string>('footwear');

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'High': return 'bg-muted-light text-foreground border border-border/30';
      case 'Medium': return 'bg-info-light text-info border border-info/30';
      case 'Low': return 'bg-success-light text-success border border-success/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <section className={cn(
      "section-background-alt border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Badge variant="info" className="mb-6">
            <Shield className="w-4 h-4 mr-2" />
            Ergonomics & Lifestyle
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            Ergonomic and Lifestyle Recommendations
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            Evidence-based lifestyle modifications to reduce nerve compression, support foot health, 
            and prevent symptom recurrence in tarsal tunnel syndrome
          </p>
        </div>

        {/* Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-12",
            deviceInfo.isMobile ? "grid-cols-2 h-auto" : "grid-cols-4 h-14"
          )}>
            {lifestyleCategories.map((category) => {
              const IconComponent = category.icon;
              return (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className={cn(
                    "flex items-center gap-2 font-medium",
                    deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
                  )}
                >
                  <IconComponent className={cn(
                    deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5"
                  )} />
                  <span className={deviceInfo.isMobile ? "text-center" : ""}>
                    {category.title}
                  </span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* Category Content */}
          {lifestyleCategories.map((category) => (
            <TabsContent key={category.id} value={category.id} className="space-y-8">
              {/* Category Description */}
              <Card className="medical-card">
                <CardHeader>
                  <CardTitle className="text-enhanced-heading flex items-center gap-3">
                    <category.icon className="w-5 h-5 text-primary" />
                    {category.title}
                  </CardTitle>
                  <p className="text-enhanced-body">{category.description}</p>
                </CardHeader>
              </Card>

              {/* Recommendations */}
              <div className="space-y-8">
                {category.recommendations.map((recommendation) => (
                  <Card key={recommendation.id} className="medical-card">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-enhanced-heading">{recommendation.title}</CardTitle>
                        <Badge className={getImportanceColor(recommendation.importance)}>
                          {recommendation.importance} Priority
                        </Badge>
                      </div>
                      <p className="text-enhanced-body">{recommendation.description}</p>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Recommendations and Avoid Actions */}
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
                      )}>
                        <div>
                          <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                            <CheckCircle className="w-4 h-4 text-success" />
                            Recommended Actions
                          </h4>
                          <ul className="space-y-2">
                            {recommendation.recommendations.map((item, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <CheckCircle className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm">{item}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                            <AlertTriangle className="w-4 h-4 text-foreground" />
                            Actions to Avoid
                          </h4>
                          <ul className="space-y-2">
                            {recommendation.avoidActions.map((item, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm">{item}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Scientific Rationale */}
                      <div className="bg-info/5 border border-info/20 rounded-lg p-4">
                        <h4 className="text-enhanced-subheading font-semibold mb-2 flex items-center gap-2">
                          <Target className="w-4 h-4 text-info" />
                          Scientific Rationale
                        </h4>
                        <p className="text-enhanced-body text-sm">{recommendation.scientificRationale}</p>
                      </div>

                      {/* Implementation Tips */}
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                          <Settings className="w-4 h-4 text-primary" />
                          Implementation Tips
                        </h4>
                        <ul className="space-y-2">
                          {recommendation.implementationTips.map((tip, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{tip}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Quick Reference Guide */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <TrendingUp className="w-5 h-5 text-primary" />
              Quick Reference Guide
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Footprints className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Footwear First</h4>
                <p className="text-enhanced-body text-sm">Proper shoes and orthotics are the foundation of tarsal tunnel syndrome management</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Activity className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Activity Modification</h4>
                <p className="text-enhanced-body text-sm">Gradual changes and proper pacing prevent symptom flare-ups</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-muted/50 border border-border/50 mb-4">
                  <Heart className="w-8 h-8 text-foreground mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Holistic Approach</h4>
                <p className="text-enhanced-body text-sm">Address overall health factors for comprehensive nerve care</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default TarsalErgonomicsSection;
