import { 
  <PERSON>, 
  Pill, 
  Target, 
  Scissors, 
  TrendingUp, 
  Clock, 
  Activity,
  CheckCircle,
  XCircle,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Gauge,
  <PERSON>,
  Brain,
  Zap
} from "lucide-react";
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface TreatmentOption {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
  effectiveness: number;
  timeToRelief: string;
  duration: string;
  cost: 'Low' | 'Medium' | 'High';
  invasiveness: 'Non-invasive' | 'Minimally invasive' | 'Invasive';
  pros: string[];
  cons: string[];
  scientificEvidence: string;
  successRate: string;
  recommendedFor: string[];
  contraindications: string[];
}

interface TarsalTreatmentComparisonProps {
  className?: string;
}

const treatmentOptions: TreatmentOption[] = [
  {
    id: 'conservative',
    title: 'Conservative Management',
    icon: Shield,
    description: 'Non-invasive approaches including activity modification, orthotics, and physical therapy',
    effectiveness: 75,
    timeToRelief: '2-6 weeks',
    duration: '3-6 months',
    cost: 'Low',
    invasiveness: 'Non-invasive',
    pros: [
      'No surgical risks or complications',
      'Can be started immediately',
      'Addresses underlying biomechanical issues',
      'Cost-effective first-line treatment',
      'Improves overall foot function'
    ],
    cons: [
      'Requires patient compliance and lifestyle changes',
      'May take longer to see significant improvement',
      'Not effective for severe structural compression',
      'Symptoms may recur if underlying causes persist'
    ],
    scientificEvidence: 'Level II evidence shows 70-80% success rate with comprehensive conservative management',
    successRate: '70-80% improvement',
    recommendedFor: [
      'Mild to moderate symptoms',
      'Recent onset (< 6 months)',
      'Patients with biomechanical issues',
      'Those preferring non-surgical options'
    ],
    contraindications: [
      'Severe muscle weakness or atrophy',
      'Complete sensory loss',
      'Structural lesions requiring surgery'
    ]
  },
  {
    id: 'medication',
    title: 'Medication Therapy',
    icon: Pill,
    description: 'Pharmacological management using neuropathic pain medications and anti-inflammatory drugs',
    effectiveness: 65,
    timeToRelief: '1-4 weeks',
    duration: '3-6 months',
    cost: 'Medium',
    invasiveness: 'Non-invasive',
    pros: [
      'Rapid symptom relief for neuropathic pain',
      'Can be combined with other treatments',
      'Adjustable dosing based on response',
      'Good for managing chronic pain',
      'Multiple medication options available'
    ],
    cons: [
      'Potential side effects and drug interactions',
      'May not address underlying compression',
      'Tolerance can develop over time',
      'Temporary solution requiring ongoing use',
      'Some medications require monitoring'
    ],
    scientificEvidence: 'Moderate evidence for gabapentin and pregabalin in peripheral neuropathic pain',
    successRate: '60-70% pain reduction',
    recommendedFor: [
      'Patients with significant neuropathic pain',
      'Those unable to undergo procedures',
      'Adjunct to other treatments',
      'Chronic pain management'
    ],
    contraindications: [
      'Allergy to specific medications',
      'Significant kidney or liver disease',
      'Drug interactions with current medications'
    ]
  },
  {
    id: 'injection',
    title: 'Injection Therapy',
    icon: Target,
    description: 'Targeted corticosteroid injections to reduce inflammation and provide diagnostic confirmation',
    effectiveness: 80,
    timeToRelief: 'Days to weeks',
    duration: '3-12 months',
    cost: 'Medium',
    invasiveness: 'Minimally invasive',
    pros: [
      'High success rate for symptom relief',
      'Diagnostic and therapeutic value',
      'Minimal downtime required',
      'Can be repeated if beneficial',
      'Precise targeting with imaging guidance'
    ],
    cons: [
      'Temporary relief in many cases',
      'Risk of injection-related complications',
      'May cause temporary weakness',
      'Limited number of injections recommended',
      'Doesn\'t address structural causes'
    ],
    scientificEvidence: 'Strong evidence for short-term relief, variable long-term outcomes',
    successRate: '75-85% initial improvement',
    recommendedFor: [
      'Diagnostic confirmation needed',
      'Moderate to severe symptoms',
      'Failed conservative treatment',
      'Surgical candidates for trial'
    ],
    contraindications: [
      'Active infection at injection site',
      'Bleeding disorders',
      'Allergy to corticosteroids or local anesthetics'
    ]
  },
  {
    id: 'surgery',
    title: 'Surgical Decompression',
    icon: Scissors,
    description: 'Surgical release of the flexor retinaculum to decompress the posterior tibial nerve',
    effectiveness: 70,
    timeToRelief: '6-12 weeks',
    duration: 'Permanent (if successful)',
    cost: 'High',
    invasiveness: 'Invasive',
    pros: [
      'Addresses structural compression directly',
      'Potential for permanent symptom relief',
      'Good outcomes in properly selected patients',
      'Can be performed with local or regional anesthesia',
      'Endoscopic options available'
    ],
    cons: [
      'Surgical risks and potential complications',
      'Variable success rates (60-85%)',
      'Recovery time and rehabilitation required',
      'Possible incomplete symptom relief',
      'Risk of scar tissue formation'
    ],
    scientificEvidence: 'Mixed evidence with success rates varying from 60-85% depending on patient selection',
    successRate: '60-85% good to excellent results',
    recommendedFor: [
      'Failed conservative treatment (6+ months)',
      'Clear structural compression on imaging',
      'Positive response to diagnostic injection',
      'Significant functional impairment'
    ],
    contraindications: [
      'Poor surgical candidates',
      'Active infection',
      'Severe peripheral vascular disease',
      'Unrealistic patient expectations'
    ]
  }
];

const TarsalTreatmentComparison: React.FC<TarsalTreatmentComparisonProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();
  const [selectedTreatment, setSelectedTreatment] = useState<string>('conservative');

  const getCostColor = (cost: string) => {
    switch (cost) {
      case 'Low': return 'bg-success-light text-success border border-success/30';
      case 'Medium': return 'bg-info-light text-info border border-info/30';
      case 'High': return 'bg-muted-light text-foreground border border-border/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getInvasivenessColor = (invasiveness: string) => {
    switch (invasiveness) {
      case 'Non-invasive': return 'bg-success-light text-success border border-success/30';
      case 'Minimally invasive': return 'bg-info-light text-info border border-info/30';
      case 'Invasive': return 'bg-muted-light text-foreground border border-border/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <section className={cn(
      "section-background border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-centre mb-20">
          <Badge variant="info" className="mb-6">
            <TrendingUp className="w-4 h-4 mr-2" />
            Treatment Comparison
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            Comprehensive Treatment Analysis
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            Evidence-based comparison of all treatment options for tarsal tunnel syndrome, 
            helping you understand the pros, cons, and scientific evidence for each approach
          </p>
        </div>

        {/* Treatment Overview Cards */}
        <div className={cn(
          "grid gap-6 mb-12",
          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2 lg:grid-cols-4"
        )}>
          {treatmentOptions.map((treatment) => {
            const IconComponent = treatment.icon;
            return (
              <Card 
                key={treatment.id} 
                className={cn(
                  "medical-card cursor-pointer transition-all duration-300 hover:shadow-lg",
                  selectedTreatment === treatment.id ? "ring-2 ring-primary" : ""
                )}
                onClick={() => setSelectedTreatment(treatment.id)}
              >
                <CardHeader className="text-centre">
                  <div className="p-4 rounded-xl bg-primary/10 border border-primary/20 mb-4 mx-auto w-fit">
                    <IconComponent className="w-8 h-8 text-primary" />
                  </div>
                  <CardTitle className="text-enhanced-subheading">{treatment.title}</CardTitle>
                </CardHeader>
                <CardContent className="text-centre space-y-3">
                  <div>
                    <p className="text-enhanced-caption mb-1">Effectiveness</p>
                    <Progress value={treatment.effectiveness} className="h-2" />
                    <p className="text-enhanced-caption mt-1">{treatment.effectiveness}%</p>
                  </div>
                  <div className="flex justify-centre gap-2">
                    <Badge className={getCostColor(treatment.cost)}>
                      {treatment.cost} Cost
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Detailed Treatment Information */}
        {(() => {
          const treatment = treatmentOptions.find(t => t.id === selectedTreatment);
          return treatment ? (
            <Card className="medical-card">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-enhanced-heading flex items-centre gap-3">
                      <treatment.icon className="w-6 h-6 text-primary" />
                      {treatment.title}
                    </CardTitle>
                    <p className="text-enhanced-body mt-2">{treatment.description}</p>
                  </div>
                  <div className="flex gap-2">
                    <Badge className={getCostColor(treatment.cost)}>
                      {treatment.cost} Cost
                    </Badge>
                    <Badge className={getInvasivenessColor(treatment.invasiveness)}>
                      {treatment.invasiveness}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="overview" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="evidence">Evidence</TabsTrigger>
                    <TabsTrigger value="selection">Selection</TabsTrigger>
                    <TabsTrigger value="outcomes">Outcomes</TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="space-y-6 mt-6">
                    <div className={cn(
                      "grid gap-6",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                    )}>
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                          <CheckCircle className="w-4 h-4 text-success" />
                          Advantages
                        </h4>
                        <ul className="space-y-2">
                          {treatment.pros.map((pro, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <CheckCircle className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{pro}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                          <XCircle className="w-4 h-4 text-foreground" />
                          Disadvantages
                        </h4>
                        <ul className="space-y-2">
                          {treatment.cons.map((con, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <XCircle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{con}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    <div className={cn(
                      "grid gap-4",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                    )}>
                      <div className="bg-info/5 border border-info/20 rounded-lg p-4">
                        <Clock className="w-5 h-5 text-info mb-2" />
                        <h5 className="text-enhanced-caption font-medium">Time to Relief</h5>
                        <p className="text-enhanced-body text-sm">{treatment.timeToRelief}</p>
                      </div>
                      <div className="bg-success/5 border border-success/20 rounded-lg p-4">
                        <Activity className="w-5 h-5 text-success mb-2" />
                        <h5 className="text-enhanced-caption font-medium">Treatment Duration</h5>
                        <p className="text-enhanced-body text-sm">{treatment.duration}</p>
                      </div>
                      <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                        <Gauge className="w-5 h-5 text-primary mb-2" />
                        <h5 className="text-enhanced-caption font-medium">Success Rate</h5>
                        <p className="text-enhanced-body text-sm">{treatment.successRate}</p>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="evidence" className="space-y-6 mt-6">
                    <div className="bg-info/5 border border-info/20 rounded-lg p-6">
                      <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                        <Award className="w-5 h-5 text-info" />
                        Scientific Evidence
                      </h4>
                      <p className="text-enhanced-body">{treatment.scientificEvidence}</p>
                    </div>
                  </TabsContent>

                  <TabsContent value="selection" className="space-y-6 mt-6">
                    <div className={cn(
                      "grid gap-6",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                    )}>
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                          <Star className="w-4 h-4 text-success" />
                          Recommended For
                        </h4>
                        <ul className="space-y-2">
                          {treatment.recommendedFor.map((indication, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <Star className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{indication}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                          <AlertTriangle className="w-4 h-4 text-foreground" />
                          Contraindications
                        </h4>
                        <ul className="space-y-2">
                          {treatment.contraindications.map((contraindication, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{contraindication}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="outcomes" className="space-y-6 mt-6">
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3">Treatment Effectiveness</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between items-centre">
                            <span className="text-enhanced-body text-sm">Overall Effectiveness</span>
                            <span className="text-enhanced-body text-sm font-medium">{treatment.effectiveness}%</span>
                          </div>
                          <Progress value={treatment.effectiveness} className="h-3" />
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          ) : null;
        })()}

        {/* Treatment Decision Framework */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-centre gap-3">
              <Brain className="w-5 h-5 text-primary" />
              Treatment Decision Framework
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Shield className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Start Conservative</h4>
                <p className="text-enhanced-body text-sm">Begin with non-invasive approaches for most patients</p>
              </div>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Zap className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Progressive Approach</h4>
                <p className="text-enhanced-body text-sm">Advance to more invasive options if conservative fails</p>
              </div>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-muted/50 border border-border/50 mb-4">
                  <Heart className="w-8 h-8 text-foreground mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Individual Factors</h4>
                <p className="text-enhanced-body text-sm">Consider patient preferences, comorbidities, and goals</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default TarsalTreatmentComparison;
