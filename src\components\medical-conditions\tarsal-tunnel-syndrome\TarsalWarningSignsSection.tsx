import { 
  Alert<PERSON>riangle, 
  Phone, 
  Clock, 
  Shield, 
  Activity,
  Target,
  Eye,
  Heart
} from "lucide-react";
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface WarningSign {
  id: string;
  sign: string;
  description: string;
  urgency: 'immediate' | 'urgent' | 'concerning';
  action: string;
  timeframe: string;
}

interface ProgressionStage {
  stage: string;
  title: string;
  description: string;
  symptoms: string[];
  reversibility: 'reversible' | 'partially-reversible' | 'permanent';
  timeframe: string;
}

interface TarsalWarningSignsSectionProps {
  className?: string;
}

const warningSignsData = {
  title: "Warning Signs and When to Seek Help",
  subtitle: "Recognize concerning symptoms and understand when immediate medical attention is needed for tarsal tunnel syndrome",
  
  warningSignsByUrgency: {
    immediate: [
      {
        id: 'severe-weakness',
        sign: 'Sudden Severe Foot Weakness',
        description: 'New onset of significant weakness in foot muscles or difficulty walking',
        urgency: 'immediate' as const,
        action: 'Seek emergency medical care immediately',
        timeframe: 'Within hours'
      },
      {
        id: 'complete-numbness',
        sign: 'Complete Loss of Sensation',
        description: 'Total loss of feeling in the sole of the foot or inability to feel touch',
        urgency: 'immediate' as const,
        action: 'Emergency department evaluation',
        timeframe: 'Within hours'
      },
      {
        id: 'infection-signs',
        sign: 'Signs of Infection',
        description: 'Fever, redness, warmth, or drainage around the ankle or foot',
        urgency: 'immediate' as const,
        action: 'Call emergency services or go to emergency department',
        timeframe: 'Immediately'
      }
    ],
    urgent: [
      {
        id: 'progressive-weakness',
        sign: 'Progressive Muscle Weakness',
        description: 'Gradual worsening of foot or toe muscle strength over days to weeks',
        urgency: 'urgent' as const,
        action: 'Contact healthcare provider within 24-48 hours',
        timeframe: 'Within 1-2 days'
      },
      {
        id: 'severe-pain',
        sign: 'Severe, Uncontrolled Pain',
        description: 'Burning pain that interferes with sleep, work, or daily activities',
        urgency: 'urgent' as const,
        action: 'Schedule urgent medical consultation',
        timeframe: 'Within 2-3 days'
      },
      {
        id: 'spreading-symptoms',
        sign: 'Spreading Numbness or Tingling',
        description: 'Symptoms extending beyond the typical foot sole distribution',
        urgency: 'urgent' as const,
        action: 'Medical evaluation needed',
        timeframe: 'Within 1 week'
      },
      {
        id: 'balance-problems',
        sign: 'Significant Balance Issues',
        description: 'New difficulty with balance or frequent falls due to foot numbness',
        urgency: 'urgent' as const,
        action: 'Urgent medical assessment',
        timeframe: 'Within 2-3 days'
      }
    ],
    concerning: [
      {
        id: 'worsening-symptoms',
        sign: 'Worsening Despite Treatment',
        description: 'Symptoms continue to worsen after 6-8 weeks of appropriate treatment',
        urgency: 'concerning' as const,
        action: 'Review treatment plan with healthcare provider',
        timeframe: 'Within 2 weeks'
      },
      {
        id: 'functional-impact',
        sign: 'Significant Functional Impact',
        description: 'Symptoms significantly affecting work, exercise, or quality of life',
        urgency: 'concerning' as const,
        action: 'Consider specialist referral',
        timeframe: 'Within 2-4 weeks'
      },
      {
        id: 'night-symptoms',
        sign: 'Severe Night-time Symptoms',
        description: 'Burning pain or numbness that consistently disrupts sleep',
        urgency: 'concerning' as const,
        action: 'Medical evaluation recommended',
        timeframe: 'Within 2-3 weeks'
      },
      {
        id: 'bilateral-symptoms',
        sign: 'Symptoms in Both Feet',
        description: 'Development of tarsal tunnel symptoms in both feet',
        urgency: 'concerning' as const,
        action: 'Comprehensive medical evaluation',
        timeframe: 'Within 2-3 weeks'
      }
    ]
  },

  progressionStages: [
    {
      stage: 'Early',
      title: 'Early Stage (0-3 months)',
      description: 'Initial nerve irritation with mild, intermittent symptoms',
      symptoms: [
        'Occasional burning or tingling in foot sole',
        'Symptoms triggered by specific activities',
        'Good response to rest and position changes',
        'Minimal impact on daily activities'
      ],
      reversibility: 'reversible' as const,
      timeframe: '0-3 months'
    },
    {
      stage: 'Moderate',
      title: 'Moderate Stage (3-12 months)',
      description: 'Established nerve compression with persistent symptoms',
      symptoms: [
        'Constant burning pain in foot sole',
        'Numbness affecting daily activities',
        'Night-time symptoms disrupting sleep',
        'Some weakness in toe movements'
      ],
      reversibility: 'partially-reversible' as const,
      timeframe: '3-12 months'
    },
    {
      stage: 'Advanced',
      title: 'Advanced Stage (>12 months)',
      description: 'Chronic nerve damage with potential permanent changes',
      symptoms: [
        'Severe, constant burning pain',
        'Significant sensory loss in foot',
        'Muscle weakness and atrophy',
        'Major functional impairment'
      ],
      reversibility: 'permanent' as const,
      timeframe: '>12 months'
    }
  ],

  whenToSeekHelp: {
    immediate: [
      'Sudden onset of severe foot weakness or paralysis',
      'Complete loss of sensation in the foot',
      'Signs of infection (fever, redness, drainage)',
      'Severe pain with signs of circulation problems'
    ],
    urgent: [
      'Progressive worsening of symptoms over days',
      'Severe pain interfering with sleep or daily activities',
      'New muscle weakness or difficulty with toe movements',
      'Symptoms spreading beyond typical distribution'
    ],
    routine: [
      'Mild symptoms persisting beyond 6-8 weeks',
      'Symptoms affecting quality of life or work performance',
      'Need for guidance on treatment options',
      'Questions about diagnosis or prognosis'
    ]
  }
};

const TarsalWarningSignsSection: React.FC<TarsalWarningSignsSectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "section-background border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Badge variant="emergency" className="mb-6">
            <AlertTriangle className="w-4 h-4 mr-2" />
            Warning Signs
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            {warningSignsData.title}
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            {warningSignsData.subtitle}
          </p>
        </div>

        {/* Emergency Contact Card */}
        <Card className="medical-card border-l-4 border-l-primary mb-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Phone className="w-5 h-5 text-primary" />
              Emergency Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <Phone className="w-8 h-8 text-foreground mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Emergency</h4>
                <p className="text-enhanced-body font-bold">000</p>
                <p className="text-enhanced-caption">Severe symptoms</p>
              </div>
              <div className="text-center">
                <Clock className="w-8 h-8 text-info mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Urgent Care</h4>
                <p className="text-enhanced-body font-bold">(02) 9999 0000</p>
                <p className="text-enhanced-caption">Same-day appointment</p>
              </div>
              <div className="text-center">
                <Shield className="w-8 h-8 text-info mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Clinic</h4>
                <p className="text-enhanced-body font-bold">(02) 8888 0000</p>
                <p className="text-enhanced-caption">Regular consultation</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Warning Signs by Urgency */}
        <div className="mb-16">
          <h3 className={cn(
            "font-bold text-foreground mb-8 text-center",
            deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
          )}>
            Warning Signs by Urgency Level
          </h3>
          
          <div className="space-y-8">
            {Object.entries(warningSignsData.warningSignsByUrgency).map(([urgency, signs]) => (
              <Card key={urgency} className="medical-card">
                <CardHeader>
                  <CardTitle className="text-enhanced-heading flex items-center gap-3">
                    <AlertTriangle className="w-5 h-5 text-foreground" />
                    {urgency.charAt(0).toUpperCase() + urgency.slice(1)} Attention Required
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {signs.map((sign) => (
                      <div key={sign.id} className="border border-border/50 rounded-lg p-4">
                        <div className="flex items-start justify-between mb-3">
                          <h4 className="text-enhanced-subheading font-semibold">{sign.sign}</h4>
                          <Badge variant={sign.urgency === 'immediate' ? 'emergency' : sign.urgency === 'urgent' ? 'urgent' : 'routine'}>
                            {sign.urgency}
                          </Badge>
                        </div>
                        <p className="text-enhanced-body mb-3">{sign.description}</p>
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <span className="text-enhanced-caption font-medium">Action Required: </span>
                            <span className="text-enhanced-body text-sm">{sign.action}</span>
                          </div>
                          <div>
                            <span className="text-enhanced-caption font-medium">Timeframe: </span>
                            <span className="text-enhanced-body text-sm">{sign.timeframe}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Disease Progression Stages */}
        <div className="mb-16">
          <h3 className={cn(
            "font-bold text-foreground mb-8 text-center",
            deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
          )}>
            Disease Progression Stages
          </h3>
          
          <div className={cn(
            "grid gap-6",
            deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
          )}>
            {warningSignsData.progressionStages.map((stage, index) => (
              <Card key={stage.stage} className="medical-card h-full">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-enhanced-heading">{stage.title}</CardTitle>
                    <Badge variant={stage.reversibility === 'reversible' ? 'routine' : stage.reversibility === 'partially-reversible' ? 'urgent' : 'emergency'}>
                      {stage.reversibility.replace('-', ' ')}
                    </Badge>
                  </div>
                  <p className="text-enhanced-body text-sm">{stage.description}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="text-enhanced-caption font-medium mb-2">Typical Symptoms:</h4>
                    <ul className="space-y-1">
                      {stage.symptoms.map((symptom, symptomIndex) => (
                        <li key={symptomIndex} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                          <span className="text-enhanced-body text-sm">{symptom}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="pt-2 border-t border-border/50">
                    <span className="text-enhanced-caption font-medium">Duration: </span>
                    <span className="text-enhanced-body text-sm">{stage.timeframe}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* When to Seek Help */}
        <div className="mb-12">
          <h3 className={cn(
            "font-bold text-foreground mb-8 text-center",
            deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
          )}>
            When to Seek Medical Help
          </h3>
          
          <div className={cn(
            "grid gap-8",
            deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
          )}>
            <Card className="medical-card border-l-4 border-l-muted">
              <CardHeader>
                <CardTitle className="text-enhanced-heading flex items-center gap-3">
                  <Phone className="w-5 h-5 text-foreground" />
                  Immediate Care
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {warningSignsData.whenToSeekHelp.immediate.map((item, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                      <span className="text-enhanced-body text-sm">{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            <Card className="medical-card border-l-4 border-l-info">
              <CardHeader>
                <CardTitle className="text-enhanced-heading flex items-center gap-3">
                  <Clock className="w-5 h-5 text-info" />
                  Urgent Care
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {warningSignsData.whenToSeekHelp.urgent.map((item, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Clock className="w-4 h-4 text-info mt-0.5 flex-shrink-0" />
                      <span className="text-enhanced-body text-sm">{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Key Takeaways */}
        <Card className="medical-card">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Target className="w-5 h-5 text-primary" />
              Key Takeaways
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Eye className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Early Recognition</h4>
                <p className="text-enhanced-body text-sm">Early identification and treatment lead to better outcomes and prevent progression</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Activity className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Prompt Action</h4>
                <p className="text-enhanced-body text-sm">Don't ignore worsening symptoms - seek appropriate medical care when indicated</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-muted/50 border border-border/50 mb-4">
                  <Heart className="w-8 h-8 text-foreground mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Trust Your Instincts</h4>
                <p className="text-enhanced-body text-sm">If something doesn't feel right, it's always better to seek medical advice</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default TarsalWarningSignsSection;
