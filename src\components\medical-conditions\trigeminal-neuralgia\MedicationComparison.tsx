import { Pill, TrendingUp, Al<PERSON>Triangle, Target, Activity, Eye } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface Medication {
  medication: string;
  mechanism: string;
  dosage: string;
  efficacy: string;
  sideEffects: string[];
  monitoring: string[];
}

interface MedicationComparisonProps {
  title: string;
  description: string;
  firstLine: Medication[];
  secondLine: Medication[];
}

export function MedicationComparison({ 
  title, 
  description, 
  firstLine, 
  secondLine 
}: MedicationComparisonProps) {
  const deviceInfo = useDeviceDetection();
  const [selectedMedication, setSelectedMedication] = useState<string>(firstLine[0]?.medication || '');

  const allMedications = [...firstLine, ...secondLine];
  const selectedMed = allMedications.find(med => med.medication === selectedMedication);

  const getMedicationCategory = (medication: string) => {
    return firstLine.some(med => med.medication === medication) ? 'First-line' : 'Second-line';
  };

  const getCategoryColor = (medication: string) => {
    return firstLine.some(med => med.medication === medication)
      ? 'badge-routine'
      : 'badge-info';
  };

  return (
    <section className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-centre mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        <Tabs value={selectedMedication} onValueChange={setSelectedMedication} className="w-full">
          {/* Medication Selection Tabs */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-4">First-line Medications</h3>
            <TabsList className={cn(
              "grid w-full mb-6",
              deviceInfo.isMobile ? "grid-cols-1 h-auto" : `grid-cols-${Math.min(firstLine.length, 3)}`
            )}>
              {firstLine.map((med) => (
                <TabsTrigger 
                  key={med.medication} 
                  value={med.medication}
                  className={cn(
                    "text-centre",
                    deviceInfo.isMobile ? "py-3 text-sm" : "py-4"
                  )}
                >
                  {med.medication}
                </TabsTrigger>
              ))}
            </TabsList>

            <h3 className="text-lg font-semibold mb-4">Second-line Medications</h3>
            <TabsList className={cn(
              "grid w-full",
              deviceInfo.isMobile ? "grid-cols-1 h-auto" : `grid-cols-${Math.min(secondLine.length, 3)}`
            )}>
              {secondLine.map((med) => (
                <TabsTrigger 
                  key={med.medication} 
                  value={med.medication}
                  className={cn(
                    "text-centre",
                    deviceInfo.isMobile ? "py-3 text-sm" : "py-4"
                  )}
                >
                  {med.medication}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          {/* Medication Details */}
          {allMedications.map((medication) => (
            <TabsContent key={medication.medication} value={medication.medication} className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-centre justify-between">
                    <CardTitle className="flex items-centre gap-2">
                      <Pill className="h-5 w-5 text-primary" />
                      {medication.medication}
                    </CardTitle>
                    <Badge className={getCategoryColor(medication.medication)}>
                      {getMedicationCategory(medication.medication)}
                    </Badge>
                  </div>
                  <CardDescription>{medication.mechanism}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Key Information */}
                  <div className={cn(
                    "grid gap-4",
                    deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                  )}>
                    <div className="flex items-centre gap-2">
                      <Target className="h-4 w-4 text-success" />
                      <span className="text-enhanced-strong font-medium">Dosage:</span>
                      <span className="text-enhanced-muted text-sm">{medication.dosage}</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <TrendingUp className="h-4 w-4 text-info" />
                      <span className="text-enhanced-strong font-medium">Efficacy:</span>
                      <Badge variant="secondary">{medication.efficacy}</Badge>
                    </div>
                  </div>

                  {/* Side Effects */}
                  <div>
                    <h4 className="font-semibold mb-3 flex items-centre gap-2">
                      <AlertTriangle className="h-4 w-4 text-info" />
                      Common Side Effects
                    </h4>
                    <div className={cn(
                      "grid gap-2",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                    )}>
                      {medication.sideEffects.map((effect, index) => (
                        <div key={index} className="flex items-centre gap-2">
                          <div className="w-1.5 h-1.5 bg-info rounded-full flex-shrink-0" />
                          <span className="text-sm">{effect}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Monitoring Requirements */}
                  <div>
                    <h4 className="font-semibold mb-3 flex items-centre gap-2">
                      <Eye className="h-4 w-4 text-medical-blue" />
                      Monitoring Requirements
                    </h4>
                    <div className={cn(
                      "grid gap-2",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                    )}>
                      {medication.monitoring.map((requirement, index) => (
                        <div key={index} className="flex items-centre gap-2">
                          <div className="w-1.5 h-1.5 bg-medical-blue rounded-full flex-shrink-0" />
                          <span className="text-sm">{requirement}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Clinical Notes */}
                  <div className="bg-info-light border border-info/30 rounded-lg p-4">
                    <h4 className="text-enhanced-heading font-semibold text-info mb-2">Clinical Considerations</h4>
                    <p className="text-enhanced-body text-sm text-info">
                      {medication.medication === 'Carbamazepine' && 
                        "Gold standard first-line treatment. Start low and titrate slowly. Monitor for serious skin reactions, especially in patients of Asian descent who should be tested for HLA-B*1502 allele."
                      }
                      {medication.medication === 'Oxcarbazepine' && 
                        "Alternative to carbamazepine with fewer drug interactions. Monitor sodium levels closely, especially in elderly patients. May be better tolerated than carbamazepine."
                      }
                      {medication.medication === 'Gabapentin' && 
                        "Useful as add-on therapy or when anticonvulsants are contraindicated. Dose adjustment required in renal impairment. Generally well-tolerated but may cause sedation."
                      }
                      {medication.medication === 'Pregabalin' && 
                        "Similar efficacy to gabapentin but with twice-daily dosing. Monitor for weight gain and peripheral oedema. May be more effective than gabapentin in some patients."
                      }
                      {medication.medication === 'Baclofen' && 
                        "Often used in combination with other medications. Particularly useful in multiple sclerosis-related trigeminal neuralgia. Must be tapered gradually to avoid withdrawal."
                      }
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>

        {/* Treatment Algorithm */}
        <div className="mt-12">
          <Card className="bg-muted">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2">
                <Activity className="h-5 w-5 text-primary" />
                Treatment Algorithm
              </CardTitle>
              <CardDescription>
                Stepwise approach to medication management in trigeminal neuralgia
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-4",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-success text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-2">
                    1
                  </div>
                  <h4 className="text-enhanced-heading font-semibold mb-1">First-line</h4>
                  <p className="text-enhanced-muted text-sm">
                    Start with carbamazepine or oxcarbazepine monotherapy
                  </p>
                </div>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-2">
                    2
                  </div>
                  <h4 className="text-enhanced-heading font-semibold mb-1">Combination</h4>
                  <p className="text-enhanced-muted text-sm">
                    Add gabapentin, pregabalin, or baclofen if inadequate response
                  </p>
                </div>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-medical-blue text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-2">
                    3
                  </div>
                  <h4 className="text-enhanced-heading font-semibold mb-1">Referral</h4>
                  <p className="text-enhanced-muted text-sm">
                    Consider surgical options if medications fail or cause intolerable side effects
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="text-centre mt-12">
          <Card className="bg-primary/5 border-primary/20">
            <CardContent className="pt-6">
              <h3 className="font-semibold mb-2">Medication Management Support</h3>
              <p className="text-muted-foreground mb-4">
                Our neurology team provides comprehensive medication management and monitoring 
                to optimise your trigeminal neuralgia treatment.
              </p>
              <Button size={deviceInfo.isMobile ? "default" : "lg"}>
                Schedule Medication Review
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default MedicationComparison;
