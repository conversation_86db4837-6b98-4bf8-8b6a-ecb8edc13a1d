import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Target, ArrowRight, ArrowLeft, Activity } from 'lucide-react';
import React, { useState, useId } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface AssessmentQuestion {
  id: string;
  question: string;
  description?: string;
  options: Array<{
    value: string;
    label: string;
    score: number;
  }>;
}

interface AssessmentResult {
  totalScore: number;
  likelihood: 'low' | 'moderate' | 'high';
  recommendation: string;
  urgency: 'routine' | 'urgent' | 'emergency';
  nextSteps: string[];
}

const assessmentQuestions: AssessmentQuestion[] = [
  {
    id: 'pain-character',
    question: 'How would you describe your facial pain?',
    description: 'The character of pain is crucial for trigeminal neuralgia diagnosis',
    options: [
      { value: 'electric', label: 'Electric shock-like, stabbing pain', score: 3 },
      { value: 'burning', label: 'Burning or aching pain', score: 1 },
      { value: 'throbbing', label: 'Throbbing or pulsating pain', score: 0 },
      { value: 'pressure', label: 'Pressure or squeezing sensation', score: 0 }
    ]
  },
  {
    id: 'pain-duration',
    question: 'How long do individual pain episodes last?',
    description: 'Duration helps distinguish trigeminal neuralgia from other conditions',
    options: [
      { value: 'seconds', label: 'Seconds to minutes', score: 3 },
      { value: 'minutes-hours', label: 'Minutes to hours', score: 1 },
      { value: 'hours-days', label: 'Hours to days', score: 0 },
      { value: 'constant', label: 'Constant pain', score: 0 }
    ]
  },
  {
    id: 'pain-location',
    question: 'Where is your pain located?',
    description: 'Pain distribution follows trigeminal nerve anatomy',
    options: [
      { value: 'unilateral', label: 'One side of face only', score: 3 },
      { value: 'bilateral', label: 'Both sides of face', score: 1 },
      { value: 'jaw-only', label: 'Jaw area only', score: 2 },
      { value: 'forehead-only', label: 'Forehead/eye area only', score: 2 }
    ]
  },
  {
    id: 'triggers',
    question: 'Is your pain triggered by light touch or activities?',
    description: 'Trigger zones are characteristic of trigeminal neuralgia',
    options: [
      { value: 'light-touch', label: 'Yes, light touch triggers severe pain', score: 3 },
      { value: 'activities', label: 'Yes, eating/talking triggers pain', score: 2 },
      { value: 'sometimes', label: 'Sometimes triggered by activities', score: 1 },
      { value: 'no-triggers', label: 'No specific triggers', score: 0 }
    ]
  },
  {
    id: 'pain-free-intervals',
    question: 'Between pain episodes, how do you feel?',
    description: 'Pain-free intervals are typical of classical trigeminal neuralgia',
    options: [
      { value: 'completely-free', label: 'Completely pain-free', score: 3 },
      { value: 'mild-discomfort', label: 'Mild background discomfort', score: 1 },
      { value: 'constant-pain', label: 'Constant background pain', score: 0 },
      { value: 'variable', label: 'Variable - sometimes free, sometimes not', score: 2 }
    ]
  },
  {
    id: 'medication-response',
    question: 'Have you tried any medications for the pain?',
    description: 'Response to specific medications can help with diagnosis',
    options: [
      { value: 'anticonvulsants-helped', label: 'Anticonvulsants (carbamazepine) helped', score: 3 },
      { value: 'painkillers-helped', label: 'Regular painkillers helped', score: 1 },
      { value: 'nothing-helped', label: 'Nothing has helped', score: 1 },
      { value: 'not-tried', label: 'Haven\'t tried medications yet', score: 0 }
    ]
  }
];

export function PainAssessmentTool() {
  const deviceInfo = useDeviceDetection();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showResults, setShowResults] = useState(false);
  const assessmentId = useId();

  const progress = ((currentQuestion + 1) / assessmentQuestions.length) * 100;
  const isLastQuestion = currentQuestion === assessmentQuestions.length - 1;
  const canProceed = answers[assessmentQuestions[currentQuestion]?.id];

  const handleAnswer = (value: string) => {
    setAnswers(prev => ({
      ...prev,
      [assessmentQuestions[currentQuestion].id]: value
    }));
  };

  const handleNext = () => {
    if (isLastQuestion) {
      setShowResults(true);
    } else {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const calculateResults = (): AssessmentResult => {
    const totalScore = Object.entries(answers).reduce((total, [questionId, answer]) => {
      const question = assessmentQuestions.find(q => q.id === questionId);
      const option = question?.options.find(o => o.value === answer);
      return total + (option?.score || 0);
    }, 0);

    let likelihood: 'low' | 'moderate' | 'high';
    let recommendation: string;
    let urgency: 'routine' | 'urgent' | 'emergency';
    let nextSteps: string[];

    if (totalScore >= 15) {
      likelihood = 'high';
      urgency = 'urgent';
      recommendation = 'Your symptoms are highly suggestive of trigeminal neuralgia. You should seek urgent medical evaluation from a neurologist or neurosurgeon.';
      nextSteps = [
        'Contact a neurologist or neurosurgeon immediately',
        'Request urgent appointment for evaluation',
        'Keep a pain diary until your appointment',
        'Avoid known triggers if identified'
      ];
    } else if (totalScore >= 10) {
      likelihood = 'moderate';
      urgency = 'urgent';
      recommendation = 'Your symptoms suggest possible trigeminal neuralgia or another facial pain condition. Medical evaluation is recommended soon.';
      nextSteps = [
        'Schedule appointment with neurologist within 1-2 weeks',
        'Document pain patterns and triggers',
        'Consider seeing GP for initial assessment',
        'Discuss medication options with doctor'
      ];
    } else if (totalScore >= 5) {
      likelihood = 'low';
      urgency = 'routine';
      recommendation = 'Your symptoms may suggest a facial pain condition, but trigeminal neuralgia is less likely. Medical evaluation is still recommended.';
      nextSteps = [
        'Schedule routine appointment with GP',
        'Monitor symptoms and document patterns',
        'Consider dental evaluation if jaw pain',
        'Discuss symptoms with healthcare provider'
      ];
    } else {
      likelihood = 'low';
      urgency = 'routine';
      recommendation = 'Your symptoms are less characteristic of trigeminal neuralgia, but other conditions should be considered.';
      nextSteps = [
        'Discuss symptoms with your GP',
        'Consider other causes of facial pain',
        'Monitor for any changes in symptoms',
        'Seek medical advice if symptoms worsen'
      ];
    }

    return { totalScore, likelihood, recommendation, urgency, nextSteps };
  };

  const results = showResults ? calculateResults() : null;

  const getLikelihoodColor = (likelihood: string) => {
    switch (likelihood) {
      case 'high': return 'bg-muted-light text-foreground border-border/70';
      case 'moderate': return 'bg-info-light text-info border-info/30';
      default: return 'bg-success-light text-success border-success/30';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'emergency': return <AlertTriangle className="h-5 w-5 text-foreground" />;
      case 'urgent': return <Target className="h-5 w-5 text-info" />;
      default: return <CheckCircle className="h-5 w-5 text-success" />;
    }
  };

  if (showResults && results) {
    return (
      <section className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}>
        <div className="container max-w-4xl">
          <Card className={cn("border-2", getLikelihoodColor(results.likelihood))}>
            <CardHeader className="text-center">
              <div className="flex items-center justify-center gap-2 mb-4">
                {getUrgencyIcon(results.urgency)}
                <CardTitle className="text-2xl">Assessment Results</CardTitle>
              </div>
              <CardDescription>
                Based on your responses, here's your personalised assessment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Score and Likelihood */}
              <div className="text-center p-6 bg-background/50 rounded-lg">
                <div className="flex items-center justify-center gap-4 mb-4">
                  <div>
                    <div className="text-enhanced-heading text-3xl font-bold text-primary">{results.totalScore}</div>
                    <div className="text-sm text-muted-foreground">Total Score</div>
                  </div>
                  <div>
                    <Badge className={getLikelihoodColor(results.likelihood)}>
                      {results.likelihood.toUpperCase()} likelihood
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Recommendation */}
              <div className="bg-background rounded-lg p-6 border">
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <Activity className="h-5 w-5 text-primary" />
                  Recommendation
                </h3>
                <p className="text-sm">{results.recommendation}</p>
              </div>

              {/* Next Steps */}
              <div className="bg-background rounded-lg p-6 border">
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <ArrowRight className="h-5 w-5 text-primary" />
                  Recommended Next Steps
                </h3>
                <ul className="space-y-2">
                  {results.nextSteps.map((step, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold mt-0.5 flex-shrink-0">
                        {index + 1}
                      </div>
                      <span className="text-sm">{step}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Important Disclaimer */}
              <div className="bg-info border border-info rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-info mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-info mb-1">Important Disclaimer</h4>
                    <p className="text-sm text-info">
                      This assessment tool is for educational purposes only and does not replace professional medical diagnosis. 
                      Always consult with qualified healthcare professionals for proper evaluation and treatment.
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className={cn("flex gap-3", deviceInfo.isMobile ? "flex-col" : "flex-row justify-center")}>
                <Button size="lg">
                  <Target className="mr-2 h-4 w-4" />
                  Find a Specialist
                </Button>
                <Button variant="outline" size="lg" onClick={() => {
                  setShowResults(false);
                  setCurrentQuestion(0);
                  setAnswers({});
                }}>
                  Retake Assessment
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    );
  }

  return (
    <section 
      className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}
      aria-labelledby={`${assessmentId}-title`}
    >
      <div className="container max-w-3xl">
        <div className="text-center mb-8">
          <h2 
            id={`${assessmentId}-title`}
            className={cn("font-bold mb-4", deviceInfo.isMobile ? "text-2xl" : "text-3xl")}
          >
            Trigeminal Neuralgia Pain Assessment
          </h2>
          <p className={cn("text-muted-foreground", deviceInfo.isMobile ? "text-sm" : "text-lg")}>
            Answer these questions to assess the likelihood of trigeminal neuralgia and receive personalised guidance
          </p>
        </div>

        {/* Progress */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Progress</span>
            <span className="text-sm text-muted-foreground">
              {currentQuestion + 1} of {assessmentQuestions.length}
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Question Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-primary" />
              Question {currentQuestion + 1}
            </CardTitle>
            <CardDescription className="text-lg font-medium">
              {assessmentQuestions[currentQuestion]?.question}
            </CardDescription>
            {assessmentQuestions[currentQuestion]?.description && (
              <p className="text-sm text-muted-foreground">
                {assessmentQuestions[currentQuestion].description}
              </p>
            )}
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={answers[assessmentQuestions[currentQuestion]?.id] || ''}
              onValueChange={handleAnswer}
              className="space-y-3"
            >
              {assessmentQuestions[currentQuestion]?.options.map((option) => (
                <div key={option.value} className="flex items-start space-x-3 p-3 rounded-lg border hover:bg-muted/50">
                  <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
                  <Label htmlFor={option.value} className="font-medium cursor-pointer flex-1">
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className={cn("flex justify-between mt-8", deviceInfo.isMobile ? "flex-col gap-3" : "")}>
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentQuestion === 0}
            className={deviceInfo.isMobile ? "order-2" : ""}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button
            onClick={handleNext}
            disabled={!canProceed}
            className={deviceInfo.isMobile ? "order-1" : ""}
          >
            {isLastQuestion ? 'Get Results' : 'Next'}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </section>
  );
}

export default PainAssessmentTool;
