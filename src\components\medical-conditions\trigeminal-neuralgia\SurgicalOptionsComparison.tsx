import { Sciss<PERSON>, Target, TrendingUp, Clock, AlertTriangle, CheckCircle, Activity } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface SurgicalProcedure {
  name: string;
  description: string;
  technique: string;
  indications: string[];
  advantages: string[];
  risks: string[];
  successRate: string;
  recovery: string;
}

interface SurgicalOptionsComparisonProps {
  title: string;
  description: string;
  procedures: SurgicalProcedure[];
}

export function SurgicalOptionsComparison({ 
  title, 
  description, 
  procedures 
}: SurgicalOptionsComparisonProps) {
  const deviceInfo = useDeviceDetection();
  const [selectedProcedure, setSelectedProcedure] = useState<string>(procedures[0]?.name || '');
  const [comparisonMode, setComparisonMode] = useState(false);

  const selectedProc = procedures.find(proc => proc.name === selectedProcedure);

  const getSuccessRateNumber = (successRate: string) => {
    const match = successRate.match(/(\d+)-?(\d+)?%/);
    if (match) {
      return match[2] ? parseInt(match[2]) : parseInt(match[1]);
    }
    return 0;
  };

  const getInvasivenessLevel = (procedureName: string) => {
    if (procedureName.includes('Microvascular')) return { level: 'High', value: 90, color: 'bg-muted' };
    if (procedureName.includes('Gamma Knife')) return { level: 'None', value: 10, color: 'bg-success' };
    if (procedureName.includes('Balloon')) return { level: 'Minimal', value: 30, color: 'bg-info' };
    return { level: 'Moderate', value: 50, color: 'bg-info' };
  };

  const getRecoveryTime = (recovery: string) => {
    if (recovery.includes('Immediate')) return { time: 'Immediate', value: 10, color: 'bg-success' };
    if (recovery.includes('Same day')) return { time: 'Same day', value: 20, color: 'bg-success' };
    if (recovery.includes('2-4 weeks')) return { time: '2-4 weeks', value: 80, color: 'bg-info' };
    return { time: 'Variable', value: 50, color: 'bg-info' };
  };

  return (
    <section className={cn("py-16", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-center mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* View Toggle */}
        <div className="flex justify-center mb-8">
          <div className="flex bg-muted rounded-lg p-1">
            <Button
              variant={!comparisonMode ? "default" : "ghost"}
              size="sm"
              onClick={() => setComparisonMode(false)}
            >
              Detailed View
            </Button>
            <Button
              variant={comparisonMode ? "default" : "ghost"}
              size="sm"
              onClick={() => setComparisonMode(true)}
            >
              Comparison View
            </Button>
          </div>
        </div>

        {!comparisonMode ? (
          /* Detailed View */
          <Tabs value={selectedProcedure} onValueChange={setSelectedProcedure} className="w-full">
            <TabsList className={cn(
              "grid w-full mb-8",
              deviceInfo.isMobile ? "grid-cols-1 h-auto" : `grid-cols-${Math.min(procedures.length, 3)}`
            )}>
              {procedures.map((procedure) => (
                <TabsTrigger 
                  key={procedure.name} 
                  value={procedure.name}
                  className={cn(
                    "text-center",
                    deviceInfo.isMobile ? "py-3 text-sm" : "py-4"
                  )}
                >
                  {procedure.name.replace('Microvascular Decompression (MVD)', 'MVD')}
                </TabsTrigger>
              ))}
            </TabsList>

            {procedures.map((procedure) => (
              <TabsContent key={procedure.name} value={procedure.name} className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Scissors className="h-5 w-5 text-primary" />
                      {procedure.name}
                    </CardTitle>
                    <CardDescription>{procedure.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Key Metrics */}
                    <div className={cn(
                      "grid gap-6",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                    )}>
                      <div>
                        <h4 className="font-semibold mb-2 flex items-center gap-2">
                          <TrendingUp className="h-4 w-4 text-success" />
                          Success Rate
                        </h4>
                        <div className="space-y-2">
                          <Progress value={getSuccessRateNumber(procedure.successRate)} className="h-2" />
                          <p className="text-sm text-muted-foreground">{procedure.successRate}</p>
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold mb-2 flex items-center gap-2">
                          <Activity className="h-4 w-4 text-info" />
                          Invasiveness
                        </h4>
                        <div className="space-y-2">
                          <Progress 
                            value={getInvasivenessLevel(procedure.name).value} 
                            className="h-2"
                          />
                          <p className="text-sm text-muted-foreground">
                            {getInvasivenessLevel(procedure.name).level}
                          </p>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-2 flex items-center gap-2">
                          <Clock className="h-4 w-4 text-medical-blue" />
                          Recovery Time
                        </h4>
                        <div className="space-y-2">
                          <Progress 
                            value={getRecoveryTime(procedure.recovery).value} 
                            className="h-2"
                          />
                          <p className="text-sm text-muted-foreground">{procedure.recovery}</p>
                        </div>
                      </div>
                    </div>

                    {/* Technique */}
                    <div>
                      <h4 className="font-semibold mb-2">Surgical Technique</h4>
                      <p className="text-sm text-muted-foreground">{procedure.technique}</p>
                    </div>

                    {/* Indications, Advantages, Risks */}
                    <div className={cn(
                      "grid gap-6",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                    )}>
                      <div>
                        <h4 className="font-semibold mb-3 flex items-center gap-2 text-info">
                          <Target className="h-4 w-4" />
                          Best For
                        </h4>
                        <ul className="space-y-2">
                          {procedure.indications.map((indication, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                              <span className="text-sm">{indication}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-3 flex items-center gap-2 text-success">
                          <CheckCircle className="h-4 w-4" />
                          Advantages
                        </h4>
                        <ul className="space-y-2">
                          {procedure.advantages.map((advantage, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                              <span className="text-sm">{advantage}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-3 flex items-center gap-2 text-foreground">
                          <AlertTriangle className="h-4 w-4" />
                          Risks
                        </h4>
                        <ul className="space-y-2">
                          {procedure.risks.map((risk, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-2 h-2 bg-muted rounded-full mt-2 flex-shrink-0" />
                              <span className="text-sm">{risk}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
        ) : (
          /* Comparison View */
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Procedure Comparison</CardTitle>
                <CardDescription>
                  Compare key metrics across all surgical options
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Procedure</th>
                        <th className="text-left p-2">Success Rate</th>
                        <th className="text-left p-2">Invasiveness</th>
                        <th className="text-left p-2">Recovery</th>
                        <th className="text-left p-2">Best For</th>
                      </tr>
                    </thead>
                    <tbody>
                      {procedures.map((procedure, index) => (
                        <tr key={index} className="border-b">
                          <td className="p-2 font-medium">
                            {procedure.name.replace('Microvascular Decompression (MVD)', 'MVD')}
                          </td>
                          <td className="p-2">
                            <Badge variant="secondary">{procedure.successRate}</Badge>
                          </td>
                          <td className="p-2">
                            <Badge className={cn(
                              "text-primary-foreground",
                              getInvasivenessLevel(procedure.name).color
                            )}>
                              {getInvasivenessLevel(procedure.name).level}
                            </Badge>
                          </td>
                          <td className="p-2">
                            <span className="text-sm">{procedure.recovery}</span>
                          </td>
                          <td className="p-2">
                            <span className="text-sm">{procedure.indications[0]}</span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            {/* Detailed Comparison Cards */}
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              {procedures.map((procedure, index) => (
                <Card key={index} className="h-full">
                  <CardHeader>
                    <CardTitle className="text-lg">
                      {procedure.name.replace('Microvascular Decompression (MVD)', 'MVD')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm font-medium">Success Rate</span>
                        <span className="text-sm">{procedure.successRate}</span>
                      </div>
                      <Progress value={getSuccessRateNumber(procedure.successRate)} className="h-2" />
                    </div>
                    
                    <div>
                      <span className="text-sm font-medium">Recovery:</span>
                      <p className="text-sm text-muted-foreground">{procedure.recovery}</p>
                    </div>
                    
                    <div>
                      <span className="text-sm font-medium">Key Advantage:</span>
                      <p className="text-sm text-muted-foreground">{procedure.advantages[0]}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Decision Support */}
        <div className="mt-12">
          <Card className="bg-primary/5 border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-primary" />
                Choosing the Right Procedure
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-4",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div>
                  <h4 className="font-semibold mb-2">Consider MVD if:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Young and healthy</li>
                    <li>• Classical trigeminal neuralgia</li>
                    <li>• Want long-term cure</li>
                    <li>• Can tolerate surgery</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Consider Gamma Knife if:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Elderly or high-risk</li>
                    <li>• Prefer non-invasive</li>
                    <li>• Previous failed surgery</li>
                    <li>• MS-related TN</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Consider Balloon Compression if:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• V3 distribution pain</li>
                    <li>• MS-related TN</li>
                    <li>• Quick procedure needed</li>
                    <li>• Previous procedures failed</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <Card className="bg-info-light border border-info/30">
            <CardContent className="pt-6">
              <h3 className="font-semibold mb-2">Expert Surgical Consultation</h3>
              <p className="text-muted-foreground mb-4">
                Our neurosurgical team specialises in trigeminal neuralgia procedures and will help 
                you choose the best surgical option based on your specific condition and circumstances.
              </p>
              <Button size={deviceInfo.isMobile ? "default" : "lg"}>
                Schedule Surgical Consultation
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default SurgicalOptionsComparison;
