import { Brain, Eye, Target, Info, ChevronDown, ChevronUp } from 'lucide-react';
import React, { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface NerveDistribution {
  branch: string;
  description: string;
  innervation: string[];
  characteristics: string[];
}

interface TrigeminalAnatomySectionProps {
  title: string;
  description: string;
  nerveDistribution: NerveDistribution[];
}

export function TrigeminalAnatomySection({ 
  title, 
  description, 
  nerveDistribution 
}: TrigeminalAnatomySectionProps) {
  const deviceInfo = useDeviceDetection();
  const [expandedBranch, setExpandedBranch] = useState<string | null>(null);

  const toggleExpanded = (branch: string) => {
    setExpandedBranch(expandedBranch === branch ? null : branch);
  };

  const getBranchIcon = (branch: string) => {
    if (branch.includes('V1') || branch.includes('Ophthalmic')) return Eye;
    if (branch.includes('V2') || branch.includes('Maxillary')) return Target;
    if (branch.includes('V3') || branch.includes('Mandibular')) return Brain;
    return Brain;
  };

  const getBranchColor = (branch: string) => {
    if (branch.includes('V1') || branch.includes('Ophthalmic')) return 'text-foreground bg-info-light border-info/30';
    if (branch.includes('V2') || branch.includes('Maxillary')) return 'text-foreground bg-success-light border-success/30';
    if (branch.includes('V3') || branch.includes('Mandibular')) return 'text-medical-blue bg-medical-blue-light border-medical-blue/30';
    return 'text-muted-foreground bg-muted border-border';
  };

  return (
    <section className={cn("py-16", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-centre mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* Anatomical Overview */}
        <div className="mb-12">
          <Card className="bg-muted">
            <CardContent className="pt-6">
              <div className={cn(
                "grid gap-8 items-centre",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h3 className="text-xl font-semibold mb-4">Trigeminal Nerve Overview</h3>
                  <p className="text-muted-foreground mb-4">
                    The trigeminal nerve (cranial nerve V) is the largest cranial nerve, responsible for 
                    sensation in the face and motor functions such as biting and chewing. It divides into 
                    three main branches, each serving different areas of the face.
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-centre gap-2">
                      <div className="w-3 h-3 bg-info rounded-full"></div>
                      <span className="text-sm">V1 - Ophthalmic Division</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <div className="w-3 h-3 bg-success rounded-full"></div>
                      <span className="text-sm">V2 - Maxillary Division</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <div className="w-3 h-3 bg-medical-blue rounded-full"></div>
                      <span className="text-sm">V3 - Mandibular Division</span>
                    </div>
                  </div>
                </div>
                <div className="flex justify-centre">
                  <div className="relative">
                    <img 
                      src="/images/neurological-conditions/trigeminal-nerve-diagram.jpg" 
                      alt="Trigeminal nerve anatomy diagram"
                      className="rounded-lg shadow-lg max-w-full h-auto"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Nerve Branch Details */}
        <div className="space-y-6">
          {nerveDistribution.map((nerve, index) => {
            const Icon = getBranchIcon(nerve.branch);
            const isExpanded = expandedBranch === nerve.branch;
            
            return (
              <Card key={index} className={cn("transition-all duration-200", getBranchColor(nerve.branch))}>
                <CardHeader>
                  <div className="flex items-centre justify-between">
                    <div className="flex items-centre gap-3">
                      <div className="p-2 rounded-full bg-background/80">
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{nerve.branch}</CardTitle>
                        <CardDescription className="text-sm">{nerve.description}</CardDescription>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleExpanded(nerve.branch)}
                      className="flex items-centre gap-1"
                    >
                      <Info className="h-4 w-4" />
                      {isExpanded ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </CardHeader>
                
                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        {/* Innervation Areas */}
                        <div>
                          <h4 className="font-semibold mb-3">Areas of Innervation</h4>
                          <ul className="space-y-2">
                            {nerve.innervation.map((area, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{area}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Characteristics */}
                        <div>
                          <h4 className="font-semibold mb-3">Clinical Characteristics</h4>
                          <ul className="space-y-2">
                            {nerve.characteristics.map((characteristic, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{characteristic}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* Clinical Significance */}
        <div className="mt-12">
          <Card className="bg-info-light border-info/30">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2 text-info">
                <Info className="h-5 w-5" />
                Clinical Significance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-info text-sm">
                Understanding trigeminal nerve anatomy is crucial for diagnosing and treating trigeminal neuralgia. 
                The pain distribution follows specific nerve pathways, and treatment approaches may target different 
                branches depending on the location of symptoms. Most commonly, the maxillary (V2) and mandibular (V3) 
                divisions are affected, with the ophthalmic (V1) division being less frequently involved.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default TrigeminalAnatomySection;
