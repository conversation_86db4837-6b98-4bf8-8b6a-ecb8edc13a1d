import { Play, Pause, RotateCcw, Timer,
  Target, TrendingUp, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle,
  Hand, Zap, Activity, Award
} from 'lucide-react';
import React, { useState } from 'react';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface Exercise {
  id: string;
  name: string;
  description: string;
  instructions: string[];
  duration: string;
  repetitions: string;
  frequency: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  benefits: string[];
  precautions: string[];
  imageUrl: string;
  videoTips?: string[];
}

interface ExerciseCategory {
  category: string;
  description: string;
  exercises: Exercise[];
  icon: any;
}

interface UlnarExerciseGuideProps {
  exerciseCategories: ExerciseCategory[];
  className?: string;
}

const UlnarExerciseGuide: React.FC<UlnarExerciseGuideProps> = ({
  exerciseCategories,
  className
}) => {
  const deviceInfo = useDeviceDetection();
  const [activeExercise, setActiveExercise] = useState<string | null>(null);
  const [completedExercises, setCompletedExercises] = useState<Set<string>>(new Set());

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-success text-success-foreground';
      case 'intermediate': return 'bg-warning text-warning-foreground';
      case 'advanced': return 'bg-destructive text-destructive-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const toggleExerciseCompletion = (exerciseId: string) => {
    const newCompleted = new Set(completedExercises);
    if (newCompleted.has(exerciseId)) {
      newCompleted.delete(exerciseId);
    } else {
      newCompleted.add(exerciseId);
    }
    setCompletedExercises(newCompleted);
  };

  const calculateProgress = () => {
    const totalExercises = exerciseCategories.reduce((total, category) => total + category.exercises.length, 0);
    return totalExercises > 0 ? (completedExercises.size / totalExercises) * 100 : 0;
  };

  const renderExerciseCard = (exercise: Exercise) => (
    <Card 
      key={exercise.id} 
      className={cn(
        "medical-card transition-all duration-300",
        activeExercise === exercise.id && "ring-2 ring-primary",
        completedExercises.has(exercise.id) && "border-success"
      )}
    >
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Hand className="w-5 h-5 text-primary" />
              {exercise.name}
              {completedExercises.has(exercise.id) && (
                <CheckCircle className="w-5 h-5 text-success" />
              )}
            </CardTitle>
            <p className="text-enhanced-body text-sm mt-2">
              {exercise.description}
            </p>
          </div>
          <Badge className={getDifficultyColor(exercise.difficulty)}>
            {exercise.difficulty}
          </Badge>
        </div>

        {/* Exercise Metrics */}
        <div className="grid grid-cols-3 gap-4 mt-4">
          <div className="text-center">
            <Timer className="w-4 h-4 text-muted-foreground mx-auto mb-1" />
            <p className="text-enhanced-caption">Duration</p>
            <p className="text-enhanced-body text-sm font-medium">{exercise.duration}</p>
          </div>
          <div className="text-center">
            <RotateCcw className="w-4 h-4 text-muted-foreground mx-auto mb-1" />
            <p className="text-enhanced-caption">Reps</p>
            <p className="text-enhanced-body text-sm font-medium">{exercise.repetitions}</p>
          </div>
          <div className="text-center">
            <TrendingUp className="w-4 h-4 text-muted-foreground mx-auto mb-1" />
            <p className="text-enhanced-caption">Frequency</p>
            <p className="text-enhanced-body text-sm font-medium">{exercise.frequency}</p>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* Exercise Image */}
        <div className="mb-4">
          <SafeImage
            src={exercise.imageUrl}
            alt={`${exercise.name} exercise demonstration`}
            className="w-full h-48 object-cover rounded-lg"
            fallbackSrc="https://images.pexels.com/photos/3768916/pexels-photo-3768916.jpeg?auto=compress&cs=tinysrgb&w=800"
          />
        </div>

        {/* Exercise Controls */}
        <div className="flex gap-2 mb-4">
          <Button
            variant={activeExercise === exercise.id ? "secondary" : "default"}
            size="sm"
            onClick={() => setActiveExercise(activeExercise === exercise.id ? null : exercise.id)}
            className="flex items-center gap-2"
          >
            {activeExercise === exercise.id ? (
              <>
                <Pause className="w-4 h-4" />
                Hide Details
              </>
            ) : (
              <>
                <Play className="w-4 h-4" />
                Show Details
              </>
            )}
          </Button>
          <Button
            variant={completedExercises.has(exercise.id) ? "success" : "outline"}
            size="sm"
            onClick={() => toggleExerciseCompletion(exercise.id)}
            className="flex items-center gap-2"
          >
            <CheckCircle className="w-4 h-4" />
            {completedExercises.has(exercise.id) ? "Completed" : "Mark Complete"}
          </Button>
        </div>

        {/* Expanded Exercise Details */}
        {activeExercise === exercise.id && (
          <div className="space-y-4 border-t border-border pt-4">
            {/* Instructions */}
            <div>
              <h4 className="text-enhanced-subheading mb-2 flex items-center gap-2">
                <Target className="w-4 h-4 text-primary" />
                Step-by-Step Instructions
              </h4>
              <ol className="space-y-2">
                {exercise.instructions.map((instruction, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <span className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0 mt-0.5">
                      {index + 1}
                    </span>
                    <span className="text-enhanced-body text-sm">{instruction}</span>
                  </li>
                ))}
              </ol>
            </div>

            {/* Benefits */}
            <div>
              <h4 className="text-enhanced-subheading mb-2 flex items-center gap-2">
                <Award className="w-4 h-4 text-success" />
                Benefits
              </h4>
              <ul className="space-y-1">
                {exercise.benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                    <span className="text-enhanced-body text-sm">{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Precautions */}
            <div>
              <h4 className="text-enhanced-subheading mb-2 flex items-center gap-2">
                <AlertTriangle className="w-4 h-4 text-warning" />
                Precautions
              </h4>
              <ul className="space-y-1">
                {exercise.precautions.map((precaution, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <AlertTriangle className="w-4 h-4 text-warning mt-0.5 flex-shrink-0" />
                    <span className="text-enhanced-body text-sm">{precaution}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Video Tips */}
            {exercise.videoTips && (
              <div>
                <h4 className="text-enhanced-subheading mb-2 flex items-center gap-2">
                  <Zap className="w-4 h-4 text-info" />
                  Pro Tips
                </h4>
                <ul className="space-y-1">
                  {exercise.videoTips.map((tip, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Zap className="w-4 h-4 text-info mt-0.5 flex-shrink-0" />
                      <span className="text-enhanced-body text-sm">{tip}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <section className={cn("section-background py-16", className)}>
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading mb-4">
            Exercise & Hand Therapy Guide
          </h2>
          <p className="text-enhanced-body max-w-3xl mx-auto leading-relaxed">
            Comprehensive exercise program designed to improve nerve mobility, reduce symptoms, 
            and prevent recurrence of ulnar neuropathy.
          </p>
        </div>

        {/* Progress Tracking */}
        <Card className="medical-card mb-8">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Activity className="w-5 h-5 text-primary" />
              Your Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Progress value={calculateProgress()} className="h-3" />
              </div>
              <div className="text-right">
                <p className="text-enhanced-body font-medium">
                  {completedExercises.size} / {exerciseCategories.reduce((total, cat) => total + cat.exercises.length, 0)} exercises
                </p>
                <p className="text-enhanced-caption">
                  {Math.round(calculateProgress())}% complete
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Exercise Categories */}
        <Tabs defaultValue={exerciseCategories[0]?.category} className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-8">
            {exerciseCategories.map((category) => (
              <TabsTrigger key={category.category} value={category.category} className="flex items-center gap-2">
                <category.icon className="w-4 h-4" />
                {category.category}
              </TabsTrigger>
            ))}
          </TabsList>

          {exerciseCategories.map((category) => (
            <TabsContent key={category.category} value={category.category}>
              <div className="mb-6">
                <h3 className="text-enhanced-subheading mb-2">{category.category}</h3>
                <p className="text-enhanced-body">{category.description}</p>
              </div>
              <div className="grid md:grid-cols-2 gap-6">
                {category.exercises.map(renderExerciseCard)}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </section>
  );
};

export default UlnarExerciseGuide;
