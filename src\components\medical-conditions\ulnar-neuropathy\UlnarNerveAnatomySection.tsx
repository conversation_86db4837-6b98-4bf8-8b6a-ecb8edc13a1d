import { <PERSON>, <PERSON><PERSON>, Hand, MapPin, Target, AlertTriangle } from 'lucide-react';
import React from 'react';

import SafeImage from '@/components/SafeImage';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface UlnarNerveAnatomySectionProps {
  title?: string;
  description?: string;
  anatomyData: {
    pathway: string[];
    innervation: string[];
    cubitalTunnel: string[];
  };
  className?: string;
}

const UlnarNerveAnatomySection: React.FC<UlnarNerveAnatomySectionProps> = ({
  title = "Ulnar Nerve Anatomy & Cubital Tunnel",
  description = "Understanding the complex anatomy of the ulnar nerve and the cubital tunnel is essential for comprehending how compression occurs and why symptoms develop.",
  anatomyData,
  className
}) => {
  const deviceInfo = useDeviceDetection();

  const anatomyComponents = [
    {
      icon: Brain,
      title: "Nerve Pathway",
      description: "The ulnar nerve's journey from spinal cord to hand",
      details: anatomyData.pathway,
      color: "border-l-primary"
    },
    {
      icon: Zap,
      title: "Nerve Innervation",
      description: "Muscles and areas controlled by the ulnar nerve",
      details: anatomyData.innervation,
      color: "border-l-info"
    },
    {
      icon: Target,
      title: "Cubital Tunnel",
      description: "The anatomical space where compression occurs",
      details: anatomyData.cubitalTunnel,
      color: "border-l-warning"
    }
  ];

  return (
    <section className={cn("section-background py-16", className)}>
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading mb-4">
            {title}
          </h2>
          <p className="text-enhanced-body max-w-3xl mx-auto leading-relaxed">
            {description}
          </p>
        </div>

        {/* Anatomy Visualization */}
        <div className="mb-12">
          <Card className="medical-card overflow-hidden">
            <CardHeader className="text-center">
              <CardTitle className="text-enhanced-heading flex items-center justify-center gap-3">
                <Hand className="w-6 h-6 text-primary" />
                Ulnar Nerve Anatomy Visualization
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="relative">
                <SafeImage
                  src="https://images.unsplash.com/photo-**********-5c350d0d3c56?auto=format&fit=crop&w=1200&q=80"
                  alt="Ulnar nerve anatomy and cubital tunnel visualization"
                  className="w-full h-64 md:h-80 object-cover"
                  fallbackSrc="https://images.pexels.com/photos/3825527/pexels-photo-3825527.jpeg?auto=compress&cs=tinysrgb&w=1200"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent" />
                <div className="absolute bottom-4 left-4 right-4">
                  <p className="text-enhanced-caption text-center">
                    The ulnar nerve travels through the cubital tunnel at the elbow, where it can become compressed
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Anatomy Components Grid */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          {anatomyComponents.map((component, index) => (
            <Card key={index} className={cn("medical-card border-l-4", component.color)}>
              <CardHeader>
                <CardTitle className="text-enhanced-heading flex items-center gap-3">
                  <component.icon className="w-5 h-5 text-primary" />
                  {component.title}
                </CardTitle>
                <p className="text-enhanced-body text-sm">
                  {component.description}
                </p>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {component.details.map((detail, detailIndex) => (
                    <li key={detailIndex} className="flex items-start gap-2">
                      <div className="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0" />
                      <span className="text-enhanced-body text-sm leading-relaxed">
                        {detail}
                      </span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Compression Mechanism */}
        <Card className="medical-card border-l-4 border-l-destructive">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <AlertTriangle className="w-5 h-5 text-destructive" />
              How Compression Occurs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-enhanced-subheading mb-3">Anatomical Factors</h4>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2">
                    <MapPin className="w-4 h-4 text-destructive mt-1 flex-shrink-0" />
                    <span className="text-enhanced-body text-sm">
                      Osborne's ligament forms the roof of the cubital tunnel
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <MapPin className="w-4 h-4 text-destructive mt-1 flex-shrink-0" />
                    <span className="text-enhanced-body text-sm">
                      Medial epicondyle and olecranon form bony boundaries
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <MapPin className="w-4 h-4 text-destructive mt-1 flex-shrink-0" />
                    <span className="text-enhanced-body text-sm">
                      Flexor carpi ulnaris muscle adds to tunnel constraints
                    </span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="text-enhanced-subheading mb-3">Compression Mechanisms</h4>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2">
                    <Target className="w-4 h-4 text-destructive mt-1 flex-shrink-0" />
                    <span className="text-enhanced-body text-sm">
                      Elbow flexion reduces tunnel space by up to 55%
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Target className="w-4 h-4 text-destructive mt-1 flex-shrink-0" />
                    <span className="text-enhanced-body text-sm">
                      Direct pressure on medial elbow compresses nerve
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Target className="w-4 h-4 text-destructive mt-1 flex-shrink-0" />
                    <span className="text-enhanced-body text-sm">
                      Repetitive flexion-extension causes nerve irritation
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default UlnarNerveAnatomySection;
