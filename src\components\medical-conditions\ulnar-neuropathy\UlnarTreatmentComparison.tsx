import { <PERSON>, Scissors, 
  TrendingUp, Clock, Al<PERSON><PERSON>riangle, <PERSON><PERSON><PERSON><PERSON>,
  Star, Award, Gauge, Users
} from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface TreatmentOption {
  icon: any;
  title: string;
  description: string;
  procedures: string[];
  effectiveness: 'high' | 'moderate' | 'variable';
  recovery: string;
  risks: string[];
  scientificEvidence?: string;
  successRate?: string;
  costLevel?: 'low' | 'medium' | 'high';
  invasiveness?: 'minimal' | 'moderate' | 'high';
}

interface UlnarTreatmentComparisonProps {
  conservativeOptions: TreatmentOption[];
  surgicalOptions: TreatmentOption[];
  className?: string;
}

const UlnarTreatmentComparison: React.FC<UlnarTreatmentComparisonProps> = ({
  conservativeOptions,
  surgicalOptions,
  className
}) => {
  const deviceInfo = useDeviceDetection();
  const [selectedTreatment, setSelectedTreatment] = useState<string | null>(null);

  const getEffectivenessColor = (effectiveness: string) => {
    switch (effectiveness) {
      case 'high': return 'bg-success text-success-foreground';
      case 'moderate': return 'bg-info text-info-foreground';
      case 'variable': return 'bg-warning text-warning-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getCostColor = (cost: string) => {
    switch (cost) {
      case 'low': return 'bg-success text-success-foreground';
      case 'medium': return 'bg-warning text-warning-foreground';
      case 'high': return 'bg-destructive text-destructive-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getInvasivenessColor = (invasiveness: string) => {
    switch (invasiveness) {
      case 'minimal': return 'bg-success text-success-foreground';
      case 'moderate': return 'bg-warning text-warning-foreground';
      case 'high': return 'bg-destructive text-destructive-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const renderTreatmentCard = (treatment: TreatmentOption, index: number) => (
    <Card 
      key={index} 
      className={cn(
        "medical-card cursor-pointer transition-all duration-300",
        selectedTreatment === treatment.title && "ring-2 ring-primary"
      )}
      onClick={() => setSelectedTreatment(selectedTreatment === treatment.title ? null : treatment.title)}
    >
      <CardHeader>
        <CardTitle className="text-enhanced-heading flex items-center gap-3">
          <treatment.icon className="w-5 h-5 text-primary" />
          {treatment.title}
        </CardTitle>
        <p className="text-enhanced-body text-sm">
          {treatment.description}
        </p>
        
        {/* Treatment Metrics */}
        <div className="flex flex-wrap gap-2 mt-3">
          <Badge className={getEffectivenessColor(treatment.effectiveness)}>
            <TrendingUp className="w-3 h-3 mr-1" />
            {treatment.effectiveness} effectiveness
          </Badge>
          {treatment.costLevel && (
            <Badge className={getCostColor(treatment.costLevel)}>
              <Gauge className="w-3 h-3 mr-1" />
              {treatment.costLevel} cost
            </Badge>
          )}
          {treatment.invasiveness && (
            <Badge className={getInvasivenessColor(treatment.invasiveness)}>
              <AlertTriangle className="w-3 h-3 mr-1" />
              {treatment.invasiveness} invasive
            </Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Key Information */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <div className="flex items-center gap-2 mb-1">
              <Clock className="w-4 h-4 text-muted-foreground" />
              <span className="text-enhanced-caption">Recovery Time</span>
            </div>
            <p className="text-enhanced-body text-sm font-medium">{treatment.recovery}</p>
          </div>
          {treatment.successRate && (
            <div>
              <div className="flex items-center gap-2 mb-1">
                <Star className="w-4 h-4 text-muted-foreground" />
                <span className="text-enhanced-caption">Success Rate</span>
              </div>
              <p className="text-enhanced-body text-sm font-medium">{treatment.successRate}</p>
            </div>
          )}
        </div>

        {/* Expanded Details */}
        {selectedTreatment === treatment.title && (
          <div className="space-y-4 border-t border-border pt-4">
            {/* Procedures */}
            <div>
              <h4 className="text-enhanced-subheading mb-2 flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-success" />
                Procedures & Techniques
              </h4>
              <ul className="space-y-1">
                {treatment.procedures.map((procedure, procIndex) => (
                  <li key={procIndex} className="flex items-start gap-2">
                    <div className="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0" />
                    <span className="text-enhanced-body text-sm">{procedure}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Scientific Evidence */}
            {treatment.scientificEvidence && (
              <div>
                <h4 className="text-enhanced-subheading mb-2 flex items-center gap-2">
                  <Award className="w-4 h-4 text-info" />
                  Scientific Evidence
                </h4>
                <p className="text-enhanced-body text-sm">{treatment.scientificEvidence}</p>
              </div>
            )}

            {/* Risks & Considerations */}
            <div>
              <h4 className="text-enhanced-subheading mb-2 flex items-center gap-2">
                <AlertTriangle className="w-4 h-4 text-warning" />
                Risks & Considerations
              </h4>
              <ul className="space-y-1">
                {treatment.risks.map((risk, riskIndex) => (
                  <li key={riskIndex} className="flex items-start gap-2">
                    <div className="w-2 h-2 rounded-full bg-warning mt-2 flex-shrink-0" />
                    <span className="text-enhanced-body text-sm">{risk}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <section className={cn("section-background py-16", className)}>
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading mb-4">
            Treatment Options Comparison
          </h2>
          <p className="text-enhanced-body max-w-3xl mx-auto leading-relaxed">
            Compare different treatment approaches for ulnar neuropathy, including their effectiveness, 
            scientific evidence, and suitability for different patient profiles.
          </p>
        </div>

        {/* Treatment Comparison Tabs */}
        <Tabs defaultValue="conservative" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="conservative" className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              Conservative Treatment
            </TabsTrigger>
            <TabsTrigger value="surgical" className="flex items-center gap-2">
              <Scissors className="w-4 h-4" />
              Surgical Options
            </TabsTrigger>
          </TabsList>

          <TabsContent value="conservative">
            <div className="grid md:grid-cols-2 gap-6">
              {conservativeOptions.map((treatment, index) => renderTreatmentCard(treatment, index))}
            </div>
          </TabsContent>

          <TabsContent value="surgical">
            <div className="grid md:grid-cols-2 gap-6">
              {surgicalOptions.map((treatment, index) => renderTreatmentCard(treatment, index))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Decision Making Guide */}
        <Card className="medical-card border-l-4 border-l-info mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Users className="w-5 h-5 text-info" />
              Treatment Decision Guide
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div>
                <h4 className="text-enhanced-subheading mb-3">Conservative First</h4>
                <ul className="space-y-2">
                  <li className="text-enhanced-body text-sm">• Mild to moderate symptoms</li>
                  <li className="text-enhanced-body text-sm">• Recent onset (&lt; 6 months)</li>
                  <li className="text-enhanced-body text-sm">• No muscle weakness</li>
                  <li className="text-enhanced-body text-sm">• Intermittent symptoms</li>
                </ul>
              </div>
              <div>
                <h4 className="text-enhanced-subheading mb-3">Consider Surgery</h4>
                <ul className="space-y-2">
                  <li className="text-enhanced-body text-sm">• Persistent symptoms &gt; 3 months</li>
                  <li className="text-enhanced-body text-sm">• Progressive muscle weakness</li>
                  <li className="text-enhanced-body text-sm">• Failed conservative treatment</li>
                  <li className="text-enhanced-body text-sm">• Severe functional impairment</li>
                </ul>
              </div>
              <div>
                <h4 className="text-enhanced-subheading mb-3">Urgent Surgery</h4>
                <ul className="space-y-2">
                  <li className="text-enhanced-body text-sm">• Severe muscle atrophy</li>
                  <li className="text-enhanced-body text-sm">• Complete sensory loss</li>
                  <li className="text-enhanced-body text-sm">• Claw hand deformity</li>
                  <li className="text-enhanced-body text-sm">• Rapid progression</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default UlnarTreatmentComparison;
