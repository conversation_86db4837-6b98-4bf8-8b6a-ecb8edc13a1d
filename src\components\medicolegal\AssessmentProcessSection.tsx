import React from 'react';

import SafeImage from '@/components/SafeImage';

interface AssessmentProcessSectionProps {
  translations: {
    assessmentProcess?: {
      title?: string;
      description?: string;
      beforeAppointment?: {
        title?: string;
        description?: string;
      };
      duringAppointment?: {
        title?: string;
        description?: string;
      };
      afterAppointment?: {
        title?: string;
        description?: string;
      };
    };
  };
}

const AssessmentProcessSection: React.FC<AssessmentProcessSectionProps> = ({ translations }) => {
  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="text-centre mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{translations.assessmentProcess?.title || 'Medicolegal Assessment Process'}</h2>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            {translations.assessmentProcess?.description || 'Medical examinations form a crucial part of the personal injury and compensation claim assessment. Dr. <PERSON> provides unbiased independent expert reviews with appropriate qualifications, deep knowledge of the problem, and specific training to provide impairment ratings.'}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {/* Before Appointment */}
          <div className="card p-6 rounded-lg shadow-md medical-card">
            <h3 className="text-xl font-semibold mb-3 text-primary">
              {translations.assessmentProcess?.beforeAppointment?.title || 'Before the Appointment'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {translations.assessmentProcess?.beforeAppointment?.description || 'Comprehensive preparation ensures thorough and efficient assessment.'}
            </p>
            <ul className="space-y-2 mb-4">
              <li className="text-muted-foreground text-sm">• Review of all medical records and imaging</li>
              <li className="text-muted-foreground text-sm">• Analysis of relevant legal documentation</li>
              <li className="text-muted-foreground text-sm">• Preparation of examination protocol</li>
              <li className="text-muted-foreground text-sm">• Coordination with referring parties</li>
              <li className="text-muted-foreground text-sm">• Scheduling and logistics arrangement</li>
            </ul>
            <SafeImage
              src="/images/neurosurgical-consultation-suite-Surrey-Hills-miNEURO-doctor-desk.jpg"
              alt="Consultation Room"
              className="w-full h-40 object-cover rounded-md mb-4"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>

          {/* During Appointment */}
          <div className="card p-6 rounded-lg shadow-md medical-card">
            <h3 className="text-xl font-semibold mb-3 text-primary">
              {translations.assessmentProcess?.duringAppointment?.title || 'During the Appointment'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {translations.assessmentProcess?.duringAppointment?.description || 'Thorough clinical examination and assessment conducted professionally.'}
            </p>
            <ul className="space-y-2 mb-4">
              <li className="text-muted-foreground text-sm">• Detailed medical history taking</li>
              <li className="text-muted-foreground text-sm">• Comprehensive neurological examination</li>
              <li className="text-muted-foreground text-sm">• Functional capacity assessment</li>
              <li className="text-muted-foreground text-sm">• Pain and symptom evaluation</li>
              <li className="text-muted-foreground text-sm">• Documentation of findings</li>
            </ul>
            <SafeImage
              src="/images/assessment-tools/neurological-assessment.jpg"
              alt="Neurological Examination"
              className="w-full h-40 object-cover rounded-md mb-4"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>

          {/* After Appointment */}
          <div className="card p-6 rounded-lg shadow-md medical-card">
            <h3 className="text-xl font-semibold mb-3 text-primary">
              {translations.assessmentProcess?.afterAppointment?.title || 'After the Appointment'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {translations.assessmentProcess?.afterAppointment?.description || 'Comprehensive reporting and follow-up services.'}
            </p>
            <ul className="space-y-2 mb-4">
              <li className="text-muted-foreground text-sm">• Detailed report preparation</li>
              <li className="text-muted-foreground text-sm">• Impairment rating calculation</li>
              <li className="text-muted-foreground text-sm">• Treatment recommendations</li>
              <li className="text-muted-foreground text-sm">• Prognosis assessment</li>
              <li className="text-muted-foreground text-sm">• Expert opinion formulation</li>
              <li className="text-muted-foreground text-sm">• Report delivery and consultation</li>
            </ul>
            <SafeImage
              src="/images/law-neurosurgery-JME-IME-assessment-report-spine-brain-insurance-claim-medicolegal.png"
              alt="Report Preparation"
              className="w-full h-40 object-cover rounded-md mb-4"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>
        </div>

        <div className="relative rounded-xl overflow-hidden shadow-lg max-w-4xl mx-auto">
          <SafeImage
            src="/images/gp-resources/care-coordination-hero.jpg"
            alt="Professional medicolegal assessment environment"
            className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
            fallbackSrc="/images/medical-consulting.jpg"
          />
        </div>
      </div>
    </section>
  );
};

AssessmentProcessSection.displayName = 'AssessmentProcessSection';

export default AssessmentProcessSection;
