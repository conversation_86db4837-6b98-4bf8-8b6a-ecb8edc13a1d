import React from 'react';

import SafeImage from '@/components/SafeImage';

interface ComprehensiveServicesSectionProps {
  comprehensiveServices: {
    title: string;
    description: string;
    services: {
      id: string;
      title: string;
      description: string;
      details?: string[];
    }[];
  };
}

const ComprehensiveServicesSection: React.FC<ComprehensiveServicesSectionProps> = ({ comprehensiveServices }) => {
  return (
    <section className="py-16 bg-muted/30">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{comprehensiveServices.title}</h2>
          <p className="text-muted-foreground max-w-4xl mx-auto">
            {comprehensiveServices.description}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {comprehensiveServices.services.map((service) => (
            <div key={service.id} className="card p-6 rounded-lg shadow-md medical-card">
              <h3 className="text-lg font-semibold mb-3 text-primary">
                {service.title}
              </h3>
              <p className="text-muted-foreground text-sm leading-relaxed">
                {service.description}
              </p>
              {service.details && (
                <ul className="mt-4 space-y-1">
                  {service.details.map((detail, detailIndex) => (
                    <li key={detailIndex} className="text-muted-foreground text-xs">
                      • {detail}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          ))}
        </div>

        {/* Additional Information Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="relative rounded-xl overflow-hidden shadow-lg">
            <SafeImage
              src="/images/treatment-discussion-neurosurgery-report-spine-brain-insurance-claim-medicolegal.jpg"
              alt="Comprehensive medicolegal services"
              className="w-full h-auto"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>
          <div>
            <h3 className="text-enhanced-heading text-2xl font-bold mb-4 text-primary">Intelligent and Accurate</h3>
            <p className="text-muted-foreground mb-6">
              Our reports are prepared in accordance with the Expert Witness Code of Conduct and the guidelines specified by AMA (American Medical Association) TAC (Transport Accident Commission) and Worksafe. They can be used for a variety of legal purposes:
            </p>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Assessment of injuries (e.g. related to work, transport accidents, public liability or medical negligence)</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Assisting with insurance and compensation claims (e.g. WorkSafe, TAC, third party claims, medical indemnity or income protection)</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Impairment assessments</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Estimation of work capacity</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};

ComprehensiveServicesSection.displayName = 'ComprehensiveServicesSection';

export default ComprehensiveServicesSection;
