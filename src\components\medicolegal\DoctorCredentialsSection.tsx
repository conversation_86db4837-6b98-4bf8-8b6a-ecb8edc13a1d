import React from 'react';

interface DoctorCredentialsSectionProps {
  doctorCredentials: {
    title: string;
    description: string;
    qualifications: string[];
    experience: string;
    specializations: string[];
  };
}

const DoctorCredentialsSection: React.FC<DoctorCredentialsSectionProps> = ({ doctorCredentials }) => {
  return (
    <section className="py-16 bg-muted/30">
      <div className="container">
        <div className="max-w-4xl mx-auto">
          <div className="text-centre mb-12">
            <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{doctorCredentials.title}</h2>
            <p className="text-muted-foreground max-w-3xl mx-auto">
              {doctorCredentials.description}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Qualifications */}
            <div className="card p-6 rounded-lg shadow-md medical-card">
              <h3 className="text-xl font-semibold mb-4 text-primary">
                Professional Qualifications
              </h3>
              <ul className="space-y-2">
                {doctorCredentials.qualifications.map((qualification, index) => (
                  <li key={index} className="text-muted-foreground">
                    • {qualification}
                  </li>
                ))}
              </ul>
            </div>

            {/* Specializations */}
            <div className="card p-6 rounded-lg shadow-md medical-card">
              <h3 className="text-xl font-semibold mb-4 text-primary">
                Medicolegal Specializations
              </h3>
              <ul className="space-y-2">
                {doctorCredentials.specializations.map((specialization, index) => (
                  <li key={index} className="text-muted-foreground">
                    • {specialization}
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Experience */}
          <div className="text-centre mt-8">
            <div className="card p-6 rounded-lg shadow-md medical-card">
              <p className="text-lg font-semibold text-primary">
                {doctorCredentials.experience}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

DoctorCredentialsSection.displayName = 'DoctorCredentialsSection';

export default DoctorCredentialsSection;
