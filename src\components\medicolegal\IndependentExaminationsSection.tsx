import React from 'react';

import SafeImage from '@/components/SafeImage';

interface IndependentExaminationsSectionProps {
  translations: {
    independentReports?: {
      title?: string;
      description?: string;
    };
  };
}

const IndependentExaminationsSection: React.FC<IndependentExaminationsSectionProps> = ({ translations }) => {
  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="text-centre mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{translations.independentReports?.title || 'Independent Examinations and Reports'}</h2>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            {translations.independentReports?.description || 'Comprehensive independent medical examinations conducted with the highest standards of objectivity and professionalism.'}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-centre mb-12">
          <div className="relative rounded-xl overflow-hidden shadow-lg">
            <SafeImage
              src="/images/examination-neurosurgery-report-spine-brain-insurance-claim-medicolegal.jpg"
              alt="Independent medical examination for legal purposes"
              className="w-full h-auto"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>
          <div>
            <h3 className="text-enhanced-heading text-2xl font-bold mb-4 text-primary">Pre-litigation Assessments</h3>
            <p className="text-muted-foreground mb-6">
              Early assessment to establish injury extent and treatment needs. Our comprehensive evaluations provide crucial information for legal proceedings and insurance claims.
            </p>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Comprehensive medical history review</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Detailed physical examination</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Imaging and investigation analysis</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Treatment recommendation</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Prognosis assessment</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-centre mb-12">
          <div className="order-2 lg:order-1">
            <h3 className="text-enhanced-heading text-2xl font-bold mb-4 text-primary">Litigation Support</h3>
            <p className="text-muted-foreground mb-6">
              Expert medical opinions for legal proceedings. Dr. Aliashkevich provides professional testimony and comprehensive reports that meet legal standards.
            </p>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Independent medical examination</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Expert witness testimony</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Medical record review</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Causation analysis</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Damages assessment</span>
              </li>
            </ul>
          </div>
          <div className="order-1 lg:order-2 relative rounded-xl overflow-hidden shadow-lg">
            <SafeImage
              src="/images/gp-resources/team-radiology-review.jpg"
              alt="Legal consultation and expert testimony"
              className="w-full h-auto"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-centre">
          <div className="relative rounded-xl overflow-hidden shadow-lg">
            <SafeImage
              src="/images/employment-worker-safety-construction-medicolegal-miNEURO-work-injury.jpg"
              alt="Insurance assessment consultation"
              className="w-full h-auto"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>
          <div>
            <h3 className="text-enhanced-heading text-2xl font-bold mb-4 text-primary">Insurance Assessments</h3>
            <p className="text-muted-foreground mb-6">
              Medical evaluations for insurance claim purposes. Comprehensive assessments that help determine coverage and treatment requirements.
            </p>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Policy coverage assessment</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Treatment necessity evaluation</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Return to work capacity</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Ongoing care requirements</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Permanent impairment rating</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};

IndependentExaminationsSection.displayName = 'IndependentExaminationsSection';

export default IndependentExaminationsSection;
