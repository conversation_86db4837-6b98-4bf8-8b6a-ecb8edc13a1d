import React from 'react';

import SafeImage from '@/components/SafeImage';

interface IntroductionSectionProps {
  introduction: {
    title: string;
    description: string;
    keyPoints: string[];
    image: string;
  };
}

const IntroductionSection: React.FC<IntroductionSectionProps> = ({ introduction }) => {
  return (
    <section className="py-16">
      <div className="container">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-centre">
          <div>
            <h2 className="text-enhanced-heading text-3xl font-bold mb-6">{introduction.title}</h2>
            <p className="text-muted-foreground mb-4">
              {introduction.description}
            </p>
            <p className="text-muted-foreground mb-6">
              Our medicolegal services are designed to provide objective, evidence-based assessments that help resolve complex cases involving neurological and spinal injuries.
            </p>
            
            {/* Key Points from medicolegal.md */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3 text-primary">Key Service Features:</h3>
              <ul className="space-y-2">
                {introduction.keyPoints.map((point, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-primary mr-2">•</span>
                    <span className="text-muted-foreground">{point}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="flex flex-wrap gap-4">
              <div className="bg-primary/5 p-3 rounded-md">
                <p className="font-medium">AMA Certified Assessor</p>
              </div>
              <div className="bg-primary/5 p-3 rounded-md">
                <p className="font-medium">WorkCover Approved</p>
              </div>
              <div className="bg-primary/5 p-3 rounded-md">
                <p className="font-medium">TAC Approved</p>
              </div>
            </div>
          </div>
          <div className="relative rounded-xl overflow-hidden shadow-lg">
            <SafeImage
              src="/images/law-neurosurgery-spine-brain-insurance-claim-medicolegal-miNEURO-work-injury-accident-traffic-TAC.jpg"
              alt="Dr Aliashkevich conducting a medicolegal assessment"
              className="w-full h-auto"
              fallbackSrc="/images/examination-neurosurgery-report-spine-brain-insurance-claim-medicolegal.jpg"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

IntroductionSection.displayName = 'IntroductionSection';

export default IntroductionSection;
