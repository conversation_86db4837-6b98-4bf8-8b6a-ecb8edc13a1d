import React from 'react';

import SafeImage from '@/components/SafeImage';

interface ServicesSectionProps {
  translations: {
    services?: {
      title?: string;
      description?: string;
    };
  };
}

const ServicesSection: React.FC<ServicesSectionProps> = ({ translations }) => {
  return (
    <section className="py-16 medical-card">
      <div className="container">
        <div className="text-centre mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{translations.services?.title || 'Our Range of Services'}</h2>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            {translations.services?.description || 'Comprehensive medicolegal services covering all aspects of neurological injury assessment.'}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {/* Assessment Services */}
          <div className="card p-6 rounded-lg shadow-md bg-background">
            <h3 className="text-xl font-semibold mb-4 text-primary">Assessment Services</h3>
            <ul className="space-y-2">
              <li className="text-muted-foreground text-sm">• Independent Medical Examinations</li>
              <li className="text-muted-foreground text-sm">• Joint Medical Examinations</li>
              <li className="text-muted-foreground text-sm">• Whole Person Impairment Assessment</li>
              <li className="text-muted-foreground text-sm">• Work Capacity Evaluations</li>
              <li className="text-muted-foreground text-sm">• Functional Capacity Assessments</li>
              <li className="text-muted-foreground text-sm">• Treatment Necessity Reviews</li>
            </ul>
            <SafeImage
              src="/images/spine-assessment-comprehensive.jpg"
              alt="Assessment Services"
              className="w-full h-40 object-cover rounded-md mt-4"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>

          {/* Reporting Services */}
          <div className="card p-6 rounded-lg shadow-md bg-background">
            <h3 className="text-xl font-semibold mb-4 text-primary">Reporting Services</h3>
            <ul className="space-y-2">
              <li className="text-muted-foreground text-sm">• Comprehensive Medical Reports</li>
              <li className="text-muted-foreground text-sm">• Impairment Rating Reports</li>
              <li className="text-muted-foreground text-sm">• Causation Analysis Reports</li>
              <li className="text-muted-foreground text-sm">• Treatment Recommendation Reports</li>
              <li className="text-muted-foreground text-sm">• Prognosis and Future Care Reports</li>
              <li className="text-muted-foreground text-sm">• Expert Witness Statements</li>
            </ul>
            <SafeImage
              src="/images/gp-resources/referral-protocols-medical.jpg"
              alt="Reporting Services"
              className="w-full h-40 object-cover rounded-md mt-4"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>

          {/* Legal Support */}
          <div className="card p-6 rounded-lg shadow-md bg-background">
            <h3 className="text-xl font-semibold mb-4 text-primary">Legal Support</h3>
            <ul className="space-y-2">
              <li className="text-muted-foreground text-sm">• Expert Witness Testimony</li>
              <li className="text-muted-foreground text-sm">• Court Appearances</li>
              <li className="text-muted-foreground text-sm">• Deposition Services</li>
              <li className="text-muted-foreground text-sm">• Case Consultation</li>
              <li className="text-muted-foreground text-sm">• Medical Record Review</li>
              <li className="text-muted-foreground text-sm">• Literature Research and Analysis</li>
            </ul>
            <SafeImage
              src="/images/gp-resources/team-review.jpg"
              alt="Legal Support Services"
              className="w-full h-40 object-cover rounded-md mt-4"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

ServicesSection.displayName = 'ServicesSection';

export default ServicesSection;
