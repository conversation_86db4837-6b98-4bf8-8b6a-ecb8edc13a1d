import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

interface CardItem {
  id: string;
  title: string;
  description: string;
  content?: React.ReactNode;
  footer?: React.ReactNode;
  link?: string;
  linkText?: string;
  icon?: React.ReactNode;
}

interface CardGridProps {
  title?: string;
  subtitle?: string;
  cards: CardItem[];
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}

const CardGrid: React.FC<CardGridProps> = ({
  title,
  subtitle,
  cards,
  columns = 3,
  className = ''
}) => {
  const getGridClass = () => {
    switch (columns) {
      case 1:
        return 'grid-cols-1';
      case 2:
        return 'grid-cols-1 md:grid-cols-2';
      case 3:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
      case 4:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
      default:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
    }
  };

  return (
    <div className={className}>
      {(title || subtitle) && (
        <div className="text-center mb-8">
          {title && <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>}
          {subtitle && <p className="text-muted-foreground text-lg">{subtitle}</p>}
        </div>
      )}
      
      <div className={`grid ${getGridClass()} gap-6`}>
        {cards.map((card) => (
          <Card key={card.id} className="h-full flex flex-col">
            <CardHeader>
              {card.icon && (
                <div className="flex justify-center mb-4">
                  <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                    {card.icon}
                  </div>
                </div>
              )}
              <CardTitle className="text-center">{card.title}</CardTitle>
              <CardDescription className="text-center">{card.description}</CardDescription>
            </CardHeader>
            
            {card.content && (
              <CardContent className="flex-1">
                {card.content}
              </CardContent>
            )}
            
            {(card.footer || card.link) && (
              <CardFooter className="pt-0">
                {card.footer || (
                  card.link && (
                    <Button asChild className="w-full">
                      <Link to={card.link}>{card.linkText || 'Learn More'}</Link>
                    </Button>
                  )
                )}
              </CardFooter>
            )}
          </Card>
        ))}
      </div>
    </div>
  );
};

export default CardGrid;
