import React from 'react';

interface ContentSectionProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  className?: string;
  backgroundVariant?: 'default' | 'muted' | 'primary';
}

const ContentSection: React.FC<ContentSectionProps> = ({
  title,
  subtitle,
  children,
  className = '',
  backgroundVariant = 'default'
}) => {
  const getBackgroundClass = () => {
    switch (backgroundVariant) {
      case 'muted':
        return 'bg-muted/30';
      case 'primary':
        return 'bg-primary/5';
      default:
        return 'bg-background';
    }
  };

  return (
    <section className={`py-12 ${getBackgroundClass()} ${className}`}>
      <div className="container">
        <div className="max-w-5xl mx-auto">
          <div className="text-centre mb-8">
            <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
            {subtitle && (
              <p className="text-muted-foreground text-lg">{subtitle}</p>
            )}
          </div>
          {children}
        </div>
      </div>
    </section>
  );
};

export default ContentSection;
