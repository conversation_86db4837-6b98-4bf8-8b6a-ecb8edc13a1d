import { Activity, FileText, Brain } from 'lucide-react';
import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { RelatedResource } from '@/data/patient-resources/ageSpecificRecommendationsData';

interface RelatedResourcesSectionProps {
  title: string;
  resources: RelatedResource[];
}

const iconMap = {
  Activity,
  FileText,
  Brain
};

const RelatedResourcesSection: React.FC<RelatedResourcesSectionProps> = ({ title, resources }) => {
  return (
    <section className="py-16 bg-muted/30">
      <div className="container">
        <h2 className="text-enhanced-heading text-3xl font-bold text-center mb-12">{title}</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {resources.map((resource) => {
            const IconComponent = iconMap[resource.icon as keyof typeof iconMap];
            
            return (
              <Card key={resource.id} className="medical-card shadow-md hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    {IconComponent && <IconComponent className="h-8 w-8 text-primary mr-3" />}
                    <h3 className="text-xl font-semibold">{resource.title}</h3>
                  </div>
                  <p className="text-muted-foreground mb-6">
                    {resource.description}
                  </p>
                  <Button asChild className="w-full">
                    <Link to={resource.link}>{resource.buttonText}</Link>
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};

RelatedResourcesSection.displayName = 'RelatedResourcesSection';

export default RelatedResourcesSection;
