import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { ResourceCategory } from '@/data/patient-resources/resourceCategories';

interface ResourceCategoriesSectionProps {
  categories: ResourceCategory[];
}

const ResourceCategoriesSection: React.FC<ResourceCategoriesSectionProps> = ({ categories }) => {
  return (
    <section className="py-16">
      <div className="container max-w-7xl">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {categories?.map((category, index) => {
            const IconComponent = category.icon;
            return (
              <div 
                key={category.id} 
                className="medical-card/50 backdrop-blur-sm border border-border/50 p-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 animate-fade-in" 
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-centre mb-6">
                  <div className="p-2 rounded-lg bg-primary/10 mr-3">
                    <IconComponent className="h-6 w-6 text-primary" />
                  </div>
                  <h2 className="text-xl font-bold text-foreground">{category.title}</h2>
                </div>
                <div className="space-y-6">
                  {category.resources?.map((resource, resourceIndex) => (
                    <div key={resourceIndex} className="border-t border-border/30 pt-4 first:border-t-0 first:pt-0">
                      <h3 className="text-lg font-semibold mb-2 text-foreground">{resource.title}</h3>
                      <p className="text-muted-foreground mb-3 text-sm leading-relaxed">{resource.description}</p>
                      <Button asChild variant="outline" size="sm" className="text-primary border-primary/20 hover:bg-primary/10">
                        <Link to={resource.link}>{resource.buttonText}</Link>
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

ResourceCategoriesSection.displayName = 'ResourceCategoriesSection';

export default ResourceCategoriesSection;
