import React from 'react';
import { Link } from 'react-router-dom';

import SafeImage from '@/components/SafeImage';
import { Button } from '@/components/ui/button';

interface SpineHealthAppSectionProps {
  title: string;
  description: string;
  features: string[];
  primaryButtonText: string;
  primaryButtonLink: string;
  secondaryButtonText: string;
  secondaryButtonLink: string;
  imageSrc: string;
  imageAlt: string;
}

const SpineHealthAppSection: React.FC<SpineHealthAppSectionProps> = ({
  title,
  description,
  features,
  primaryButtonText,
  primaryButtonLink,
  secondaryButtonText,
  secondaryButtonLink,
  imageSrc,
  imageAlt
}) => {
  return (
    <section className="py-16 bg-muted/30">
      <div className="container max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-centre">
          <div className="relative rounded-lg overflow-hidden shadow-lg">
            <SafeImage
              src={imageSrc}
              alt={imageAlt}
              className="w-full h-auto"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>
          
          <div className="space-y-6">
            <div>
              <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{title}</h2>
              <p className="text-muted-foreground leading-relaxed">{description}</p>
            </div>
            
            {features && features.length > 0 && (
              <div>
                <p className="text-muted-foreground mb-4 font-medium">Features include:</p>
                <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                  {features.map((feature, index) => (
                    <li key={index} className="leading-relaxed">{feature}</li>
                  ))}
                </ul>
              </div>
            )}
            
            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <Button asChild size="lg" className="flex-1 sm:flex-none">
                <Link to={primaryButtonLink}>{primaryButtonText}</Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="flex-1 sm:flex-none">
                <Link to={secondaryButtonLink}>{secondaryButtonText}</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

SpineHealthAppSection.displayName = 'SpineHealthAppSection';

export default SpineHealthAppSection;
