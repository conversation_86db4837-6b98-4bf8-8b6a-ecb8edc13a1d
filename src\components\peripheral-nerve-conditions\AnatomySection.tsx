import React from 'react';
import { Link } from 'react-router-dom';

import SafeImage from '@/components/SafeImage';
import { Button } from '@/components/ui/button';

interface PeripheralNerveAnatomySectionProps {
  title: string;
  subtitle: string;
  paragraphs: string[];
  anatomyList: Array<{
    label: string;
    description: string;
  }>;
  image: {
    src: string;
    alt: string;
  };
  ctaButton: {
    text: string;
    link: string;
  };
}

/**
 * PeripheralNerveAnatomySection Component
 * Anatomy education section for peripheral nerve system
 * Follows the established pattern from spine conditions with nerve-specific content
 */
const PeripheralNerveAnatomySection: React.FC<PeripheralNerveAnatomySectionProps> = ({
  title,
  subtitle,
  paragraphs,
  anatomyList,
  image,
  ctaButton
}) => {
  return (
    <section className="py-16 bg-gradient-to-br from-muted/10 to-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-enhanced-heading mb-4">
            {title}
          </h2>
          <p className="text-xl text-enhanced-muted max-w-3xl mx-auto">
            {subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <div className="space-y-6 mb-8">
              {paragraphs.map((paragraph, index) => (
                <p key={index} className="text-lg text-enhanced-muted leading-relaxed">
                  {paragraph}
                </p>
              ))}
            </div>

            <div className="space-y-4 mb-8">
              {anatomyList.map((item, index) => (
                <div key={index} className="flex items-start gap-4 p-4 bg-card rounded-lg border">
                  <div className="w-3 h-3 bg-primary rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-enhanced-heading mb-1">
                      {item.label}
                    </h4>
                    <p className="text-enhanced-muted">
                      {item.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            <Button asChild className="w-full sm:w-auto">
              <Link to={ctaButton.link}>
                {ctaButton.text}
              </Link>
            </Button>
          </div>

          <div className="relative">
            <SafeImage
              src={image.src}
              alt={image.alt}
              className="w-full h-96 object-cover rounded-xl shadow-lg"
              fallbackSrc="https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent rounded-xl" />
          </div>
        </div>
      </div>
    </section>
  );
};

PeripheralNerveAnatomySection.displayName = 'PeripheralNerveAnatomySection';

export default PeripheralNerveAnatomySection;
