import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';

interface PeripheralNerveCTASectionProps {
  title: string;
  description: string;
  buttons: Array<{
    text: string;
    link: string;
    variant: 'default' | 'outline';
  }>;
}

/**
 * PeripheralNerveCTASection Component
 * Call-to-action section for peripheral nerve conditions library
 * Follows the established pattern from spine conditions
 */
const PeripheralNerveCTASection: React.FC<PeripheralNerveCTASectionProps> = ({
  title,
  description,
  buttons
}) => {
  return (
    <section className="py-16 bg-gradient-to-br from-primary/5 to-secondary/5">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-enhanced-heading mb-6">
            {title}
          </h2>
          <p className="text-xl text-enhanced-muted mb-8 leading-relaxed">
            {description}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {buttons.map((button, index) => (
              <Button
                key={index}
                asChild
                variant={button.variant}
                size="lg"
                className="px-8 py-3"
              >
                <Link to={button.link}>
                  {button.text}
                </Link>
              </Button>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

PeripheralNerveCTASection.displayName = 'PeripheralNerveCTASection';

export default PeripheralNerveCTASection;
