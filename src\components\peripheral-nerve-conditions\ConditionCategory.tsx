import React from 'react';

import PeripheralNerveConditionsGrid from './ConditionsGrid';
import PeripheralNerveHeader from './PeripheralNerveHeader';

import type { PeripheralNerveCategoryData } from '@/data/peripheral-nerve-conditions/peripheralNerveConditionsData';

interface PeripheralNerveConditionCategoryProps {
  category: PeripheralNerveCategoryData;
  className?: string;
}

/**
 * PeripheralNerveConditionCategory Component
 * Complete category section with header and conditions grid
 * Follows the established pattern from spine conditions
 */
const PeripheralNerveConditionCategory: React.FC<PeripheralNerveConditionCategoryProps> = ({ 
  category, 
  className = "" 
}) => {
  return (
    <section className={`py-16 ${className}`}>
      <div className="container mx-auto px-4">
        <PeripheralNerveHeader category={category} />
        <PeripheralNerveConditionsGrid conditions={category.conditions} />
      </div>
    </section>
  );
};

PeripheralNerveConditionCategory.displayName = 'PeripheralNerveConditionCategory';

export default PeripheralNerveConditionCategory;
