import React from 'react';

import PeripheralNerveConditionCard from './ConditionCard';

import type { PeripheralNerveConditionData } from '@/data/peripheral-nerve-conditions/peripheralNerveConditionsData';

interface PeripheralNerveConditionsGridProps {
  conditions: PeripheralNerveConditionData[];
  className?: string;
}

/**
 * PeripheralNerveConditionsGrid Component
 * Grid layout for displaying peripheral nerve conditions
 * Follows the established pattern from spine conditions
 */
const PeripheralNerveConditionsGrid: React.FC<PeripheralNerveConditionsGridProps> = ({ 
  conditions, 
  className = "" 
}) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 ${className}`}>
      {conditions.map((condition) => (
        <PeripheralNerveConditionCard
          key={condition.id}
          condition={condition}
        />
      ))}
    </div>
  );
};

PeripheralNerveConditionsGrid.displayName = 'PeripheralNerveConditionsGrid';

export default PeripheralNerveConditionsGrid;
