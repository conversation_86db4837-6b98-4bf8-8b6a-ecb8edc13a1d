import React from 'react';

import SafeImage from '@/components/SafeImage';

interface PeripheralNerveIntroductionSectionProps {
  title: string;
  paragraphs: string[];
  image: {
    src: string;
    alt: string;
    caption: string;
  };
}

/**
 * PeripheralNerveIntroductionSection Component
 * Introduction section for the peripheral nerve conditions library
 * Follows the established pattern from spine conditions with nerve-specific content
 */
const PeripheralNerveIntroductionSection: React.FC<PeripheralNerveIntroductionSectionProps> = ({
  title,
  paragraphs,
  image
}) => {
  return (
    <section className="py-16 bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-4xl font-bold text-enhanced-heading mb-8">
              {title}
            </h2>
            <div className="space-y-6">
              {paragraphs.map((paragraph, index) => (
                <p key={index} className="text-lg text-enhanced-muted leading-relaxed">
                  {paragraph}
                </p>
              ))}
            </div>
          </div>
          <div className="relative">
            <SafeImage
              src={image.src}
              alt={image.alt}
              className="w-full h-96 object-cover rounded-xl shadow-lg"
              fallbackSrc="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent rounded-xl" />
            <div className="absolute bottom-4 left-4 right-4">
              <p className="text-primary-foreground text-sm bg-foreground/50 backdrop-blur-sm rounded-lg px-3 py-2">
                {image.caption}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

PeripheralNerveIntroductionSection.displayName = 'PeripheralNerveIntroductionSection';

export default PeripheralNerveIntroductionSection;
