import React from 'react';

import SafeImage from '@/components/SafeImage';
import type { PeripheralNerveCategoryData } from '@/data/peripheral-nerve-conditions/peripheralNerveConditionsData';

interface PeripheralNerveHeaderProps {
  category: PeripheralNerveCategoryData;
}

/**
 * PeripheralNerveHeader Component
 * Header section for each peripheral nerve category
 * Follows the established pattern from spine conditions with nerve-specific theming
 */
const PeripheralNerveHeader: React.FC<PeripheralNerveHeaderProps> = ({ category }) => {
  return (
    <div className="mb-12">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div>
          <h2 className="text-4xl font-bold text-enhanced-heading mb-6">
            {category.title}
          </h2>
          <p className="text-lg text-enhanced-muted leading-relaxed">
            {category.description}
          </p>
        </div>
        <div className="relative">
          <SafeImage
            src={category.anatomyImage.src}
            alt={category.anatomyImage.alt}
            className="w-full h-80 object-cover rounded-xl shadow-lg"
            fallbackSrc="/images/peripheral-nerve-conditions/default-nerve-anatomy.jpg"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent rounded-xl" />
        </div>
      </div>
    </div>
  );
};

PeripheralNerveHeader.displayName = 'PeripheralNerveHeader';

export default PeripheralNerveHeader;
