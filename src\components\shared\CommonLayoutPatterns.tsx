import React from 'react';

import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

/**
 * Common Layout Patterns
 * Extracts layout patterns found repeatedly throughout the codebase
 * Provides consistent spacing, responsive behavior, and device-aware styling
 */

export interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

/**
 * Responsive container with device-aware sizing
 * Replaces repeated container patterns found in large components
 */
export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className,
  size = 'lg'
}) => {
  const deviceInfo = useDeviceDetection();

  const sizeClasses = {
    sm: deviceInfo.isMobile ? 'mobile-container' : 'container max-w-2xl',
    md: deviceInfo.isMobile ? 'mobile-container' : 'container max-w-4xl',
    lg: deviceInfo.isMobile ? 'mobile-container' : 'container max-w-6xl',
    xl: deviceInfo.isMobile ? 'mobile-container' : 'container max-w-7xl',
    full: deviceInfo.isMobile ? 'mobile-container' : 'container',
  };

  return (
    <div className={cn(sizeClasses[size], className)}>
      {children}
    </div>
  );
};

export interface ResponsiveSectionProps {
  children: React.ReactNode;
  className?: string;
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  background?: 'default' | 'muted' | 'card';
  id?: string;
}

/**
 * Responsive section with device-aware spacing
 * Replaces repeated section patterns found throughout the codebase
 */
export const ResponsiveSection: React.FC<ResponsiveSectionProps> = ({
  children,
  className,
  spacing = 'md',
  background = 'default',
  id
}) => {
  const deviceInfo = useDeviceDetection();

  const spacingClasses = {
    none: '',
    sm: deviceInfo.isMobile ? 'mobile-section-sm' : 'py-8',
    md: deviceInfo.isMobile ? 'mobile-section' : 'py-16',
    lg: deviceInfo.isMobile ? 'mobile-section-lg' : 'py-24',
    xl: deviceInfo.isMobile ? 'mobile-section-xl' : 'py-32',
  };

  const backgroundClasses = {
    default: '',
    muted: 'bg-muted/30',
    card: 'bg-card',
  };

  return (
    <section
      id={id}
      className={cn(
        spacingClasses[spacing],
        backgroundClasses[background],
        className
      )}
    >
      {children}
    </section>
  );
};

export interface FlexLayoutProps {
  children: React.ReactNode;
  direction?: 'row' | 'col';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  wrap?: boolean;
  className?: string;
}

/**
 * Flexible layout component with responsive behavior
 */
export const FlexLayout: React.FC<FlexLayoutProps> = ({
  children,
  direction = 'row',
  align = 'start',
  justify = 'start',
  gap = 'md',
  wrap = false,
  className
}) => {
  const deviceInfo = useDeviceDetection();

  const directionClasses = {
    row: deviceInfo.isMobile ? 'flex-col' : 'flex-row',
    col: 'flex-col',
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
  };

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly',
  };

  const gapClasses = {
    none: 'gap-0',
    sm: deviceInfo.isMobile ? 'gap-mobile-sm' : 'gap-2',
    md: deviceInfo.isMobile ? 'gap-mobile-md' : 'gap-4',
    lg: deviceInfo.isMobile ? 'gap-mobile-lg' : 'gap-6',
    xl: deviceInfo.isMobile ? 'gap-mobile-xl' : 'gap-8',
  };

  return (
    <div className={cn(
      'flex',
      directionClasses[direction],
      alignClasses[align],
      justifyClasses[justify],
      gapClasses[gap],
      wrap && 'flex-wrap',
      className
    )}>
      {children}
    </div>
  );
};

export interface CardLayoutProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'ghost';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  className?: string;
  interactive?: boolean;
}

/**
 * Card layout with consistent styling and responsive behavior
 */
export const CardLayout: React.FC<CardLayoutProps> = ({
  children,
  variant = 'default',
  padding = 'md',
  className,
  interactive = false
}) => {
  const deviceInfo = useDeviceDetection();

  const variantClasses = {
    default: 'bg-card border border-border',
    elevated: 'bg-card shadow-lg border border-border',
    outlined: 'bg-transparent border-2 border-border',
    ghost: 'bg-transparent',
  };

  const paddingClasses = {
    none: '',
    sm: deviceInfo.isMobile ? 'p-mobile-sm' : 'p-3',
    md: deviceInfo.isMobile ? 'p-mobile-md' : 'p-6',
    lg: deviceInfo.isMobile ? 'p-mobile-lg' : 'p-8',
  };

  const interactiveClasses = interactive
    ? deviceInfo.isMobile
      ? 'touch-feedback'
      : 'hover:shadow-xl transition-all duration-300 hover:scale-105'
    : '';

  return (
    <div className={cn(
      'rounded-lg',
      variantClasses[variant],
      paddingClasses[padding],
      interactiveClasses,
      className
    )}>
      {children}
    </div>
  );
};

export interface AnimatedContentProps {
  children: React.ReactNode;
  animation?: 'fade' | 'slide' | 'scale' | 'none';
  delay?: number;
  className?: string;
}

/**
 * Animated content wrapper with consistent animation patterns
 */
export const AnimatedContent: React.FC<AnimatedContentProps> = ({
  children,
  animation = 'fade',
  delay = 0,
  className
}) => {
  const animationClasses = {
    fade: 'animate-fade-in',
    slide: 'animate-slide-in',
    scale: 'animate-scale-in',
    none: '',
  };

  return (
    <div 
      className={cn(animationClasses[animation], className)}
      style={{ animationDelay: `${delay}ms` }}
    >
      {children}
    </div>
  );
};

export interface CentredContentProps {
  children: React.ReactNode;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | 'none';
  className?: string;
}

/**
 * Centred content with responsive max-width
 */
export const CentredContent: React.FC<CentredContentProps> = ({
  children,
  maxWidth = 'lg',
  className
}) => {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    none: '',
  };

  return (
    <div className={cn(
      'mx-auto text-center',
      maxWidthClasses[maxWidth],
      className
    )}>
      {children}
    </div>
  );
};

export interface StackLayoutProps {
  children: React.ReactNode;
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

/**
 * Vertical stack layout with consistent spacing
 */
export const StackLayout: React.FC<StackLayoutProps> = ({
  children,
  spacing = 'md',
  className
}) => {
  const deviceInfo = useDeviceDetection();

  const spacingClasses = {
    none: 'space-y-0',
    sm: deviceInfo.isMobile ? 'space-y-mobile-sm' : 'space-y-2',
    md: deviceInfo.isMobile ? 'space-y-mobile-md' : 'space-y-4',
    lg: deviceInfo.isMobile ? 'space-y-mobile-lg' : 'space-y-6',
    xl: deviceInfo.isMobile ? 'space-y-mobile-xl' : 'space-y-8',
  };

  return (
    <div className={cn(spacingClasses[spacing], className)}>
      {children}
    </div>
  );
};

export default {
  ResponsiveContainer,
  ResponsiveSection,
  FlexLayout,
  CardLayout,
  AnimatedContent,
  CentredContent,
  StackLayout,
};
