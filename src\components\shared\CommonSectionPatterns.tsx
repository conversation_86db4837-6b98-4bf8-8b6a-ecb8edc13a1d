import React from 'react';
import { <PERSON> } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

/**
 * Common Section Patterns
 * Reusable section components that capture patterns found throughout the codebase
 * These replace repetitive section structures in large components
 */

interface SectionHeaderProps {
  subtitle?: string;
  title: string;
  description?: string;
  className?: string;
  centered?: boolean;
}

/**
 * Standard section header pattern found in many components
 */
const SectionHeader: React.FC<SectionHeaderProps> = ({
  subtitle,
  title,
  description,
  className,
  centered = true,
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <div className={cn(
      'mb-mobile-xl',
      deviceInfo.isMobile ? 'max-w-full' : 'max-w-3xl mb-12',
      centered && 'text-center mx-auto',
      className
    )}>
      {subtitle && (
        <span className={cn(
          'text-primary font-medium uppercase tracking-wider',
          deviceInfo.isMobile ? 'mobile-text' : 'text-sm'
        )}>
          {subtitle}
        </span>
      )}
      <h2 className={cn(
        'font-bold mt-2 mb-mobile-md',
        deviceInfo.isMobile
          ? 'mobile-3xl'
          : 'text-3xl md:text-4xl mb-4'
      )}>
        {title}
      </h2>
      {description && (
        <p className={cn(
          'text-muted-foreground',
          deviceInfo.isMobile ? 'mobile-text' : ''
        )}>
          {description}
        </p>
      )}
    </div>
  );
};

interface ServiceCardProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  link?: string;
  linkText?: string;
  animationDelay?: string;
  gradientColors?: string;
  className?: string;
}

/**
 * Service card pattern found in homepage and other service sections
 */
const ServiceCard: React.FC<ServiceCardProps> = ({
  title,
  description,
  icon,
  link,
  linkText = 'Learn More',
  animationDelay = '0ms',
  gradientColors = 'bg-info-light border border-info/30',
  className,
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <div 
      className={cn(
        'card rounded-lg shadow-md bg-background animate-fade-in transition-all duration-300',
        deviceInfo.isMobile
          ? 'p-mobile-lg touch-feedback'
          : 'p-6 hover:shadow-xl hover:scale-105',
        className
      )} 
      style={{ animationDelay }}
    >
      {icon && (
        <div className={cn(
          'flex justify-center',
          deviceInfo.isMobile ? 'mb-mobile-md' : 'mb-4'
        )}>
          <div className={cn(
            `rounded-2xl ${gradientColors} flex items-center justify-center shadow-lg backdrop-blur-sm`,
            deviceInfo.isMobile ? 'w-16 h-16' : 'w-20 h-20'
          )}>
            {icon}
          </div>
        </div>
      )}
      <h3 className={cn(
        'font-semibold text-center mb-mobile-sm',
        deviceInfo.isMobile
          ? 'mobile-subheading'
          : 'text-xl mb-3'
      )}>
        {title}
      </h3>
      <p className={cn(
        'text-muted-foreground text-center',
        deviceInfo.isMobile ? 'mobile-text' : ''
      )}>
        {description}
      </p>
      {link && (
        <div className={cn(
          'text-center',
          deviceInfo.isMobile ? 'mt-mobile-md' : 'mt-4'
        )}>
          <Link
            to={link}
            className={cn(
              'text-primary touch-feedback',
              deviceInfo.isMobile ? '' : 'hover:underline'
            )}
          >
            {linkText}
          </Link>
        </div>
      )}
    </div>
  );
};

interface TwoColumnLayoutProps {
  leftContent: React.ReactNode;
  rightContent: React.ReactNode;
  reverseOnMobile?: boolean;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Two-column layout pattern found in many sections
 */
const TwoColumnLayout: React.FC<TwoColumnLayoutProps> = ({
  leftContent,
  rightContent,
  reverseOnMobile = false,
  gap = 'lg',
  className,
}) => {
  const deviceInfo = useDeviceDetection();

  const gapClasses = {
    sm: deviceInfo.isMobile ? 'gap-mobile-md' : 'gap-6',
    md: deviceInfo.isMobile ? 'gap-mobile-lg' : 'gap-8',
    lg: deviceInfo.isMobile ? 'gap-mobile-xl' : 'gap-12',
  };

  return (
    <div className={cn(
      'items-center',
      deviceInfo.isMobile
        ? `grid grid-cols-1 ${gapClasses[gap]}`
        : `grid grid-cols-1 lg:grid-cols-2 ${gapClasses[gap]}`,
      className
    )}>
      <div className={cn(
        'animate-fade-in [animation-delay:100ms]',
        reverseOnMobile && deviceInfo.isMobile && 'order-2'
      )}>
        {leftContent}
      </div>
      <div className={cn(
        'relative rounded-lg overflow-hidden shadow-xl',
        reverseOnMobile && deviceInfo.isMobile && 'order-1'
      )}>
        {rightContent}
      </div>
    </div>
  );
};

interface GridLayoutProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Responsive grid layout pattern
 */
const GridLayout: React.FC<GridLayoutProps> = ({
  children,
  columns = 4,
  gap = 'lg',
  className,
}) => {
  const deviceInfo = useDeviceDetection();

  const gapClasses = {
    sm: deviceInfo.isMobile ? 'gap-mobile-md' : 'gap-4',
    md: deviceInfo.isMobile ? 'gap-mobile-lg' : 'gap-6',
    lg: deviceInfo.isMobile ? 'gap-mobile-xl' : 'gap-8',
  };

  const columnClasses = {
    1: 'grid-cols-1',
    2: deviceInfo.isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2',
    3: deviceInfo.isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: deviceInfo.isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };

  return (
    <div className={cn(
      'grid',
      columnClasses[columns],
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  );
};

interface CallToActionProps {
  title: string;
  description?: string;
  primaryAction?: {
    text: string;
    href: string;
    external?: boolean;
  };
  secondaryAction?: {
    text: string;
    href: string;
    external?: boolean;
  };
  className?: string;
}

/**
 * Call-to-action section pattern
 */
const CallToAction: React.FC<CallToActionProps> = ({
  title,
  description,
  primaryAction,
  secondaryAction,
  className,
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <div className={cn(
      'text-center',
      deviceInfo.isMobile ? 'py-mobile-xl' : 'py-16',
      className
    )}>
      <h2 className={cn(
        'font-bold mb-mobile-md',
        deviceInfo.isMobile
          ? 'mobile-3xl'
          : 'text-3xl md:text-4xl mb-4'
      )}>
        {title}
      </h2>
      {description && (
        <p className={cn(
          'text-muted-foreground mb-mobile-lg',
          deviceInfo.isMobile ? 'mobile-text' : 'text-lg mb-8'
        )}>
          {description}
        </p>
      )}
      <div className={cn(
        'flex gap-4 justify-center',
        deviceInfo.isMobile ? 'flex-col' : 'flex-row'
      )}>
        {primaryAction && (
          <Button asChild size={deviceInfo.isMobile ? 'lg' : 'default'}>
            {primaryAction.external ? (
              <a 
                href={primaryAction.href}
                target="_blank"
                rel="noopener noreferrer"
              >
                {primaryAction.text}
              </a>
            ) : (
              <Link to={primaryAction.href}>
                {primaryAction.text}
              </Link>
            )}
          </Button>
        )}
        {secondaryAction && (
          <Button 
            variant="outline" 
            asChild 
            size={deviceInfo.isMobile ? 'lg' : 'default'}
          >
            {secondaryAction.external ? (
              <a 
                href={secondaryAction.href}
                target="_blank"
                rel="noopener noreferrer"
              >
                {secondaryAction.text}
              </a>
            ) : (
              <Link to={secondaryAction.href}>
                {secondaryAction.text}
              </Link>
            )}
          </Button>
        )}
      </div>
    </div>
  );
};

// Export all components and types as named exports for better Fast Refresh compatibility
export {
  SectionHeader,
  ServiceCard,
  TwoColumnLayout,
  GridLayout,
  CallToAction,
};

export type {
  SectionHeaderProps,
  ServiceCardProps,
  TwoColumnLayoutProps,
  GridLayoutProps,
  CallToActionProps,
};
