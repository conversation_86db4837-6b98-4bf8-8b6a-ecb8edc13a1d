import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';

interface CTASectionProps {
  title: string;
  description: string;
  buttons: {
    text: string;
    link: string;
    variant: 'default' | 'outline';
  }[];
}

/**
 * CTASection Component
 * Call-to-action section with buttons
 * Preserves exact styling and functionality from original implementation
 */
const CTASection: React.FC<CTASectionProps> = ({
  title,
  description,
  buttons
}) => {
  return (
    <section className="py-24 bg-gradient-to-br from-primary/10 via-primary/5 to-background border-y border-border/50">
      <div className="container">
        <div className="text-centre max-w-5xl mx-auto">
          <h2 className="text-enhanced-heading text-3xl lg:text-enhanced-heading text-4xl font-bold mb-8 text-foreground">{title}</h2>
          <p className="text-foreground/80 mb-12 text-lg leading-relaxed font-medium">
            {description}
          </p>
          <div className="flex flex-col sm:flex-row justify-centre gap-6">
            {buttons.map((button, index) => (
              <Button
                key={index}
                asChild
                variant={button.variant}
                size="lg"
                className="font-bold px-8 py-4 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105 border-2"
              >
                <Link to={button.link}>{button.text}</Link>
              </Button>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

CTASection.displayName = 'CTASection';

export default CTASection;
