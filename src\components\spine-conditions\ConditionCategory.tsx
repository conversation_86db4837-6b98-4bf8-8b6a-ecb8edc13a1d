import React from 'react';

import ConditionsGrid from './ConditionsGrid';
import SpineRegionHeader from './SpineRegionHeader';

import type { SpineRegionData } from '@/data/spine-conditions/spineConditionsData';

interface ConditionCategoryProps {
  region: SpineRegionData;
}

/**
 * ConditionCategory Component
 * Complete tab content for a spine region
 * Preserves exact styling and structure from original implementation
 */
const ConditionCategory: React.FC<ConditionCategoryProps> = ({ region }) => {
  return (
    <div className="medical-card/90 backdrop-blur-sm p-10 rounded-xl shadow-xl border border-border/50 hover:shadow-xl transition-all duration-300">
      <SpineRegionHeader
        title={region.title}
        description={region.description}
        anatomyImage={region.anatomyImage}
      />
      <ConditionsGrid conditions={region.conditions} />
    </div>
  );
};

ConditionCategory.displayName = 'ConditionCategory';

export default ConditionCategory;
