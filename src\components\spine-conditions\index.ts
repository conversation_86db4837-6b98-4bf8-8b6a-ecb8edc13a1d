/**
 * Spine Conditions Components Barrel Export
 * Provides clean imports for all spine conditions related components
 */

export { default as ConditionCard } from './ConditionCard';
export { default as ConditionsGrid } from './ConditionsGrid';
export { default as SpineRegionHeader } from './SpineRegionHeader';
export { default as ConditionCategory } from './ConditionCategory';
export { default as IntroductionSection } from './IntroductionSection';
export { default as SpineAnatomySection } from './SpineAnatomySection';
export { default as CTASection } from './CTASection';

// Re-export types for convenience
export type {
  ConditionData,
  SpineRegionData,
  SpineConditionsPageData
} from '@/data/spine-conditions/spineConditionsData';
