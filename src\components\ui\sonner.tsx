import React, { useEffect, useState } from "react";
import { Toaster as Sonner } from "sonner";

type ToasterProps = React.ComponentProps<typeof Sonner>;

const Toaster = ({ ...props }: ToasterProps) => {
  const [theme, setTheme] = useState<"light" | "dark" | "system">("system");

  useEffect(() => {
    // Listen for theme changes
    const handleThemeChange = () => {
      try {
        const savedTheme = localStorage.getItem("theme");
        if (savedTheme === "dark" || savedTheme === "light") {
          setTheme(savedTheme);
        } else {
          const isDark = window.matchMedia("(prefers-colour-scheme: dark)").matches;
          setTheme(isDark ? "dark" : "light");
        }
      } catch {
        // Fallback to system preference if localStorage is not available
        const isDark = window.matchMedia("(prefers-colour-scheme: dark)").matches;
        setTheme(isDark ? "dark" : "light");
      }
    };

    // Check for saved theme preference
    try {
      const savedTheme = localStorage.getItem("theme");
      if (savedTheme === "dark" || savedTheme === "light") {
        setTheme(savedTheme);
      } else {
        // Check system preference
        const isDark = window.matchMedia("(prefers-colour-scheme: dark)").matches;
        setTheme(isDark ? "dark" : "light");
      }
    } catch {
      // Fallback to system preference if localStorage is not available
      const isDark = window.matchMedia("(prefers-colour-scheme: dark)").matches;
      setTheme(isDark ? "dark" : "light");
    }

    // Listen for storage changes (theme changes from other tabs)
    window.addEventListener("storage", handleThemeChange);

    // Listen for system theme changes
    const mediaQuery = window.matchMedia("(prefers-colour-scheme: dark)");
    mediaQuery.addEventListener("change", handleThemeChange);

    return () => {
      window.removeEventListener("storage", handleThemeChange);
      mediaQuery.removeEventListener("change", handleThemeChange);
    };
  }, []);

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
          description: "group-[.toast]:text-muted-foreground",
          actionButton:
            "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"
        }
      }}
      {...props}
    />
  );
};

export { Toaster };