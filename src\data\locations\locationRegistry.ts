/**
 * Location Data Registry
 * Centralized registry for all location data and configurations
 * Provides a single source of truth for location management
 */

import { LocationSpecialSections } from '@/components/locations/GenericLocationTemplate';
import { LocationConfig, locationConfigs } from '@/lib/location-config';
import { CompleteLocationData } from '@/types/location';

export interface LocationDataModule {
  locationData: CompleteLocationData;
  specialSections?: LocationSpecialSections;
  config: LocationConfig;
}

/**
 * Location data loader function type
 */
export type LocationDataLoader = () => Promise<{
  locationData: CompleteLocationData;
  specialSections?: LocationSpecialSections;
}>;

/**
 * Registry of location data loaders
 * Each location has a lazy loader to optimise bundle size
 */
export const locationDataLoaders: Record<string, LocationDataLoader> = {
  sunbury: async () => {
    const module = await import('./sunburyData');
    return {
      locationData: module.sunburyLocationData,
      specialSections: module.sunburySpecialSections,
    };
  },

  heidelberg: async () => {
    const module = await import('./heidelbergData');
    return {
      locationData: module.heidelbergLocationData,
      specialSections: module.heidelbergSpecialSections,
    };
  },

  frankston: async () => {
    const module = await import('./frankstonData');
    return {
      locationData: module.frankstonLocationData,
      specialSections: module.frankstonSpecialSections || null,
    };
  },

  bundoora: async () => {
    const module = await import('./bundooraData');
    return {
      locationData: module.bundooraLocationData,
      specialSections: module.bundooraSpecialSections || null,
    };
  },

  dandenong: async () => {
    const module = await import('./dandenongData');
    return {
      locationData: module.dandenongLocationData,
      specialSections: module.dandenongSpecialSections || null,
    };
  },

  langwarrin: async () => {
    const module = await import('./langwarrinData');
    return {
      locationData: module.langwarrinLocationData,
      specialSections: module.langwarrinSpecialSections || null,
    };
  },

  'moonee-ponds': async () => {
    const module = await import('./mooneePondsData');
    return {
      locationData: module.mooneePondsLocationData,
      specialSections: module.mooneePondsSpecialSections || null,
    };
  },

  mornington: async () => {
    const module = await import('./morningtonData');
    return {
      locationData: module.morningtonLocationData,
      specialSections: module.morningtonSpecialSections || null,
    };
  },

  'surrey-hills': async () => {
    const module = await import('./surreyHillsData');
    return {
      locationData: module.surreyHillsLocationData,
      specialSections: module.surreyHillsSpecialSections || null,
    };
  },

  wantirna: async () => {
    const module = await import('./wantirnaData');
    return {
      locationData: module.wantirnaLocationData,
      specialSections: module.wantirnaSpecialSections || null,
    };
  },

  werribee: async () => {
    const module = await import('./werribeeData');
    return {
      locationData: module.werribeeLocationData,
      specialSections: module.werribeeSpecialSections || null,
    };
  },
};

/**
 * Get all available location slugs
 */
export const getAvailableLocationSlugs = (): string[] => {
  return Object.keys(locationDataLoaders);
};

/**
 * Check if a location slug is valid
 */
export const isValidLocationSlug = (slug: string): boolean => {
  return slug in locationDataLoaders;
};

/**
 * Load location data by slug
 */
export const loadLocationData = async (slug: string): Promise<LocationDataModule | null> => {
  if (!isValidLocationSlug(slug)) {
    return null;
  }

  try {
    const loader = locationDataLoaders[slug];
    const data = await loader();
    const config = locationConfigs[slug];

    return {
      locationData: data.locationData,
      specialSections: data.specialSections,
      config,
    };
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error(`Failed to load location data for ${slug}:`, error);
    }
    return null;
  }
};

/**
 * Preload location data for better performance
 */
export const preloadLocationData = async (slugs: string[]): Promise<void> => {
  const promises = slugs
    .filter(isValidLocationSlug)
    .map(slug => loadLocationData(slug));

  try {
    await Promise.all(promises);
  } catch (error) {
    if (import.meta.env.DEV) {
      console.warn('Some location data failed to preload:', error);
    }
  }
};

/**
 * Get location summary information without loading full data
 */
export const getLocationSummary = (slug: string) => {
  const config = locationConfigs[slug];
  if (!config) return null;

  return {
    id: config.id,
    name: config.name,
    slug: config.slug,
    layoutVariant: config.layoutVariant,
    hasTherapeuticInterventions: config.hasTherapeuticInterventions,
    hasAmenities: config.hasAmenities,
    hasConditionsTreated: config.hasConditionsTreated,
    hasInsuranceAndFunding: config.hasInsuranceAndFunding,
    hasPatientsPrivacy: config.hasPatientsPrivacy,
  };
};

/**
 * Get all location summaries
 */
export const getAllLocationSummaries = () => {
  return getAvailableLocationSlugs().map(getLocationSummary).filter(Boolean);
};

/**
 * Location data validation
 */
export const validateLocationData = (data: CompleteLocationData): {
  isValid: boolean;
  missingFields: string[];
  warnings: string[];
} => {
  const missingFields: string[] = [];
  const warnings: string[] = [];

  // Required fields
  const requiredFields = [
    'hero.title',
    'hero.subtitle',
    'hero.introduction1',
    'contact.address.street',
    'contact.address.suburb',
    'contact.phone',
    'contact.email',
    'map.embedUrl',
    'facilities.title',
    'nearbyAmenities.title',
    'nearbyHospitals.title',
    'cta.title',
  ];

  for (const field of requiredFields) {
    const value = field.split('.').reduce((obj: unknown, key: string) =>
      (obj as Record<string, unknown>)?.[key], data);
    if (!value) {
      missingFields.push(field);
    }
  }

  // Optional but recommended fields
  const recommendedFields = [
    'hero.introduction2',
    'hero.imageUrl',
    'facilities.gallery',
    'therapeuticInterventions',
  ];

  for (const field of recommendedFields) {
    const value = field.split('.').reduce((obj: unknown, key: string) =>
      (obj as Record<string, unknown>)?.[key], data);
    if (!value) {
      warnings.push(`Recommended field missing: ${field}`);
    }
  }

  return {
    isValid: missingFields.length === 0,
    missingFields,
    warnings,
  };
};

export default {
  locationDataLoaders,
  getAvailableLocationSlugs,
  isValidLocationSlug,
  loadLocationData,
  preloadLocationData,
  getLocationSummary,
  getAllLocationSummaries,
  validateLocationData,
};
