export interface NavSubmenuItem {
  name: string;
  path: string;
}

export interface NavLink {
  name: string;
  path: string;
  submenu?: NavSubmenuItem[];
}

export interface NavigationConfig {
  mainLinks: NavLink[];
  ctaButton: {
    text: string;
    path: string;
  };
}

interface TranslationObject {
  nav?: Record<string, string>;
  patientResources?: {
    categories?: Record<string, string>;
  };
}

export const getNavigationData = (t?: TranslationObject): NavigationConfig => {
  // Safe fallback for translations - use correct translation structure
  const finalT = (t && t.navigation && t.patientResources) ? t : {
    navigation: {
      home: "Home",
      expertise: "Expertise",
      about: "About",
      locations: "Locations",
      patientResources: "Patient Resources",
      contact: "Contact",
      bookAppointment: "Book Appointment",
      language: "Language",
      menu: "Menu",
      close: "Close",
      skipToContent: "Skip to Content"
    },
    // Add expertise submenu structure for compatibility
    expertise: {
      title: "Expertise",
      subtitle: "Specialized Care",
      brainSurgery: {
        title: "Brain Surgery",
        description: "Expert brain surgery"
      },
      spineSurgery: {
        title: "Spine Surgery",
        description: "Expert spine surgery"
      },
      peripheralNerve: {
        title: "Nerve Surgery",
        description: "Expert nerve surgery"
      },
      viewAll: "View All",
      learnMore: "Learn More"
    },
    patientResources: {
      title: "Patient Resources",
      subtitle: "Helpful Information",
      description: "Resources for patients",
      learnMore: "Learn More"
    },
    location: {
      title: "Locations",
      subtitle: "Find Us",
      description: "Our clinic locations",
      address: "Address",
      phone: "Phone",
      hours: "Hours",
      directions: "Directions"
    }
  };

  return {
    mainLinks: [
      { name: finalT.navigation.home, path: "/" },
      {
        name: finalT.navigation.expertise,
        path: "/expertise",
        submenu: [
          { name: "Cervical Disc Replacement", path: "/expertise/cervical-disc-replacement" },
          { name: "Lumbar Disc Replacement", path: "/expertise/lumbar-disc-replacement" },
          { name: "Image-Guided Surgery", path: "/expertise/image-guided-surgery" },
          { name: "Robotic Spine Surgery", path: "/expertise/robotic-spine-surgery" },
        ]
      },

      {
        name: finalT.patientResources.title,
        path: "/patient-resources",
        submenu: [
          { name: "Brain Conditions", path: "/patient-resources/brain-conditions" },
          { name: "Spine Conditions", path: "/patient-resources/spine-conditions" },
          { name: "Peripheral Nerve Conditions", path: "/patient-resources/peripheral-nerve-conditions" },
          { name: "Exercise Library", path: "/patient-resources/exercise-library" },
          { name: "Brain and Spine Health", path: "/patient-resources/brain-and-spine-health" },
          { name: "FAQ", path: "/faq" },
        ]
      },
      {
        name: "GP Resources",
        path: "/gp-resources",
        submenu: [
          { name: "Referral & Triage Protocols", path: "/gp-resources/referral-protocols" },
          { name: "Neurosurgical Diagnostics", path: "/gp-resources/diagnostics" },
          { name: "Multidisciplinary Care", path: "/gp-resources/care-coordination" },
          { name: "Emergencies & Red Flags", path: "/gp-resources/emergencies" },
        ]
      },
      { name: "Medico-Legal", path: "/medicolegal" },
      {
        name: finalT.navigation.locations,
        path: "/locations",
        submenu: [
          { name: "Mornington", path: "/locations/mornington" },
          { name: "Frankston", path: "/locations/frankston" },
          { name: "Langwarrin", path: "/locations/langwarrin" },
          { name: "Moonee Ponds", path: "/locations/moonee-ponds" },
          { name: "Sunbury", path: "/locations/sunbury" },
          { name: "Werribee", path: "/locations/werribee" },
          { name: "Bundoora", path: "/locations/bundoora" },
          { name: "Dandenong", path: "/locations/dandenong" },
          { name: "Heidelberg", path: "/locations/heidelberg" },
          { name: "Wantirna", path: "/locations/wantirna" },
          { name: "Surrey Hills", path: "/locations/surrey-hills" },
        ]
      },
      { name: finalT.navigation.contact, path: "/contact" }
    ],
    ctaButton: {
      text: finalT.navigation.bookAppointment,
      path: "/appointments"
    }
  };
};
