import { useCallback, useEffect, useState } from 'react';

import { useTimeout } from './useTimeout';

/**
 * Custom hook for homepage navigation and scroll management
 * Extracts navigation logic for better separation of concerns
 * Provides scroll-to-top, section navigation, and scroll tracking
 */
export const useHomepageNavigation = () => {
  const [scrollY, setScrollY] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const [activeSection, setActiveSection] = useState<string>('hero');
  const { setTimer, clearTimer } = useTimeout();

  // Handle scroll events
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);
      setIsScrolling(true);

      // Clear existing timer and set new one
      clearTimer();
      setTimer(() => {
        setIsScrolling(false);
      }, 150);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimer();
    };
  }, [setTimer, clearTimer]);

  // Scroll to top function
  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }, []);

  // Scroll to specific section
  const scrollToSection = useCallback((sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerOffset = 80; // Account for fixed header
      const elementPosition = element.offsetTop;
      const offsetPosition = elementPosition - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  }, []);

  // Track active section based on scroll position
  useEffect(() => {
    const sections = [
      'hero',
      'welcome',
      'services',
      'expertise',
      'minimally-invasive',
      'when-less-is-more',
      'arthroplasty',
      'image-guided',
      'treatment-decisions',
      'reviews',
      'appointments',
      'feedback'
    ];

    const handleSectionTracking = () => {
      const scrollPosition = window.scrollY + 100; // Offset for header

      for (let i = sections.length - 1; i >= 0; i--) {
        const section = document.getElementById(sections[i]);
        if (section && section.offsetTop <= scrollPosition) {
          setActiveSection(sections[i]);
          break;
        }
      }
    };

    window.addEventListener('scroll', handleSectionTracking, { passive: true });
    
    // Initial check
    handleSectionTracking();

    return () => {
      window.removeEventListener('scroll', handleSectionTracking);
    };
  }, []);

  // Calculate parallax offset for hero background
  const getParallaxOffset = useCallback((factor: number = 0.5) => {
    return scrollY * factor;
  }, [scrollY]);

  // Check if user has scrolled past hero section
  const isScrolledPastHero = useCallback(() => {
    return scrollY > window.innerHeight * 0.8;
  }, [scrollY]);

  // Get scroll progress as percentage
  const getScrollProgress = useCallback(() => {
    const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
    return documentHeight > 0 ? (scrollY / documentHeight) * 100 : 0;
  }, [scrollY]);

  // Smooth scroll with custom easing
  const smoothScrollTo = useCallback((targetY: number, duration: number = 1000) => {
    const startY = window.scrollY;
    const distance = targetY - startY;
    const startTime = performance.now();

    const easeInOutCubic = (t: number): number => {
      return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    };

    const animateScroll = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easedProgress = easeInOutCubic(progress);
      
      window.scrollTo(0, startY + distance * easedProgress);

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      }
    };

    requestAnimationFrame(animateScroll);
  }, []);

  // Navigation helpers for homepage sections
  const navigationHelpers = {
    goToHero: () => scrollToSection('hero'),
    goToWelcome: () => scrollToSection('welcome'),
    goToServices: () => scrollToSection('services'),
    goToExpertise: () => scrollToSection('expertise'),
    goToAppointments: () => scrollToSection('appointments'),
    goToContact: () => scrollToSection('contact'),
  };

  return {
    // Scroll state
    scrollY,
    isScrolling,
    activeSection,
    
    // Navigation functions
    scrollToTop,
    scrollToSection,
    smoothScrollTo,
    
    // Utility functions
    getParallaxOffset,
    isScrolledPastHero,
    getScrollProgress,
    
    // Navigation helpers
    ...navigationHelpers,
  };
};

/**
 * Hook for managing homepage section visibility
 * Tracks which sections are currently visible in viewport
 */
export const useHomepageSectionVisibility = () => {
  const [visibleSections, setVisibleSections] = useState<Set<string>>(new Set());

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        setVisibleSections(prev => {
          const newVisible = new Set(prev);
          
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              newVisible.add(entry.target.id);
            } else {
              newVisible.delete(entry.target.id);
            }
          });
          
          return newVisible;
        });
      },
      {
        threshold: 0.1, // Trigger when 10% of section is visible
        rootMargin: '-50px 0px', // Account for header
      }
    );

    // Observe all sections
    const sections = document.querySelectorAll('section[id]');
    sections.forEach(section => observer.observe(section));

    return () => {
      sections.forEach(section => observer.unobserve(section));
    };
  }, []);

  const isSectionVisible = useCallback((sectionId: string) => {
    return visibleSections.has(sectionId);
  }, [visibleSections]);

  return {
    visibleSections,
    isSectionVisible,
  };
};

/**
 * Hook for homepage performance monitoring
 * Tracks scroll performance and provides optimization hints
 */
export const useHomepagePerformance = () => {
  const [scrollPerformance, setScrollPerformance] = useState({
    averageFrameTime: 0,
    droppedFrames: 0,
    isOptimal: true,
  });

  useEffect(() => {
    let frameCount = 0;
    let totalFrameTime = 0;
    let lastFrameTime = performance.now();
    let droppedFrames = 0;

    const measurePerformance = () => {
      const currentTime = performance.now();
      const frameTime = currentTime - lastFrameTime;
      
      frameCount++;
      totalFrameTime += frameTime;
      
      // Consider frame dropped if it takes longer than 16.67ms (60fps)
      if (frameTime > 16.67) {
        droppedFrames++;
      }

      // Update performance metrics every 60 frames
      if (frameCount % 60 === 0) {
        const averageFrameTime = totalFrameTime / frameCount;
        const isOptimal = droppedFrames / frameCount < 0.1; // Less than 10% dropped frames

        setScrollPerformance({
          averageFrameTime,
          droppedFrames,
          isOptimal,
        });
      }

      lastFrameTime = currentTime;
      requestAnimationFrame(measurePerformance);
    };

    const animationId = requestAnimationFrame(measurePerformance);

    return () => {
      cancelAnimationFrame(animationId);
    };
  }, []);

  return scrollPerformance;
};

export default useHomepageNavigation;
