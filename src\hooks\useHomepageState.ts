import { useCallback, useEffect, useMemo, useState } from 'react';

import { useHomepageData } from './useHomepageData';
import { useHomepageNavigation } from './useHomepageNavigation';
import { useHomepageTranslations } from './useHomepageTranslations';

import { useDeviceDetection } from '@/contexts/DeviceContext';
import { logger } from '@/lib/logger';
import { generatePageSEO, generateMedicalPracticeStructuredData, PRACTICE_DATA } from '@/lib/seo';

/**
 * Comprehensive homepage state management hook
 * Combines all homepage-related state and logic for better organization
 * Provides a single source of truth for homepage functionality
 */
export const useHomepageState = () => {
  // Core hooks
  const deviceInfo = useDeviceDetection();
  const homepageData = useHomepageData();
  const translations = useHomepageTranslations();
  const navigation = useHomepageNavigation();

  // Local state
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // SEO data generation
  const seoData = useMemo(() => {
    try {
      return generatePageSEO('home', {
        structuredData: generateMedicalPracticeStructuredData(PRACTICE_DATA)
      });
    } catch (error) {
      logger.error('Failed to generate homepage SEO data', { error });
      setHasError(true);
      setErrorMessage('Failed to load page metadata');
      return null;
    }
  }, []);

  // Initialize homepage
  useEffect(() => {
    const initializeHomepage = async () => {
      try {
        setIsLoading(true);
        
        // Scroll to top when component mounts
        window.scrollTo(0, 0);
        
        // Log homepage visit
        logger.info('Homepage loaded', {
          device: deviceInfo.isMobile ? 'mobile' : 'desktop',
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
        });

        // Simulate any async initialization if needed
        await new Promise(resolve => setTimeout(resolve, 100));
        
        setIsLoading(false);
      } catch (error) {
        logger.error('Homepage initialization failed', { error });
        setHasError(true);
        setErrorMessage('Failed to initialize homepage');
        setIsLoading(false);
      }
    };

    initializeHomepage();
  }, [deviceInfo.isMobile]);

  // Error recovery
  const retryInitialization = useCallback(() => {
    setHasError(false);
    setErrorMessage('');
    setIsLoading(true);
    
    // Trigger re-initialization
    window.location.reload();
  }, []);

  // Section data with error handling
  const getSectionData = useCallback((sectionName: keyof typeof homepageData) => {
    try {
      return homepageData[sectionName];
    } catch (error) {
      logger.error(`Failed to get section data: ${sectionName}`, { error });
      return null;
    }
  }, [homepageData]);

  // Translation helpers with error handling
  const getTranslationSafe = useCallback((path: string, fallback: string = '') => {
    try {
      return translations.getTranslation(path, fallback);
    } catch (error) {
      logger.error(`Translation failed for path: ${path}`, { error });
      return fallback;
    }
  }, [translations]);

  // Performance monitoring
  const [performanceMetrics, setPerformanceMetrics] = useState({
    loadTime: 0,
    renderTime: 0,
    interactionTime: 0,
  });

  useEffect(() => {
    const startTime = performance.now();
    
    // Measure load time
    const handleLoad = () => {
      const loadTime = performance.now() - startTime;
      setPerformanceMetrics(prev => ({ ...prev, loadTime }));
      
      logger.performance('Homepage load time', loadTime, 'ms', {
        device: deviceInfo.isMobile ? 'mobile' : 'desktop',
      });
    };

    // Measure first interaction
    const handleFirstInteraction = () => {
      const interactionTime = performance.now() - startTime;
      setPerformanceMetrics(prev => ({ ...prev, interactionTime }));
      
      logger.performance('Homepage first interaction', interactionTime, 'ms');
      
      // Remove listener after first interaction
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('touchstart', handleFirstInteraction);
    };

    window.addEventListener('load', handleLoad);
    document.addEventListener('click', handleFirstInteraction);
    document.addEventListener('touchstart', handleFirstInteraction);

    return () => {
      window.removeEventListener('load', handleLoad);
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('touchstart', handleFirstInteraction);
    };
  }, [deviceInfo.isMobile]);

  // Accessibility helpers
  const accessibilityHelpers = useMemo(() => ({
    // Skip to main content
    skipToMain: () => {
      const mainContent = document.getElementById('main-content');
      if (mainContent) {
        mainContent.focus();
        mainContent.scrollIntoView({ behavior: 'smooth' });
      }
    },
    
    // Announce section change to screen readers
    announceSection: (sectionName: string) => {
      const announcement = `Navigated to ${sectionName} section`;
      const announcer = document.createElement('div');
      announcer.setAttribute('aria-live', 'polite');
      announcer.setAttribute('aria-atomic', 'true');
      announcer.className = 'sr-only';
      announcer.textContent = announcement;
      document.body.appendChild(announcer);
      
      setTimeout(() => {
        document.body.removeChild(announcer);
      }, 1000);
    },
  }), []);

  // User interaction tracking
  const trackUserInteraction = useCallback((action: string, element: string, context?: Record<string, unknown>) => {
    logger.userAction(action, element, {
      page: 'homepage',
      device: deviceInfo.isMobile ? 'mobile' : 'desktop',
      scrollPosition: navigation.scrollY,
      activeSection: navigation.activeSection,
      ...context,
    });
  }, [deviceInfo.isMobile, navigation.scrollY, navigation.activeSection]);

  // Section visibility tracking
  const trackSectionVisibility = useCallback((sectionId: string, isVisible: boolean) => {
    if (isVisible) {
      logger.info(`Section viewed: ${sectionId}`, {
        page: 'homepage',
        device: deviceInfo.isMobile ? 'mobile' : 'desktop',
        scrollPosition: navigation.scrollY,
      });
    }
  }, [deviceInfo.isMobile, navigation.scrollY]);

  return {
    // State
    isLoading,
    hasError,
    errorMessage,
    performanceMetrics,
    
    // Core data
    deviceInfo,
    homepageData,
    translations,
    navigation,
    seoData,
    
    // Helper functions
    getSectionData,
    getTranslationSafe,
    retryInitialization,
    trackUserInteraction,
    trackSectionVisibility,
    
    // Accessibility
    accessibility: accessibilityHelpers,
    
    // Computed values
    isReady: !isLoading && !hasError,
    shouldShowError: hasError && !isLoading,
  };
};

/**
 * Hook for managing homepage section states
 * Tracks loading, error, and interaction states for each section
 */
export const useHomepageSectionStates = () => {
  const [sectionStates, setSectionStates] = useState<Record<string, {
    isLoading: boolean;
    hasError: boolean;
    isInteracted: boolean;
    lastViewed: Date | null;
  }>>({});

  const updateSectionState = useCallback((
    sectionId: string, 
    updates: Partial<typeof sectionStates[string]>
  ) => {
    setSectionStates(prev => ({
      ...prev,
      [sectionId]: {
        ...prev[sectionId],
        ...updates,
      }
    }));
  }, []);

  const markSectionViewed = useCallback((sectionId: string) => {
    updateSectionState(sectionId, { lastViewed: new Date() });
  }, [updateSectionState]);

  const markSectionInteracted = useCallback((sectionId: string) => {
    updateSectionState(sectionId, { isInteracted: true });
  }, [updateSectionState]);

  return {
    sectionStates,
    updateSectionState,
    markSectionViewed,
    markSectionInteracted,
  };
};

export default useHomepageState;
