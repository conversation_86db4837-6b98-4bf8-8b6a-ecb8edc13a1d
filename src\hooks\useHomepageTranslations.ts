import { useCallback, useMemo } from 'react';

import { useLanguage } from '@/contexts/LanguageContext';
import { createSafeTranslationFunction, getHeroTranslation } from '@/lib/translation-utils';
import en from '@/locales/en';

/**
 * Custom hook for homepage-specific translations
 * Extracts translation logic from Index.tsx for better separation of concerns
 * Provides safe translation access with proper fallbacks
 */
export const useHomepageTranslations = () => {
  const { t } = useLanguage();

  // Create safe translations object with fallbacks
  const safeT = useMemo(() => t || en, [t]);
  const homeTranslations = useMemo(() => (safeT?.hero) || en.hero || {}, [safeT]);

  // Safe translation function for hero section
  const getTranslation = useCallback((path: string, fallback: string = '') => {
    return getHeroTranslation(safeT, path, fallback);
  }, [safeT]);

  // General safe translation function
  const safeTranslate = useMemo(() => {
    return createSafeTranslationFunction(safeT, en);
  }, [safeT]);

  // Hero-specific translations with fallbacks
  const heroTranslations = useMemo(() => ({
    subtitle: getTranslation('subtitle', 'THE GOLD STANDARD FOR BRAIN AND SPINE SURGERY'),
    title: getTranslation('title', 'Neurosurgical Expertise and Innovative Technology for Superior Brain and Spine Surgery Results'),
    description: getTranslation('description', 'Our practice specialises in cutting-edge future-minded treatment of conditions affecting the brain, spine, and peripheral nerves.'),
    credentials: getTranslation('credentials', 'Dr Ales Aliashkevich is a progressive and conservative neurosurgeon committed to providing the highest level of care and expertise in a dedicated, personalised way.'),
  }), [getTranslation]);

  // Welcome section translations
  const welcomeTranslations = useMemo(() => ({
    subtitle: getTranslation('welcome.subtitle', 'Neurosurgical Excellence'),
    title: getTranslation('welcome.title', 'Advanced Neurosurgical Care'),
    description1: getTranslation('welcome.description1', 'Comprehensive neurosurgical services with cutting-edge technology.'),
    description2: getTranslation('welcome.description2', 'Expert care for brain, spine, and peripheral nerve conditions.'),
  }), [getTranslation]);

  // Navigation translations
  const navTranslations = useMemo(() => ({
    home: safeTranslate('navigation.home', 'Home'),
    expertise: safeTranslate('navigation.expertise', 'Expertise'),
    appointments: safeTranslate('navigation.appointments', 'Appointments'),
    contact: safeTranslate('navigation.contact', 'Contact'),
    about: safeTranslate('navigation.about', 'About'),
  }), [safeTranslate]);

  // CTA translations
  const ctaTranslations = useMemo(() => ({
    title: safeTranslate('cta.title', 'Book Your Consultation'),
    description: safeTranslate('cta.description', 'Schedule your appointment today'),
    primaryButton: safeTranslate('cta.primaryButton', 'Book Now'),
    secondaryButton: safeTranslate('cta.secondaryButton', 'Learn More'),
  }), [safeTranslate]);

  return {
    // Translation functions
    getTranslation,
    safeTranslate,
    
    // Grouped translations
    hero: heroTranslations,
    welcome: welcomeTranslations,
    nav: navTranslations,
    cta: ctaTranslations,
    
    // Raw translation objects
    safeT,
    homeTranslations,
  };
};

/**
 * Hook for getting specific homepage section translations
 * Useful when components only need specific section translations
 */
export const useHomepageSectionTranslations = (sectionName: string) => {
  const { safeTranslate } = useHomepageTranslations();
  
  return useCallback((key: string, fallback: string = '') => {
    return safeTranslate(`${sectionName}.${key}`, fallback);
  }, [safeTranslate, sectionName]);
};

/**
 * Hook for getting homepage metadata translations
 * Provides SEO and meta information translations
 */
export const useHomepageMetadata = () => {
  const { safeTranslate } = useHomepageTranslations();

  return useMemo(() => ({
    title: safeTranslate('meta.title', 'Dr Ales Aliashkevich - Neurosurgeon'),
    description: safeTranslate('meta.description', 'Expert neurosurgical care with cutting-edge technology'),
    keywords: safeTranslate('meta.keywords', 'neurosurgeon, brain surgery, spine surgery'),
    ogTitle: safeTranslate('meta.ogTitle', 'Dr Ales Aliashkevich - Neurosurgeon'),
    ogDescription: safeTranslate('meta.ogDescription', 'Expert neurosurgical care with cutting-edge technology'),
  }), [safeTranslate]);
};

/**
 * Hook for getting homepage button translations
 * Centralizes all button text translations
 */
export const useHomepageButtons = () => {
  const { safeTranslate } = useHomepageTranslations();

  return useMemo(() => ({
    bookNow: safeTranslate('buttons.bookNow', 'Book Now'),
    learnMore: safeTranslate('buttons.learnMore', 'Learn More'),
    readMore: safeTranslate('buttons.readMore', 'Read More'),
    viewAll: safeTranslate('buttons.viewAll', 'View All'),
    contact: safeTranslate('buttons.contact', 'Contact'),
    schedule: safeTranslate('buttons.schedule', 'Schedule'),
    explore: safeTranslate('buttons.explore', 'Explore'),
  }), [safeTranslate]);
};

/**
 * Hook for getting homepage form translations
 * Provides translations for forms and inputs
 */
export const useHomepageForms = () => {
  const { safeTranslate } = useHomepageTranslations();

  return useMemo(() => ({
    name: safeTranslate('forms.name', 'Name'),
    email: safeTranslate('forms.email', 'Email'),
    phone: safeTranslate('forms.phone', 'Phone'),
    message: safeTranslate('forms.message', 'Message'),
    submit: safeTranslate('forms.submit', 'Submit'),
    required: safeTranslate('forms.required', 'Required'),
    optional: safeTranslate('forms.optional', 'Optional'),
  }), [safeTranslate]);
};

export default useHomepageTranslations;
