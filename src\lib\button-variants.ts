import { cva } from 'class-variance-authority';

export const buttonVariants = cva(
  "inline-flex items-centre justify-centre gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 touch-manipulation select-none",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 shadow-md hover:shadow-lg",
        muted:
          "bg-muted text-foreground-foreground hover:bg-muted/90 shadow-md hover:shadow-lg",
        outline:
          "border-2 border-border bg-card text-card-foreground hover:bg-accent hover:text-accent-foreground shadow-sm hover:shadow-md",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm hover:shadow-md",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        hero: "bg-primary-foreground/20 text-primary-foreground backdrop-blur-sm border border-primary-foreground/30 hover:bg-primary-foreground/30 transition-all duration-300 shadow-lg hover:shadow-xl",
        heroSolid: "bg-primary text-primary-foreground hover:bg-primary/90 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-[1.02]",
        success: "bg-success text-foreground-foreground hover:bg-success/90 shadow-md hover:shadow-lg",
        warning: "bg-info text-foreground hover:bg-info/90 shadow-md hover:shadow-lg",
        info: "bg-info text-foreground hover:bg-info/90 shadow-md hover:shadow-lg",
        medical: "bg-medical-blue text-medical-blue-foreground hover:bg-medical-blue-dark shadow-md hover:shadow-lg",
        emergency: "bg-muted text-foreground hover:bg-muted/90 shadow-md hover:shadow-lg",
        routine: "bg-success text-foreground hover:bg-success/90 shadow-md hover:shadow-lg"
      },
      size: {
        default: "h-10 px-4 py-2 mobile:min-h-[44px]",
        sm: "h-9 rounded-md px-3 mobile:min-h-[40px]",
        lg: "h-11 rounded-md px-8 mobile:min-h-[48px]",
        icon: "h-10 w-10 mobile:min-h-[44px] mobile:min-w-[44px]",
        mobile: "min-h-[44px] px-4 py-2",
        "mobile-lg": "min-h-[48px] px-6 py-3"
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default"
    }
  }
);
