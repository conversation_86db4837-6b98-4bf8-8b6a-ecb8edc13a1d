/**
 * Development-only console utilities
 * 
 * This module provides console logging that only executes in development mode.
 * In production builds, these calls are stripped out or become no-ops.
 */

/**
 * Console interface for type safety
 */
interface DevConsole {
  log: (...args: unknown[]) => void;
  warn: (...args: unknown[]) => void;
  error: (...args: unknown[]) => void;
  info: (...args: unknown[]) => void;
  debug: (...args: unknown[]) => void;
  group: (...args: unknown[]) => void;
  groupEnd: () => void;
  groupCollapsed: (...args: unknown[]) => void;
  table: (data: unknown) => void;
  time: (label: string) => void;
  timeEnd: (label: string) => void;
}

/**
 * No-op functions for production
 */
const noOp = () => {};

/**
 * Development console that only logs in development mode
 */
export const devConsole: DevConsole = {
  log: import.meta.env.DEV ? console.log.bind(console) : noOp,
  warn: import.meta.env.DEV ? console.warn.bind(console) : noOp,
  error: import.meta.env.DEV ? console.error.bind(console) : noOp,
  info: import.meta.env.DEV ? console.info.bind(console) : noOp,
  debug: import.meta.env.DEV ? console.debug.bind(console) : noOp,
  group: import.meta.env.DEV ? console.group.bind(console) : noOp,
  groupEnd: import.meta.env.DEV ? console.groupEnd.bind(console) : noOp,
  groupCollapsed: import.meta.env.DEV ? console.groupCollapsed.bind(console) : noOp,
  table: import.meta.env.DEV ? console.table.bind(console) : noOp,
  time: import.meta.env.DEV ? console.time.bind(console) : noOp,
  timeEnd: import.meta.env.DEV ? console.timeEnd.bind(console) : noOp,
};

/**
 * Conditional logging functions for specific use cases
 */

/**
 * Log performance metrics (only in development and when enabled)
 */
export const logPerformance = (message: string, data?: unknown): void => {
  if (import.meta.env.DEV && !import.meta.env.VITE_DISABLE_PERFORMANCE_LOGS) {
    console.log(`🚀 [PERFORMANCE] ${message}`, data);
  }
};

/**
 * Log error information (only in development)
 */
export const logError = (message: string, error?: unknown): void => {
  if (import.meta.env.DEV) {
    console.error(`❌ [ERROR] ${message}`, error);
  }
};

/**
 * Log warning information (only in development)
 */
export const logWarning = (message: string, data?: unknown): void => {
  if (import.meta.env.DEV) {
    console.warn(`⚠️ [WARNING] ${message}`, data);
  }
};

/**
 * Log debug information (only in development)
 */
export const logDebug = (message: string, data?: unknown): void => {
  if (import.meta.env.DEV) {
    console.debug(`🐛 [DEBUG] ${message}`, data);
  }
};

/**
 * Log hook lifecycle events (only in development)
 */
export const logHook = (hookName: string, event: string, data?: unknown): void => {
  if (import.meta.env.DEV) {
    console.log(`🪝 [HOOK:${hookName}] ${event}`, data);
  }
};

/**
 * Log SEO-related information (only in development)
 */
export const logSEO = (message: string, data?: unknown): void => {
  if (import.meta.env.DEV) {
    console.log(`🔍 [SEO] ${message}`, data);
  }
};

/**
 * Log route-related information (only in development)
 */
export const logRoute = (message: string, data?: unknown): void => {
  if (import.meta.env.DEV) {
    console.log(`🛣️ [ROUTE] ${message}`, data);
  }
};

/**
 * Log form-related information (only in development)
 */
export const logForm = (message: string, data?: unknown): void => {
  if (import.meta.env.DEV) {
    console.log(`📝 [FORM] ${message}`, data);
  }
};

/**
 * Utility to create a scoped logger for a specific component or module
 */
export const createScopedLogger = (scope: string) => ({
  log: (message: string, data?: unknown) => devConsole.log(`[${scope}] ${message}`, data),
  warn: (message: string, data?: unknown) => devConsole.warn(`[${scope}] ${message}`, data),
  error: (message: string, data?: unknown) => devConsole.error(`[${scope}] ${message}`, data),
  debug: (message: string, data?: unknown) => devConsole.debug(`[${scope}] ${message}`, data),
});

/**
 * Default export for convenience
 */
export default devConsole;
