import React from 'react';

import LocationPageWrapper from '@/components/locations/LocationPageWrapper';

/**
 * Location Page Factory
 * Creates standardized location page components with content injection
 * Eliminates the need for individual location page files
 */

export interface LocationPageOptions {
  customSections?: React.ReactNode[];
  displayName?: string;
}

/**
 * Creates a location page component for a specific location
 */
export const createLocationPage = (
  locationSlug: string,
  options: LocationPageOptions = {}
): React.FC => {
  const LocationPage: React.FC = () => {
    return (
      <LocationPageWrapper
        locationSlug={locationSlug}
        customSections={options.customSections}
      />
    );
  };

  LocationPage.displayName = options.displayName || `${locationSlug}LocationPage`;

  return LocationPage;
};

/**
 * Pre-configured location page components
 * These can replace the individual location page files
 */
export const SunburyLocationPage = createLocationPage('sunbury', {
  displayName: 'SunburyLocationPage'
});

export const HeidelbergLocationPage = createLocationPage('heidelberg', {
  displayName: 'HeidelbergLocationPage'
});

export const FrankstonLocationPage = createLocationPage('frankston', {
  displayName: 'FrankstonLocationPage'
});

export const BundooraLocationPage = createLocationPage('bundoora', {
  displayName: 'BundooraLocationPage'
});

export const DandenongLocationPage = createLocationPage('dandenong', {
  displayName: 'DandenongLocationPage'
});

export const LangwarrinLocationPage = createLocationPage('langwarrin', {
  displayName: 'LangwarrinLocationPage'
});

export const MooneePondsLocationPage = createLocationPage('moonee-ponds', {
  displayName: 'MooneePondsLocationPage'
});

export const MorningtonLocationPage = createLocationPage('mornington', {
  displayName: 'MorningtonLocationPage'
});

export const SurreyHillsLocationPage = createLocationPage('surrey-hills', {
  displayName: 'SurreyHillsLocationPage'
});

export const WantirnaLocationPage = createLocationPage('wantirna', {
  displayName: 'WantirnaLocationPage'
});

export const WerribeeLocationPage = createLocationPage('werribee', {
  displayName: 'WerribeeLocationPage'
});

/**
 * Location page registry for dynamic routing
 */
export const locationPageRegistry = {
  sunbury: SunburyLocationPage,
  heidelberg: HeidelbergLocationPage,
  frankston: FrankstonLocationPage,
  bundoora: BundooraLocationPage,
  dandenong: DandenongLocationPage,
  langwarrin: LangwarrinLocationPage,
  'moonee-ponds': MooneePondsLocationPage,
  mornington: MorningtonLocationPage,
  'surrey-hills': SurreyHillsLocationPage,
  wantirna: WantirnaLocationPage,
  werribee: WerribeeLocationPage,
};

/**
 * Get a location page component by slug
 */
export const getLocationPageComponent = (slug: string): React.FC | null => {
  return locationPageRegistry[slug as keyof typeof locationPageRegistry] || null;
};

/**
 * Check if a location page exists
 */
export const hasLocationPage = (slug: string): boolean => {
  return slug in locationPageRegistry;
};

/**
 * Get all available location page slugs
 */
export const getAvailableLocationPages = (): string[] => {
  return Object.keys(locationPageRegistry);
};

export default {
  createLocationPage,
  locationPageRegistry,
  getLocationPageComponent,
  hasLocationPage,
  getAvailableLocationPages,
};
