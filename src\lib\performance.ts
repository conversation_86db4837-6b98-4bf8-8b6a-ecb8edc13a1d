import React from 'react';

import { logPerformance } from '@/lib/dev-console';

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, unknown>;
}

/**
 * Performance monitoring configuration
 */
interface PerformanceConfig {
  enableResourceTiming: boolean;
  enableNavigationTiming: boolean;
  enableUserTiming: boolean;
  enableLongTaskTiming: boolean;
  enableLayoutShiftTiming: boolean;
  enableLargestContentfulPaint: boolean;
  enableFirstInputDelay: boolean;
  maxMetrics: number;
}

/**
 * Performance monitoring singleton class
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private observers: PerformanceObserver[] = [];
  private config: PerformanceConfig;
  private initialized = false;
  private initializing = false;

  private constructor() {
    this.config = {
      enableResourceTiming: false, // Disabled to reduce console noise
      enableNavigationTiming: true,
      enableUserTiming: true,
      enableLongTaskTiming: false, // Disabled to reduce console noise
      enableLayoutShiftTiming: true,
      enableLargestContentfulPaint: true,
      enableFirstInputDelay: true,
      maxMetrics: 1000
    };
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Initialize performance monitoring
   */
  initialize(): void {
    if (this.initialized || this.initializing) {
      return;
    }

    this.initializing = true;

    try {
      if (typeof window === 'undefined' || !('performance' in window)) {
        this.initializing = false;
        return;
      }

      // Initialize observers
      this.initializeObservers();

      this.initialized = true;
      this.initializing = false;
    } catch {
      // Reset flags on failure and cleanup any partial observers
      this.initializing = false;
      this.initialized = false;
      this.cleanupObservers();
    }
  }

  /**
   * Initialize performance observers
   */
  private initializeObservers(): void {
    if (!('PerformanceObserver' in window)) {
      return;
    }

    try {
      // Navigation timing observer
      if (this.config.enableNavigationTiming) {
        const navObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.entryType === 'navigation') {
              this.logNavigationMetrics(entry as PerformanceNavigationTiming);
            }
          });
        });
        navObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navObserver);
      }

      // Resource timing observer
      if (this.config.enableResourceTiming) {
        const resourceObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.entryType === 'resource') {
              this.logResourceMetrics(entry as PerformanceResourceTiming);
            }
          });
        });
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);
      }
    } catch {
      // Silently handle observer initialization errors
    }
  }

  /**
   * Start measuring a performance metric
   */
  startMeasure(name: string, metadata?: Record<string, unknown>): void {
    try {
      const startTime = typeof performance !== 'undefined' && performance.now ? performance.now() : Date.now();

      // Enforce memory limit
      if (this.metrics.size >= this.config.maxMetrics) {
        // Remove oldest metrics (first entries)
        const keysToRemove = Array.from(this.metrics.keys()).slice(0, Math.floor(this.config.maxMetrics * 0.1));
        keysToRemove.forEach(key => this.metrics.delete(key));
      }

      this.metrics.set(name, {
        name,
        startTime,
        metadata
      });
    } catch (error) {
      // Silently handle performance API errors
      logWarning('Performance measurement failed', error);
    }
  }

  /**
   * End measuring a performance metric
   */
  endMeasure(name: string): PerformanceMetrics | null {
    try {
      const metric = this.metrics.get(name);
      if (!metric) {
        return null;
      }
      const endTime = typeof performance !== 'undefined' && performance.now ? performance.now() : Date.now();
      const duration = endTime - metric.startTime;

      const completedMetric: PerformanceMetrics = {
        ...metric,
        endTime,
        duration
      };

      this.metrics.set(name, completedMetric);
      this.logMetric(name, metric.startTime, endTime, metric.metadata);

      return completedMetric;
    } catch (error) {
      // Silently handle performance API errors
      logWarning('Performance measurement failed', error);
      return null;
    }
  }

  /**
   * Log a performance metric
   */
  private logMetric(
    _name: string,
    _startTime: number,
    _endTime: number,
    _metadata?: Record<string, unknown>
  ): void {
    // In production, send this to an analytics service
    // Example: sendToAnalytics({ name, duration, metadata });
  }

  /**
   * Log navigation metrics
   */
  logNavigationMetrics(entry: PerformanceNavigationTiming): void {
    const metrics = {
      'DNS Lookup': entry.domainLookupEnd - entry.domainLookupStart,
      'TCP Connection': entry.connectEnd - entry.connectStart,
      'TLS Handshake': entry.secureConnectionStart > 0 ? entry.connectEnd - entry.secureConnectionStart : 0,
      'Request': entry.responseStart - entry.requestStart,
      'Response': entry.responseEnd - entry.responseStart,
      'DOM Processing': entry.domComplete - entry.domContentLoadedEventStart,
      'Load Complete': entry.loadEventEnd - entry.loadEventStart,
      'Total Load Time': entry.loadEventEnd - entry.fetchStart
    };

    const navigationMetrics = {
      pageLoadTime: entry.loadEventEnd - entry.loadEventStart,
      domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      networkTime: entry.responseEnd - entry.requestStart,
    };

    logPerformance('Navigation Metrics', navigationMetrics);

    Object.entries(metrics).forEach(([name, duration]) => {
      if (duration > 0) {
        this.logMetric(`Navigation: ${name}`, 0, duration);
      }
    });
  }

  /**
   * Log resource metrics (selective logging for important resources only)
   */
  logResourceMetrics(entry: PerformanceResourceTiming): void {
    const resourceType = this.getResourceType(entry.name);

    // Only log important resources to reduce console noise
    const shouldLog = this.shouldLogResource(entry.name, entry.transferSize || 0);

    if (shouldLog) {
      const resourceMetrics = {
        resource: entry.name,
        loadTime: entry.responseEnd - (entry.requestStart || entry.startTime || 0),
        size: entry.transferSize,
        cached: entry.transferSize === 0 && entry.decodedBodySize > 0
      };

      logPerformance('Resource Metrics', resourceMetrics);
    }

    this.logMetric(`Resource: ${resourceType}`, entry.startTime, entry.responseEnd, {
      url: entry.name,
      size: entry.transferSize,
      cached: entry.transferSize === 0 && entry.decodedBodySize > 0
    });
  }

  /**
   * Determine if a resource should be logged (to reduce console noise)
   */
  private shouldLogResource(url: string, size: number): boolean {
    // Log large resources (>100KB)
    if (size > 100000) return true;

    // Log main application bundles
    if (url.includes('index-') && url.includes('.js')) return true;

    // Log CSS files
    if (url.includes('.css')) return true;

    // Log images larger than 50KB
    if ((url.includes('.jpg') || url.includes('.png') || url.includes('.webp')) && size > 50000) return true;

    // Log slow loading resources (>2s)
    // This would need to be calculated in the calling context

    // Don't log small assets, fonts, or development files
    return false;
  }

  /**
   * Log Largest Contentful Paint metrics (Core Web Vitals)
   */
  logLargestContentfulPaint(entry: PerformanceEntry): void {
    const lcpEntry = entry as PerformanceEntry & { element?: Element }; // Type assertion for LCP-specific properties

    const lcpMetrics = {
      lcp: entry.startTime,
      element: lcpEntry.element?.tagName || 'UNKNOWN',
    };

    logPerformance('Largest Contentful Paint', lcpMetrics);

    this.logMetric('LCP', 0, entry.startTime, {
      element: lcpEntry.element?.tagName || 'UNKNOWN',
      url: lcpEntry.url || '',
    });
  }

  /**
   * Log Layout Shift metrics (Core Web Vitals)
   */
  logLayoutShift(entry: PerformanceEntry): void {
    const clsEntry = entry as PerformanceEntry & { value?: number; hadRecentInput?: boolean }; // Type assertion for CLS-specific properties

    const clsMetrics = {
      cls: clsEntry.value || 0,
      hadRecentInput: clsEntry.hadRecentInput || false,
    };

    logPerformance('Layout Shift', clsMetrics);

    this.logMetric('CLS', 0, clsEntry.value || 0, {
      hadRecentInput: clsEntry.hadRecentInput || false,
      sources: clsEntry.sources || [],
    });
  }

  /**
   * Get resource type from URL
   */
  private getResourceType(url: string): string {
    if (url.includes('.css')) return 'CSS';
    if (url.includes('.js')) return 'JavaScript';
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) return 'Image';
    if (url.match(/\.(woff|woff2|ttf|eot)$/i)) return 'Font';
    return 'Other';
  }

  /**
   * Get all metrics
   */
  getMetrics(): PerformanceMetrics[] {
    return Array.from(this.metrics.values());
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics.clear();
  }

  /**
   * Check if performance monitoring is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Check if performance monitoring is currently initializing
   */
  isInitializing(): boolean {
    return this.initializing;
  }

  /**
   * Get number of active observers
   */
  getObserverCount(): number {
    return this.observers?.length || 0;
  }

  /**
   * Internal cleanup method for observers only
   */
  private cleanupObservers(): void {
    if (this.observers?.length > 0) {
      this.observers.forEach(observer => observer.disconnect());
      this.observers = [];
    }
  }

  /**
   * Public cleanup method for complete reset
   */
  cleanup(): void {
    this.cleanupObservers();
    this.initialized = false;
    this.initializing = false;
  }

  /**
   * Destroy method - alias for cleanup for test compatibility
   */
  destroy(): void {
    this.cleanup();
  }
}

/**
 * Measure component render time
 */
export function measureComponentRender<T extends Record<string, unknown>>(
  Component: React.ComponentType<T>,
  componentName?: string
): React.ComponentType<T> {
  const name = componentName || Component.displayName || Component.name || 'Component';

  return function MeasuredComponent(props: T) {
    React.useEffect(() => {
      // Lazy access to monitor to avoid race condition
      const monitor = PerformanceMonitor.getInstance();
      monitor.startMeasure(`${name} Mount`);
      return () => {
        monitor.endMeasure(`${name} Mount`);
      };
    }, []);

    React.useEffect(() => {
      // Lazy access to monitor to avoid race condition
      const monitor = PerformanceMonitor.getInstance();
      monitor.startMeasure(`${name} Render`);
      monitor.endMeasure(`${name} Render`);
    });

    return React.createElement(Component, props);
  };
}

/**
 * Hook for measuring custom performance metrics
 */
export function usePerformanceMetric(name: string, dependencies: React.DependencyList = []) {
  // Use lazy initialization to avoid race condition
  const getMonitor = React.useCallback(() => PerformanceMonitor.getInstance(), []);

  React.useEffect(() => {
    const monitor = getMonitor();
    monitor.startMeasure(name);
    return () => {
      monitor.endMeasure(name);
    };
  }, [getMonitor, name, dependencies]);

  return React.useMemo(() => ({
    startMeasure: (metricName: string) => getMonitor().startMeasure(metricName),
    endMeasure: (metricName: string) => getMonitor().endMeasure(metricName)
  }), [getMonitor]);
}

/**
 * Initialize performance monitoring (should only be called once from main.tsx)
 */
export function initializePerformanceMonitoring(): PerformanceMonitor {
  const monitor = PerformanceMonitor.getInstance();
  monitor.initialize();
  return monitor;
}

/**
 * Get the performance monitor instance (only after initialization)
 */
export function getPerformanceMonitor(): PerformanceMonitor {
  return PerformanceMonitor.getInstance();
}
