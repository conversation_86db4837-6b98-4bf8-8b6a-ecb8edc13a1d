import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import React, { ReactElement, ReactNode } from 'react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { afterEach, beforeEach, vi } from 'vitest';

import ErrorBoundary from '@/components/ErrorBoundary';
import { TooltipProvider } from '@/components/ui/tooltip';

/**
 * Comprehensive testing utilities
 * Provides consistent testing setup and utilities for components, hooks, and integration tests
 */

// Mock implementations for testing
export const mockDeviceInfo = {
  isMobile: false,
  isTablet: false,
  isDesktop: true,
  isTouchDevice: false,
  hasHover: true,
  orientation: 'landscape' as const,
  screenSize: 'lg' as const,
  width: 1024,
  height: 768
};

export const mockLanguageContext = {
  language: 'en' as const,
  setLanguage: vi.fn(),
  t: (key: string) => key, // Simple function that returns the key as translation
  isLanguageLoaded: true
};

// Custom render function with all providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[];
  queryClient?: QueryClient;
  deviceInfo?: typeof mockDeviceInfo;
  languageContext?: typeof mockLanguageContext;
  withErrorBoundary?: boolean;
}

export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult {
  const {
    _initialEntries = ['/'],
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
          gcTime: 0
        },
        mutations: {
          retry: false
        }
      }
    }),
    _deviceInfo = mockDeviceInfo,
    _languageContext = mockLanguageContext,
    withErrorBoundary = true,
    ...renderOptions
  } = options;

  // Note: Mocks are handled in individual test files due to Vitest hoisting

  function Wrapper({ children }: { children: ReactNode }) {
    // Use simple mock providers that just pass through children
    // The actual mocking is handled at the global level in setupTests.ts
    const MockLanguageProvider = ({ children }: { children: ReactNode }) => {
      return <div data-testid="mock-language-provider">{children}</div>;
    };

    const MockDeviceProvider = ({ children }: { children: ReactNode }) => {
      return <div data-testid="mock-device-provider">{children}</div>;
    };

    const content = (
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <MockDeviceProvider>
            <MockLanguageProvider>
              <TooltipProvider>
                {children}
              </TooltipProvider>
            </MockLanguageProvider>
          </MockDeviceProvider>
        </BrowserRouter>
      </QueryClientProvider>
    );

    return withErrorBoundary ? (
      <ErrorBoundary>{content}</ErrorBoundary>
    ) : content;
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

/**
 * Create a mock intersection observer for testing lazy loading
 */
export function createMockIntersectionObserver() {
  const mockIntersectionObserver = vi.fn();
  mockIntersectionObserver.mockReturnValue({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  });
  window.IntersectionObserver = mockIntersectionObserver;
  return mockIntersectionObserver;
  }

  /**
 * Create a mock resize observer for testing responsive components
 */
export function createMockResizeObserver() {
  const mockResizeObserver = vi.fn();
  mockResizeObserver.mockReturnValue({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  });
  window.ResizeObserver = mockResizeObserver;
  return mockResizeObserver;
  }

  /**
 * Mock window.matchMedia for testing responsive behavior
 */
export function mockMatchMedia(matches: boolean = false) {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn()
    }))
  });

}

/**
 * Mock performance API for testing performance monitoring
 */
export function mockPerformanceAPI() {
  const mockPerformance = {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByType: vi.fn(() => []),
    getEntriesByName: vi.fn(() => []),
    clearMarks: vi.fn(),
    clearMeasures: vi.fn()
  };

  Object.defineProperty(window, 'performance', {
    writable: true,
    value: mockPerformance
  });

  return mockPerformance;
  }

  /**
 * Mock fetch API for testing network requests
 */
export function mockFetch(response: Record<string, unknown> = {}, ok: boolean = true) {
  const mockFetch = vi.fn(() =>
    Promise.resolve({
      ok,
      status: ok ? 200 : 500,
      json: () => Promise.resolve(response),
      text: () => Promise.resolve(JSON.stringify(response))
    })
  );

  global.fetch = mockFetch;
  return mockFetch;
  }

  /**
 * Mock localStorage for testing storage functionality
 */
export function mockLocalStorage() {
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn()
  };

  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
  });

  return localStorageMock;
}

  /**
 * Mock sessionStorage for testing session functionality
 */
export function mockSessionStorage() {
  const sessionStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn()
  };

  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock
  });

  return sessionStorageMock;
}

  /**
 * Wait for async operations to complete
 */
export function waitForAsync(ms: number = 0, signal?: AbortSignal): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    const timeoutId = setTimeout(resolve, ms);

    if (signal) {
      const abortHandler = () => {
        clearTimeout(timeoutId);
        reject(new Error('Wait cancelled'));
      };

      signal.addEventListener('abort', abortHandler, { once: true });
    }
  });
}

/**
 * Create a mock procedure for testing
 */
export function createMockProcedure(overrides: Record<string, unknown> = {}) {
  return {
    id: 'test-procedure-1',
    name: 'Test Procedure',
    description: 'A test medical procedure',
    consultationFee: 200,
    patientType: 'All Patients',
    recoveryTime: '14 days',
    image: '/images/test-procedure.jpg',
    location: 'Brain',
    benefits: ['Minimally Invasive', 'Quick Recovery'],
    complexity: 7,
    ...overrides
  };
   }

  /**
 * Create mock translation data
 */
export function createMockTranslations(overrides: Record<string, unknown> = {}) {
  return {
    navigation: mockLanguageContext.t.navigation,
    hero: mockLanguageContext.t.hero,
    ...overrides
  };
}

  /**
 * Test helper for error boundary testing
 */
export function ThrowError({ shouldThrow }: { shouldThrow: boolean }) {
  if (shouldThrow) throw new Error('Test error');

  return <div>No error</div>;
}

  /**
 * Custom matchers for testing
 */
export const customMatchers = {
  toBeAccessible: (received: HTMLElement) => {
    // Basic accessibility checks
    const hasAriaLabel = received.getAttribute('aria-label');
    const hasRole = received.getAttribute('role');
    const hasTabIndex = received.getAttribute('tabindex');
    
    const pass = !!(hasAriaLabel || hasRole || hasTabIndex);
    
    return {
      message: () =>
        pass
          ? `Expected element not to be accessible`
          : `Expected element to be accessible (have aria-label, role, or tabindex)`,
      pass
    };
  },
  
  toHaveValidSEO: (received: Document) => {
    const title = received.querySelector('title');
    const description = received.querySelector('meta[name="description"]');
    const canonical = received.querySelector('link[rel="canonical"]');
    
    const pass = !!(title && description && canonical);
    
    return {
      message: () =>
        pass
          ? `Expected document not to have valid SEO`
          : `Expected document to have title, description, and canonical link`,
      pass
    };
  }
};

/**
 * Standardized mock utilities for consistent testing
 */
export const mockUtils = {
  /**
   * Create a mock context provider that passes through children
   */
  createMockProvider: (name: string) => ({ children }: { children: React.ReactNode }) => {
    return React.createElement('div', { 'data-testid': `mock-${name}-provider` }, children);
  },

  /**
   * Create a mock hook with default return values
   */
  createMockHook: <T,>(defaultReturn: T) => vi.fn(() => defaultReturn),

  /**
   * Suppress console errors for error boundary tests
   * Note: Direct console manipulation is intentional for testing
   */
  suppressConsoleErrors: () => {
     
    const originalError = console.error;
    beforeEach(() => {
       
      console.error = vi.fn();
    });
    afterEach(() => {
       
      console.error = originalError;
    });
  },

  /**
   * Reset all mocks and clear timers
   */
  resetAllMocks: () => {
    vi.clearAllMocks();
    vi.clearAllTimers();
    vi.restoreAllMocks();
  }
};

/**
 * Common mock configurations for reuse across tests
 */
export const mockConfigurations = {
  deviceContext: {
    DeviceProvider: ({ children }: { children: React.ReactNode }) => children,
    useDeviceDetection: vi.fn(() => mockDeviceInfo),
  },

  languageContext: {
    LanguageProvider: ({ children }: { children: React.ReactNode }) => children,
    useLanguage: vi.fn(() => mockLanguageContext),
  },

  seoHooks: {
    useSEO: vi.fn(),
  },

  seoLib: {
    generatePageSEO: vi.fn(() => ({
      title: 'Test Page | miNEURO',
      description: 'Test description',
      keywords: 'test, keywords',
    })),
  },
};

// Setup function to be called in test setup files
export function setupTestEnvironment() {
  // Mock all the browser APIs only if not already mocked
  if (!window.IntersectionObserver) {
    createMockIntersectionObserver();
  }
  if (!window.ResizeObserver) {
    createMockResizeObserver();
  }
  if (!window.matchMedia) {
    mockMatchMedia();
  }
  if (!window.performance.mark) {
    mockPerformanceAPI();
  }
  if (!window.localStorage) {
    mockLocalStorage();
  }
  if (!window.sessionStorage) {
    mockSessionStorage();
  }

  // Extend Jest matchers
  expect.extend(customMatchers);
}

/**
 * TestWrapper component for integration tests
 * Provides all necessary providers for comprehensive testing
 */
export const TestWrapper: React.FC<{
  children: ReactNode;
  disableErrorBoundary?: boolean;
}> = ({ children, disableErrorBoundary = false }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        refetchOnWindowFocus: false,
      },
      mutations: {
        retry: false,
      },
    },
  });

  const content = (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <div data-testid="mock-device-provider">
          <div data-testid="mock-language-provider">
            <TooltipProvider>
              {children}
            </TooltipProvider>
          </div>
        </div>
      </QueryClientProvider>
    </BrowserRouter>
  );

  if (disableErrorBoundary) {
    return content;
  }

  return (
    <ErrorBoundary>
      {content}
    </ErrorBoundary>
  );
};

// Export everything for easy importing
export * from '@testing-library/jest-dom';
export * from '@testing-library/react';
export { renderWithProviders as render };

