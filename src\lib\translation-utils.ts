/**
 * Translation Fallback Utilities
 * Centralizes duplicate translation fallback logic found throughout the codebase
 * Ensures consistent translation handling with proper fallbacks
 */

import en from '@/locales/en';

export type TranslationPath = string;
export type TranslationValue = string | number | boolean | object | undefined;
export type TranslationObject = Record<string, unknown>;

/**
 * Safely access nested translation properties with fallback support
 * Replaces duplicate translation access patterns found in multiple components
 */
export const getTranslation = (
  translations: TranslationObject | undefined,
  path: TranslationPath,
  fallback: string = '',
  fallbackTranslations?: TranslationObject
): string => {
  // Use provided translations or fallback to English
  const safeTranslations = translations || fallbackTranslations || en;
  
  if (!safeTranslations || typeof safeTranslations !== 'object') {
    return fallback;
  }

  const keys = path.split('.');
  let current: unknown = safeTranslations;

  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = (current as Record<string, unknown>)[key];
    } else {
      // Try fallback translations if primary fails
      if (fallbackTranslations && fallbackTranslations !== safeTranslations) {
        return getTranslation(fallbackTranslations, path, fallback);
      }
      return fallback;
    }
  }

  return typeof current === 'string' ? current : fallback;
};

/**
 * Create a safe translation function with automatic fallbacks
 * Replaces the pattern found in Index.tsx and other components
 */
export const createSafeTranslationFunction = (
  translations: TranslationObject | undefined,
  fallbackTranslations: TranslationObject = en
) => {
  const safeT = translations || fallbackTranslations;
  
  return (path: TranslationPath, fallback: string = '') => {
    return getTranslation(safeT, path, fallback, fallbackTranslations);
  };
};

/**
 * Get translation with multiple fallback attempts
 * Useful for complex translation structures with multiple fallback levels
 */
export const getTranslationWithFallbacks = (
  path: TranslationPath,
  fallback: string,
  ...translationSources: (TranslationObject | undefined)[]
): string => {
  for (const translations of translationSources) {
    if (translations) {
      const result = getTranslation(translations, path, '');
      if (result) {
        return result;
      }
    }
  }
  return fallback;
};

/**
 * Validate translation object structure
 * Ensures translation objects have required properties
 */
export const validateTranslationStructure = (
  translations: unknown,
  requiredPaths: TranslationPath[]
): { isValid: boolean; missingPaths: TranslationPath[] } => {
  const missingPaths: TranslationPath[] = [];
  
  if (!translations || typeof translations !== 'object') {
    return { isValid: false, missingPaths: requiredPaths };
  }

  for (const path of requiredPaths) {
    const value = getTranslation(translations as TranslationObject, path, '');
    if (!value) {
      missingPaths.push(path);
    }
  }

  return {
    isValid: missingPaths.length === 0,
    missingPaths
  };
};

/**
 * Create translation getter for specific section
 * Useful for components that work with specific translation sections
 */
export const createSectionTranslationGetter = (
  translations: TranslationObject | undefined,
  sectionPath: TranslationPath,
  fallbackTranslations: TranslationObject = en
) => {
  const sectionTranslations = getTranslation(translations, sectionPath, '', fallbackTranslations);
  const fallbackSection = getTranslation(fallbackTranslations, sectionPath, '');
  
  return (subPath: TranslationPath, fallback: string = '') => {
    if (typeof sectionTranslations === 'object' && sectionTranslations) {
      return getTranslation(sectionTranslations as TranslationObject, subPath, fallback);
    }
    if (typeof fallbackSection === 'object' && fallbackSection) {
      return getTranslation(fallbackSection as TranslationObject, subPath, fallback);
    }
    return fallback;
  };
};

/**
 * Safe translation access for homepage hero section
 * Replaces the specific pattern found in Index.tsx
 */
export const getHeroTranslation = (
  translations: TranslationObject | undefined,
  path: TranslationPath,
  fallback: string = ''
): string => {
  const heroTranslations = translations?.hero || en.hero || {};
  return getTranslation(heroTranslations as TranslationObject, path, fallback);
};

/**
 * Safe translation access for navigation
 * Common pattern for navigation-related translations
 */
export const getNavTranslation = (
  translations: TranslationObject | undefined,
  path: TranslationPath,
  fallback: string = ''
): string => {
  const navTranslations = translations?.nav || en.nav || {};
  return getTranslation(navTranslations as TranslationObject, path, fallback);
};

/**
 * Safe translation access for footer
 * Common pattern for footer-related translations
 */
export const getFooterTranslation = (
  translations: TranslationObject | undefined,
  path: TranslationPath,
  fallback: string = ''
): string => {
  const footerTranslations = translations?.footer || en.footer || {};
  return getTranslation(footerTranslations as TranslationObject, path, fallback);
};

/**
 * Create a translation context for a specific page
 * Provides all common translation getters for a page
 */
export const createPageTranslationContext = (
  translations: TranslationObject | undefined,
  pageName: string
) => {
  const pageTranslations = translations?.[pageName] || en[pageName] || {};
  
  return {
    // General translation getter
    t: createSafeTranslationFunction(translations),
    
    // Page-specific translation getter
    pageT: (path: TranslationPath, fallback: string = '') => 
      getTranslation(pageTranslations as TranslationObject, path, fallback),
    
    // Section-specific getters
    hero: (path: TranslationPath, fallback: string = '') => 
      getHeroTranslation(translations, path, fallback),
    
    nav: (path: TranslationPath, fallback: string = '') => 
      getNavTranslation(translations, path, fallback),
    
    footer: (path: TranslationPath, fallback: string = '') => 
      getFooterTranslation(translations, path, fallback),
    
    // Validation
    validate: (requiredPaths: TranslationPath[]) => 
      validateTranslationStructure(pageTranslations, requiredPaths),
  };
};

/**
 * Default translation fallback structure
 * Minimal fallback for when translations are completely unavailable
 */
export const defaultTranslationFallback: TranslationObject = {
  nav: { 
    home: "Home", 
    expertise: "Expertise", 
    appointments: "Appointments", 
    contact: "Contact" 
  },
  hero: { 
    title: "Welcome", 
    subtitle: "Professional Care", 
    description: "Expert medical services" 
  },
  footer: { 
    description: "Professional medical practice", 
    quickLinks: "Quick Links", 
    contact: "Contact" 
  }
};

export default {
  getTranslation,
  createSafeTranslationFunction,
  getTranslationWithFallbacks,
  validateTranslationStructure,
  createSectionTranslationGetter,
  getHeroTranslation,
  getNavTranslation,
  getFooterTranslation,
  createPageTranslationContext,
  defaultTranslationFallback
};
