import React, { useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';

import ClinicLocationsSection from '@/components/contact/ClinicLocationsSection';
import ContactFAQSection from '@/components/contact/ContactFAQSection';
import ContactForm from '@/components/contact/ContactForm';
import ContactFormsSection from '@/components/contact/ContactFormsSection';
import ContactInformation from '@/components/contact/ContactInformation';
import EmergencyContact from '@/components/contact/EmergencyContact';
import HospitalAffiliationsSection from '@/components/contact/HospitalAffiliationsSection';
import InteractiveMapsSection from '@/components/contact/InteractiveMapsSection';
import ProfessionalCommunicationSection from '@/components/contact/ProfessionalCommunicationSection';
import PageHeader from '@/components/PageHeader';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import contactData from '@/data/pages/contact';
import { generatePageSEO } from '@/lib/seo';
import { cn } from '@/lib/utils';

/**
 * Unified Contact Component
 *
 * Comprehensive contact page with all documentation requirements:
 * - Multiple contact forms (General, Appointment, GP Referral, Professional)
 * - Emergency contact information and procedures
 * - Hospital affiliations and emergency departments
 * - Interactive maps with accessibility information
 * - Professional communication channels
 * - Clinic locations with detailed information
 * - FAQ section for common questions
 * - Insurance information and GP referral processes
 */

const Contact: React.FC = () => {
  const deviceInfo = useDeviceDetection();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const contactSeoData = generatePageSEO('contact');

  return (
    <StandardPageLayout
      pageType="contact"
      seoData={contactSeoData}
      showHeader={false}
    >
      {/* Hero Section */}
      <PageHeader
        title={contactData.hero.title}
        subtitle={contactData.hero.subtitle}
        backgroundImage={contactData.hero.backgroundImage}
        enableParallax={true}
      />

      <div className="flex-1">
        {/* Primary Contact Information & Basic Form */}
        <section className="py-16">
          <div className="container">
            <div className={cn(
              "grid gap-12",
              deviceInfo.isMobile
                ? "grid-cols-1"
                : "grid-cols-1 lg:grid-cols-2"
            )}>
              {/* Left Column - Contact Information */}
              <div>
                <ContactInformation
                  title={contactData.contactInfo.title}
                  image={contactData.contactInfo.image}
                  details={contactData.contactInfo.details}
                />
              </div>

              {/* Right Column - Basic Contact Form */}
              <div>
                <ContactForm
                  title={contactData.contactForm.title}
                  image={contactData.contactForm.image}
                  description={contactData.contactForm.description}
                  fields={contactData.contactForm.fields}
                  submitText={contactData.contactForm.submitText}
                  successMessage={contactData.contactForm.successMessage}
                />
              </div>
            </div>
          </div>
        </section>

        {/* Multiple Contact Forms Section */}
        <ContactFormsSection
          title={contactData.contactForms.title}
          subtitle={contactData.contactForms.subtitle}
          forms={contactData.contactForms.forms}
        />

        {/* Emergency Contact Information */}
        <section className="py-16 bg-muted">
          <div className="container">
            <EmergencyContact
              title={contactData.emergencyContact.title}
              sections={contactData.emergencyContact.sections}
            />
          </div>
        </section>

        {/* Professional Communication */}
        <ProfessionalCommunicationSection
          title={contactData.professionalCommunication.title}
          subtitle={contactData.professionalCommunication.subtitle}
          sections={contactData.professionalCommunication.sections}
        />

        {/* Hospital Affiliations */}
        <HospitalAffiliationsSection
          title={contactData.hospitalAffiliations.title}
          subtitle={contactData.hospitalAffiliations.subtitle}
          hospitals={contactData.hospitalAffiliations.hospitals}
          emergencyProcedures={contactData.hospitalAffiliations.emergencyProcedures}
        />

        {/* Clinic Locations */}
        <ClinicLocationsSection
          title={contactData.clinicLocations.title}
          subtitle={contactData.clinicLocations.subtitle}
          locations={contactData.clinicLocations.locations}
        />

        {/* Interactive Maps */}
        <InteractiveMapsSection
          title={contactData.map.title}
          embedUrl={contactData.map.embedUrl}
          features={contactData.map.features}
          transportation={contactData.map.transportation}
        />

        {/* GP Referrals Section */}
        <section className="py-16">
          <div className="container">
            <div className="text-center max-w-4xl mx-auto">
              <div className="mb-8">
                <contactData.referrals.icon className="h-16 w-16 text-primary mx-auto mb-4" />
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4">
                  {contactData.referrals.title}
                </h2>
                <div className="space-y-4">
                  {contactData.referrals.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground leading-relaxed">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </div>
              <Button asChild size="lg">
                <Link to={contactData.referrals.buttonLink}>
                  {contactData.referrals.buttonText}
                </Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Insurance Information */}
        <section className="py-16 bg-muted/30">
          <div className="container">
            <div className="text-center mb-12">
              <h2 className="text-enhanced-heading text-3xl font-bold mb-8">
                {contactData.insurance.title}
              </h2>
            </div>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile
                ? "grid-cols-1"
                : "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
            )}>
              {contactData.insurance.options.map((option, index) => (
                <Card key={index} className="shadow-md hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <option.icon className="h-8 w-8 text-primary" />
                      <h3 className="text-xl font-semibold">{option.title}</h3>
                    </div>
                    <p className="text-muted-foreground">{option.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <ContactFAQSection
          title={contactData.faqs.title}
          subtitle={contactData.faqs.subtitle}
          questions={contactData.faqs.questions}
        />
      </div>
    </StandardPageLayout>
  );
};

Contact.displayName = 'Contact';

export default Contact;
