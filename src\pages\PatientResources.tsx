import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import PageHeader from '@/components/PageHeader';
import AppointmentCallToActionSection from '@/components/patient-resources/AppointmentCallToActionSection';
import EducationalMaterialsSection from '@/components/patient-resources/EducationalMaterialsSection';
import ExpandedResourcesSection from '@/components/patient-resources/ExpandedResourcesSection';
import ResourceCategoriesSection from '@/components/patient-resources/ResourceCategoriesSection';
import SpineHealthAppSection from '@/components/patient-resources/SpineHealthAppSection';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/LanguageContext';
import { educationalMaterials } from '@/data/patient-resources/educationalMaterials';
import { expandedResources } from '@/data/patient-resources/expandedResources';
import { getResourceCategories } from '@/data/patient-resources/resourceCategories';
import en from '@/locales/en';

/**
 * Patient Resources Page Component
 * Unified implementation combining comprehensive content with modular architecture
 * Includes all sections from both original and refactored versions
 */

const PatientResources: React.FC = () => {
  const { t } = useLanguage();

  // Safe fallback for translations
  const safeT = t || en;
  const finalT = (safeT && safeT.patientResources && safeT.patientResources.categories && safeT.patientResources.resources) ? safeT : {
    patientResources: {
      title: "Patient Resources",
      subtitle: "Comprehensive resources to support your healthcare journey",
      categories: {
        beforeVisit: "Before Your Visit",
        conditionsTreatments: "Conditions & Treatments",
        surgeryInfo: "Surgery Information",
        patientSupport: "Patient Support"
      },
      resources: {
        newPatientForms: "New Patient Forms",
        newPatientFormsDesc: "Download and complete forms before your appointment",
        insuranceInfo: "Insurance Information",
        insuranceInfoDesc: "Understanding your insurance coverage and benefits",
        preparingForAppointment: "Preparing for Your Appointment",
        preparingForAppointmentDesc: "What to expect and how to prepare",
        brainConditions: "Brain Conditions",
        brainConditionsDesc: "Information about brain tumours and neurological conditions",
        spineConditions: "Spine Conditions",
        spineConditionsDesc: "Comprehensive spine condition information and treatments",
        minimallyInvasive: "Minimally Invasive Surgery",
        minimallyInvasiveDesc: "Advanced surgical techniques for better outcomes",
        preSurgeryInstructions: "Pre-Surgery Instructions",
        preSurgeryInstructionsDesc: "Important guidelines before your surgery",
        postSurgeryCare: "Post-Surgery Care",
        postSurgeryCareDesc: "Recovery guidelines and care instructions",
        hospitalInfo: "Hospital Information",
        hospitalInfoDesc: "Information about our partner hospitals",
        supportGroups: "Support Groups",
        supportGroupsDesc: "Connect with others on similar journeys",
        rehabilitationResources: "Rehabilitation Resources",
        rehabilitationResourcesDesc: "Resources to support your recovery",
        mentalHealthSupport: "Mental Health Support",
        mentalHealthSupportDesc: "Psychological support during treatment"
      },
      educationalMaterials: "Educational Materials",
      needAppointment: "Need an Appointment?",
      appointmentDesc: "Ready to take the next step? Book a consultation with our expert neurosurgical team.",
      viewInstructions: "View Instructions",
      viewHospitals: "View Hospitals",
      findSupport: "Find Support",
      viewResources: "View Resources"
    }
  };

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Get data using helper functions
  const resourceCategories = getResourceCategories(finalT);

  // Spine Health App data
  const spineHealthAppData = {
    title: "Spine Health App",
    description: "Our new Spine Health App provides personalized guidance for patients with chronic neck and back problems. Based on evidence-based medicine, biomechanics, and neurophysiology, the app helps you understand your condition and offers tailored recommendations for improvement.",
    features: [
      "Comprehensive educational content about spine conditions",
      "Personalized exercise recommendations",
      "Detailed assessment tools",
      "Progress tracking and goal setting",
      "Visual demonstrations of exercises"
    ],
    primaryButtonText: "Explore Spine Health App",
    primaryButtonLink: "/patient-resources/individual-spine-health-programme",
    secondaryButtonText: "Take Assessment",
    secondaryButtonLink: "/patient-resources/individual-spine-health-programme#assessment",
    imageSrc: "/images/individual-spine-health-1.jpg",
    imageAlt: "Spine Health App Preview"
  };

  // Individual Spine Health Program data (additional section from original)
  const individualSpineHealthProgramData = {
    title: "Individual Spine Health Programme",
    description: "Our integrated approach combines assessment tools, personalised recommendations, and progress tracking in one platform to help you understand and manage your spine condition effectively.",
    subtitle: "This all-in-one program replaces our previous separate tools and provides:",
    features: [
      "Detailed assessment of your symptoms and condition",
      "Evidence-based educational resources",
      "Personalised exercise recommendations",
      "Progress tracking and goal setting",
      "Secure dashboard access",
      "Integration with your healthcare team"
    ],
    buttonText: "Explore Individual Spine Health Programme",
    buttonLink: "/patient-resources/individual-spine-health-programme",
    imageSrc: "/images/individual-spine-health-1.jpg",
    imageAlt: "Individual spine health program"
  };

  return (
    <StandardPageLayout showHeader={false}>
      <PageHeader
        title={finalT.patientResources.title}
        subtitle={finalT.patientResources.subtitle}
        backgroundImage="/images/patient-resources/spine-health-hero.jpg"
        enableParallax={true}
      />

      <main className="flex-1">
        {/* Resource Categories */}
        <ResourceCategoriesSection categories={resourceCategories} />

        {/* Educational Materials */}
        <EducationalMaterialsSection 
          materials={educationalMaterials} 
          title={finalT.patientResources.educationalMaterials} 
        />

        {/* Spine Health App */}
        <SpineHealthAppSection {...spineHealthAppData} />

        {/* Expanded Resources */}
        <ExpandedResourcesSection 
          resources={expandedResources} 
          title="Expanded Patient Resources" 
        />

        {/* Medical Conditions Library - New comprehensive section */}
        <section className="section-spacing section-background">
          <div className="container max-w-7xl">
            <div className="text-center mb-12">
              <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">Medical Conditions Library</h2>
              <p className="text-enhanced-body max-w-3xl mx-auto">
                Comprehensive information about spine and neurological conditions, including symptoms,
                diagnosis, treatment options, and recovery guidance.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="medical-card p-6">
                <h3 className="text-enhanced-heading text-xl font-semibold mb-3">Common Spine Conditions</h3>
                <p className="text-enhanced-muted mb-4">
                  Learn about herniated discs, spinal stenosis, sciatica, and other common spine conditions.
                </p>
                <Button asChild className="w-full">
                  <Link to="/patient-resources/spine-conditions-library">Browse Spine Conditions</Link>
                </Button>
              </div>

              <div className="medical-card p-6">
                <h3 className="text-enhanced-heading text-xl font-semibold mb-3">Brain & Neurological Conditions</h3>
                <p className="text-enhanced-muted mb-4">
                  Information about brain tumours, neurological disorders, and treatment approaches.
                </p>
                <Button asChild className="w-full">
                  <Link to="/patient-resources/brain-conditions">Learn About Brain Conditions</Link>
                </Button>
              </div>

              <div className="medical-card p-6">
                <h3 className="text-enhanced-heading text-xl font-semibold mb-3">Peripheral Nerve Conditions</h3>
                <p className="text-enhanced-muted mb-4">
                  Comprehensive information about peripheral nerve disorders, entrapments, and treatment options.
                </p>
                <Button asChild className="w-full">
                  <Link to="/patient-resources/peripheral-nerve-conditions-library">Explore Nerve Conditions</Link>
                </Button>
              </div>

              <div className="medical-card p-6">
                <h3 className="text-enhanced-heading text-xl font-semibold mb-3">Condition-Specific Information</h3>
                <p className="text-enhanced-muted mb-4">
                  Detailed pages for specific conditions with symptoms, causes, and treatment options.
                </p>
                <Button asChild className="w-full">
                  <Link to="/patient-resources/condition-information">View All Conditions</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Individual Spine Health Program - Enhanced section from original */}
        <section className="section-spacing section-background-alt">
          <div className="container max-w-7xl">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="relative rounded-lg overflow-hidden shadow-lg">
                <img
                  src={individualSpineHealthProgramData.imageSrc}
                  alt={individualSpineHealthProgramData.imageAlt}
                  className="w-full h-auto"
                />
              </div>
              <div className="space-y-6">
                <div>
                  <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">
                    {individualSpineHealthProgramData.title}
                  </h2>
                  <p className="text-muted-foreground leading-relaxed mb-4">
                    {individualSpineHealthProgramData.description}
                  </p>
                  <p className="text-muted-foreground leading-relaxed mb-4">
                    {individualSpineHealthProgramData.subtitle}
                  </p>
                </div>
                
                <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                  {individualSpineHealthProgramData.features.map((feature, index) => (
                    <li key={index} className="leading-relaxed">{feature}</li>
                  ))}
                </ul>
                
                <div className="pt-4">
                  <Button asChild size="lg">
                    <Link to={individualSpineHealthProgramData.buttonLink}>
                      {individualSpineHealthProgramData.buttonText}
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Appointment Call to Action */}
        <AppointmentCallToActionSection
          title={finalT.patientResources.needAppointment}
          description={finalT.patientResources.appointmentDesc}
          primaryButtonText="Book Appointment"
          primaryButtonLink="/appointments"
          secondaryButtonText="Contact Us"
          secondaryButtonLink="/contact"
        />
      </main>
    </StandardPageLayout>
  );
};

PatientResources.displayName = 'PatientResources';

export default PatientResources;
