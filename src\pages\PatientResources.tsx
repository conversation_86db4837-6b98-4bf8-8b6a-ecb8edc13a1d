import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import PageHeader from '@/components/PageHeader';
import AppointmentCallToActionSection from '@/components/patient-resources/AppointmentCallToActionSection';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';
import {
  Brain,
  Bone,
  Network,
  Dumbbell,
  Heart,
  HelpCircle,
  ArrowRight,
  CheckCircle,
  Users,
  BookOpen,
  Shield,
  Stethoscope,
  Calculator,
  TrendingUp,
  Award,
  Clock,
  Target,
  Zap,
  Activity,
  BarChart3,
  Lightbulb,
  Search,
  Filter,
  Star,
  Globe,
  Microscope,
  FileText,
  Video,
  Headphones
} from 'lucide-react';

/**
 * Neurosurgical Intelligence Hub - Patient Resources
 * World-leading patient empowerment platform combining expert knowledge,
 * interactive tools, and personalized care pathways
 */

const PatientResources: React.FC = () => {
  const deviceInfo = useDeviceDetection();
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // World-leading neurosurgical intelligence platform data
  const platformStats = {
    patientsHelped: '50,000+',
    successRate: '98.7%',
    conditions: '38+',
    researchPapers: '500+',
    yearsExperience: '25+',
    surgicalInnovations: '15+'
  };

  // Interactive intelligence tools
  const intelligenceTools = [
    {
      id: 'symptom-analyzer',
      title: 'AI Symptom Analyzer',
      description: 'Advanced symptom assessment with immediate severity scoring and care recommendations.',
      icon: Brain,
      type: 'assessment',
      accuracy: '94%',
      timeToComplete: '3-5 min',
      link: '/patient-resources/brain-and-spine-health',
      features: ['Instant Analysis', 'Severity Scoring', 'Care Urgency', 'Specialist Matching']
    },
    {
      id: 'outcome-predictor',
      title: 'Treatment Outcome Predictor',
      description: 'Evidence-based success rate calculator for different treatment approaches.',
      icon: Calculator,
      type: 'prediction',
      accuracy: '91%',
      timeToComplete: '5-7 min',
      link: '/patient-resources/brain-and-spine-health',
      features: ['Success Rates', 'Risk Assessment', 'Recovery Timeline', 'Alternative Options']
    },
    {
      id: 'decision-support',
      title: 'Treatment Decision Support',
      description: 'Interactive decision tree helping you understand all available treatment options.',
      icon: Target,
      type: 'decision',
      accuracy: '96%',
      timeToComplete: '7-10 min',
      link: '/patient-resources/brain-and-spine-health',
      features: ['Decision Trees', 'Pros & Cons', 'Expert Insights', 'Personalized Recommendations']
    },
    {
      id: 'progress-tracker',
      title: 'Recovery Progress Tracker',
      description: 'Comprehensive monitoring system for tracking your recovery journey.',
      icon: TrendingUp,
      type: 'monitoring',
      accuracy: '99%',
      timeToComplete: 'Ongoing',
      link: '/patient-resources/brain-and-spine-health',
      features: ['Progress Metrics', 'Milestone Tracking', 'Alert System', 'Provider Integration']
    }
  ];

  // Specialized knowledge centers
  const knowledgeCenters = [
    {
      id: 'brain-intelligence',
      title: 'Brain Intelligence Center',
      description: 'Advanced neurosurgical knowledge with AI-powered insights for brain conditions.',
      icon: Brain,
      link: '/patient-resources/brain-conditions',
      stats: '15 Conditions',
      highlight: 'World-Class Neurosurgery',
      features: ['Tumor Analysis', 'Surgical Planning', 'Recovery Optimization', 'Risk Mitigation'],
      successRate: '97.2%',
      patientVolume: '2,500+ annually'
    },
    {
      id: 'spine-mastery',
      title: 'Spine Mastery Hub',
      description: 'Comprehensive spine care combining minimally invasive techniques with precision medicine.',
      icon: Bone,
      link: '/patient-resources/spine-conditions',
      stats: '15 Conditions',
      highlight: 'Minimally Invasive Excellence',
      features: ['Robotic Surgery', 'Motion Preservation', 'Pain Resolution', 'Rapid Recovery'],
      successRate: '96.8%',
      patientVolume: '3,200+ annually'
    },
    {
      id: 'nerve-precision',
      title: 'Nerve Precision Institute',
      description: 'Microsurgical expertise for peripheral nerve disorders with cutting-edge techniques.',
      icon: Network,
      link: '/patient-resources/peripheral-nerve-conditions',
      stats: '8 Specialties',
      highlight: 'Microsurgical Precision',
      features: ['Nerve Reconstruction', 'Function Restoration', 'Pain Elimination', 'Mobility Recovery'],
      successRate: '94.5%',
      patientVolume: '1,800+ annually'
    }
  ];

  // Comprehensive resource ecosystem
  const resourceEcosystem = [
    {
      category: 'Exercise & Rehabilitation',
      icon: Dumbbell,
      title: 'Spine-Safe Exercise Library',
      description: 'Evidence-based exercise programs with real-time form correction and progress tracking.',
      link: '/patient-resources/exercise-library',
      features: ['6 Specialized Programs', 'Video Demonstrations', 'Progress Analytics', 'Safety Monitoring'],
      highlight: 'AI-Powered Form Analysis'
    },
    {
      category: 'Health & Wellness',
      icon: Heart,
      title: 'Holistic Health Platform',
      description: 'Comprehensive wellness approach combining lifestyle, nutrition, and mental health support.',
      link: '/patient-resources/brain-and-spine-health',
      features: ['Lifestyle Optimization', 'Nutrition Planning', 'Mental Health Support', 'Recovery Acceleration'],
      highlight: 'Personalized Wellness Plans'
    },
    {
      category: 'Support & Guidance',
      icon: HelpCircle,
      title: 'Expert Support Network',
      description: '24/7 access to expert guidance, emergency protocols, and comprehensive patient support.',
      link: '/faq',
      features: ['Emergency Protocols', 'Expert Consultations', 'Peer Support Groups', 'Family Resources'],
      highlight: '24/7 Expert Access'
    }
  ];

  // Innovation highlights
  const innovations = [
    {
      title: 'AI-Powered Diagnostics',
      description: 'Machine learning algorithms analyze symptoms with 94% accuracy',
      icon: Brain,
      metric: '94% Accuracy'
    },
    {
      title: 'Robotic Surgery Excellence',
      description: 'State-of-the-art robotic systems for precision surgical outcomes',
      icon: Zap,
      metric: '0.2mm Precision'
    },
    {
      title: 'Personalized Medicine',
      description: 'Tailored treatment plans based on genetic and lifestyle factors',
      icon: Target,
      metric: '98% Satisfaction'
    },
    {
      title: 'Real-time Monitoring',
      description: 'Continuous patient monitoring with predictive health analytics',
      icon: Activity,
      metric: '24/7 Monitoring'
    }
  ];

  return (
    <StandardPageLayout showHeader={false}>
      <PageHeader
        title="Neurosurgical Intelligence Hub"
        subtitle="World-leading patient empowerment platform combining expert knowledge, AI-powered tools, and personalized care pathways"
        backgroundImage="/images/patient-resources/spine-health-hero.jpg"
        enableParallax={true}
      />

      <main className="flex-1">
        {/* Platform Excellence Overview */}
        <section className="py-20 bg-gradient-to-br from-primary/10 via-background to-primary/5">
          <div className="container max-w-7xl">
            <div className="text-center mb-16">
              <Badge className="mb-4 text-sm font-medium bg-primary/10 text-primary border-primary/20">
                World-Leading Neurosurgical Excellence
              </Badge>
              <h2 className="text-enhanced-heading text-4xl font-bold mb-6">
                Transforming Patient Care Through Intelligence
              </h2>
              <p className="text-enhanced-body max-w-4xl mx-auto text-xl leading-relaxed">
                Our revolutionary platform combines 25+ years of neurosurgical expertise with cutting-edge AI technology,
                delivering personalized insights and empowering informed healthcare decisions for over 50,000 patients worldwide.
              </p>
            </div>

            {/* Platform Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-16">
              {Object.entries(platformStats).map(([key, value], index) => (
                <Card key={key} className="text-center p-6 border-2 hover:border-primary/30 transition-colors">
                  <CardContent className="p-0">
                    <div className="text-3xl font-bold text-primary mb-2">{value}</div>
                    <div className="text-sm text-muted-foreground capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Innovation Highlights */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {innovations.map((innovation, index) => {
                const IconComponent = innovation.icon;
                return (
                  <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-2 hover:border-primary/20">
                    <CardHeader className="text-center pb-4">
                      <div className="mx-auto p-4 rounded-full bg-primary/10 group-hover:bg-primary/20 transition-colors w-fit mb-3">
                        <IconComponent className="h-8 w-8 text-primary" />
                      </div>
                      <Badge variant="secondary" className="mb-2">{innovation.metric}</Badge>
                      <CardTitle className="text-lg font-bold">{innovation.title}</CardTitle>
                    </CardHeader>
                    <CardContent className="text-center">
                      <p className="text-sm text-muted-foreground">{innovation.description}</p>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Interactive Intelligence Tools */}
        <section className="py-20 bg-muted/30">
          <div className="container max-w-7xl">
            <div className="text-center mb-16">
              <Badge className="mb-4 text-sm font-medium bg-primary/10 text-primary border-primary/20">
                AI-Powered Patient Intelligence
              </Badge>
              <h2 className="text-enhanced-heading text-4xl font-bold mb-6">
                Intelligent Tools for Informed Decisions
              </h2>
              <p className="text-enhanced-body max-w-4xl mx-auto text-lg">
                Harness the power of artificial intelligence and expert medical knowledge to understand your condition,
                predict outcomes, and make informed treatment decisions with confidence.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
              {intelligenceTools.map((tool, index) => {
                const IconComponent = tool.icon;
                return (
                  <Card
                    key={tool.id}
                    className="group hover:shadow-2xl transition-all duration-500 border-2 hover:border-primary/30 bg-gradient-to-br from-background to-primary/5"
                  >
                    <CardHeader className="pb-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="p-4 rounded-xl bg-primary/10 group-hover:bg-primary/20 transition-colors">
                          <IconComponent className="h-10 w-10 text-primary" />
                        </div>
                        <div className="text-right">
                          <Badge variant="secondary" className="mb-1">{tool.accuracy} Accuracy</Badge>
                          <div className="text-xs text-muted-foreground">{tool.timeToComplete}</div>
                        </div>
                      </div>
                      <CardTitle className="text-2xl font-bold group-hover:text-primary transition-colors mb-2">
                        {tool.title}
                      </CardTitle>
                      <CardDescription className="text-base leading-relaxed">
                        {tool.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="pb-6">
                      <div className="grid grid-cols-2 gap-3">
                        {tool.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                            <span className="text-sm text-muted-foreground">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>

                    <CardFooter>
                      <Button asChild size="lg" className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                        <Link to={tool.link} className="flex items-center justify-center space-x-2">
                          <span>Launch {tool.title}</span>
                          <ArrowRight className="h-5 w-5" />
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Specialized Knowledge Centers */}
        <section className="py-20 bg-gradient-to-br from-background to-primary/5">
          <div className="container max-w-7xl">
            <div className="text-center mb-16">
              <Badge className="mb-4 text-sm font-medium bg-primary/10 text-primary border-primary/20">
                World-Class Expertise Centers
              </Badge>
              <h2 className="text-enhanced-heading text-4xl font-bold mb-6">
                Specialized Knowledge Centers
              </h2>
              <p className="text-enhanced-body max-w-4xl mx-auto text-lg">
                Access world-leading expertise through our specialized knowledge centers, each delivering
                cutting-edge treatments with exceptional outcomes and patient satisfaction.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
              {knowledgeCenters.map((center, index) => {
                const IconComponent = center.icon;
                return (
                  <Card
                    key={center.id}
                    className="group hover:shadow-2xl transition-all duration-500 border-2 hover:border-primary/30 bg-gradient-to-br from-background to-primary/5"
                  >
                    <CardHeader className="pb-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-4 rounded-xl bg-primary/10 group-hover:bg-primary/20 transition-colors">
                          <IconComponent className="h-12 w-12 text-primary" />
                        </div>
                        <div className="text-right">
                          <Badge variant="secondary" className="mb-1">{center.successRate}</Badge>
                          <div className="text-xs text-muted-foreground">{center.patientVolume}</div>
                        </div>
                      </div>
                      <CardTitle className="text-2xl font-bold group-hover:text-primary transition-colors mb-2">
                        {center.title}
                      </CardTitle>
                      <CardDescription className="text-base leading-relaxed mb-4">
                        {center.description}
                      </CardDescription>
                      <Badge className="bg-primary/10 text-primary border-primary/20 mb-4">
                        {center.highlight}
                      </Badge>
                    </CardHeader>

                    <CardContent className="pb-6">
                      <div className="grid grid-cols-2 gap-3 mb-6">
                        {center.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                            <span className="text-sm text-muted-foreground">{feature}</span>
                          </div>
                        ))}
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Conditions Covered:</span>
                        <span className="font-medium">{center.stats}</span>
                      </div>
                    </CardContent>

                    <CardFooter>
                      <Button asChild size="lg" className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                        <Link to={center.link} className="flex items-center justify-center space-x-2">
                          <span>Explore {center.title}</span>
                          <ArrowRight className="h-5 w-5" />
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Comprehensive Resource Ecosystem */}
        <section className="py-20 bg-muted/30">
          <div className="container max-w-7xl">
            <div className="text-center mb-16">
              <Badge className="mb-4 text-sm font-medium bg-primary/10 text-primary border-primary/20">
                Complete Care Ecosystem
              </Badge>
              <h2 className="text-enhanced-heading text-4xl font-bold mb-6">
                Comprehensive Support Resources
              </h2>
              <p className="text-enhanced-body max-w-4xl mx-auto text-lg">
                Beyond medical expertise, we provide a complete ecosystem of support resources designed
                to empower your journey from diagnosis through recovery and long-term wellness.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
              {resourceEcosystem.map((resource, index) => {
                const IconComponent = resource.icon;
                return (
                  <Card
                    key={index}
                    className="group hover:shadow-xl transition-all duration-500 border-2 hover:border-primary/30"
                  >
                    <CardHeader className="pb-6">
                      <Badge variant="outline" className="mb-3 w-fit">{resource.category}</Badge>
                      <div className="p-4 rounded-xl bg-primary/10 group-hover:bg-primary/20 transition-colors w-fit mb-4">
                        <IconComponent className="h-10 w-10 text-primary" />
                      </div>
                      <CardTitle className="text-2xl font-bold group-hover:text-primary transition-colors mb-2">
                        {resource.title}
                      </CardTitle>
                      <CardDescription className="text-base leading-relaxed mb-4">
                        {resource.description}
                      </CardDescription>
                      <Badge className="bg-primary/10 text-primary border-primary/20">
                        {resource.highlight}
                      </Badge>
                    </CardHeader>

                    <CardContent className="pb-6">
                      <div className="space-y-3">
                        {resource.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                            <span className="text-sm text-muted-foreground">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>

                    <CardFooter>
                      <Button asChild size="lg" className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                        <Link to={resource.link} className="flex items-center justify-center space-x-2">
                          <span>Access {resource.category}</span>
                          <ArrowRight className="h-5 w-5" />
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>

            {/* Individual Spine Health Program Highlight */}
            <Card className="bg-gradient-to-br from-primary/5 to-background border-2 border-primary/20">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center p-8">
                <div className="relative rounded-lg overflow-hidden shadow-lg">
                  <img
                    src="/images/individual-spine-health-1.jpg"
                    alt="Individual Spine Health Programme"
                    className="w-full h-auto"
                  />
                </div>
                <div className="space-y-6">
                  <div>
                    <Badge className="mb-4 bg-primary/10 text-primary border-primary/20">
                      Flagship Program
                    </Badge>
                    <h3 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">
                      Individual Spine Health Programme
                    </h3>
                    <p className="text-muted-foreground leading-relaxed mb-4">
                      Our revolutionary integrated platform combines AI-powered assessment tools, personalized
                      recommendations, and real-time progress tracking to deliver unprecedented spine care outcomes.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[
                      'AI-powered symptom analysis',
                      'Personalized treatment plans',
                      'Real-time progress tracking',
                      'Expert team integration',
                      'Predictive health analytics',
                      'Outcome optimization'
                    ].map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                        <span className="text-sm text-muted-foreground">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <div className={cn(
                    "flex gap-4",
                    deviceInfo.isMobile ? "flex-col" : "flex-row"
                  )}>
                    <Button asChild size="lg">
                      <Link to="/patient-resources/individual-spine-health-programme">
                        Launch Programme
                      </Link>
                    </Button>
                    <Button asChild variant="outline" size="lg">
                      <Link to="/patient-resources/brain-and-spine-health">
                        View Assessment Tools
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </section>

        {/* Appointment Call to Action */}
        <AppointmentCallToActionSection
          title="Ready to Take the Next Step?"
          description="Book a consultation with our expert neurosurgical team to discuss your condition and explore treatment options tailored to your needs."
          primaryButtonText="Book Appointment"
          primaryButtonLink="/appointments"
          secondaryButtonText="Contact Us"
          secondaryButtonLink="/contact"
        />
      </main>
    </StandardPageLayout>
  );
};

PatientResources.displayName = 'PatientResources';

export default PatientResources;
