import React, { useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';

import PageHeader from '@/components/PageHeader';
import AppointmentCallToActionSection from '@/components/patient-resources/AppointmentCallToActionSection';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';
import {
  Brain,
  Bone,
  Network,
  Dumbbell,
  Heart,
  HelpCircle,
  ArrowRight,
  CheckCircle,
  Users,
  BookOpen,
  Shield,
  Stethoscope
} from 'lucide-react';

/**
 * Patient Resources Main Landing Page Component
 * Comprehensive landing page with navigation to all 6 sub-category landing pages
 * Features unique value propositions and clear navigation structure
 */

const PatientResources: React.FC = () => {
  const deviceInfo = useDeviceDetection();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Main category data with unique value propositions
  const mainCategories = [
    {
      id: 'brain-conditions',
      title: 'Brain Conditions',
      description: 'Comprehensive information about brain tumours, neurological disorders, and advanced treatment options.',
      icon: Brain,
      link: '/patient-resources/brain-conditions',
      stats: '15+ Conditions',
      highlight: 'Expert Neurosurgical Care',
      features: ['Brain Tumours', 'Neurological Disorders', 'Advanced Diagnostics', 'Treatment Options']
    },
    {
      id: 'spine-conditions',
      title: 'Spine Conditions',
      description: 'Complete guide to spine health, from common conditions to advanced surgical treatments.',
      icon: Bone,
      link: '/patient-resources/spine-conditions',
      stats: '15+ Conditions',
      highlight: 'Minimally Invasive Surgery',
      features: ['Disc Problems', 'Spinal Stenosis', 'Scoliosis', 'Recovery Guidance']
    },
    {
      id: 'peripheral-nerve-conditions',
      title: 'Peripheral Nerve Conditions',
      description: 'Specialized care for nerve entrapments, neuropathies, and peripheral nerve disorders.',
      icon: Network,
      link: '/patient-resources/peripheral-nerve-conditions',
      stats: '8+ Conditions',
      highlight: 'Precision Nerve Surgery',
      features: ['Carpal Tunnel', 'Nerve Entrapments', 'Neuropathies', 'Microsurgery']
    },
    {
      id: 'exercise-library',
      title: 'Exercise Library',
      description: 'Evidence-based exercise programs designed for spine health and safe rehabilitation.',
      icon: Dumbbell,
      link: '/patient-resources/exercise-library',
      stats: '6+ Programs',
      highlight: 'Spine-Safe Exercises',
      features: ['Rehabilitation', 'Pain Management', 'Strength Building', 'Safety Guidelines']
    },
    {
      id: 'brain-and-spine-health',
      title: 'Brain and Spine Health',
      description: 'Educational resources, anatomy guides, and lifestyle modifications for optimal health.',
      icon: Heart,
      link: '/patient-resources/brain-and-spine-health',
      stats: 'Comprehensive Guide',
      highlight: 'Holistic Health Approach',
      features: ['Anatomy Education', 'Lifestyle Tips', 'Recovery Support', 'Assessment Tools']
    },
    {
      id: 'faq',
      title: 'FAQ & Support',
      description: 'Frequently asked questions, emergency information, and comprehensive patient support.',
      icon: HelpCircle,
      link: '/faq',
      stats: '8+ Categories',
      highlight: '24/7 Emergency Guidance',
      features: ['Common Questions', 'Emergency Info', 'Preparation Guides', 'Support Resources']
    }
  ];

  // Additional resources and features
  const additionalFeatures = [
    {
      icon: BookOpen,
      title: 'Educational Materials',
      description: 'Comprehensive guides and resources to help you understand your condition and treatment options.',
      link: '/patient-resources/brain-and-spine-health'
    },
    {
      icon: Users,
      title: 'Patient Support',
      description: 'Connect with support groups and access mental health resources during your journey.',
      link: '/faq'
    },
    {
      icon: Shield,
      title: 'Safety Information',
      description: 'Important safety guidelines, emergency information, and preparation instructions.',
      link: '/faq'
    },
    {
      icon: Stethoscope,
      title: 'Assessment Tools',
      description: 'Interactive tools to help assess your symptoms and track your progress.',
      link: '/patient-resources/brain-and-spine-health'
    }
  ];

  // Key benefits of our patient resources
  const keyBenefits = [
    'Evidence-based medical information',
    'Comprehensive condition coverage',
    'Expert-reviewed content',
    'Interactive assessment tools',
    'Personalized exercise programs',
    'Emergency guidance available',
    'Regular content updates',
    'Mobile-friendly access'
  ];

  return (
    <StandardPageLayout showHeader={false}>
      <PageHeader
        title="Patient Resources"
        subtitle="Comprehensive medical information and support for your healthcare journey"
        backgroundImage="/images/patient-resources/spine-health-hero.jpg"
        enableParallax={true}
      />

      <main className="flex-1">
        {/* Hero Introduction */}
        <section className="py-16 bg-gradient-to-br from-primary/5 to-background">
          <div className="container max-w-7xl">
            <div className="text-center mb-12">
              <h2 className="text-enhanced-heading text-3xl font-bold mb-4">
                Your Complete Healthcare Resource Hub
              </h2>
              <p className="text-enhanced-body max-w-4xl mx-auto text-lg">
                Access expert-reviewed medical information, interactive tools, and comprehensive support
                resources designed to empower you throughout your healthcare journey. Our resources are
                organized into six key categories for easy navigation.
              </p>
            </div>

            {/* Key Benefits Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12">
              {keyBenefits.map((benefit, index) => (
                <div key={index} className="flex items-center space-x-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                  <span className="text-muted-foreground">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Main Categories Navigation */}
        <section className="py-16">
          <div className="container max-w-7xl">
            <div className="text-center mb-12">
              <h2 className="text-enhanced-heading text-3xl font-bold mb-4">
                Explore Our Resource Categories
              </h2>
              <p className="text-enhanced-body max-w-3xl mx-auto">
                Choose from our comprehensive categories to find the information and support you need.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {mainCategories.map((category, index) => {
                const IconComponent = category.icon;
                return (
                  <Card
                    key={category.id}
                    className={cn(
                      "medical-card group hover:shadow-xl transition-all duration-300 border-2 hover:border-primary/20",
                      "animate-fade-in"
                    )}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <CardHeader className="pb-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="p-3 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                          <IconComponent className="h-8 w-8 text-primary" />
                        </div>
                        <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full">
                          {category.stats}
                        </span>
                      </div>
                      <CardTitle className="text-xl font-bold group-hover:text-primary transition-colors">
                        {category.title}
                      </CardTitle>
                      <CardDescription className="text-sm leading-relaxed">
                        {category.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="pb-4">
                      <div className="mb-4">
                        <div className="flex items-center space-x-2 mb-3">
                          <CheckCircle className="h-4 w-4 text-primary" />
                          <span className="text-sm font-medium text-primary">{category.highlight}</span>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          {category.features.map((feature, featureIndex) => (
                            <div key={featureIndex} className="flex items-center space-x-1">
                              <div className="h-1.5 w-1.5 bg-primary/60 rounded-full" />
                              <span className="text-xs text-muted-foreground">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </CardContent>

                    <CardFooter>
                      <Button asChild className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                        <Link to={category.link} className="flex items-center justify-center space-x-2">
                          <span>Explore {category.title}</span>
                          <ArrowRight className="h-4 w-4" />
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Additional Features Section */}
        <section className="py-16 bg-muted/30">
          <div className="container max-w-7xl">
            <div className="text-center mb-12">
              <h2 className="text-enhanced-heading text-3xl font-bold mb-4">
                Additional Resources & Support
              </h2>
              <p className="text-enhanced-body max-w-3xl mx-auto">
                Beyond our main categories, we offer additional tools and resources to support
                your healthcare journey and provide comprehensive care.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {additionalFeatures.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <Card
                    key={index}
                    className="medical-card group hover:shadow-lg transition-all duration-300"
                  >
                    <CardHeader className="text-center pb-4">
                      <div className="mx-auto p-3 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors w-fit">
                        <IconComponent className="h-6 w-6 text-primary" />
                      </div>
                      <CardTitle className="text-lg font-semibold group-hover:text-primary transition-colors">
                        {feature.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="text-center pb-4">
                      <CardDescription className="text-sm leading-relaxed">
                        {feature.description}
                      </CardDescription>
                    </CardContent>
                    <CardFooter>
                      <Button asChild variant="outline" className="w-full">
                        <Link to={feature.link}>Learn More</Link>
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Individual Spine Health Program Highlight */}
        <section className="py-16 bg-gradient-to-br from-primary/5 to-background">
          <div className="container max-w-7xl">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="relative rounded-lg overflow-hidden shadow-lg">
                <img
                  src="/images/individual-spine-health-1.jpg"
                  alt="Individual Spine Health Programme"
                  className="w-full h-auto"
                />
              </div>
              <div className="space-y-6">
                <div>
                  <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">
                    Individual Spine Health Programme
                  </h2>
                  <p className="text-muted-foreground leading-relaxed mb-4">
                    Our integrated approach combines assessment tools, personalised recommendations,
                    and progress tracking in one comprehensive platform to help you understand and
                    manage your spine condition effectively.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    'Detailed symptom assessment',
                    'Evidence-based resources',
                    'Personalised exercises',
                    'Progress tracking',
                    'Secure dashboard access',
                    'Healthcare team integration'
                  ].map((feature, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                      <span className="text-sm text-muted-foreground">{feature}</span>
                    </div>
                  ))}
                </div>

                <div className={cn(
                  "flex gap-4",
                  deviceInfo.isMobile ? "flex-col" : "flex-row"
                )}>
                  <Button asChild size="lg">
                    <Link to="/patient-resources/individual-spine-health-programme">
                      Explore Programme
                    </Link>
                  </Button>
                  <Button asChild variant="outline" size="lg">
                    <Link to="/patient-resources/brain-and-spine-health">
                      View Assessment Tools
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Appointment Call to Action */}
        <AppointmentCallToActionSection
          title="Ready to Take the Next Step?"
          description="Book a consultation with our expert neurosurgical team to discuss your condition and explore treatment options tailored to your needs."
          primaryButtonText="Book Appointment"
          primaryButtonLink="/appointments"
          secondaryButtonText="Contact Us"
          secondaryButtonLink="/contact"
        />
      </main>
    </StandardPageLayout>
  );
};

PatientResources.displayName = 'PatientResources';

export default PatientResources;
