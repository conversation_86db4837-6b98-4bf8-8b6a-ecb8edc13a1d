import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import ExpertiseCallToAction from '@/components/expertise/ExpertiseCallToAction';
import ExpertiseHero from '@/components/expertise/ExpertiseHero';
import ExpertiseSidebar from '@/components/expertise/ExpertiseSidebar';
import SafeImage from '@/components/SafeImage';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { imageGuidedBrainSurgeryData } from '@/data/expertise/imageGuidedBrainSurgeryData';

/**
 * Image-Guided Brain Surgery Component
 * Comprehensive brain surgery page with specialised content from documentation
 * Focuses on brain-specific applications of image-guided surgery
 */

const ImageGuidedBrainSurgery: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const data = imageGuidedBrainSurgeryData;

  return (
    <StandardPageLayout title="Image-Guided Brain Surgery" showHeader={false}>
      <ExpertiseHero
        title={data.hero.title}
        subtitle={data.hero.subtitle}
        backgroundImage={data.hero.backgroundImage}
      />

      <div className="flex-1">
        {/* Modern Navigation Header */}
        <section className="py-6 bg-muted/30 border-b">
          <div className="container px-4 sm:px-6">
            <div className="flex items-centre justify-between">
              <div className="flex items-centre gap-4">
                <Link
                  to="/expertise/image-guided-surgery"
                  className="flex items-centre gap-2 text-muted-foreground hover:text-primary transition-colors group"
                >
                  <svg className="w-5 h-5 group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  <span className="text-sm font-medium">Image-Guided Surgery</span>
                </Link>
              </div>
              <div className="text-right">
                <h1 className="text-xl font-bold text-foreground">Brain Surgery</h1>
                <p className="text-sm text-muted-foreground">Advanced Neurosurgical Procedures</p>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              <div className="lg:col-span-2">
                {/* Overview */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.overview.title}</h2>
                  {data.overview.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-6">
                      {paragraph}
                    </p>
                  ))}
                  
                  <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                    <SafeImage
                      src={data.images.overview}
                      alt="Advanced image-guided brain surgery technology"
                      className="w-full h-full object-cover"
                      fallbackSrc="/images/medical-consulting.jpg"
                    />
                  </div>
                </div>

                {/* Navigation Technologies */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.navigationTechnologies.title}</h2>
                  <p className="text-muted-foreground mb-6">{data.navigationTechnologies.introduction}</p>
                  
                  <div className="space-y-6">
                    {data.navigationTechnologies.technologies.map((tech, index) => (
                      <div key={index} className="border rounded-lg p-6 medical-card">
                        <h3 className="text-lg font-semibold mb-3 text-primary">{tech.title}</h3>
                        <p className="text-muted-foreground">{tech.description}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Clinical Applications */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.clinicalApplications.title}</h2>
                  
                  <div className="space-y-8">
                    {data.clinicalApplications.sections.map((section, index) => (
                      <div key={index} className="bg-muted/30 p-6 rounded-lg">
                        <h3 className="text-xl font-semibold mb-4">{section.title}</h3>
                        {section.content.map((paragraph, pIndex) => (
                          <p key={pIndex} className="text-muted-foreground mb-4">
                            {paragraph}
                          </p>
                        ))}
                        
                        {section.procedures && (
                          <div className="mt-4">
                            <h4 className="text-lg font-semibold mb-3">Key Procedures:</h4>
                            <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                              {section.procedures.map((procedure, procIndex) => (
                                <li key={procIndex}>{procedure}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Technological Advantages */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.technologicalAdvantages.title}</h2>
                  
                  <div className="space-y-6">
                    {data.technologicalAdvantages.sections.map((section, index) => (
                      <div key={index} className="bg-primary/10 p-6 rounded-lg">
                        <h3 className="text-lg font-semibold mb-3 text-primary">{section.title}</h3>
                        {section.content.map((paragraph, pIndex) => (
                          <p key={pIndex} className="text-muted-foreground mb-4">
                            {paragraph}
                          </p>
                        ))}
                        
                        {section.features && (
                          <div className="grid grid-cols-2 gap-2 mt-4">
                            {section.features.map((feature, featureIndex) => (
                              <div key={featureIndex} className="flex items-centre text-sm text-muted-foreground">
                                <span className="w-2 h-2 bg-primary rounded-full mr-2"></span>
                                {feature}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Patient Benefits */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.patientBenefits.title}</h2>
                  {data.patientBenefits.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-4">
                      {paragraph}
                    </p>
                  ))}
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                    {data.patientBenefits.benefits.map((benefit, index) => (
                      <div key={index} className="flex items-centre text-sm text-muted-foreground bg-success border border-success p-3 rounded">
                        <span className="w-2 h-2 bg-success rounded-full mr-3"></span>
                        {benefit}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Clinical Outcomes */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.outcomes.title}</h2>
                  {data.outcomes.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-4">
                      {paragraph}
                    </p>
                  ))}
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                    {data.outcomes.statistics.map((stat, index) => (
                      <div key={index} className="text-centre p-4 bg-primary/5 rounded-lg">
                        <div className="text-enhanced-heading text-2xl font-bold text-primary mb-2">{stat.value}</div>
                        <div className="text-sm font-semibold mb-1">{stat.metric}</div>
                        <div className="text-xs text-muted-foreground">{stat.description}</div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Related Pages Navigation */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Related Image-Guided Procedures</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="card p-6 rounded-lg shadow-md medical-card border-l-4 border-l-primary">
                      <h3 className="text-xl font-semibold mb-3 text-primary">Image-Guided Surgery Overview</h3>
                      <p className="text-muted-foreground mb-4">
                        Learn about the general principles and applications of image-guided surgery across neurosurgery.
                      </p>
                      <Button asChild variant="outline">
                        <Link to="/expertise/image-guided-surgery">Learn More</Link>
                      </Button>
                    </div>
                    
                    <div className="card p-6 rounded-lg shadow-md medical-card border-l-4 border-l-secondary">
                      <h3 className="text-xl font-semibold mb-3 text-secondary">Image-Guided Spine Surgery</h3>
                      <p className="text-muted-foreground mb-4">
                        Discover how image-guided technology enhances precision in spinal procedures and instrumentation.
                      </p>
                      <Button asChild variant="outline">
                        <Link to="/expertise/image-guided-spine-surgery">Learn More</Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <ExpertiseSidebar sections={data.sidebar} />
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <ExpertiseCallToAction
          title={data.callToAction.title}
          description={data.callToAction.description}
          primaryButton={data.callToAction.primaryButton}
          secondaryButton={data.callToAction.secondaryButton}
        />
      </div>
    </StandardPageLayout>
  );
};

ImageGuidedBrainSurgery.displayName = 'ImageGuidedBrainSurgery';

export default ImageGuidedBrainSurgery;
