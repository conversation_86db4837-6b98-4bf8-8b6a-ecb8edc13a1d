import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import ExpertiseCallToAction from '@/components/expertise/ExpertiseCallToAction';
import ExpertiseHero from '@/components/expertise/ExpertiseHero';
import ExpertiseSection from '@/components/expertise/ExpertiseSection';
import ExpertiseSidebar from '@/components/expertise/ExpertiseSidebar';
import SafeImage from '@/components/SafeImage';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { imageGuidedSurgeryData } from '@/data/expertise/imageGuidedSurgeryData';

/**
 * Image-Guided Surgery Component
 * Unified implementation combining comprehensive content with modular architecture
 * Includes all sections from both original and refactored versions plus documentation content
 */

const ImageGuidedSurgery: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const data = imageGuidedSurgeryData;

  // Enhanced content from documentation
  const imagingModalities = [
    {
      title: 'Intraoperative CT Scanning',
      features: ['Real-time bone visualization', 'Immediate verification', 'Precision guidance', 'Quality assurance']
    },
    {
      title: 'MRI Integration',
      features: ['Soft tissue visualization', 'Tumor localization', 'Functional mapping', 'Safety monitoring']
    },
    {
      title: 'Fluoroscopy Navigation',
      features: ['Real-time X-ray guidance', 'Instrument tracking', 'Continuous monitoring', 'Radiation optimization']
    }
  ];

  const surgicalApplications = [
    {
      category: 'Spine Surgery Applications',
      procedures: ['Pedicle screw placement', 'Tumor resection', 'Deformity correction', 'Minimally invasive procedures']
    },
    {
      category: 'Brain Surgery Applications',
      procedures: ['Tumor localization and removal', 'Functional neurosurgery', 'Stereotactic procedures', 'Vascular interventions']
    }
  ];

  const precisionFeatures = [
    {
      title: 'Submillimeter Accuracy',
      description: 'Advanced tracking systems provide real-time feedback with error correction and quality verification.'
    },
    {
      title: 'Safety Enhancements',
      description: 'Critical structure avoidance with real-time monitoring, immediate adjustments, and risk minimization.'
    }
  ];

  const patientBenefits = [
    {
      category: 'Minimally Invasive Approach',
      benefits: ['Smaller incisions', 'Reduced tissue trauma', 'Faster healing', 'Better cosmetic results']
    },
    {
      category: 'Improved Outcomes',
      benefits: ['Higher precision', 'Reduced complications', 'Shorter recovery', 'Better functional results']
    }
  ];

  return (
    <StandardPageLayout title="Image-Guided Surgery" showHeader={false}>
      <ExpertiseHero
        title={data.hero.title}
        subtitle={data.hero.subtitle}
        backgroundImage={data.hero.backgroundImage}
      />

      <div className="flex-1">

        {/* Main Content */}
        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              <div className="lg:col-span-2">
                {/* Main Sections */}
                {data.sections.map((section) => (
                  <ExpertiseSection key={section.id} section={section} />
                ))}

                {/* Technology Overview */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Advanced Navigation Technology</h2>
                  <p className="text-muted-foreground mb-6">
                    Image-guided surgery represents cutting-edge medical technology that combines real-time surgical navigation with 3D imaging integration, providing precision targeting capabilities and enhanced surgical safety.
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-primary/5 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4">Technology Benefits</h3>
                      <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                        <li>Submillimeter accuracy</li>
                        <li>Reduced complications</li>
                        <li>Minimized tissue damage</li>
                        <li>Improved surgical outcomes</li>
                      </ul>
                    </div>

                    <div className="bg-primary/5 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4">Navigation Systems</h3>
                      <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                        <li>Real-time surgical navigation</li>
                        <li>3D imaging integration</li>
                        <li>Precision targeting capabilities</li>
                        <li>Enhanced surgical safety</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Imaging Modalities */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Imaging Modalities</h2>
                  <p className="text-muted-foreground mb-6">
                    Various imaging technologies are integrated to provide comprehensive visualization during surgery:
                  </p>

                  <div className="space-y-6">
                    {imagingModalities.map((modality, index) => (
                      <div key={index} className="border rounded-lg p-6 medical-card">
                        <h3 className="text-lg font-semibold mb-3 text-primary">{modality.title}</h3>
                        <div className="grid grid-cols-2 gap-2">
                          {modality.features.map((feature, featureIndex) => (
                            <div key={featureIndex} className="flex items-center text-sm text-muted-foreground">
                              <span className="w-2 h-2 bg-primary rounded-full mr-2"></span>
                              {feature}
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Surgical Applications */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Surgical Applications</h2>
                  <p className="text-muted-foreground mb-6">
                    Image-guided surgery is utilized across various neurosurgical procedures:
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {surgicalApplications.map((application, index) => (
                      <div key={index} className="bg-muted/30 p-6 rounded-lg">
                        <h3 className="text-lg font-semibold mb-4">{application.category}</h3>
                        <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                          {application.procedures.map((procedure, procIndex) => (
                            <li key={procIndex}>{procedure}</li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Process */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.process.title}</h2>
                  <p className="text-muted-foreground mb-4">{data.process.description}</p>
                  <ol className="list-decimal list-inside text-muted-foreground mb-8 space-y-2">
                    {data.process.steps.map((step, index) => (
                      <li key={index}>{step}</li>
                    ))}
                  </ol>
                </div>

                {/* Precision and Accuracy */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Precision and Accuracy</h2>
                  <p className="text-muted-foreground mb-6">
                    The precision capabilities of image-guided surgery ensure optimal surgical outcomes:
                  </p>

                  <div className="space-y-6">
                    {precisionFeatures.map((feature, index) => (
                      <div key={index} className="bg-primary/10 p-6 rounded-lg">
                        <h3 className="text-lg font-semibold mb-3 text-primary">{feature.title}</h3>
                        <p className="text-muted-foreground">{feature.description}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Patient Benefits */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Patient Benefits</h2>
                  <p className="text-muted-foreground mb-6">
                    Image-guided surgery offers significant advantages for patients:
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {patientBenefits.map((benefit, index) => (
                      <div key={index} className="bg-success border border-success p-6 rounded-lg">
                        <h3 className="text-lg font-semibold mb-4 text-success">{benefit.category}</h3>
                        <ul className="list-disc pl-5 space-y-2 text-success">
                          {benefit.benefits.map((item, itemIndex) => (
                            <li key={itemIndex}>{item}</li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Benefits */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Benefits of Image-Guided Surgery</h2>
                  <p className="text-muted-foreground mb-4">
                    The use of navigated technology has multiple benefits for patients when compared to traditional open surgery:
                  </p>
                  <ul className="list-disc list-inside text-muted-foreground mb-8 space-y-2">
                    {data.benefits.map((benefit, index) => (
                      <li key={index}>{benefit}</li>
                    ))}
                  </ul>

                  <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                    <SafeImage
                      src={data.images.drAliashkevich.src}
                      alt={data.images.drAliashkevich.alt}
                      className="w-full h-full object-cover"
                      fallbackSrc="/images/medical-consulting.jpg"
                    />
                  </div>
                </div>

                {/* Specialized Applications */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Specialised Applications</h2>
                  <p className="text-muted-foreground mb-6">
                    Image-guided surgery encompasses specialised applications across different areas of neurosurgery. Explore our comprehensive coverage of brain and spine procedures:
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Brain Surgery Card */}
                    <div className="card p-8 rounded-lg shadow-lg bg-muted border-l-4 border-l-blue-500">
                      <div className="flex items-center mb-4">
                        <div className="w-12 h-12 bg-info rounded-lg flex items-center justify-center mr-4">
                          <span className="text-primary-foreground text-2xl">🧠</span>
                        </div>
                        <h3 className="text-xl font-bold text-info">Image-Guided Brain Surgery</h3>
                      </div>
                      <p className="text-info mb-6">
                        Revolutionary precision in neurosurgical care with cutting-edge navigation technologies for brain tumors, vascular malformations, and stereotactic procedures.
                      </p>
                      <ul className="list-disc pl-5 space-y-2 text-info mb-6 text-sm">
                        <li>Brain tumor surgery with intraoperative MRI</li>
                        <li>Vascular neurosurgery procedures</li>
                        <li>Stereotactic brain biopsy</li>
                        <li>Awake craniotomy procedures</li>
                        <li>Deep brain stimulation placement</li>
                      </ul>
                      <Button asChild className="w-full bg-info hover:bg-info">
                        <Link to="/expertise/image-guided-brain-surgery">Explore Brain Surgery</Link>
                      </Button>
                    </div>

                    {/* Spine Surgery Card */}
                    <div className="card p-8 rounded-lg shadow-lg bg-muted border-l-4 border-l-green-500">
                      <div className="flex items-center mb-4">
                        <div className="w-12 h-12 bg-success rounded-lg flex items-center justify-center mr-4">
                          <span className="text-primary-foreground text-2xl">🦴</span>
                        </div>
                        <h3 className="text-xl font-bold text-success">Image-Guided Spine Surgery</h3>
                      </div>
                      <p className="text-success mb-6">
                        Sub-millimetre accuracy for decompression, stabilisation and motion-preserving spinal procedures with advanced navigation technology.
                      </p>
                      <ul className="list-disc pl-5 space-y-2 text-success mb-6 text-sm">
                        <li>Precision pedicle screw placement</li>
                        <li>Complex spinal deformity correction</li>
                        <li>Minimally invasive spine procedures</li>
                        <li>Spinal tumor resection</li>
                        <li>Revision spine surgery</li>
                      </ul>
                      <Button asChild className="w-full bg-success hover:bg-success">
                        <Link to="/expertise/image-guided-spine-surgery">Explore Spine Surgery</Link>
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Technology Integration */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Technology Integration</h2>
                  <p className="text-muted-foreground mb-6">
                    Image guidance seamlessly integrates with other advanced technologies:
                  </p>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-primary/5 rounded-lg">
                      <div className="text-sm font-semibold">Robotic Surgery</div>
                      <div className="text-xs text-muted-foreground">Compatibility</div>
                    </div>
                    <div className="text-center p-4 bg-primary/5 rounded-lg">
                      <div className="text-sm font-semibold">Navigation Systems</div>
                      <div className="text-xs text-muted-foreground">Integration</div>
                    </div>
                    <div className="text-center p-4 bg-primary/5 rounded-lg">
                      <div className="text-sm font-semibold">Real-time</div>
                      <div className="text-xs text-muted-foreground">Monitoring</div>
                    </div>
                    <div className="text-center p-4 bg-primary/5 rounded-lg">
                      <div className="text-sm font-semibold">Quality Assurance</div>
                      <div className="text-xs text-muted-foreground">Protocols</div>
                    </div>
                  </div>
                </div>

                <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Advanced Imaging Technologies</h2>
                <p className="text-muted-foreground mb-4">
                  <a href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">Dr Aliashkevich</a> utilizes several cutting-edge imaging technologies in his practice:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <div className="card p-6 rounded-lg shadow-md medical-card">
                    <h3 className="text-xl font-semibold mb-3 text-primary">Magnetic Resonance Imaging (MRI)</h3>
                    <p className="text-muted-foreground">
                      Nowadays, MRI is the number one modality in neurosurgical and spinal imaging due to its superb image resolution and contrast. We use it in all patients with brain and spine problems with only rare exceptions when it is not possible due to old cardiac pacemakers, cochlear implants, stimulators or other medical conditions. High-quality MRI is available not only at a preoperative planning stage but can also be used intraoperatively, e.g. to estimate the extent of tumour resection.
                    </p>
                  </div>

                  <div className="card p-6 rounded-lg shadow-md medical-card">
                    <h3 className="text-xl font-semibold mb-3 text-primary">Computed Tomography (CT)</h3>
                    <p className="text-muted-foreground">
                      CT is the preferred modality for visualisation of bone and is much more compact than MRI. It is also significantly faster and cheaper, which makes it ideally suited for spine image-guided surgery when accurate placement of vertebral screws is required. It can also be used for intracranial surgery, including lesion removal, biopsy and other stereotactic procedures. It can also be used in conjunction with other imaging modalities, e.g. SPECT or PET for better representation of metabolic changes and chronic inflammatory conditions.
                    </p>
                  </div>

                  <div className="card p-6 rounded-lg shadow-md medical-card">
                    <h3 className="text-xl font-semibold mb-3 text-primary">Neuronavigation Systems</h3>
                    <p className="text-muted-foreground">
                      Advanced computer systems that integrate pre-operative imaging with real-time surgical navigation, providing continuous guidance throughout the procedure. These systems allow for precise tracking of surgical instruments in relation to the patient's anatomy.
                    </p>
                  </div>

                  <div className="card p-6 rounded-lg shadow-md medical-card">
                    <h3 className="text-xl font-semibold mb-3 text-primary">Fluoroscopy and X-ray C-arms</h3>
                    <p className="text-muted-foreground">
                      Although standard x-rays in fluoroscopy provide only two-dimensional images, the technological evolution allowed implementation of rotational C-arms which provide high image quality, compatible with CT scanner. The images obtained before or during surgery are uploaded into the computer workstation and processed to plan the approach trajectory, position of the lesion, predict placement of the implants, and manipulate 3D views during surgery.
                    </p>
                  </div>
                </div>

                <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                  <SafeImage
                    src="/images/brain-tumour-navigated-image-guided-surgery-miNEURO-Aliashekvich-robotic.jpg"
                    alt="Advanced neuronavigation system used for image-guided brain surgery"
                    className="w-full h-full object-cover"
                    fallbackSrc="/images/medical-consulting.jpg"
                  />
                </div>

                <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Why Choose Dr Aliashkevich for Image-Guided Surgery?</h2>
                <p className="text-muted-foreground mb-4">
                  <a href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">Dr Aliashkevich</a> is highly experienced in image-guided neurosurgical procedures and offers:
                </p>
                <ul className="list-disc list-inside text-muted-foreground mb-8 space-y-2">
                  <li>Expertise in the latest navigation technologies and techniques</li>
                  <li>Specialised training in minimally invasive approaches</li>
                  <li>Personalized treatment plans tailored to each patient's unique anatomy and condition</li>
                  <li>Comprehensive pre-operative planning using advanced imaging</li>
                  <li>Commitment to achieving optimal surgical outcomes with minimal invasiveness</li>
                  <li>Experience with complex brain and spine pathologies requiring precise navigation</li>
                  <li>Access to state-of-the-art imaging and navigation equipment</li>
                  <li>Ongoing education and training in the latest image-guided techniques</li>
                </ul>

                <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                  <SafeImage
                    src="/images/Aliashkevich-operating-spine-brain-image-guided-neurosurgeon-robotic-spine-Mazor-surgery.jpg"
                    alt="Dr Aliashkevich performing image-guided spine surgery with robotic assistance"
                    className="w-full h-full object-cover"
                    fallbackSrc="/images/medical-consulting.jpg"
                  />
                </div>

                <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Robotic Spine Surgery: The Evolution of Image-Guided Technology</h2>
                <p className="text-muted-foreground mb-6">
                  Robotic technology is the logical step in the evolution of image-guided surgical systems, which contributes to the surgical toolset of minimally invasive approaches. Robotic spine surgery combines the precision of image-guided navigation with robotic assistance to further enhance surgical accuracy and patient outcomes.
                </p>

                <p className="text-muted-foreground mb-6">
                  The robotic system works in conjunction with image-guided navigation to provide:
                </p>

                <ul className="list-disc list-inside text-muted-foreground mb-8 space-y-2">
                  <li>Enhanced precision in screw placement</li>
                  <li>Reduced radiation exposure for both patient and surgical team</li>
                  <li>Improved surgical planning capabilities</li>
                  <li>Increased stability during the procedure</li>
                  <li>Potential for smaller incisions and less tissue disruption</li>
                </ul>

                <p className="text-muted-foreground mb-6">
                  <a href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">Dr Aliashkevich</a> utilizes robotic assistance for appropriate spine procedures, combining the benefits of image-guided navigation with robotic precision for optimal patient outcomes.
                </p>

                <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                  <SafeImage
                    src="/images/robotic-spine-surgery-lumbar-fusion-minimally-invasive-Mazor-Stryker-NuVasive-Renaissance-navigation-neurosurgery.jpg"
                    alt="Robotic spine surgery system used in conjunction with image-guided navigation"
                    className="w-full h-full object-cover"
                    fallbackSrc="/images/medical-consulting.jpg"
                  />
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <ExpertiseSidebar sections={data.sidebar} />
              </div>
            </div>
          </div>
        </section>
        {/* Call to Action */}
        <ExpertiseCallToAction
          title={data.callToAction.title}
          description={data.callToAction.description}
          primaryButton={data.callToAction.primaryButton}
          secondaryButton={data.callToAction.secondaryButton}
        />
      </div>
    </StandardPageLayout>
  );
};

ImageGuidedSurgery.displayName = 'ImageGuidedSurgery';

export default ImageGuidedSurgery;
