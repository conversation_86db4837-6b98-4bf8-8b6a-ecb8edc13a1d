import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import ExpertiseCallToAction from '@/components/expertise/ExpertiseCallToAction';
import ExpertiseHero from '@/components/expertise/ExpertiseHero';
import ExpertiseSidebar from '@/components/expertise/ExpertiseSidebar';
import SafeImage from '@/components/SafeImage';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { roboticSpineSurgeryProceduresData } from '@/data/expertise/roboticSpineSurgeryProceduresData';

/**
 * Robotic Spine Surgery Procedures Component
 * Comprehensive procedures page with specialised content from documentation
 * Focuses on clinical applications, evidence-based outcomes, and patient benefits
 */

const RoboticSpineSurgeryProcedures: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const data = roboticSpineSurgeryProceduresData;

  return (
    <StandardPageLayout title="Robotic Spine Surgery Procedures" showHeader={false}>
      <ExpertiseHero
        title={data.hero.title}
        subtitle={data.hero.subtitle}
        backgroundImage={data.hero.backgroundImage}
      />

      <div className="flex-1">
        {/* Modern Navigation Header */}
        <section className="py-6 bg-muted/30 border-b">
          <div className="container px-4 sm:px-6">
            <div className="flex items-centre justify-between">
              <div className="flex items-centre gap-4">
                <Link
                  to="/expertise/robotic-spine-surgery"
                  className="flex items-centre gap-2 text-muted-foreground hover:text-primary transition-colors group"
                >
                  <svg className="w-5 h-5 group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  <span className="text-sm font-medium">Robotic Spine Surgery</span>
                </Link>
              </div>
              <div className="text-right">
                <h1 className="text-xl font-bold text-foreground">Procedures</h1>
                <p className="text-sm text-muted-foreground">Clinical Applications & Outcomes</p>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              <div className="lg:col-span-2">
                {/* Executive Summary */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.executiveSummary.title}</h2>
                  {data.executiveSummary.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-6">
                      {paragraph}
                    </p>
                  ))}
                </div>

                {/* Overview */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.overview.title}</h2>
                  {data.overview.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-6">
                      {paragraph}
                    </p>
                  ))}
                  
                  <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                    <SafeImage
                      src={data.images.overview}
                      alt="Robotic spine surgery procedure in progress"
                      className="w-full h-full object-cover"
                      fallbackSrc="/images/medical-consulting.jpg"
                    />
                  </div>
                </div>

                {/* Major Platforms */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.majorPlatforms.title}</h2>
                  <p className="text-muted-foreground mb-6">{data.majorPlatforms.introduction}</p>
                  
                  <div className="space-y-4 mb-6">
                    {data.majorPlatforms.platforms.map((platform, index) => (
                      <div key={index} className="border rounded-lg p-6 medical-card">
                        <div className="flex items-centre justify-between mb-3">
                          <h3 className="text-lg font-semibold text-primary">{platform.system}</h3>
                          <span className="text-sm text-muted-foreground">{platform.manufacturer}</span>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {platform.keyFeatures.map((feature, featureIndex) => (
                            <div key={featureIndex} className="flex items-centre text-sm text-muted-foreground">
                              <span className="w-2 h-2 bg-primary rounded-full mr-2"></span>
                              {feature}
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="bg-info border border-info p-4 rounded-lg">
                    <p className="text-info text-sm">{data.majorPlatforms.additionalInfo}</p>
                  </div>
                </div>

                {/* How It Works */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.howItWorks.title}</h2>
                  <p className="text-muted-foreground mb-6">{data.howItWorks.introduction}</p>
                  
                  <div className="space-y-4">
                    {data.howItWorks.steps.map((step, index) => (
                      <div key={index} className="flex items-start space-x-4 p-4 bg-muted/30 rounded-lg">
                        <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre text-sm font-bold">
                          {index + 1}
                        </div>
                        <div>
                          <h3 className="font-semibold mb-2">{step.step}</h3>
                          <p className="text-muted-foreground text-sm">{step.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Benefiting Conditions */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.benefitingConditions.title}</h2>
                  <p className="text-muted-foreground mb-6">{data.benefitingConditions.introduction}</p>
                  
                  <div className="space-y-6 mb-6">
                    {data.benefitingConditions.conditions.map((condition, index) => (
                      <div key={index} className="bg-muted/30 p-6 rounded-lg">
                        <h3 className="text-lg font-semibold mb-3">{condition.category}</h3>
                        <p className="text-muted-foreground mb-4 text-sm">{condition.description}</p>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                          {condition.examples.map((example, exampleIndex) => (
                            <div key={exampleIndex} className="flex items-centre text-sm text-muted-foreground">
                              <span className="w-2 h-2 bg-primary rounded-full mr-2"></span>
                              {example}
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="bg-info border border-info p-4 rounded-lg">
                    <p className="text-info text-sm">{data.benefitingConditions.additionalNote}</p>
                  </div>
                </div>

                {/* Documented Advantages */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.documentedAdvantages.title}</h2>
                  <p className="text-muted-foreground mb-6">{data.documentedAdvantages.introduction}</p>
                  
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse border border-border rounded-lg">
                      <thead>
                        <tr className="bg-primary/10">
                          <th className="border border-border p-3 text-left font-semibold">Domain</th>
                          <th className="border border-border p-3 text-left font-semibold">Robotic</th>
                          <th className="border border-border p-3 text-left font-semibold">Traditional</th>
                          <th className="border border-border p-3 text-left font-semibold">Evidence</th>
                        </tr>
                      </thead>
                      <tbody>
                        {data.documentedAdvantages.comparisons.map((comparison, index) => (
                          <tr key={index} className={index % 2 === 0 ? 'bg-muted' : 'bg-background'}>
                            <td className="border border-border p-3 font-medium">{comparison.domain}</td>
                            <td className="border border-border p-3 text-success font-semibold">{comparison.robotic}</td>
                            <td className="border border-border p-3 text-info">{comparison.traditional}</td>
                            <td className="border border-border p-3 text-sm text-muted-foreground">{comparison.evidence}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* Clinical Outcomes */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.clinicalOutcomes.title}</h2>
                  {data.clinicalOutcomes.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-4">
                      {paragraph}
                    </p>
                  ))}
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                    {data.clinicalOutcomes.outcomes.map((outcome, index) => (
                      <div key={index} className="text-centre p-4 bg-primary/5 rounded-lg">
                        <div className="text-enhanced-heading text-2xl font-bold text-primary mb-2">{outcome.value}</div>
                        <div className="text-sm font-semibold mb-1">{outcome.metric}</div>
                        <div className="text-xs text-muted-foreground">{outcome.description}</div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Patient Benefits */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.patientBenefits.title}</h2>
                  {data.patientBenefits.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-4">
                      {paragraph}
                    </p>
                  ))}
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                    {data.patientBenefits.benefits.map((benefit, index) => (
                      <div key={index} className="flex items-centre text-sm text-muted-foreground bg-success border border-success p-3 rounded">
                        <span className="w-2 h-2 bg-success rounded-full mr-3"></span>
                        {benefit}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Related Pages Navigation */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Related Robotic Surgery Information</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="card p-6 rounded-lg shadow-md medical-card border-l-4 border-l-primary">
                      <h3 className="text-xl font-semibold mb-3 text-primary">Robotic Spine Surgery Overview</h3>
                      <p className="text-muted-foreground mb-4">
                        Learn about the general principles and applications of robotic spine surgery.
                      </p>
                      <Button asChild variant="outline">
                        <Link to="/expertise/robotic-spine-surgery">Learn More</Link>
                      </Button>
                    </div>
                    
                    <div className="card p-6 rounded-lg shadow-md medical-card border-l-4 border-l-secondary">
                      <h3 className="text-xl font-semibold mb-3 text-secondary">Robotic Surgery Technology</h3>
                      <p className="text-muted-foreground mb-4">
                        Discover the advanced technology and robotic systems used in spine surgery.
                      </p>
                      <Button asChild variant="outline">
                        <Link to="/expertise/robotic-spine-surgery/technology">Learn More</Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <ExpertiseSidebar sections={data.sidebar} />
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <ExpertiseCallToAction
          title={data.callToAction.title}
          description={data.callToAction.description}
          primaryButton={data.callToAction.primaryButton}
          secondaryButton={data.callToAction.secondaryButton}
        />
      </div>
    </StandardPageLayout>
  );
};

RoboticSpineSurgeryProcedures.displayName = 'RoboticSpineSurgeryProcedures';

export default RoboticSpineSurgeryProcedures;
