import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import ExpertiseCallToAction from '@/components/expertise/ExpertiseCallToAction';
import ExpertiseHero from '@/components/expertise/ExpertiseHero';
import ExpertiseSection from '@/components/expertise/ExpertiseSection';
import StandardPageLayout from '@/components/StandardPageLayout';

/**
 * Lumbar Disc Replacement - Technology & Implants Sub-page
 * Comprehensive coverage of implant technology, materials, and device examples
 */

const TechnologyImplants: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const sections = [
    {
      id: 'disc-implants',
      title: 'Lumbar Disc Implants',
      content: [
        'Decades of biomechanical research, engineering and meticulous clinical studies were required to achieve advances in medical device technology and to produce a current line of artificial intervertebral discs. Modern artificial lumbar disc replacement implants are available in variable shapes, sizes, heights and articulation types.',
        'It means that there is still no ideal implant available to mimic the biomechanical properties of a healthy natural disc and perfectly cushion and transfer loads in the lumbar spine. The perfect artificial disc should achieve the following goals: distribute loads evenly across the front, back and sides of the vertebrae; provide stability and natural lumbar spine movements; bear prolonged loads; unload facet joints; restore healthy space between the vertebrae; restore foraminal height decompressing the nerve roots; be biologically compatible and last for the patient\'s lifetime.',
        'The crucial biomechanical factor in a lumbar disc implant is the position of its centre of rotation. As the natural human disc can move in all 3 planes and does not have a fixed rotational centre, a surgeon needs to choose an implant design with similar kinematics.',
        'Many of the disc implant designs are uni- or bi-articular. Some old models are metal-on-metal ball-and-socket designs. Others are metal-on-plastic with one or two ball-in-socket or saddle-type articulations. The healthy disc is elastic, and apart from flexion/extension, lateral bending and rotation can also be compressed to absorb axial shock.'
      ]
    },
    {
      id: 'implant-materials',
      title: 'Materials and Design Features',
      content: [
        'Titanium is the most common disc implant material used, followed by cobalt alloys. Special surface treatment increases its integration with the vertebral bones through large footprint to cover the maximum area of the vertebral endplate, porosity, keels, spikes and screws, and specialised coatings (calcium phosphate, plasma-sprayed titanium, aluminium oxide, hydroxyapatite).',
        'The metallic components may produce distortions (artifacts) on magnetic resonance imaging (MRI) but because they are fixed to the bone, should not be considered as a contraindication for a scan.',
        'Less important is the implant classification into constrained, semi-constrained, and unconstrained. A constrained disc has a mechanical stop that limits its range of motion and provides physical stability. Semi-constrained devices can move slightly outside of the physiological range of motion, and unconstrained prostheses rely only on natural limiters of segmental mobility like spinal ligaments and facet joints.'
      ]
    },
    {
      id: 'biomechanical-engineering',
      title: 'Biomechanical Engineering',
      content: [
        'The lumbar discs bear the weight of the whole body and are much larger than the discs in the neck or thoracic spine. The facet joints at each level provide additional stability and prevent discs from excessive translation (spondylolisthesis).',
        'The discs are made up of the inner jelly-like portion (nucleus pulposus), and outer fibrous material called the annulus fibrosus, which supports the inner nucleus. The annulus is innervated by multiple pain receptors, which signal any injuries, tears or cracks.',
        'With disc degeneration, the nucleus material can bulge out and press on the nerves causing referred radicular pain. The lower two discs of the spine (L4/5 and L5/S1) bear the most weight and are exposed to considerable stress and pressure. They are the most frequently damaged discs with injuries and prone to degenerate faster than other discs.'
      ]
    }
  ];

  const implantExamples = [
    {
      name: 'ProDisc-L (DePuy Synthes)',
      description: 'A ball-and-socket design with metal endplates and a polyethylene core. It allows for controlled motion in multiple directions and has been in clinical use for over 15 years with excellent long-term results.'
    },
    {
      name: 'Maverick Disc (Medtronic)',
      description: 'This device is made of a cobalt chromium alloy endplate and a polyurethane core. It is designed to be implanted without the need for cement or screws, providing natural motion preservation.'
    },
    {
      name: 'Kineflex/SpineArt',
      description: 'This is a one-piece device that consists of a titanium endplate and a polyurethane core. It is designed to provide motion in all directions while maintaining stability.'
    },
    {
      name: 'M6-L (Spinal Kinetics)',
      description: 'This artificial disc is designed to mimic the natural movement of the lumbar spine. It is made of a titanium alloy endplate and a high molecular weight polyethylene core with unique viscoelastic properties.'
    },
    {
      name: 'Charité Disc (DePuy)',
      description: 'One of the first generation lumbar disc replacements, consisting of two cobalt-chromium endplates with a polyethylene sliding core. It has a long clinical history and extensive follow-up data.'
    },
    {
      name: 'FlexiCore Disc (Stryker)',
      description: 'Features a unique design with a flexible core that allows for natural motion patterns. Made with cobalt-chromium endplates and a proprietary polymer core material.'
    },
    {
      name: 'LP-ESP (FH Orthopedics)',
      description: 'A one-piece implant with titanium alloy endplates and an elastomeric polycarbonate urethane core. Features variable centre of rotation and has shown excellent durability in testing.'
    }
  ];

  const callToActionData = {
    title: 'Learn More About Lumbar Disc Replacement',
    description: 'Explore the surgical procedure and recovery process, or learn about risks and how disc replacement compares to fusion surgery.',
    primaryButton: {
      text: 'Surgery & Recovery',
      link: '/expertise/lumbar-disc-replacement/surgery-recovery'
    },
    secondaryButton: {
      text: 'Risks & Comparison',
      link: '/expertise/lumbar-disc-replacement/risks-comparison'
    }
  };

  return (
    <StandardPageLayout title="Lumbar Disc Replacement - Technology & Implants" showHeader={false}>
      <ExpertiseHero
        title="Technology & Implants"
        subtitle="Advanced artificial disc technology and biomaterial engineering for lumbar spine treatment"
        backgroundImage="/images/LP-ESP-arthroplasty-disc-replacement-lumbar-kinetics-Aliashkevich-spine-neurosurgery-miNEURO-advanced-technology.jpg"
      />

      <div className="flex-1">
        {/* Breadcrumb Navigation */}
        <section className="py-4 bg-muted/30">
          <div className="container">
            <nav className="flex items-centre space-x-2 text-sm text-muted-foreground">
              <Link to="/expertise/lumbar-disc-replacement" className="hover:text-primary">
                Lumbar Disc Replacement
              </Link>
              <span>→</span>
              <span className="text-foreground">Technology & Implants</span>
            </nav>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              <div className="lg:col-span-2">
                {/* Main Sections */}
                {sections.map((section) => (
                  <ExpertiseSection key={section.id} section={section} />
                ))}

                {/* Implant Examples */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Implant Examples</h2>
                  <p className="text-muted-foreground mb-6">
                    There are several types of artificial lumbar disc replacement prostheses available on the market. Here are some examples of commonly used devices:
                  </p>
                  
                  <div className="grid grid-cols-1 gap-6">
                    {implantExamples.map((implant, index) => (
                      <div key={index} className="border rounded-lg p-6 medical-card">
                        <h3 className="text-lg font-semibold mb-3 text-primary">{implant.name}</h3>
                        <p className="text-muted-foreground">{implant.description}</p>
                      </div>
                    ))}
                  </div>
                  
                  <div className="mt-6 p-4 bg-primary/10 rounded-lg">
                    <p className="text-muted-foreground">
                      <strong>Important Note:</strong> The choice of prosthesis is typically made by a surgeon based on the specific needs of the patient and their medical history. For more information about available lumbar disc prostheses, please refer to: 
                      <a href="https://www.medicalexpo.com/medical-manufacturer/disc-prosthesis-7221.html" 
                         target="_blank" 
                         rel="noopener noreferrer" 
                         className="text-primary hover:underline ml-1">
                        Medical Expo Disc Prosthesis Directory
                      </a>
                    </p>
                  </div>
                </div>

                {/* Design Goals */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Design Goals for the Perfect Artificial Disc</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-primary/5 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4">Mechanical Properties</h3>
                      <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                        <li>Distribute loads evenly across vertebrae</li>
                        <li>Provide stability and natural movements</li>
                        <li>Bear prolonged loads</li>
                        <li>Unload facet joints</li>
                      </ul>
                    </div>
                    
                    <div className="bg-primary/5 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4">Biological Properties</h3>
                      <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                        <li>Restore healthy space between vertebrae</li>
                        <li>Restore foraminal height</li>
                        <li>Be biologically compatible</li>
                        <li>Last for the patient's lifetime</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <div className="sticky top-8 space-y-6">
                  <div className="medical-card p-6 rounded-lg shadow-md">
                    <h3 className="text-lg font-semibold mb-4">Related Pages</h3>
                    <div className="space-y-3">
                      <Link to="/expertise/lumbar-disc-replacement" className="block text-primary hover:underline">
                        ← Back to Overview
                      </Link>
                      <Link to="/expertise/lumbar-disc-replacement/surgery-recovery" className="block text-primary hover:underline">
                        Surgery & Recovery →
                      </Link>
                      <Link to="/expertise/lumbar-disc-replacement/risks-comparison" className="block text-primary hover:underline">
                        Risks & Comparison →
                      </Link>
                    </div>
                  </div>
                  
                  <div className="bg-primary/5 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Key Technologies</h3>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>• Motion preservation technology</li>
                      <li>• Biomechanical engineering</li>
                      <li>• Advanced materials (titanium, cobalt alloys)</li>
                      <li>• Variable centre of rotation</li>
                      <li>• Elastic shock absorption</li>
                      <li>• Large footprint design</li>
                    </ul>
                  </div>
                  
                  <div className="bg-primary/5 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Lumbar Spine Facts</h3>
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-semibold">Most Affected Levels</p>
                        <p className="text-sm text-muted-foreground">L4/5 and L5/S1 discs</p>
                      </div>
                      <div>
                        <p className="text-sm font-semibold">Load Bearing</p>
                        <p className="text-sm text-muted-foreground">Supports entire body weight</p>
                      </div>
                      <div>
                        <p className="text-sm font-semibold">Size</p>
                        <p className="text-sm text-muted-foreground">Larger than cervical/thoracic discs</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <ExpertiseCallToAction
          title={callToActionData.title}
          description={callToActionData.description}
          primaryButton={callToActionData.primaryButton}
          secondaryButton={callToActionData.secondaryButton}
        />
      </div>
    </StandardPageLayout>
  );
};

TechnologyImplants.displayName = 'TechnologyImplants';

export default TechnologyImplants;
