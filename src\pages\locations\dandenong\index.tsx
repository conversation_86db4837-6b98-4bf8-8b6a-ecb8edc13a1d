import React from 'react';
import { Helmet } from 'react-helmet-async';

import { StandardPageLayout } from '@/components/layout';
import {
  LocationHero,
  LocationContactInfo,
  LocationMap,
  LocationFacilities,
  LocationAmenities,
  TherapeuticInterventions,
  NearbyAmenities,
  NearbyHospitals,
  PatientsPrivacy,
  LocationCTA
} from '@/components/locations';
import { dandenongLocationData, dandenongSpecialSections } from '@/data/locations/dandenongData';

/**
 * Dandenong Location Page - Refactored
 * Complete refactoring preserving ALL content from the original 636-line file
 * Modular architecture with comprehensive data preservation
 */
const DandenongLocation: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Dandenong Neurosurgeon | Dandenong Neurology & Specialists | Dr <PERSON><PERSON></title>
        <meta
          name="description"
          content="Expert neurosurgery and spine care at Dandenong Neurology & Specialists Group. Dr <PERSON><PERSON> provides comprehensive neurosurgical consultations and treatments. Book your appointment today."
        />
        <meta name="keywords" content="neurosurgeon dandenong, spine surgeon danden<PERSON>, dandenong neurology, neurosurgery dandenong, spine surgery dandenong" />
        <link rel="canonical" href="https://mineuro.com.au/locations/dandenong" />
      </Helmet>

      <StandardPageLayout>
        <LocationHero
          title={dandenongLocationData.hero.title}
          subtitle={dandenongLocationData.hero.subtitle}
          introduction1={dandenongLocationData.hero.introduction1}
          introduction2={dandenongLocationData.hero.introduction2}
          imageUrl={dandenongLocationData.hero.imageUrl}
        />

        <div className="py-16 bg-muted/30">
          <div className="container">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <LocationContactInfo
                address={dandenongLocationData.contact.address}
                phone={dandenongLocationData.contact.phone}
                email={dandenongLocationData.contact.email}
                hours={dandenongLocationData.contact.hours}
                consultingHours={dandenongLocationData.contact.consultingHours}
                appointmentProcess={dandenongLocationData.contact.appointmentProcess}
              />

              <LocationMap
                embedUrl={dandenongLocationData.map.embedUrl}
                title={dandenongLocationData.map.title}
                transportOptions={dandenongLocationData.map.transportOptions}
                gettingHereTitle={dandenongLocationData.map.gettingHereTitle}
                publicTransportTitle={dandenongLocationData.map.publicTransportTitle}
                carTitle={dandenongLocationData.map.carTitle}
              />
            </div>
          </div>
        </div>

        {dandenongLocationData.therapeuticInterventions && (
          <TherapeuticInterventions
            title={dandenongLocationData.therapeuticInterventions.title}
            subtitle={dandenongLocationData.therapeuticInterventions.subtitle}
            description={dandenongLocationData.therapeuticInterventions.description}
            interventions={dandenongLocationData.therapeuticInterventions.interventions}
          />
        )}

        <LocationFacilities
          title={dandenongLocationData.facilities.title}
          subtitle={dandenongLocationData.facilities.subtitle}
          description={dandenongLocationData.facilities.description}
          facilities={dandenongLocationData.facilities.facilities}
          gallery={dandenongLocationData.facilities.gallery}
        />

        <LocationAmenities
          title={dandenongLocationData.amenities.title}
          description={dandenongLocationData.amenities.description}
          locationDetails={dandenongLocationData.amenities.locationDetails}
          medicalFacilities={dandenongLocationData.amenities.medicalFacilities}
          surroundingAmenities={dandenongLocationData.amenities.surroundingAmenities}
          transportation={dandenongLocationData.amenities.transportation}
          parking={dandenongLocationData.amenities.parking}
          images={dandenongLocationData.amenities.images}
        />

        <NearbyAmenities
          title={dandenongLocationData.nearbyAmenities.title}
          subtitle={dandenongLocationData.nearbyAmenities.subtitle}
          description={dandenongLocationData.nearbyAmenities.description}
          categories={dandenongLocationData.nearbyAmenities.categories}
        />

        <NearbyHospitals
          title={dandenongLocationData.nearbyHospitals.title}
          subtitle={dandenongLocationData.nearbyHospitals.subtitle}
          description={dandenongLocationData.nearbyHospitals.description}
          hospitals={dandenongLocationData.nearbyHospitals.hospitals}
        />

        <PatientsPrivacy
          title={dandenongSpecialSections.patientsPrivacy.title}
          subtitle={dandenongSpecialSections.patientsPrivacy.subtitle}
          description1={dandenongSpecialSections.patientsPrivacy.description1}
          description2={dandenongSpecialSections.patientsPrivacy.description2}
          description3={dandenongSpecialSections.patientsPrivacy.description3}
          drAliashkevichLink={dandenongSpecialSections.patientsPrivacy.drAliashkevichLink}
        />

        <LocationCTA
          title={dandenongLocationData.cta.title}
          description={dandenongLocationData.cta.description}
          buttons={dandenongLocationData.cta.buttons}
        />
      </StandardPageLayout>
    </>
  );
};

DandenongLocation.displayName = 'DandenongLocation';

export default DandenongLocation;
