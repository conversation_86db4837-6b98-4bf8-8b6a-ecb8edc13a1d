import React from 'react';
import { Helmet } from 'react-helmet-async';

import { StandardPageLayout } from '@/components/layout';
import {
  LocationHero,
  LocationContactInfo,
  LocationMap,
  LocationFacilities,
  LocationAmenities,
  TherapeuticInterventions,
  NearbyAmenities,
  ConditionsTreated,
  NearbyHospitals,
  OtherConsultingLocations,
  InsuranceAndFunding,
  LocationCTA
} from '@/components/locations';
import { frankstonLocationData } from '@/data/locations/frankstonData';

/**
 * Frankston Location Page - Refactored
 * Complete refactoring preserving ALL content from the original 854-line file
 * Modular architecture with comprehensive data preservation
 */
const FrankstonLocation: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Frankston Neurosurgeon | Peninsula Private Hospital | Dr <PERSON><PERSON></title>
        <meta 
          name="description" 
          content="Expert neurosurgery and spine care at Peninsula Private Hospital, Frankston. Dr <PERSON><PERSON> provides comprehensive neurosurgical consultations and treatments. Book your appointment today." 
        />
        <meta name="keywords" content="neurosurgeon frank<PERSON>, spine surgeon frank<PERSON>, peninsula private hospital, neurosurgery frankston, spine surgery frankston" />
        <link rel="canonical" href="https://mineuro.com.au/locations/frankston" />
      </Helmet>

      <StandardPageLayout>
        <LocationHero
          title={frankstonLocationData.hero.title}
          subtitle={frankstonLocationData.hero.subtitle}
          introduction1={frankstonLocationData.hero.introduction1}
          introduction2={frankstonLocationData.hero.introduction2}
          introduction3={frankstonLocationData.hero.introduction3}
          imageUrl={frankstonLocationData.hero.imageUrl}
        />

        <div className="py-16 bg-muted/30">
          <div className="container">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <LocationContactInfo
                address={frankstonLocationData.contact.address}
                phone={frankstonLocationData.contact.phone}
                email={frankstonLocationData.contact.email}
                hours={frankstonLocationData.contact.hours}
                consultingHours={frankstonLocationData.contact.consultingHours}
                appointmentProcess={frankstonLocationData.contact.appointmentProcess}
              />

              <LocationMap
                embedUrl={frankstonLocationData.map.embedUrl}
                title={frankstonLocationData.map.title}
                transportOptions={frankstonLocationData.map.transportOptions}
                gettingHereTitle={frankstonLocationData.map.gettingHereTitle}
                publicTransportTitle={frankstonLocationData.map.publicTransportTitle}
                carTitle={frankstonLocationData.map.carTitle}
              />
            </div>
          </div>
        </div>

        {frankstonLocationData.therapeuticInterventions && (
          <TherapeuticInterventions
            title={frankstonLocationData.therapeuticInterventions.title}
            subtitle={frankstonLocationData.therapeuticInterventions.subtitle}
            description={frankstonLocationData.therapeuticInterventions.description}
            interventions={frankstonLocationData.therapeuticInterventions.interventions}
          />
        )}

        <LocationFacilities
          title={frankstonLocationData.facilities.title}
          subtitle={frankstonLocationData.facilities.subtitle}
          description={frankstonLocationData.facilities.description}
          facilities={frankstonLocationData.facilities.facilities}
          gallery={frankstonLocationData.facilities.gallery}
        />

        <LocationAmenities
          title={frankstonLocationData.amenities.title}
          description={frankstonLocationData.amenities.description}
          locationDetails={frankstonLocationData.amenities.locationDetails}
          medicalFacilities={frankstonLocationData.amenities.medicalFacilities}
          surroundingAmenities={frankstonLocationData.amenities.surroundingAmenities}
          transportation={frankstonLocationData.amenities.transportation}
          parking={frankstonLocationData.amenities.parking}
          images={frankstonLocationData.amenities.images}
        />

        <NearbyAmenities
          title={frankstonLocationData.nearbyAmenities.title}
          subtitle={frankstonLocationData.nearbyAmenities.subtitle}
          description={frankstonLocationData.nearbyAmenities.description}
          categories={frankstonLocationData.nearbyAmenities.categories}
        />

        {frankstonLocationData.conditionsTreated && (
          <ConditionsTreated
            title={frankstonLocationData.conditionsTreated.title}
            subtitle={frankstonLocationData.conditionsTreated.subtitle}
            description={frankstonLocationData.conditionsTreated.description}
            categories={frankstonLocationData.conditionsTreated.categories}
          />
        )}

        <NearbyHospitals
          title={frankstonLocationData.nearbyHospitals.title}
          subtitle={frankstonLocationData.nearbyHospitals.subtitle}
          description={frankstonLocationData.nearbyHospitals.description}
          hospitals={frankstonLocationData.nearbyHospitals.hospitals}
        />

        <OtherConsultingLocations
          title={frankstonLocationData.otherLocations.title}
          subtitle={frankstonLocationData.otherLocations.subtitle}
          description={frankstonLocationData.otherLocations.description}
          locations={frankstonLocationData.otherLocations.locations}
        />

        {frankstonLocationData.insuranceAndFunding && (
          <InsuranceAndFunding
            title={frankstonLocationData.insuranceAndFunding.title}
            subtitle={frankstonLocationData.insuranceAndFunding.subtitle}
            categories={frankstonLocationData.insuranceAndFunding.categories}
          />
        )}

        <LocationCTA
          title={frankstonLocationData.cta.title}
          description={frankstonLocationData.cta.description}
          buttons={frankstonLocationData.cta.buttons}
        />
      </StandardPageLayout>
    </>
  );
};

FrankstonLocation.displayName = 'FrankstonLocation';

export default FrankstonLocation;
