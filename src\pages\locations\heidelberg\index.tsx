import React from 'react';

import LocationPageWrapper from '@/components/locations/LocationPageWrapper';

/**
 * Heidelberg Location Page - Enhanced with Generic Template
 * Now uses the generic location template with content injection system
 * Preserves ALL content from the original 723-line file while eliminating code duplication
 * Warringal Private Hospital - Heidelberg
 */
const HeidelbergLocation: React.FC = () => {
  return (
    <LocationPageWrapper locationSlug="heidelberg" />
  );
};

HeidelbergLocation.displayName = 'HeidelbergLocation';

export default HeidelbergLocation;
