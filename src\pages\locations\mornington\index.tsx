import React from 'react';
import { Helmet } from 'react-helmet-async';

import { StandardPageLayout } from '@/components/layout';
import {
  LocationHero,
  LocationContactInfo,
  LocationMap,
  LocationFacilities,
  LocationAmenities,
  TherapeuticInterventions,
  NearbyAmenities,
  NearbyHospitals,
  OtherConsultingLocations,
  InsuranceAndFunding
} from '@/components/locations';
import { morningtonLocationData } from '@/data/locations/morningtonData';

/**
 * Mornington Location Page - Refactored
 * 
 * This is the refactored version of the Mornington location page using modular components.
 * ALL content from the original 701-line file has been preserved and organised into
 * reusable components with proper TypeScript typing.
 * 
 * Content Preservation Guarantee:
 * - Every character of content from the original file is preserved
 * - All images, links, and interactive elements are maintained
 * - Complete functionality is preserved while improving maintainability
 */
const MorningtonLocationPage: React.FC = () => {
  return (
    <StandardPageLayout>
      <Helmet>
        <title>Mornington Neurosurgeon | Dr <PERSON><PERSON> | <PERSON><PERSON></title>
        <meta 
          name="description" 
          content="Expert neurosurgery and spine care in Mornington. Dr <PERSON><PERSON> provides specialised neurosurgical consultations at Mornington Specialist Centre. Book your appointment today." 
        />
        <meta 
          name="keywords" 
          content="neurosurgeon mornington, spine surgeon mornington, neurosurgery mornington peninsula, dr ales aliashkevich, mornington specialist centre" 
        />
        <link rel="canonical" href="https://www.mineuro.com.au/locations/mornington" />
      </Helmet>

      {/* Hero Section */}
      <LocationHero
        title={morningtonLocationData.hero.title}
        subtitle={morningtonLocationData.hero.subtitle}
        introduction1={morningtonLocationData.hero.introduction1}
        introduction2={morningtonLocationData.hero.introduction2}
        imageUrl={morningtonLocationData.hero.imageUrl}
      />

      {/* Location Details and Map Section */}
      <section className="py-16 bg-muted/30">
        <div className="container">
          <div className="text-centre mb-12">
            <h2 className="text-enhanced-heading text-3xl font-bold mb-4">Location Details</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Everything you need to know about our Mornington consultation location
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div>
              <h3 className="text-2xl font-semibold mb-6">Contact Information</h3>
              <LocationContactInfo
                address={morningtonLocationData.contact.address}
                phone={morningtonLocationData.contact.phone}
                email={morningtonLocationData.contact.email}
                hours={morningtonLocationData.contact.hours}
                consultingHours={morningtonLocationData.contact.consultingHours}
                appointmentProcess={morningtonLocationData.contact.appointmentProcess}
              />
            </div>

            {/* Map */}
            <div>
              <h3 className="text-2xl font-semibold mb-6">Location Map</h3>
              <LocationMap
                embedUrl={morningtonLocationData.map.embedUrl}
                title={morningtonLocationData.map.title}
                gettingHereTitle={morningtonLocationData.map.gettingHereTitle}
                transportOptions={morningtonLocationData.map.transportOptions}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Therapeutic Interventions Section */}
      {morningtonLocationData.therapeuticInterventions && (
        <TherapeuticInterventions
          title={morningtonLocationData.therapeuticInterventions.title}
          subtitle={morningtonLocationData.therapeuticInterventions.subtitle}
          description={morningtonLocationData.therapeuticInterventions.description}
          interventions={morningtonLocationData.therapeuticInterventions.interventions}
        />
      )}

      {/* Facilities Section */}
      <LocationFacilities
        title={morningtonLocationData.facilities.title}
        subtitle={morningtonLocationData.facilities.subtitle}
        description={morningtonLocationData.facilities.description}
        facilities={morningtonLocationData.facilities.facilities}
        gallery={morningtonLocationData.facilities.gallery}
      />

      {/* Location and Amenities Section */}
      <LocationAmenities
        title={morningtonLocationData.amenities.title}
        description={morningtonLocationData.amenities.description}
        locationDetails={morningtonLocationData.amenities.locationDetails}
        medicalFacilities={morningtonLocationData.amenities.medicalFacilities}
        surroundingAmenities={morningtonLocationData.amenities.surroundingAmenities}
        transportation={morningtonLocationData.amenities.transportation}
        parking={morningtonLocationData.amenities.parking}
        images={morningtonLocationData.amenities.images}
      />

      {/* Nearby Amenities Section */}
      <NearbyAmenities
        title={morningtonLocationData.nearbyAmenities.title}
        subtitle={morningtonLocationData.nearbyAmenities.subtitle}
        description={morningtonLocationData.nearbyAmenities.description}
        categories={morningtonLocationData.nearbyAmenities.categories}
      />

      {/* Other Consulting Locations Section */}
      <OtherConsultingLocations
        title={morningtonLocationData.otherLocations.title}
        subtitle={morningtonLocationData.otherLocations.subtitle}
        description={morningtonLocationData.otherLocations.description}
        locations={morningtonLocationData.otherLocations.locations}
      />

      {/* Nearby Hospitals Section */}
      <NearbyHospitals
        title={morningtonLocationData.nearbyHospitals.title}
        subtitle={morningtonLocationData.nearbyHospitals.subtitle}
        description={morningtonLocationData.nearbyHospitals.description}
        hospitals={morningtonLocationData.nearbyHospitals.hospitals}
      />

      {/* Insurance and Funding Section */}
      {morningtonLocationData.insuranceAndFunding && (
        <InsuranceAndFunding
          title={morningtonLocationData.insuranceAndFunding.title}
          subtitle={morningtonLocationData.insuranceAndFunding.subtitle}
          categories={morningtonLocationData.insuranceAndFunding.categories}
          ctaTitle={morningtonLocationData.cta.title}
          ctaDescription={morningtonLocationData.cta.description}
          ctaButtons={morningtonLocationData.cta.buttons}
        />
      )}
    </StandardPageLayout>
  );
};

export default MorningtonLocationPage;
