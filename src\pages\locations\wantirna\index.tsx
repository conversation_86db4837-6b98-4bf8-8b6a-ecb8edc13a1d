import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import { StandardPageLayout } from '@/components/layout';
import {
  LocationHero,
  LocationContactInfo,
  LocationMap,
  LocationFacilities,
  NearbyAmenities,
  NearbyHospitals,
  OtherConsultingLocations,
  LocationCTA
} from '@/components/locations';
import { wantirnaLocationData } from '@/data/locations/wantirnaData';

/**
 * Wantirna Location Page - Refactored
 * Complete refactoring preserving ALL content from the original 598-line file
 * Modular architecture with comprehensive data preservation
 * Knox Audiology Specialist Medical Suites - 230 Mountain Highway, WANTIRNA VIC 3152
 */
const WantirnaLocation: React.FC = () => {
  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout>
      <Helmet>
        <title>Wantirna Consulting Location - Knox Audiology Specialist Medical Suites | miNEURO</title>
        <meta name="description" content="Dr <PERSON><PERSON> provides expert neurosurgical consultations at Knox Audiology Specialist Medical Suites in Wantirna. Convenient location with modern facilities and comprehensive care." />
        <meta name="keywords" content="<PERSON><PERSON><PERSON> neurosurgeon, Knox Audiology, spine surgeon <PERSON><PERSON><PERSON>, neurosurgical consultation, Dr <PERSON>es <PERSON>ashkevich, Mountain Highway medical" />
        <link rel="canonical" href="https://mineuro.com.au/locations/wantirna" />
      </Helmet>

      {/* Hero Section */}
      <LocationHero
        title={wantirnaLocationData.hero.title}
        subtitle={wantirnaLocationData.hero.subtitle}
        introduction1={wantirnaLocationData.hero.introduction1}
        introduction2={wantirnaLocationData.hero.introduction2}
        imageUrl={wantirnaLocationData.hero.imageUrl}
      />
      {/* Location Details Section */}
      <section className="py-16">
        <div className="container">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="md:w-1/2">
              <div className="mb-8">
                <h2 className="text-enhanced-heading text-2xl font-bold mb-4">Location Details</h2>
                <p className="text-muted-foreground">
                  Everything you need to know about our Wantirna consulting location
                </p>
              </div>

              <LocationContactInfo
                address={wantirnaLocationData.contact.address}
                phone={wantirnaLocationData.contact.phone}
                email={wantirnaLocationData.contact.email}
                hours={wantirnaLocationData.contact.hours}
                consultingHours={wantirnaLocationData.contact.consultingHours}
                appointmentProcess={wantirnaLocationData.contact.appointmentProcess}
              />
            </div>

            <div className="md:w-1/2">
              <div className="mb-8">
                <h2 className="text-enhanced-heading text-2xl font-bold mb-4">Location Map</h2>
              </div>

              <LocationMap
                embedUrl={wantirnaLocationData.map.embedUrl}
                title={wantirnaLocationData.map.title}
                transportOptions={wantirnaLocationData.map.transportOptions}
                gettingHereTitle={wantirnaLocationData.map.gettingHereTitle}
                publicTransportTitle={wantirnaLocationData.map.publicTransportTitle}
                carTitle={wantirnaLocationData.map.carTitle}
              />
            </div>
          </div>
        </div>
      </section>
      {/* Consulting Room Facilities Section */}
      <LocationFacilities
        title={wantirnaLocationData.facilities.title}
        subtitle={wantirnaLocationData.facilities.subtitle}
        description={wantirnaLocationData.facilities.description}
        facilities={wantirnaLocationData.facilities.facilities}
        gallery={wantirnaLocationData.facilities.gallery}
      />
      {/* Nearby Hospitals Section */}
      <NearbyHospitals
        title={wantirnaLocationData.nearbyHospitals.title}
        subtitle={wantirnaLocationData.nearbyHospitals.subtitle}
        description={wantirnaLocationData.nearbyHospitals.description}
        hospitals={wantirnaLocationData.nearbyHospitals.hospitals}
      />


      {/* Nearby Amenities Section */}
      <NearbyAmenities
        title={wantirnaLocationData.nearbyAmenities.title}
        subtitle={wantirnaLocationData.nearbyAmenities.subtitle}
        description={wantirnaLocationData.nearbyAmenities.description}
        categories={wantirnaLocationData.nearbyAmenities.categories}
      />

      {/* Other Consulting Locations Section */}
      <OtherConsultingLocations
        title={wantirnaLocationData.otherLocations.title}
        subtitle={wantirnaLocationData.otherLocations.subtitle}
        description={wantirnaLocationData.otherLocations.description}
        locations={wantirnaLocationData.otherLocations.locations}
      />

      {/* CTA Section */}
      <LocationCTA
        title={wantirnaLocationData.cta.title}
        description={wantirnaLocationData.cta.description}
        buttons={wantirnaLocationData.cta.buttons}
      />
    </StandardPageLayout>
  );
};

WantirnaLocation.displayName = 'WantirnaLocation';

export default WantirnaLocation;
