import React from 'react';
import { Helmet } from 'react-helmet-async';

import { StandardPageLayout } from '@/components/layout';
import {
  LocationHero,
  LocationContactInfo,
  LocationMap,
  LocationFacilities,
  LocationAmenities,
  TherapeuticInterventions,
  NearbyAmenities,
  NearbyHospitals,
  OtherConsultingLocations,
  PatientsPrivacy,
  LocationCTA
} from '@/components/locations';
import { werribeeLocationData, werribeeSpecialSections } from '@/data/locations/werribeeData';

/**
 * Werribee Location Page - Refactored
 * Complete refactoring preserving ALL content from the original 721-line file
 * Modular architecture with comprehensive data preservation
 */
const WerribeeLocation: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Werribee Neurosurgeon | Consulting Rooms Werribee | Dr <PERSON><PERSON></title>
        <meta
          name="description"
          content="Expert neurosurgery and spine care in Werribee. Dr <PERSON><PERSON> provides comprehensive neurosurgical consultations and treatments. Book your appointment today."
        />
        <meta name="keywords" content="neurosurgeon werribee, spine surgeon werribee, neurosurgery werribee, spine surgery werribee, consulting rooms werribee" />
        <link rel="canonical" href="https://mineuro.com.au/locations/werribee" />
      </Helmet>

      <StandardPageLayout>
        <LocationHero
          title={werribeeLocationData.hero.title}
          subtitle={werribeeLocationData.hero.subtitle}
          introduction1={werribeeLocationData.hero.introduction1}
          introduction2={werribeeLocationData.hero.introduction2}
          imageUrl={werribeeLocationData.hero.imageUrl}
        />

        <div className="py-16 bg-muted/30">
          <div className="container">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <LocationContactInfo
                address={werribeeLocationData.contact.address}
                phone={werribeeLocationData.contact.phone}
                email={werribeeLocationData.contact.email}
                hours={werribeeLocationData.contact.hours}
                consultingHours={werribeeLocationData.contact.consultingHours}
                appointmentProcess={werribeeLocationData.contact.appointmentProcess}
              />

              <LocationMap
                embedUrl={werribeeLocationData.map.embedUrl}
                title={werribeeLocationData.map.title}
                transportOptions={werribeeLocationData.map.transportOptions}
                gettingHereTitle={werribeeLocationData.map.gettingHereTitle}
                publicTransportTitle={werribeeLocationData.map.publicTransportTitle}
                carTitle={werribeeLocationData.map.carTitle}
              />
            </div>
          </div>
        </div>
        {werribeeLocationData.therapeuticInterventions && (
          <TherapeuticInterventions
            title={werribeeLocationData.therapeuticInterventions.title}
            subtitle={werribeeLocationData.therapeuticInterventions.subtitle}
            description={werribeeLocationData.therapeuticInterventions.description}
            interventions={werribeeLocationData.therapeuticInterventions.interventions}
          />
        )}

        <LocationFacilities
          title={werribeeLocationData.facilities.title}
          subtitle={werribeeLocationData.facilities.subtitle}
          description={werribeeLocationData.facilities.description}
          facilities={werribeeLocationData.facilities.facilities}
          gallery={werribeeLocationData.facilities.gallery}
        />

        <LocationAmenities
          title={werribeeLocationData.amenities.title}
          description={werribeeLocationData.amenities.description}
          locationDetails={werribeeLocationData.amenities.locationDetails}
          medicalFacilities={werribeeLocationData.amenities.medicalFacilities}
          surroundingAmenities={werribeeLocationData.amenities.surroundingAmenities}
          transportation={werribeeLocationData.amenities.transportation}
          parking={werribeeLocationData.amenities.parking}
          images={werribeeLocationData.amenities.images}
        />

        <NearbyAmenities
          title={werribeeLocationData.nearbyAmenities.title}
          subtitle={werribeeLocationData.nearbyAmenities.subtitle}
          description={werribeeLocationData.nearbyAmenities.description}
          categories={werribeeLocationData.nearbyAmenities.categories}
        />

        <OtherConsultingLocations
          title={werribeeLocationData.otherLocations.title}
          subtitle={werribeeLocationData.otherLocations.subtitle}
          description={werribeeLocationData.otherLocations.description}
          locations={werribeeLocationData.otherLocations.locations}
        />

        <NearbyHospitals
          title={werribeeLocationData.nearbyHospitals.title}
          subtitle={werribeeLocationData.nearbyHospitals.subtitle}
          description={werribeeLocationData.nearbyHospitals.description}
          hospitals={werribeeLocationData.nearbyHospitals.hospitals}
        />

        <PatientsPrivacy
          title={werribeeSpecialSections.patientsPrivacy.title}
          subtitle={werribeeSpecialSections.patientsPrivacy.subtitle}
          description1={werribeeSpecialSections.patientsPrivacy.description1}
          description2={werribeeSpecialSections.patientsPrivacy.description2}
          description3={werribeeSpecialSections.patientsPrivacy.description3}
          drAliashkevichLink={werribeeSpecialSections.patientsPrivacy.drAliashkevichLink}
        />

        <LocationCTA
          title={werribeeLocationData.cta.title}
          description={werribeeLocationData.cta.description}
          buttons={werribeeLocationData.cta.buttons}
        />
      </StandardPageLayout>
    </>
  );
};

WerribeeLocation.displayName = 'WerribeeLocation';

export default WerribeeLocation;
