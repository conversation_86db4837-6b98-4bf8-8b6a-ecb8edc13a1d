import { <PERSON>, Layers, Shield, Users, TrendingUp, Search, ArrowRight, Lock, BookOpen, Activity } from 'lucide-react';
import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { Link } from 'react-router-dom';

import PageHeader from '@/components/PageHeader';
import { SectionHeader, GridLayout } from '@/components/shared/CommonSectionPatterns';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface HealthResource {
  id: string;
  name: string;
  description: string;
  type: 'assessment' | 'education' | 'program' | 'anatomy' | 'recovery';
  requiresLogin: boolean;
  path: string;
  category: string;
  featured: boolean;
}

const healthResources: HealthResource[] = [
  {
    id: 'expert-guide',
    name: 'Expert Guide to Spine & Brain Health',
    description: 'Comprehensive guide covering prevention, maintenance, and optimization of spine and brain health.',
    type: 'education',
    requiresLogin: false,
    path: '/patient-resources/expert-guide-spine-brain-health',
    category: 'Education',
    featured: true
  },
  {
    id: 'individual-programme',
    name: 'Individual Spine Health Programme',
    description: 'Personalized spine health program tailored to your specific needs and condition.',
    type: 'program',
    requiresLogin: false,
    path: '/patient-resources/individual-spine-health-programme',
    category: 'Programs',
    featured: true
  },
  {
    id: 'assessment-tools',
    name: 'Assessment Tools',
    description: 'Comprehensive health assessment tools to evaluate your spine and brain health status.',
    type: 'assessment',
    requiresLogin: true,
    path: '/patient-resources/assessment-tools',
    category: 'Assessment',
    featured: true
  },
  {
    id: 'patient-dashboard',
    name: 'Patient Dashboard',
    description: 'Personal health dashboard to track your progress, appointments, and treatment plans.',
    type: 'program',
    requiresLogin: true,
    path: '/patient-resources/patient-dashboard',
    category: 'Management',
    featured: true
  },
  {
    id: 'spine-health-app',
    name: 'Spine Health App',
    description: 'Mobile application for spine health monitoring, exercises, and progress tracking.',
    type: 'program',
    requiresLogin: false,
    path: '/patient-resources/spine-health-app',
    category: 'Technology',
    featured: false
  },
  {
    id: 'cervical-injury-recovery',
    name: 'Cervical Spine Injury Recovery',
    description: 'Specialized recovery protocols and guidelines for cervical spine injuries.',
    type: 'recovery',
    requiresLogin: false,
    path: '/patient-resources/cervical-spine-injury',
    category: 'Recovery',
    featured: false
  },
  {
    id: 'lifestyle-modifications',
    name: 'Lifestyle Modifications',
    description: 'Evidence-based lifestyle changes to improve spine and brain health outcomes.',
    type: 'education',
    requiresLogin: false,
    path: '/patient-resources/lifestyle-modifications',
    category: 'Education',
    featured: false
  },
  {
    id: 'brain-anatomy',
    name: 'Brain Anatomy',
    description: 'Interactive guide to brain anatomy and function for patient education.',
    type: 'anatomy',
    requiresLogin: false,
    path: '/patient-resources/brain-anatomy',
    category: 'Anatomy',
    featured: false
  },
  {
    id: 'nerve-anatomy',
    name: 'Nerve Anatomy',
    description: 'Comprehensive guide to peripheral and central nervous system anatomy.',
    type: 'anatomy',
    requiresLogin: false,
    path: '/patient-resources/nerve-anatomy',
    category: 'Anatomy',
    featured: false
  },
  {
    id: 'spine-anatomy',
    name: 'Spine Anatomy',
    description: 'Detailed anatomical guide to spinal structure and function.',
    type: 'anatomy',
    requiresLogin: false,
    path: '/patient-resources/spine-anatomy',
    category: 'Anatomy',
    featured: false
  }
];

const BrainAndSpineHealth: React.FC = () => {
  const deviceInfo = useDeviceDetection();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [showLoginRequired, setShowLoginRequired] = useState(true);

  // Filter resources based on search and type
  const filteredResources = healthResources
    .filter(resource => 
      resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.category.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .filter(resource => selectedType === 'all' || resource.type === selectedType)
    .filter(resource => showLoginRequired || !resource.requiresLogin);

  const featuredResources = filteredResources.filter(resource => resource.featured);
  const otherResources = filteredResources.filter(resource => !resource.featured);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'assessment': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'education': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'program': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'anatomy': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'recovery': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'assessment': return Activity;
      case 'education': return BookOpen;
      case 'program': return Shield;
      case 'anatomy': return Brain;
      case 'recovery': return TrendingUp;
      default: return Brain;
    }
  };

  return (
    <StandardPageLayout showHeader={false}>
      <Helmet>
        <title>Brain and Spine Health | Comprehensive Health Management | miNEURO</title>
        <meta 
          name="description" 
          content="Comprehensive brain and spine health resources including assessment tools, educational guides, and personalized health programs. Expert neurosurgical care and health management." 
        />
        <meta name="keywords" content="brain health, spine health, assessment tools, health programs, neurosurgical care, patient dashboard" />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/brain-and-spine-health" />
      </Helmet>

      <PageHeader
        title="Brain and Spine Health"
        subtitle="Comprehensive health management tools, educational resources, and personalized programs for optimal neurological wellness"
        backgroundImage="/images/health-management/brain-spine-health-hero.jpg"
        enableParallax={true}
      />

      <main className="flex-1">
        {/* Overview Section */}
        <section className="py-16 bg-gradient-to-br from-muted/10 to-background">
          <div className="container">
            <SectionHeader
              title="Your Health Management Hub"
              subtitle="Comprehensive tools and resources for optimal neurological health"
              description="Access personalized assessment tools, educational resources, and health management programs designed to support your brain and spine health journey."
              centered={true}
            />

            {/* Quick Stats */}
            <div className={cn(
              "grid gap-6 mt-12",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
            )}>
              <Card className="medical-card text-center">
                <CardContent className="pt-6">
                  <Shield className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-enhanced-heading mb-2">10</h3>
                  <p className="text-muted-foreground">Health Resources Available</p>
                </CardContent>
              </Card>
              <Card className="medical-card text-center">
                <CardContent className="pt-6">
                  <Users className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-enhanced-heading mb-2">5000+</h3>
                  <p className="text-muted-foreground">Active Health Program Users</p>
                </CardContent>
              </Card>
              <Card className="medical-card text-center">
                <CardContent className="pt-6">
                  <TrendingUp className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-enhanced-heading mb-2">85%</h3>
                  <p className="text-muted-foreground">Health Improvement Rate</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Search and Filter Section */}
        <section className="py-12 bg-muted/30">
          <div className="container">
            <div className={cn(
              "flex gap-4 mb-8",
              deviceInfo.isMobile ? "flex-col" : "flex-row items-center"
            )}>
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search health resources..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2 flex-wrap">
                {['all', 'assessment', 'education', 'program', 'anatomy', 'recovery'].map((type) => (
                  <Button
                    key={type}
                    variant={selectedType === type ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedType(type)}
                    className="capitalize"
                  >
                    {type}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Featured Resources */}
        {featuredResources.length > 0 && (
          <section className="py-16">
            <div className="container">
              <h2 className="text-2xl font-bold text-enhanced-heading mb-8 text-center">
                Featured Health Resources
              </h2>
              <GridLayout
                items={featuredResources}
                renderItem={(resource) => {
                  const TypeIcon = getTypeIcon(resource.type);
                  return (
                    <Card key={resource.id} className="medical-card h-full hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <TypeIcon className="h-5 w-5 text-primary" />
                            <Badge className={getTypeColor(resource.type)}>
                              {resource.type}
                            </Badge>
                            {resource.requiresLogin && (
                              <Badge variant="outline" className="text-xs">
                                <Lock className="h-3 w-3 mr-1" />
                                Login Required
                              </Badge>
                            )}
                          </div>
                        </div>
                        <CardTitle className="text-xl">{resource.name}</CardTitle>
                        <CardDescription className="text-sm text-muted-foreground">
                          {resource.category}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="flex-1">
                        <p className="text-muted-foreground mb-4">{resource.description}</p>
                      </CardContent>
                      <div className="p-6 pt-0">
                        <Button asChild className="w-full">
                          <Link to={resource.path}>
                            Access Resource
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </Card>
                  );
                }}
                columns={deviceInfo.isMobile ? 1 : 2}
                gap="lg"
              />
            </div>
          </section>
        )}

        {/* All Resources */}
        {otherResources.length > 0 && (
          <section className="py-16 bg-muted/30">
            <div className="container">
              <h2 className="text-2xl font-bold text-enhanced-heading mb-8 text-center">
                All Health Resources
              </h2>
              <GridLayout
                items={otherResources}
                renderItem={(resource) => {
                  const TypeIcon = getTypeIcon(resource.type);
                  return (
                    <Card key={resource.id} className="medical-card h-full hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <TypeIcon className="h-5 w-5 text-primary" />
                            <Badge className={getTypeColor(resource.type)}>
                              {resource.type}
                            </Badge>
                            {resource.requiresLogin && (
                              <Badge variant="outline" className="text-xs">
                                <Lock className="h-3 w-3 mr-1" />
                                Login Required
                              </Badge>
                            )}
                          </div>
                        </div>
                        <CardTitle className="text-lg">{resource.name}</CardTitle>
                        <CardDescription className="text-sm text-muted-foreground">
                          {resource.category}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="flex-1">
                        <p className="text-muted-foreground text-sm mb-4">{resource.description}</p>
                      </CardContent>
                      <div className="p-6 pt-0">
                        <Button asChild className="w-full" size="sm">
                          <Link to={resource.path}>
                            Access Resource
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </Card>
                  );
                }}
                columns={deviceInfo.isMobile ? 1 : 3}
                gap="md"
              />
            </div>
          </section>
        )}

        {filteredResources.length === 0 && (
          <section className="py-16">
            <div className="container">
              <div className="text-center py-12">
                <Shield className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">No resources found</h3>
                <p className="text-muted-foreground mb-4">
                  Try adjusting your search terms or filters
                </p>
                <Button onClick={() => { setSearchTerm(''); setSelectedType('all'); }}>
                  Clear Filters
                </Button>
              </div>
            </div>
          </section>
        )}

        {/* Health Categories Overview */}
        <section className="py-16 bg-gradient-to-br from-primary/5 to-background">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl font-bold text-enhanced-heading mb-6">
                Comprehensive Health Management
              </h2>
              <p className="text-lg text-muted-foreground mb-12">
                Our integrated approach to brain and spine health combines assessment, education, and personalized care programs.
              </p>
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2 md:grid-cols-3"
              )}>
                {[
                  {
                    icon: Activity,
                    title: 'Assessment Tools',
                    description: 'Comprehensive health evaluations and progress tracking'
                  },
                  {
                    icon: BookOpen,
                    title: 'Educational Resources',
                    description: 'Evidence-based guides and anatomical references'
                  },
                  {
                    icon: Shield,
                    title: 'Health Programs',
                    description: 'Personalized care plans and wellness programs'
                  },
                  {
                    icon: Brain,
                    title: 'Anatomy Guides',
                    description: 'Interactive anatomical education and visualization'
                  },
                  {
                    icon: TrendingUp,
                    title: 'Recovery Support',
                    description: 'Specialized recovery protocols and guidelines'
                  },
                  {
                    icon: Users,
                    title: 'Patient Community',
                    description: 'Connect with others on similar health journeys'
                  }
                ].map((category, index) => {
                  const Icon = category.icon;
                  return (
                    <Card key={index} className="medical-card text-center">
                      <CardContent className="pt-6">
                        <Icon className="h-12 w-12 text-primary mx-auto mb-4" />
                        <h3 className="font-semibold mb-2">{category.title}</h3>
                        <p className="text-sm text-muted-foreground">{category.description}</p>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary text-primary-foreground">
          <div className="container text-center">
            <h2 className="text-3xl font-bold mb-4">
              Start Your Health Journey Today
            </h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Take control of your brain and spine health with our comprehensive resources and personalized care programs.
            </p>
            <div className={cn(
              "flex gap-4 justify-center",
              deviceInfo.isMobile ? "flex-col items-center" : "flex-row"
            )}>
              <Button asChild size="lg" variant="secondary">
                <Link to="/patient-resources/assessment-tools">Start Assessment</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
                <Link to="/appointments">Book Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
    </StandardPageLayout>
  );
};

export default BrainAndSpineHealth;
