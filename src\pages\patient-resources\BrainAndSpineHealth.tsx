import {
  <PERSON>,
  Layers,
  Shield,
  Users,
  TrendingUp,
  Search,
  ArrowRight,
  Lock,
  BookOpen,
  Activity,
  Bone,
  Network,
  Zap,
  Eye,
  Target,
  Disc,
  Heart,
  Microscope,
  Stethoscope
} from 'lucide-react';
import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { <PERSON> } from 'react-router-dom';

import PageHeader from '@/components/PageHeader';
import SafeImage from '@/components/SafeImage';
import { SectionHeader, GridLayout } from '@/components/shared/CommonSectionPatterns';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface HealthResource {
  id: string;
  name: string;
  description: string;
  type: 'assessment' | 'education' | 'program' | 'anatomy' | 'recovery';
  requiresLogin: boolean;
  path: string;
  category: string;
  featured: boolean;
}

interface AnatomicalStructure {
  id: string;
  name: string;
  description: string;
  function: string;
  pathophysiology: string;
  clinicalSignificance: string;
  image: string;
  icon: any;
  category: 'brain' | 'spine' | 'nervous-system';
}

interface HealthCondition {
  id: string;
  name: string;
  description: string;
  symptoms: string[];
  causes: string[];
  treatment: string;
  prevention: string;
  urgency: 'routine' | 'urgent' | 'emergency';
  category: 'brain' | 'spine' | 'nerve';
}

// Comprehensive anatomical structures with pathophysiology
const anatomicalStructures: AnatomicalStructure[] = [
  {
    id: 'cerebral-cortex',
    name: 'Cerebral Cortex',
    description: 'The outer layer of the brain responsible for higher cognitive functions, sensory processing, and motor control.',
    function: 'Controls conscious thought, language, memory, personality, and voluntary movements. Divided into four lobes: frontal, parietal, temporal, and occipital.',
    pathophysiology: 'Damage to the cortex can result from stroke, trauma, tumours, or degenerative diseases. Different regions control specific functions, so localised damage produces predictable deficits.',
    clinicalSignificance: 'Cortical lesions can cause aphasia, motor weakness, sensory loss, visual field defects, or personality changes depending on location. Early recognition is crucial for treatment.',
    image: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop',
    icon: Brain,
    category: 'brain'
  },
  {
    id: 'brainstem',
    name: 'Brainstem',
    description: 'The vital connection between the brain and spinal cord, controlling essential life functions.',
    function: 'Regulates breathing, heart rate, blood pressure, consciousness, and sleep-wake cycles. Contains cranial nerve nuclei and ascending/descending pathways.',
    pathophysiology: 'Brainstem lesions can be life-threatening due to disruption of vital functions. Small lesions can cause significant neurological deficits due to compact anatomy.',
    clinicalSignificance: 'Brainstem strokes, tumours, or trauma require immediate medical attention. Symptoms include altered consciousness, breathing difficulties, and cranial nerve palsies.',
    image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop',
    icon: Network,
    category: 'brain'
  },
  {
    id: 'cerebellum',
    name: 'Cerebellum',
    description: 'The "little brain" responsible for balance, coordination, and motor learning.',
    function: 'Coordinates voluntary movements, maintains balance and posture, and contributes to motor learning and cognitive functions.',
    pathophysiology: 'Cerebellar dysfunction results in ataxia (uncoordinated movements), tremor, balance problems, and speech difficulties. Does not cause paralysis.',
    clinicalSignificance: 'Cerebellar disorders affect quality of life significantly. Common causes include stroke, tumours, alcohol toxicity, and genetic conditions.',
    image: 'https://images.unsplash.com/photo-1582719471384-894fbb16e074?w=800&h=600&fit=crop',
    icon: Target,
    category: 'brain'
  },
  {
    id: 'spinal-cord',
    name: 'Spinal Cord',
    description: 'The main pathway for information connecting the brain and peripheral nervous system.',
    function: 'Transmits motor commands from brain to muscles and sensory information from body to brain. Contains reflex circuits for rapid responses.',
    pathophysiology: 'Spinal cord injury can cause paralysis, sensory loss, and autonomic dysfunction below the level of injury. Severity depends on completeness of injury.',
    clinicalSignificance: 'Spinal cord injuries are often permanent. Early recognition and treatment of compression can prevent irreversible damage.',
    image: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=800&h=600&fit=crop',
    icon: Zap,
    category: 'spine'
  },
  {
    id: 'vertebrae',
    name: 'Vertebrae',
    description: 'The bony structures that protect the spinal cord and provide structural support.',
    function: 'Protect spinal cord, support body weight, allow spinal movement, and provide attachment points for muscles and ligaments.',
    pathophysiology: 'Vertebral fractures, degeneration, or misalignment can compress neural structures, causing pain, weakness, and neurological deficits.',
    clinicalSignificance: 'Vertebral pathology is common with aging. Compression fractures, disc degeneration, and spinal stenosis are frequent causes of back pain.',
    image: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=800&h=600&fit=crop',
    icon: Bone,
    category: 'spine'
  },
  {
    id: 'intervertebral-discs',
    name: 'Intervertebral Discs',
    description: 'Fibrocartilaginous cushions between vertebrae that absorb shock and allow spinal movement.',
    function: 'Absorb mechanical stress, distribute loads evenly, and allow spinal flexibility while maintaining stability.',
    pathophysiology: 'Disc degeneration leads to loss of height, reduced shock absorption, and potential herniation. Herniated discs can compress nerve roots.',
    clinicalSignificance: 'Disc problems are a leading cause of back pain and sciatica. Treatment ranges from conservative management to surgical intervention.',
    image: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop',
    icon: Disc,
    category: 'spine'
  }
];

// Common neurological conditions with comprehensive information
const commonConditions: HealthCondition[] = [
  {
    id: 'stroke',
    name: 'Stroke',
    description: 'Sudden loss of brain function due to interrupted blood supply, requiring immediate medical attention.',
    symptoms: ['Sudden weakness or numbness', 'Speech difficulties', 'Vision problems', 'Severe headache', 'Loss of coordination'],
    causes: ['Blood clot blocking artery', 'Bleeding in brain', 'High blood pressure', 'Atrial fibrillation', 'Atherosclerosis'],
    treatment: 'Emergency treatment with clot-busting drugs or mechanical thrombectomy. Rehabilitation focuses on recovery of lost functions.',
    prevention: 'Control blood pressure, manage diabetes, quit smoking, exercise regularly, maintain healthy diet, treat atrial fibrillation.',
    urgency: 'emergency',
    category: 'brain'
  },
  {
    id: 'herniated-disc',
    name: 'Herniated Disc',
    description: 'Displacement of disc material that can compress nerve roots, causing pain and neurological symptoms.',
    symptoms: ['Lower back pain', 'Leg pain (sciatica)', 'Numbness or tingling', 'Muscle weakness', 'Difficulty walking'],
    causes: ['Age-related degeneration', 'Sudden lifting or twisting', 'Repetitive stress', 'Genetic factors', 'Obesity'],
    treatment: 'Conservative: rest, physiotherapy, medications. Surgical: discectomy or fusion for severe cases.',
    prevention: 'Maintain good posture, regular exercise, proper lifting techniques, weight management, core strengthening.',
    urgency: 'routine',
    category: 'spine'
  },
  {
    id: 'spinal-stenosis',
    name: 'Spinal Stenosis',
    description: 'Narrowing of the spinal canal that can compress the spinal cord or nerve roots.',
    symptoms: ['Back pain', 'Leg pain with walking', 'Numbness in legs', 'Weakness', 'Balance problems'],
    causes: ['Age-related changes', 'Arthritis', 'Ligament thickening', 'Disc degeneration', 'Bone spurs'],
    treatment: 'Conservative: physiotherapy, medications, injections. Surgical: decompression procedures.',
    prevention: 'Regular exercise, maintain flexibility, good posture, weight management, avoid prolonged sitting.',
    urgency: 'routine',
    category: 'spine'
  }
];

const healthResources: HealthResource[] = [
  {
    id: 'expert-guide',
    name: 'Expert Guide to Spine & Brain Health',
    description: 'Comprehensive guide covering prevention, maintenance, and optimization of spine and brain health.',
    type: 'education',
    requiresLogin: false,
    path: '/patient-resources/expert-guide-spine-brain-health',
    category: 'Education',
    featured: true
  },
  {
    id: 'individual-programme',
    name: 'Individual Spine Health Programme',
    description: 'Personalised spine health program tailored to your specific needs and condition.',
    type: 'program',
    requiresLogin: false,
    path: '/patient-resources/individual-spine-health-programme',
    category: 'Programs',
    featured: true
  },
  {
    id: 'assessment-tools',
    name: 'Assessment Tools',
    description: 'Comprehensive health assessment tools to evaluate your spine and brain health status.',
    type: 'assessment',
    requiresLogin: true,
    path: '/patient-resources/assessment-tools',
    category: 'Assessment',
    featured: true
  },
  {
    id: 'patient-dashboard',
    name: 'Patient Dashboard',
    description: 'Personal health dashboard to track your progress, appointments, and treatment plans.',
    type: 'program',
    requiresLogin: true,
    path: '/patient-resources/patient-dashboard',
    category: 'Management',
    featured: true
  },
  {
    id: 'spine-health-app',
    name: 'Spine Health App',
    description: 'Mobile application for spine health monitoring, exercises, and progress tracking.',
    type: 'program',
    requiresLogin: false,
    path: '/patient-resources/spine-health-app',
    category: 'Technology',
    featured: false
  },
  {
    id: 'cervical-injury-recovery',
    name: 'Cervical Spine Injury Recovery',
    description: 'Specialised recovery protocols and guidelines for cervical spine injuries.',
    type: 'recovery',
    requiresLogin: false,
    path: '/patient-resources/cervical-spine-injury',
    category: 'Recovery',
    featured: false
  },
  {
    id: 'lifestyle-modifications',
    name: 'Lifestyle Modifications',
    description: 'Evidence-based lifestyle changes to improve spine and brain health outcomes.',
    type: 'education',
    requiresLogin: false,
    path: '/patient-resources/lifestyle-modifications',
    category: 'Education',
    featured: false
  },
  {
    id: 'brain-anatomy',
    name: 'Brain Anatomy',
    description: 'Interactive guide to brain anatomy and function for patient education.',
    type: 'anatomy',
    requiresLogin: false,
    path: '/patient-resources/brain-anatomy',
    category: 'Anatomy',
    featured: false
  },
  {
    id: 'nerve-anatomy',
    name: 'Nerve Anatomy',
    description: 'Comprehensive guide to peripheral and central nervous system anatomy.',
    type: 'anatomy',
    requiresLogin: false,
    path: '/patient-resources/nerve-anatomy',
    category: 'Anatomy',
    featured: false
  },
  {
    id: 'spine-anatomy',
    name: 'Spine Anatomy',
    description: 'Detailed anatomical guide to spinal structure and function.',
    type: 'anatomy',
    requiresLogin: false,
    path: '/patient-resources/spine-anatomy',
    category: 'Anatomy',
    featured: false
  }
];

const BrainAndSpineHealth: React.FC = () => {
  const deviceInfo = useDeviceDetection();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [showLoginRequired, setShowLoginRequired] = useState(true);

  // Filter resources based on search and type
  const filteredResources = healthResources
    .filter(resource => 
      resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.category.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .filter(resource => selectedType === 'all' || resource.type === selectedType)
    .filter(resource => showLoginRequired || !resource.requiresLogin);

  const featuredResources = filteredResources.filter(resource => resource.featured);
  const otherResources = filteredResources.filter(resource => !resource.featured);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'assessment': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'education': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'program': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'anatomy': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'recovery': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'assessment': return Activity;
      case 'education': return BookOpen;
      case 'program': return Shield;
      case 'anatomy': return Brain;
      case 'recovery': return TrendingUp;
      default: return Brain;
    }
  };

  return (
    <StandardPageLayout showHeader={false}>
      <Helmet>
        <title>Brain and Spine Health | Comprehensive Anatomy & Pathophysiology Guide | miNEURO</title>
        <meta
          name="description"
          content="Comprehensive brain and spine anatomy guide with pathophysiology insights. Learn about neurological structures, common conditions, and evidence-based health management from expert neurosurgeons."
        />
        <meta name="keywords" content="brain anatomy, spine anatomy, pathophysiology, neurological conditions, brain health, spine health, neurosurgical education, medical anatomy, nervous system" />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/brain-and-spine-health" />

        {/* Open Graph tags for social sharing */}
        <meta property="og:title" content="Brain and Spine Health | Comprehensive Anatomy Guide" />
        <meta property="og:description" content="Expert guide to brain and spine anatomy with pathophysiology insights and health management resources." />
        <meta property="og:image" content="https://images.unsplash.com/photo-**********-5c350d0d3c56?w=1200&h=630&fit=crop" />
        <meta property="og:type" content="article" />

        {/* Medical schema markup */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "MedicalWebPage",
            "name": "Brain and Spine Health",
            "description": "Comprehensive guide to brain and spine anatomy with pathophysiology insights",
            "medicalAudience": "Patient",
            "about": {
              "@type": "MedicalCondition",
              "name": "Neurological Health"
            },
            "author": {
              "@type": "Organization",
              "name": "miNEURO Brain and Spine Surgery"
            }
          })}
        </script>
      </Helmet>

      <PageHeader
        title="Brain and Spine Health"
        subtitle="Comprehensive anatomical education, pathophysiology insights, and personalised health management for optimal neurological wellness"
        backgroundImage="https://images.unsplash.com/photo-**********-5c350d0d3c56?w=1920&h=1080&fit=crop"
        enableParallax={true}
      />

      <main className="flex-1">
        {/* Overview Section */}
        <section className="py-16 bg-gradient-to-br from-muted/10 to-background">
          <div className="container">
            <SectionHeader
              title="Your Health Management Hub"
              subtitle="Comprehensive tools and resources for optimal neurological health"
              description="Access personalised assessment tools, educational resources, and health management programs designed to support your brain and spine health journey."
              centered={true}
            />

            {/* Quick Stats */}
            <div className={cn(
              "grid gap-6 mt-12",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
            )}>
              <Card className="medical-card text-centre">
                <CardContent className="pt-6">
                  <Shield className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-enhanced-heading mb-2">10</h3>
                  <p className="text-muted-foreground">Health Resources Available</p>
                </CardContent>
              </Card>
              <Card className="medical-card text-centre">
                <CardContent className="pt-6">
                  <Users className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-enhanced-heading mb-2">5000+</h3>
                  <p className="text-muted-foreground">Active Health Program Users</p>
                </CardContent>
              </Card>
              <Card className="medical-card text-centre">
                <CardContent className="pt-6">
                  <TrendingUp className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-enhanced-heading mb-2">85%</h3>
                  <p className="text-muted-foreground">Health Improvement Rate</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Comprehensive Anatomy & Pathophysiology Section */}
        <section className="py-16 bg-gradient-to-br from-background to-muted/20">
          <div className="container">
            <SectionHeader
              title="Neuroanatomy & Pathophysiology"
              subtitle="Understanding the structure and function of your nervous system"
              description="Comprehensive guide to brain and spine anatomy with clinical insights into how diseases affect these vital structures."
              centered={true}
            />

            <Tabs defaultValue="brain-anatomy" className="mt-12">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="brain-anatomy">Brain Anatomy</TabsTrigger>
                <TabsTrigger value="spine-anatomy">Spine Anatomy</TabsTrigger>
                <TabsTrigger value="pathophysiology">Pathophysiology</TabsTrigger>
              </TabsList>

              {/* Brain Anatomy Tab */}
              <TabsContent value="brain-anatomy" className="mt-8">
                <div className="grid gap-8">
                  <div className="text-centre mb-8">
                    <h3 className="text-2xl font-bold text-enhanced-heading mb-4">
                      Brain Structure and Function
                    </h3>
                    <p className="text-muted-foreground max-w-3xl mx-auto">
                      The human brain is the most complex organ in the body, controlling all aspects of human life.
                      Understanding its anatomy is crucial for recognising symptoms and treatment options.
                    </p>
                  </div>

                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {anatomicalStructures
                      .filter(structure => structure.category === 'brain')
                      .map((structure) => {
                        const Icon = structure.icon;
                        return (
                          <Card key={structure.id} className="medical-card h-full">
                            <CardHeader>
                              <div className="flex items-centre gap-3 mb-3">
                                <Icon className="h-6 w-6 text-primary" />
                                <CardTitle className="text-lg">{structure.name}</CardTitle>
                              </div>
                              <SafeImage
                                src={structure.image}
                                alt={`${structure.name} anatomy`}
                                className="w-full h-48 object-cover rounded-lg"
                                fallbackSrc="/images/anatomy/brain-default.jpg"
                              />
                            </CardHeader>
                            <CardContent className="space-y-4">
                              <div>
                                <h4 className="font-semibold text-sm text-primary mb-2">Description</h4>
                                <p className="text-sm text-muted-foreground">{structure.description}</p>
                              </div>
                              <div>
                                <h4 className="font-semibold text-sm text-primary mb-2">Function</h4>
                                <p className="text-sm text-muted-foreground">{structure.function}</p>
                              </div>
                              <div>
                                <h4 className="font-semibold text-sm text-primary mb-2">Clinical Significance</h4>
                                <p className="text-sm text-muted-foreground">{structure.clinicalSignificance}</p>
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })}
                  </div>
                </div>
              </TabsContent>

              {/* Spine Anatomy Tab */}
              <TabsContent value="spine-anatomy" className="mt-8">
                <div className="grid gap-8">
                  <div className="text-centre mb-8">
                    <h3 className="text-2xl font-bold text-enhanced-heading mb-4">
                      Spinal Structure and Function
                    </h3>
                    <p className="text-muted-foreground max-w-3xl mx-auto">
                      The spine is a complex structure providing support, protection, and mobility.
                      It houses the spinal cord and nerve roots that control movement and sensation throughout the body.
                    </p>
                  </div>

                  <div className="grid gap-6 md:grid-cols-2">
                    {anatomicalStructures
                      .filter(structure => structure.category === 'spine')
                      .map((structure) => {
                        const Icon = structure.icon;
                        return (
                          <Card key={structure.id} className="medical-card h-full">
                            <CardHeader>
                              <div className="flex items-centre gap-3 mb-3">
                                <Icon className="h-6 w-6 text-primary" />
                                <CardTitle className="text-lg">{structure.name}</CardTitle>
                              </div>
                              <SafeImage
                                src={structure.image}
                                alt={`${structure.name} anatomy`}
                                className="w-full h-48 object-cover rounded-lg"
                                fallbackSrc="/images/anatomy/spine-default.jpg"
                              />
                            </CardHeader>
                            <CardContent className="space-y-4">
                              <div>
                                <h4 className="font-semibold text-sm text-primary mb-2">Description</h4>
                                <p className="text-sm text-muted-foreground">{structure.description}</p>
                              </div>
                              <div>
                                <h4 className="font-semibold text-sm text-primary mb-2">Function</h4>
                                <p className="text-sm text-muted-foreground">{structure.function}</p>
                              </div>
                              <div>
                                <h4 className="font-semibold text-sm text-primary mb-2">Pathophysiology</h4>
                                <p className="text-sm text-muted-foreground">{structure.pathophysiology}</p>
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })}
                  </div>
                </div>
              </TabsContent>

              {/* Pathophysiology Tab */}
              <TabsContent value="pathophysiology" className="mt-8">
                <div className="grid gap-8">
                  <div className="text-centre mb-8">
                    <h3 className="text-2xl font-bold text-enhanced-heading mb-4">
                      Common Neurological Conditions
                    </h3>
                    <p className="text-muted-foreground max-w-3xl mx-auto">
                      Understanding how diseases affect the nervous system helps in early recognition,
                      appropriate treatment, and better outcomes for patients.
                    </p>
                  </div>

                  <div className="grid gap-6">
                    {commonConditions.map((condition) => {
                      const getUrgencyColor = (urgency: string) => {
                        switch (urgency) {
                          case 'emergency': return 'bg-error-light/30 text-foreground border-error/50';
                          case 'urgent': return 'bg-info-light/30 text-foreground border-info/50';
                          default: return 'bg-success-light/30 text-foreground border-success/50';
                        }
                      };

                      return (
                        <Card key={condition.id} className={cn("medical-card", getUrgencyColor(condition.urgency))}>
                          <CardHeader>
                            <div className="flex items-centre justify-between">
                              <CardTitle className="text-xl">{condition.name}</CardTitle>
                              <Badge variant={condition.urgency === 'emergency' ? 'destructive' : 'secondary'}>
                                {condition.urgency}
                              </Badge>
                            </div>
                            <CardDescription className="text-foreground/80">
                              {condition.description}
                            </CardDescription>
                          </CardHeader>
                          <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                              <div>
                                <h4 className="font-semibold text-sm mb-2 flex items-centre gap-2">
                                  <Eye className="h-4 w-4" />
                                  Symptoms
                                </h4>
                                <ul className="text-sm space-y-1">
                                  {condition.symptoms.map((symptom, index) => (
                                    <li key={index} className="flex items-start gap-2">
                                      <span className="w-1 h-1 bg-current rounded-full mt-2 flex-shrink-0" />
                                      {symptom}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                              <div>
                                <h4 className="font-semibold text-sm mb-2 flex items-centre gap-2">
                                  <Target className="h-4 w-4" />
                                  Common Causes
                                </h4>
                                <ul className="text-sm space-y-1">
                                  {condition.causes.map((cause, index) => (
                                    <li key={index} className="flex items-start gap-2">
                                      <span className="w-1 h-1 bg-current rounded-full mt-2 flex-shrink-0" />
                                      {cause}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                            <div>
                              <h4 className="font-semibold text-sm mb-2 flex items-centre gap-2">
                                <Stethoscope className="h-4 w-4" />
                                Treatment Approach
                              </h4>
                              <p className="text-sm">{condition.treatment}</p>
                            </div>
                            <div>
                              <h4 className="font-semibold text-sm mb-2 flex items-centre gap-2">
                                <Shield className="h-4 w-4" />
                                Prevention
                              </h4>
                              <p className="text-sm">{condition.prevention}</p>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </section>

        {/* Search and Filter Section */}
        <section className="py-12 bg-muted/30">
          <div className="container">
            <div className={cn(
              "flex gap-4 mb-8",
              deviceInfo.isMobile ? "flex-col" : "flex-row items-centre"
            )}>
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search health resources..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2 flex-wrap">
                {['all', 'assessment', 'education', 'program', 'anatomy', 'recovery'].map((type) => (
                  <Button
                    key={type}
                    variant={selectedType === type ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedType(type)}
                    className="capitalize"
                  >
                    {type}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Featured Resources */}
        {featuredResources.length > 0 && (
          <section className="py-16">
            <div className="container">
              <h2 className="text-2xl font-bold text-enhanced-heading mb-8 text-centre">
                Featured Health Resources
              </h2>
              <GridLayout
                items={featuredResources}
                renderItem={(resource) => {
                  const TypeIcon = getTypeIcon(resource.type);
                  return (
                    <Card key={resource.id} className="medical-card h-full hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-centre gap-2">
                            <TypeIcon className="h-5 w-5 text-primary" />
                            <Badge className={getTypeColor(resource.type)}>
                              {resource.type}
                            </Badge>
                            {resource.requiresLogin && (
                              <Badge variant="outline" className="text-xs">
                                <Lock className="h-3 w-3 mr-1" />
                                Login Required
                              </Badge>
                            )}
                          </div>
                        </div>
                        <CardTitle className="text-xl">{resource.name}</CardTitle>
                        <CardDescription className="text-sm text-muted-foreground">
                          {resource.category}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="flex-1">
                        <p className="text-muted-foreground mb-4">{resource.description}</p>
                      </CardContent>
                      <div className="p-6 pt-0">
                        <Button asChild className="w-full">
                          <Link to={resource.path}>
                            Access Resource
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </Card>
                  );
                }}
                columns={deviceInfo.isMobile ? 1 : 2}
                gap="lg"
              />
            </div>
          </section>
        )}

        {/* All Resources */}
        {otherResources.length > 0 && (
          <section className="py-16 bg-muted/30">
            <div className="container">
              <h2 className="text-2xl font-bold text-enhanced-heading mb-8 text-centre">
                All Health Resources
              </h2>
              <GridLayout
                items={otherResources}
                renderItem={(resource) => {
                  const TypeIcon = getTypeIcon(resource.type);
                  return (
                    <Card key={resource.id} className="medical-card h-full hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-centre gap-2">
                            <TypeIcon className="h-5 w-5 text-primary" />
                            <Badge className={getTypeColor(resource.type)}>
                              {resource.type}
                            </Badge>
                            {resource.requiresLogin && (
                              <Badge variant="outline" className="text-xs">
                                <Lock className="h-3 w-3 mr-1" />
                                Login Required
                              </Badge>
                            )}
                          </div>
                        </div>
                        <CardTitle className="text-lg">{resource.name}</CardTitle>
                        <CardDescription className="text-sm text-muted-foreground">
                          {resource.category}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="flex-1">
                        <p className="text-muted-foreground text-sm mb-4">{resource.description}</p>
                      </CardContent>
                      <div className="p-6 pt-0">
                        <Button asChild className="w-full" size="sm">
                          <Link to={resource.path}>
                            Access Resource
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </Card>
                  );
                }}
                columns={deviceInfo.isMobile ? 1 : 3}
                gap="md"
              />
            </div>
          </section>
        )}

        {filteredResources.length === 0 && (
          <section className="py-16">
            <div className="container">
              <div className="text-centre py-12">
                <Shield className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">No resources found</h3>
                <p className="text-muted-foreground mb-4">
                  Try adjusting your search terms or filters
                </p>
                <Button onClick={() => { setSearchTerm(''); setSelectedType('all'); }}>
                  Clear Filters
                </Button>
              </div>
            </div>
          </section>
        )}

        {/* Spine Conditions Library Integration */}
        <section className="py-16 bg-muted/30">
          <div className="container">
            <SectionHeader
              title="Common Neurological Conditions"
              subtitle="Evidence-based information from our comprehensive conditions library"
              description="Learn about the most common brain and spine conditions, their symptoms, and treatment options from our expert neurosurgical team."
              centered={true}
            />

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mt-12">
              {[
                {
                  name: "Cervical Myelopathy",
                  description: "Spinal cord compression in the neck causing weakness and coordination problems",
                  urgency: "urgent",
                  category: "spine",
                  path: "/patient-resources/conditions/cervical-myelopathy",
                  image: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=400&h=300&fit=crop"
                },
                {
                  name: "Brain Tumour",
                  description: "Abnormal growth requiring specialised neurosurgical evaluation and treatment",
                  urgency: "urgent",
                  category: "brain",
                  path: "/patient-resources/conditions/brain-tumour",
                  image: "https://images.unsplash.com/photo-**********-0eb30cd8c063?w=400&h=300&fit=crop"
                },
                {
                  name: "Herniated Disc",
                  description: "Disc displacement causing nerve compression and radiating pain",
                  urgency: "routine",
                  category: "spine",
                  path: "/patient-resources/conditions/herniated-disc",
                  image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop"
                },
                {
                  name: "Trigeminal Neuralgia",
                  description: "Severe facial pain from trigeminal nerve dysfunction",
                  urgency: "routine",
                  category: "brain",
                  path: "/patient-resources/conditions/trigeminal-neuralgia",
                  image: "https://images.unsplash.com/photo-1582719471384-894fbb16e074?w=400&h=300&fit=crop"
                },
                {
                  name: "Spinal Stenosis",
                  description: "Narrowing of spinal canal causing nerve compression",
                  urgency: "routine",
                  category: "spine",
                  path: "/patient-resources/conditions/spinal-stenosis",
                  image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop"
                },
                {
                  name: "Hydrocephalus",
                  description: "Fluid accumulation in brain requiring drainage procedures",
                  urgency: "urgent",
                  category: "brain",
                  path: "/patient-resources/conditions/hydrocephalus",
                  image: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=400&h=300&fit=crop"
                }
              ].map((condition, index) => {
                const getUrgencyColor = (urgency: string) => {
                  switch (urgency) {
                    case 'emergency': return 'bg-error-light/30 border-error/50';
                    case 'urgent': return 'bg-info-light/30 border-info/50';
                    default: return 'bg-success-light/30 border-success/50';
                  }
                };

                return (
                  <Card key={index} className={cn("medical-card hover:shadow-lg transition-shadow", getUrgencyColor(condition.urgency))}>
                    <CardHeader>
                      <SafeImage
                        src={condition.image}
                        alt={condition.name}
                        className="w-full h-40 object-cover rounded-lg mb-3"
                        fallbackSrc="/images/conditions/default-condition.jpg"
                      />
                      <div className="flex items-centre justify-between">
                        <CardTitle className="text-lg">{condition.name}</CardTitle>
                        <Badge variant={condition.urgency === 'urgent' ? 'secondary' : 'outline'}>
                          {condition.urgency}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-4">{condition.description}</p>
                      <Button asChild className="w-full" size="sm">
                        <Link to={condition.path}>
                          Learn More
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            <div className="text-centre mt-12">
              <Button asChild size="lg" variant="outline">
                <Link to="/patient-resources/spine-conditions-library">
                  View Complete Conditions Library
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Health Categories Overview */}
        <section className="py-16 bg-gradient-to-br from-primary/5 to-background">
          <div className="container">
            <div className="max-w-4xl mx-auto text-centre">
              <h2 className="text-3xl font-bold text-enhanced-heading mb-6">
                Comprehensive Health Management
              </h2>
              <p className="text-lg text-muted-foreground mb-12">
                Our integrated approach to brain and spine health combines anatomical education, pathophysiology understanding, assessment tools, and personalised care programs.
              </p>
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2 md:grid-cols-3"
              )}>
                {[
                  {
                    icon: Activity,
                    title: 'Assessment Tools',
                    description: 'Comprehensive health evaluations and progress tracking'
                  },
                  {
                    icon: BookOpen,
                    title: 'Educational Resources',
                    description: 'Evidence-based guides and anatomical references'
                  },
                  {
                    icon: Shield,
                    title: 'Health Programs',
                    description: 'Personalised care plans and wellness programs'
                  },
                  {
                    icon: Brain,
                    title: 'Anatomy Guides',
                    description: 'Interactive anatomical education and visualization'
                  },
                  {
                    icon: TrendingUp,
                    title: 'Recovery Support',
                    description: 'Specialised recovery protocols and guidelines'
                  },
                  {
                    icon: Users,
                    title: 'Patient Community',
                    description: 'Connect with others on similar health journeys'
                  }
                ].map((category, index) => {
                  const Icon = category.icon;
                  return (
                    <Card key={index} className="medical-card text-centre">
                      <CardContent className="pt-6">
                        <Icon className="h-12 w-12 text-primary mx-auto mb-4" />
                        <h3 className="font-semibold mb-2">{category.title}</h3>
                        <p className="text-sm text-muted-foreground">{category.description}</p>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          </div>
        </section>

        {/* Lifestyle and Prevention Section */}
        <section className="py-16 bg-gradient-to-br from-muted/20 to-background">
          <div className="container">
            <SectionHeader
              title="Lifestyle Modifications for Brain and Spine Health"
              subtitle="Evidence-based strategies for optimal neurological wellness"
              description="Discover practical lifestyle changes that can significantly impact your brain and spine health, backed by current neuroscientific research."
              centered={true}
            />

            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4 mt-12">
              {[
                {
                  icon: Heart,
                  title: "Cardiovascular Health",
                  description: "Regular aerobic exercise improves blood flow to the brain and spine, reducing risk of stroke and promoting healing.",
                  tips: ["30 minutes daily exercise", "Control blood pressure", "Manage cholesterol", "Quit smoking"]
                },
                {
                  icon: Brain,
                  title: "Cognitive Stimulation",
                  description: "Mental challenges and learning new skills promote neuroplasticity and may reduce cognitive decline.",
                  tips: ["Learn new skills", "Read regularly", "Solve puzzles", "Social engagement"]
                },
                {
                  icon: Bone,
                  title: "Spinal Health",
                  description: "Proper posture, core strengthening, and ergonomics prevent spinal degeneration and injury.",
                  tips: ["Maintain good posture", "Strengthen core muscles", "Ergonomic workspace", "Regular stretching"]
                },
                {
                  icon: Shield,
                  title: "Injury Prevention",
                  description: "Safety measures and protective equipment significantly reduce risk of traumatic brain and spine injuries.",
                  tips: ["Wear helmets", "Use seatbelts", "Fall prevention", "Safe lifting techniques"]
                }
              ].map((category, index) => {
                const Icon = category.icon;
                return (
                  <Card key={index} className="medical-card h-full">
                    <CardHeader className="text-centre">
                      <Icon className="h-12 w-12 text-primary mx-auto mb-4" />
                      <CardTitle className="text-lg">{category.title}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <p className="text-sm text-muted-foreground">{category.description}</p>
                      <div>
                        <h4 className="font-semibold text-sm mb-2">Key Strategies:</h4>
                        <ul className="text-sm space-y-1">
                          {category.tips.map((tip, tipIndex) => (
                            <li key={tipIndex} className="flex items-start gap-2">
                              <span className="w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0" />
                              {tip}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Nutrition and Sleep Section */}
            <div className="grid gap-8 md:grid-cols-2 mt-12">
              <Card className="medical-card">
                <CardHeader>
                  <CardTitle className="flex items-centre gap-3">
                    <Microscope className="h-6 w-6 text-primary" />
                    Nutrition for Neurological Health
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Proper nutrition supports brain function, reduces inflammation, and promotes healing of neural tissues.
                  </p>
                  <div className="grid gap-3">
                    <div>
                      <h4 className="font-semibold text-sm mb-1">Brain-Healthy Foods:</h4>
                      <p className="text-sm text-muted-foreground">Omega-3 fatty fish, berries, leafy greens, nuts, and whole grains</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-sm mb-1">Anti-Inflammatory Foods:</h4>
                      <p className="text-sm text-muted-foreground">Turmeric, ginger, olive oil, and colourful vegetables</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-sm mb-1">Hydration:</h4>
                      <p className="text-sm text-muted-foreground">8-10 glasses of water daily for optimal brain function</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="medical-card">
                <CardHeader>
                  <CardTitle className="flex items-centre gap-3">
                    <Activity className="h-6 w-6 text-primary" />
                    Sleep and Recovery
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Quality sleep is essential for brain detoxification, memory consolidation, and spinal disc rehydration.
                  </p>
                  <div className="grid gap-3">
                    <div>
                      <h4 className="font-semibold text-sm mb-1">Sleep Duration:</h4>
                      <p className="text-sm text-muted-foreground">7-9 hours nightly for optimal neurological function</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-sm mb-1">Sleep Hygiene:</h4>
                      <p className="text-sm text-muted-foreground">Consistent schedule, cool environment, limit screen time</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-sm mb-1">Spinal Positioning:</h4>
                      <p className="text-sm text-muted-foreground">Supportive mattress and pillow for proper spinal alignment</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Enhanced CTA Section */}
        <section className="py-16 bg-gradient-to-r from-primary to-primary-dark text-primary-foreground">
          <div className="container text-centre">
            <h2 className="text-3xl font-bold mb-6">
              Take Control of Your Neurological Health
            </h2>
            <p className="text-xl mb-8 opacity-90 max-w-3xl mx-auto">
              Start your journey to better brain and spine health with our comprehensive anatomical education, assessment tools, and personalised programs backed by expert neurosurgical care.
            </p>
            <div className={cn(
              "flex gap-4 justify-centre",
              deviceInfo.isMobile ? "flex-col items-centre" : "flex-row flex-wrap"
            )}>
              <Button asChild size="lg" variant="secondary">
                <Link to="/patient-resources/assessment-tools">
                  <Lock className="mr-2 h-5 w-5" />
                  Access Assessment Tools
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
                <Link to="/patient-resources/exercise-library">
                  <Activity className="mr-2 h-5 w-5" />
                  Explore Exercise Programs
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
                <Link to="/patient-resources/spine-conditions-library">
                  <BookOpen className="mr-2 h-5 w-5" />
                  Conditions Library
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
                <Link to="/appointments">
                  <Stethoscope className="mr-2 h-5 w-5" />
                  Book Consultation
                </Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
    </StandardPageLayout>
  );
};

export default BrainAndSpineHealth;
