import {
  <PERSON>,
  Users,
  TrendingUp,
  Alert<PERSON>riangle,
  Search,
  ArrowRight,
  CheckCircle,
  Clock,
  Stethoscope,
  Activity,
  Shield,
  Filter,
  Star,
  Zap
} from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { Link } from 'react-router-dom';

import PageHeader from '@/components/PageHeader';
import AppointmentCallToActionSection from '@/components/patient-resources/AppointmentCallToActionSection';
import { SectionHeader, GridLayout } from '@/components/shared/CommonSectionPatterns';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface BrainCondition {
  id: string;
  name: string;
  description: string;
  prevalence: string;
  urgency: 'emergency' | 'urgent' | 'routine';
  symptoms: string[];
  path: string;
  frequency: number; // 1-9, 1 being most common
  category: 'tumour' | 'vascular' | 'functional' | 'structural';
  treatmentOptions: string[];
  keyFeatures: string[];
  valueProposition: string;
  estimatedReadTime: string;
}

const brainConditions: BrainCondition[] = [
  {
    id: 'brain-tumour',
    name: 'Brain Tumour',
    description: 'Abnormal growth of cells within the brain or central nervous system, requiring specialized diagnosis and treatment.',
    prevalence: '24 per 100,000 people',
    urgency: 'urgent',
    symptoms: ['Persistent headaches', 'Seizures', 'Cognitive changes', 'Vision problems'],
    path: '/patient-resources/conditions/brain-tumour',
    frequency: 1
  },
  {
    id: 'trigeminal-neuralgia',
    name: 'Trigeminal Neuralgia',
    description: 'Severe facial pain caused by dysfunction of the trigeminal nerve, often described as electric shock-like pain.',
    prevalence: '12 per 100,000 people',
    urgency: 'urgent',
    symptoms: ['Sharp facial pain', 'Triggered by light touch', 'Pain in jaw/cheek', 'Brief pain episodes'],
    path: '/patient-resources/conditions/trigeminal-neuralgia',
    frequency: 2
  },
  {
    id: 'cerebral-aneurysm',
    name: 'Cerebral Aneurysm',
    description: 'Weakened area in a brain blood vessel that can rupture, potentially causing life-threatening bleeding.',
    prevalence: '3-5% of population',
    urgency: 'emergency',
    symptoms: ['Sudden severe headache', 'Neck stiffness', 'Vision changes', 'Loss of consciousness'],
    path: '/patient-resources/conditions/cerebral-aneurysm',
    frequency: 3
  },
  {
    id: 'hemifacial-spasm',
    name: 'Hemifacial Spasm',
    description: 'Involuntary muscle contractions on one side of the face, typically caused by blood vessel compression.',
    prevalence: '8 per 100,000 people',
    urgency: 'routine',
    symptoms: ['Facial twitching', 'Eye closure spasms', 'Mouth pulling', 'Progressive symptoms'],
    path: '/patient-resources/conditions/hemifacial-spasm',
    frequency: 4
  },
  {
    id: 'cerebral-meningioma',
    name: 'Cerebral Meningioma',
    description: 'Usually benign tumour arising from the protective membranes covering the brain and spinal cord.',
    prevalence: '8 per 100,000 people',
    urgency: 'routine',
    symptoms: ['Gradual headaches', 'Seizures', 'Weakness', 'Personality changes'],
    path: '/patient-resources/conditions/cerebral-meningioma',
    frequency: 5
  },
  {
    id: 'hydrocephalus',
    name: 'Hydrocephalus',
    description: 'Accumulation of cerebrospinal fluid in the brain, causing increased pressure and potential brain damage.',
    prevalence: '1-2 per 1,000 births',
    urgency: 'urgent',
    symptoms: ['Enlarged head (infants)', 'Headaches', 'Nausea', 'Balance problems'],
    path: '/patient-resources/conditions/hydrocephalus',
    frequency: 6
  },
  {
    id: 'cerebral-avm',
    name: 'Cerebral AVM',
    description: 'Abnormal connection between arteries and veins in the brain, bypassing normal brain tissue.',
    prevalence: '1 per 100,000 people',
    urgency: 'urgent',
    symptoms: ['Seizures', 'Headaches', 'Neurological deficits', 'Brain hemorrhage'],
    path: '/patient-resources/conditions/cerebral-avm',
    frequency: 7
  },
  {
    id: 'cerebral-cavernoma',
    name: 'Cerebral Cavernoma',
    description: 'Cluster of abnormal blood vessels in the brain that can cause seizures and bleeding.',
    prevalence: '0.5% of population',
    urgency: 'routine',
    symptoms: ['Seizures', 'Headaches', 'Neurological symptoms', 'Brain hemorrhage'],
    path: '/patient-resources/conditions/cerebral-cavernoma',
    frequency: 8
  },
  {
    id: 'chiari-malformation',
    name: 'Chiari Malformation',
    description: 'Structural defect where brain tissue extends into the spinal canal, affecting cerebrospinal fluid flow.',
    prevalence: '1 per 1,000 people',
    urgency: 'routine',
    symptoms: ['Neck pain', 'Headaches', 'Balance problems', 'Numbness'],
    path: '/patient-resources/conditions/chiari-malformation',
    frequency: 9
  }
];

const BrainConditions: React.FC = () => {
  const deviceInfo = useDeviceDetection();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUrgency, setSelectedUrgency] = useState<string>('all');

  // Filter conditions based on search and urgency
  const filteredConditions = brainConditions
    .filter(condition => 
      condition.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      condition.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      condition.symptoms.some(symptom => symptom.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .filter(condition => selectedUrgency === 'all' || condition.urgency === selectedUrgency)
    .sort((a, b) => a.frequency - b.frequency);

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'emergency': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'urgent': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'routine': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'emergency': return AlertTriangle;
      case 'urgent': return TrendingUp;
      case 'routine': return Users;
      default: return Brain;
    }
  };

  return (
    <StandardPageLayout showHeader={false}>
      <Helmet>
        <title>Brain Conditions | Comprehensive Guide | miNEURO</title>
        <meta 
          name="description" 
          content="Comprehensive guide to brain conditions including brain tumours, aneurysms, trigeminal neuralgia, and more. Expert neurosurgical care and treatment options." 
        />
        <meta name="keywords" content="brain conditions, brain tumour, cerebral aneurysm, trigeminal neuralgia, neurosurgery, brain surgery" />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/brain-conditions" />
      </Helmet>

      <PageHeader
        title="Brain Conditions"
        subtitle="Comprehensive information about neurological conditions affecting the brain, from common disorders to complex surgical cases"
        backgroundImage="/images/brain-conditions/brain-anatomy-hero.jpg"
        enableParallax={true}
      />

      <main className="flex-1">
        {/* Overview Section */}
        <section className="py-16 bg-gradient-to-br from-muted/10 to-background">
          <div className="container">
            <SectionHeader
              title="Understanding Brain Conditions"
              subtitle="Expert neurosurgical care for complex brain disorders"
              description="Our comprehensive approach to brain conditions combines advanced diagnostic techniques with cutting-edge surgical interventions. Each condition requires specialized expertise and personalized treatment planning."
              centered={true}
            />

            {/* Quick Stats */}
            <div className={cn(
              "grid gap-6 mt-12",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
            )}>
              <Card className="medical-card text-center">
                <CardContent className="pt-6">
                  <Brain className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-enhanced-heading mb-2">9</h3>
                  <p className="text-muted-foreground">Brain Conditions Covered</p>
                </CardContent>
              </Card>
              <Card className="medical-card text-center">
                <CardContent className="pt-6">
                  <Users className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-enhanced-heading mb-2">1000+</h3>
                  <p className="text-muted-foreground">Patients Treated Annually</p>
                </CardContent>
              </Card>
              <Card className="medical-card text-center">
                <CardContent className="pt-6">
                  <TrendingUp className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-enhanced-heading mb-2">95%</h3>
                  <p className="text-muted-foreground">Treatment Success Rate</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Search and Filter Section */}
        <section className="py-12 bg-muted/30">
          <div className="container">
            <div className={cn(
              "flex gap-4 mb-8",
              deviceInfo.isMobile ? "flex-col" : "flex-row items-center"
            )}>
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search conditions or symptoms..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                {['all', 'emergency', 'urgent', 'routine'].map((urgency) => (
                  <Button
                    key={urgency}
                    variant={selectedUrgency === urgency ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedUrgency(urgency)}
                    className="capitalize"
                  >
                    {urgency}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Conditions Grid */}
        <section className="py-16">
          <div className="container">
            <GridLayout
              items={filteredConditions}
              renderItem={(condition) => {
                const UrgencyIcon = getUrgencyIcon(condition.urgency);
                return (
                  <Card key={condition.id} className="medical-card h-full hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Brain className="h-5 w-5 text-primary" />
                          <Badge className={getUrgencyColor(condition.urgency)}>
                            {condition.urgency}
                          </Badge>
                        </div>
                        <UrgencyIcon className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <CardTitle className="text-xl">{condition.name}</CardTitle>
                      <CardDescription className="text-sm text-muted-foreground">
                        Prevalence: {condition.prevalence}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-1">
                      <p className="text-muted-foreground mb-4">{condition.description}</p>
                      <div className="mb-4">
                        <h4 className="font-semibold mb-2 text-sm">Common Symptoms:</h4>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {condition.symptoms.slice(0, 3).map((symptom, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <div className="w-1 h-1 rounded-full bg-primary" />
                              {symptom}
                            </li>
                          ))}
                          {condition.symptoms.length > 3 && (
                            <li className="text-xs text-muted-foreground">
                              +{condition.symptoms.length - 3} more symptoms
                            </li>
                          )}
                        </ul>
                      </div>
                    </CardContent>
                    <div className="p-6 pt-0">
                      <Button asChild className="w-full">
                        <Link to={condition.path}>
                          Learn More
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </Card>
                );
              }}
              columns={deviceInfo.isMobile ? 1 : 3}
              gap="lg"
            />

            {filteredConditions.length === 0 && (
              <div className="text-center py-12">
                <Brain className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">No conditions found</h3>
                <p className="text-muted-foreground mb-4">
                  Try adjusting your search terms or filters
                </p>
                <Button onClick={() => { setSearchTerm(''); setSelectedUrgency('all'); }}>
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        </section>

        {/* Emergency Information */}
        <section className="py-16 bg-red-50 dark:bg-red-950/20">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center">
              <AlertTriangle className="h-16 w-16 text-red-600 mx-auto mb-6" />
              <h2 className="text-3xl font-bold text-enhanced-heading mb-4">
                Emergency Brain Symptoms
              </h2>
              <p className="text-lg text-muted-foreground mb-8">
                Seek immediate medical attention if you experience any of these symptoms:
              </p>
              <div className={cn(
                "grid gap-4 text-left",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                {[
                  'Sudden, severe headache unlike any before',
                  'Sudden loss of consciousness or confusion',
                  'Sudden weakness or numbness in face, arm, or leg',
                  'Sudden difficulty speaking or understanding speech',
                  'Sudden severe dizziness or loss of balance',
                  'Sudden vision loss or double vision'
                ].map((symptom, index) => (
                  <div key={index} className="flex items-start gap-3 p-4 bg-white dark:bg-gray-900 rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{symptom}</span>
                  </div>
                ))}
              </div>
              <div className="mt-8">
                <Button size="lg" className="bg-red-600 hover:bg-red-700">
                  Call Emergency Services: 000
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary text-primary-foreground">
          <div className="container text-center">
            <h2 className="text-3xl font-bold mb-4">
              Expert Brain Condition Care
            </h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Our specialized neurosurgical team provides comprehensive diagnosis and treatment for all brain conditions. 
              Schedule a consultation to discuss your specific needs.
            </p>
            <div className={cn(
              "flex gap-4 justify-center",
              deviceInfo.isMobile ? "flex-col items-center" : "flex-row"
            )}>
              <Button asChild size="lg" variant="secondary">
                <Link to="/appointments">Book Consultation</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
                <Link to="/contact">Contact Us</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
    </StandardPageLayout>
  );
};

export default BrainConditions;
