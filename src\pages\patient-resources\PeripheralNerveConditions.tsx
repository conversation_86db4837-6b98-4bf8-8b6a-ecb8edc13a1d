import { Zap, Users, TrendingUp, AlertTriangle, Search, ArrowRight, Activity } from 'lucide-react';
import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { Link } from 'react-router-dom';

import PageHeader from '@/components/PageHeader';
import { SectionHeader, GridLayout } from '@/components/shared/CommonSectionPatterns';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface PeripheralNerveCondition {
  id: string;
  name: string;
  description: string;
  prevalence: string;
  severity: 'mild' | 'moderate' | 'severe';
  symptoms: string[];
  path: string;
  frequency: number; // 1-7, 1 being most common
  location: 'upper-extremity' | 'lower-extremity' | 'head-neck' | 'general';
}

const peripheralNerveConditions: PeripheralNerveCondition[] = [
  {
    id: 'carpal-tunnel-syndrome',
    name: 'Carpal Tunnel Syndrome',
    description: 'Compression of the median nerve at the wrist, causing numbness, tingling, and weakness in the hand.',
    prevalence: '3-6% of population',
    severity: 'moderate',
    symptoms: ['Hand numbness', 'Tingling fingers', 'Wrist pain', 'Grip weakness'],
    path: '/patient-resources/conditions/carpal-tunnel-syndrome',
    frequency: 1,
    location: 'upper-extremity'
  },
  {
    id: 'ulnar-neuropathy',
    name: 'Ulnar Neuropathy',
    description: 'Compression or damage to the ulnar nerve, commonly at the elbow, causing numbness and weakness.',
    prevalence: '2% of population',
    severity: 'moderate',
    symptoms: ['Pinky numbness', 'Ring finger tingling', 'Hand weakness', 'Elbow pain'],
    path: '/patient-resources/conditions/ulnar-neuropathy',
    frequency: 2,
    location: 'upper-extremity'
  },
  {
    id: 'tarsal-tunnel-syndrome',
    name: 'Tarsal Tunnel Syndrome',
    description: 'Compression of the posterior tibial nerve at the ankle, causing foot pain and numbness.',
    prevalence: '1% of population',
    severity: 'moderate',
    symptoms: ['Foot numbness', 'Heel pain', 'Arch pain', 'Toe tingling'],
    path: '/patient-resources/conditions/tarsal-tunnel-syndrome',
    frequency: 3,
    location: 'lower-extremity'
  },
  {
    id: 'meralgia-paresthetica',
    name: 'Meralgia Paresthetica',
    description: 'Compression of the lateral femoral cutaneous nerve, causing numbness and burning in the outer thigh.',
    prevalence: '0.4% of population',
    severity: 'mild',
    symptoms: ['Thigh numbness', 'Burning sensation', 'Tingling', 'Sensitivity to touch'],
    path: '/patient-resources/conditions/meralgia-paresthetica',
    frequency: 4,
    location: 'lower-extremity'
  },
  {
    id: 'peroneal-nerve-palsy',
    name: 'Peroneal Nerve Palsy',
    description: 'Damage to the peroneal nerve causing foot drop and difficulty lifting the foot.',
    prevalence: '1.5 per 100,000',
    severity: 'moderate',
    symptoms: ['Foot drop', 'Ankle weakness', 'Toe numbness', 'Walking difficulty'],
    path: '/patient-resources/conditions/peroneal-nerve-palsy',
    frequency: 5,
    location: 'lower-extremity'
  },
  {
    id: 'occipital-neuralgia',
    name: 'Occipital Neuralgia',
    description: 'Inflammation or injury to the occipital nerves, causing severe headaches and neck pain.',
    prevalence: '3.2 per 100,000',
    severity: 'severe',
    symptoms: ['Sharp head pain', 'Neck pain', 'Scalp tenderness', 'Light sensitivity'],
    path: '/patient-resources/conditions/occipital-neuralgia',
    frequency: 6,
    location: 'head-neck'
  },
  {
    id: 'peripheral-nerve-tumors',
    name: 'Peripheral Nerve Tumors',
    description: 'Benign or malignant growths affecting peripheral nerves, requiring specialized surgical management.',
    prevalence: '0.001% of population',
    severity: 'severe',
    symptoms: ['Growing mass', 'Nerve pain', 'Weakness', 'Sensory changes'],
    path: '/patient-resources/conditions/peripheral-nerve-tumors',
    frequency: 7,
    location: 'general'
  }
];

const PeripheralNerveConditions: React.FC = () => {
  const deviceInfo = useDeviceDetection();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all');
  const [selectedLocation, setSelectedLocation] = useState<string>('all');

  // Filter conditions based on search, severity, and location
  const filteredConditions = peripheralNerveConditions
    .filter(condition => 
      condition.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      condition.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      condition.symptoms.some(symptom => symptom.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .filter(condition => selectedSeverity === 'all' || condition.severity === selectedSeverity)
    .filter(condition => selectedLocation === 'all' || condition.location === selectedLocation)
    .sort((a, b) => a.frequency - b.frequency);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'severe': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'moderate': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'mild': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getLocationColor = (location: string) => {
    switch (location) {
      case 'upper-extremity': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'lower-extremity': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'head-neck': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'general': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getLocationDisplayName = (location: string) => {
    switch (location) {
      case 'upper-extremity': return 'Upper Extremity';
      case 'lower-extremity': return 'Lower Extremity';
      case 'head-neck': return 'Head & Neck';
      case 'general': return 'General';
      default: return location;
    }
  };

  return (
    <StandardPageLayout showHeader={false}>
      <Helmet>
        <title>Peripheral Nerve Conditions | Comprehensive Guide | miNEURO</title>
        <meta 
          name="description" 
          content="Comprehensive guide to peripheral nerve conditions including carpal tunnel syndrome, ulnar neuropathy, and nerve entrapments. Expert nerve surgery and treatment options." 
        />
        <meta name="keywords" content="peripheral nerve conditions, carpal tunnel syndrome, ulnar neuropathy, nerve entrapment, nerve surgery" />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/peripheral-nerve-conditions" />
      </Helmet>

      <PageHeader
        title="Peripheral Nerve Conditions"
        subtitle="Comprehensive information about peripheral nerve disorders, from common entrapments to complex nerve injuries requiring specialized surgical care"
        backgroundImage="/images/nerve-conditions/peripheral-nerve-anatomy-hero.jpg"
        enableParallax={true}
      />

      <main className="flex-1">
        {/* Overview Section */}
        <section className="py-16 bg-gradient-to-br from-muted/10 to-background">
          <div className="container">
            <SectionHeader
              title="Understanding Peripheral Nerve Conditions"
              subtitle="Expert nerve surgery and microsurgical techniques"
              description="Our specialized approach to peripheral nerve conditions utilizes advanced microsurgical techniques and nerve reconstruction procedures. Each condition requires precise diagnosis and tailored treatment planning."
              centered={true}
            />

            {/* Quick Stats */}
            <div className={cn(
              "grid gap-6 mt-12",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
            )}>
              <Card className="medical-card text-center">
                <CardContent className="pt-6">
                  <Zap className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-enhanced-heading mb-2">7</h3>
                  <p className="text-muted-foreground">Nerve Conditions Covered</p>
                </CardContent>
              </Card>
              <Card className="medical-card text-center">
                <CardContent className="pt-6">
                  <Users className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-enhanced-heading mb-2">500+</h3>
                  <p className="text-muted-foreground">Nerve Surgeries Annually</p>
                </CardContent>
              </Card>
              <Card className="medical-card text-center">
                <CardContent className="pt-6">
                  <Activity className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-enhanced-heading mb-2">95%</h3>
                  <p className="text-muted-foreground">Microsurgical Success Rate</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Search and Filter Section */}
        <section className="py-12 bg-muted/30">
          <div className="container">
            <div className={cn(
              "flex gap-4 mb-8",
              deviceInfo.isMobile ? "flex-col" : "flex-row items-center"
            )}>
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search conditions or symptoms..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2 flex-wrap">
                <div className="flex gap-1">
                  {['all', 'mild', 'moderate', 'severe'].map((severity) => (
                    <Button
                      key={severity}
                      variant={selectedSeverity === severity ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedSeverity(severity)}
                      className="capitalize"
                    >
                      {severity}
                    </Button>
                  ))}
                </div>
                <div className="flex gap-1">
                  {['all', 'upper-extremity', 'lower-extremity', 'head-neck', 'general'].map((location) => (
                    <Button
                      key={location}
                      variant={selectedLocation === location ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedLocation(location)}
                      className="text-xs"
                    >
                      {getLocationDisplayName(location)}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Conditions Grid */}
        <section className="py-16">
          <div className="container">
            <GridLayout
              items={filteredConditions}
              renderItem={(condition) => (
                <Card key={condition.id} className="medical-card h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2 flex-wrap">
                        <Zap className="h-5 w-5 text-primary" />
                        <Badge className={getSeverityColor(condition.severity)}>
                          {condition.severity}
                        </Badge>
                        <Badge className={getLocationColor(condition.location)}>
                          {getLocationDisplayName(condition.location)}
                        </Badge>
                      </div>
                    </div>
                    <CardTitle className="text-xl">{condition.name}</CardTitle>
                    <CardDescription className="text-sm text-muted-foreground">
                      Prevalence: {condition.prevalence}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="flex-1">
                    <p className="text-muted-foreground mb-4">{condition.description}</p>
                    <div className="mb-4">
                      <h4 className="font-semibold mb-2 text-sm">Common Symptoms:</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        {condition.symptoms.map((symptom, index) => (
                          <li key={index} className="flex items-center gap-2">
                            <div className="w-1 h-1 rounded-full bg-primary" />
                            {symptom}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                  <div className="p-6 pt-0">
                    <Button asChild className="w-full">
                      <Link to={condition.path}>
                        Learn More
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </Card>
              )}
              columns={deviceInfo.isMobile ? 1 : 3}
              gap="lg"
            />

            {filteredConditions.length === 0 && (
              <div className="text-center py-12">
                <Zap className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">No conditions found</h3>
                <p className="text-muted-foreground mb-4">
                  Try adjusting your search terms or filters
                </p>
                <Button onClick={() => { setSearchTerm(''); setSelectedSeverity('all'); setSelectedLocation('all'); }}>
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        </section>

        {/* Nerve Anatomy Education */}
        <section className="py-16 bg-muted/30">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl font-bold text-enhanced-heading mb-6">
                Understanding Your Peripheral Nerves
              </h2>
              <p className="text-lg text-muted-foreground mb-8">
                Peripheral nerves carry signals between your brain, spinal cord, and the rest of your body. 
                When these nerves are compressed, damaged, or diseased, they can cause significant symptoms.
              </p>
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <Card className="medical-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="h-5 w-5 text-primary" />
                      Motor Nerves
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      Control muscle movement and strength. Damage can cause weakness, muscle wasting, and loss of function.
                    </p>
                  </CardContent>
                </Card>
                <Card className="medical-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5 text-primary" />
                      Sensory Nerves
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      Carry sensation information like touch, temperature, and pain. Damage can cause numbness, tingling, or burning pain.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Warning Signs */}
        <section className="py-16 bg-orange-50 dark:bg-orange-950/20">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center">
              <AlertTriangle className="h-16 w-16 text-orange-600 mx-auto mb-6" />
              <h2 className="text-3xl font-bold text-enhanced-heading mb-4">
                When to Seek Treatment
              </h2>
              <p className="text-lg text-muted-foreground mb-8">
                Early treatment of nerve conditions often leads to better outcomes. Seek medical attention if you experience:
              </p>
              <div className={cn(
                "grid gap-4 text-left",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                {[
                  'Progressive weakness in hands or feet',
                  'Persistent numbness or tingling',
                  'Severe nerve pain that interferes with daily activities',
                  'Loss of fine motor control (difficulty with buttons, writing)',
                  'Muscle wasting or visible changes in muscle size',
                  'Symptoms that worsen over time despite conservative treatment'
                ].map((symptom, index) => (
                  <div key={index} className="flex items-start gap-3 p-4 bg-white dark:bg-gray-900 rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{symptom}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary text-primary-foreground">
          <div className="container text-center">
            <h2 className="text-3xl font-bold mb-4">
              Expert Peripheral Nerve Care
            </h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Our specialized microsurgical team provides comprehensive diagnosis and treatment for all peripheral nerve conditions. 
              Schedule a consultation to discuss your specific needs.
            </p>
            <div className={cn(
              "flex gap-4 justify-center",
              deviceInfo.isMobile ? "flex-col items-center" : "flex-row"
            )}>
              <Button asChild size="lg" variant="secondary">
                <Link to="/appointments">Book Consultation</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
                <Link to="/patient-resources/peripheral-nerve-conditions-library">View Detailed Library</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
                <Link to="/contact">Contact Us</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
    </StandardPageLayout>
  );
};

export default PeripheralNerveConditions;
