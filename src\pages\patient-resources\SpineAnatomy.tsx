import { Brain, Bone, Activity, Eye, Target, Layers, Network, Disc, Zap } from 'lucide-react';
import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import PageHeader from '@/components/PageHeader';
import SafeImage from '@/components/SafeImage';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLanguage } from '@/contexts/LanguageContext';
import en from '@/locales/en';

const SpineAnatomy: React.FC = () => {
  const { t } = useLanguage();

  // Safe fallback for translations
  const safeT = t || en;
  const _finalT = safeT || {
    nav: { home: "Home", expertise: "Expertise", appointments: "Appointments", contact: "Contact" },
    hero: { title: "Welcome", subtitle: "Professional Care", description: "Expert medical services" },
    footer: { description: "Professional medical practice", quickLinks: "Quick Links", contact: "Contact" }
  };

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Spine regions data
  const spineRegions = [
    {
      name: "Cervical Spine",
      vertebrae: "C1-C7",
      count: 7,
      description: "The neck region, providing mobility and supporting the head",
      characteristics: [
        "Most mobile region of the spine",
        "Smallest vertebrae with unique anatomy",
        "Atlas (C1) and Axis (C2) allow head rotation",
        "Vertebral arteries pass through transverse foramina"
      ],
      commonConditions: ["Cervical radiculopathy", "Cervical stenosis", "Whiplash injuries"],
      icon: Brain,
      colour: "text-info"
    },
    {
      name: "Thoracic Spine",
      vertebrae: "T1-T12",
      count: 12,
      description: "The mid-back region, connecting to ribs for chest stability",
      characteristics: [
        "Least mobile due to rib cage attachment",
        "Larger vertebrae than cervical spine",
        "Costal facets for rib articulation",
        "Natural kyphotic (backward) curve"
      ],
      commonConditions: ["Thoracic compression fractures", "Kyphosis", "Intercostal neuralgia"],
      icon: Layers,
      colour: "text-success"
    },
    {
      name: "Lumbar Spine",
      vertebrae: "L1-L5",
      count: 5,
      description: "The lower back region, bearing most body weight",
      characteristics: [
        "Largest and strongest vertebrae",
        "Primary weight-bearing region",
        "Natural lordotic (forward) curve",
        "Most common site of disc problems"
      ],
      commonConditions: ["Lumbar disc herniation", "Lumbar stenosis", "Spondylolisthesis"],
      icon: Target,
      colour: "text-info"
    },
    {
      name: "Sacrum & Coccyx",
      vertebrae: "S1-S5, Co1-Co4",
      count: 9,
      description: "Fused vertebrae forming the back of the pelvis",
      characteristics: [
        "Sacrum: 5 fused vertebrae",
        "Coccyx: 3-5 fused vertebrae (tailbone)",
        "Forms back wall of pelvis",
        "Sacroiliac joint connections"
      ],
      commonConditions: ["Sacroiliac joint dysfunction", "Coccydynia", "Sacral fractures"],
      icon: Bone,
      colour: "text-medical-blue"
    }
  ];

  // Anatomical components
  const anatomicalComponents = [
    {
      name: "Vertebrae",
      description: "The bony building blocks of the spine",
      details: [
        "Vertebral body: weight-bearing portion",
        "Vertebral arch: protects spinal cord",
        "Spinous process: muscle attachment point",
        "Transverse processes: muscle and ligament attachments",
        "Facet joints: allow controlled movement"
      ],
      icon: Bone
    },
    {
      name: "Intervertebral Discs",
      description: "Shock absorbers between vertebrae",
      details: [
        "Nucleus pulposus: gel-like centre (80% water)",
        "Annulus fibrosus: tough outer ring",
        "Cartilaginous endplates: nutrient exchange",
        "Avascular structure: heals slowly",
        "Degenerates with age and stress"
      ],
      icon: Disc
    },
    {
      name: "Spinal Cord & Nerves",
      description: "The nervous system within the spine",
      details: [
        "Spinal cord: extends from brain to L1-L2",
        "Cauda equina: nerve roots below spinal cord",
        "31 pairs of spinal nerves",
        "Nerve roots exit through foramina",
        "Controls movement and sensation"
      ],
      icon: Zap
    },
    {
      name: "Ligaments & Muscles",
      description: "Support structures providing stability",
      details: [
        "Anterior/posterior longitudinal ligaments",
        "Ligamentum flavum: elastic yellow ligament",
        "Interspinous and supraspinous ligaments",
        "Deep and superficial muscle layers",
        "Core muscles: diaphragm, pelvic floor, multifidus"
      ],
      icon: Network
    }
  ];

  return (
    <StandardPageLayout showHeader={false}>
      <PageHeader
        title="Spine Anatomy: Complete Guide"
        subtitle="Understanding Your Spine's Structure and Function"
        backgroundImage="/images/spine-anatomy/spine-overview.jpg"
      />

      {/* Overview Section */}
      <section className="py-16 bg-background">
        <div className="container max-w-7xl">
          <div className="max-w-4xl mx-auto">
            <div className="text-centre mb-12">
              <p className="text-lg text-muted-foreground leading-relaxed mb-6">
                Your spine is a remarkable engineering marvel—33 vertebrae working together to provide structure, protection, and mobility. Understanding your spine's anatomy is the first step toward maintaining its health and addressing any problems that may arise.
              </p>

              <p className="text-base text-muted-foreground leading-relaxed">
                The spine serves three critical functions: protecting the spinal cord and nerve roots, providing structural support for the body, and enabling flexible movement in multiple directions. Each component has evolved to work in harmony, creating a system that can bear tremendous loads while maintaining remarkable flexibility.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Spine Regions Section */}
      <section className="py-16 bg-muted/30">
        <div className="container max-w-7xl">
          <div className="text-centre mb-12">
            <h2 className="text-enhanced-heading text-3xl font-bold mb-4">Spine Regions</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              The spine is divided into four distinct regions, each with unique characteristics and functions.
            </p>
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-12 items-start">
            <div className="space-y-6">
              {spineRegions.map((region, index) => {
                const IconComponent = region.icon;
                return (
                  <Card key={index} className="medical-card/50 backdrop-blur-sm border-border/50">
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-centre gap-3 text-lg">
                        <IconComponent className={`h-6 w-6 ${region.colour}`} />
                        {region.name}
                        <Badge variant="secondary" className="ml-auto">{region.vertebrae}</Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-muted-foreground mb-4 text-sm leading-relaxed">{region.description}</p>

                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold text-sm mb-2 text-foreground">Key Characteristics:</h4>
                          <ul className="text-xs space-y-1 text-muted-foreground">
                            {region.characteristics.map((char, idx) => (
                              <li key={idx} className="flex items-start">
                                <span className="text-primary mr-2 mt-0.5">•</span>
                                <span>{char}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-semibold text-sm mb-2 text-foreground">Common Conditions:</h4>
                          <div className="flex flex-wrap gap-1">
                            {region.commonConditions.map((condition, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs">
                                {condition}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            <div className="flex items-centre justify-centre lg:sticky lg:top-8">
              <div className="relative max-w-lg w-full">
                <SafeImage
                  src="/images/spine-anatomy/spine-regions.jpg"
                  alt="Spine regions anatomy"
                  className="w-full h-auto rounded-lg shadow-lg"
                  fallbackSrc="/images/spine-anatomy/vertebrae.jpg"
                />
                <div className="absolute bottom-4 left-4 right-4 bg-foreground/70 text-primary-foreground p-3 rounded text-sm backdrop-blur-sm">
                  The spine's four main regions each have unique characteristics and functions
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Anatomical Components Section */}
      <section className="py-16 bg-background">
        <div className="container max-w-7xl">
          <div className="text-centre mb-12">
            <h2 className="text-enhanced-heading text-3xl font-bold mb-4">Key Anatomical Components</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Understanding the key structures that make up your spine and how they work together.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {anatomicalComponents.map((component, index) => {
              const IconComponent = component.icon;
              return (
                <Card key={index} className="h-full medical-card/50 backdrop-blur-sm border-border/50">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-centre gap-3 text-lg">
                      <div className="p-2 rounded-lg bg-primary/10">
                        <IconComponent className="h-5 w-5 text-primary" />
                      </div>
                      {component.name}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-muted-foreground mb-4 text-sm leading-relaxed">{component.description}</p>
                    <ul className="text-sm space-y-2">
                      {component.details.map((detail, idx) => (
                        <li key={idx} className="flex items-start">
                          <span className="text-primary mr-2 mt-0.5 font-bold">•</span>
                          <span className="text-muted-foreground">{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Interactive Learning Section */}
      <section className="py-16 bg-muted/30">
        <div className="container max-w-7xl">
          <div className="text-centre mb-12">
            <h2 className="text-enhanced-heading text-3xl font-bold mb-4">Learn More About Your Spine</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Explore how your spine moves, common problems that can occur, and how to maintain optimal spine health.
            </p>
          </div>

          <Tabs defaultValue="movement" className="w-full max-w-5xl mx-auto">
            <TabsList className="grid grid-cols-3 mb-8 bg-background/50 backdrop-blur-sm">
              <TabsTrigger value="movement" className="text-sm">Movement & Function</TabsTrigger>
              <TabsTrigger value="problems" className="text-sm">Common Problems</TabsTrigger>
              <TabsTrigger value="health" className="text-sm">Maintaining Health</TabsTrigger>
            </TabsList>
            
            <TabsContent value="movement" className="space-y-6">
              <Card className="medical-card/50 backdrop-blur-sm border-border/50">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold mb-6 text-foreground">How Your Spine Moves</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="space-y-4">
                      <h4 className="font-semibold mb-3 text-foreground">Types of Movement:</h4>
                      <ul className="space-y-3 text-sm">
                        <li className="flex items-start">
                          <span className="text-primary mr-2 mt-0.5">•</span>
                          <span><strong className="text-foreground">Flexion:</strong> <span className="text-muted-foreground">Bending forward</span></span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-primary mr-2 mt-0.5">•</span>
                          <span><strong className="text-foreground">Extension:</strong> <span className="text-muted-foreground">Bending backward</span></span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-primary mr-2 mt-0.5">•</span>
                          <span><strong className="text-foreground">Lateral flexion:</strong> <span className="text-muted-foreground">Side bending</span></span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-primary mr-2 mt-0.5">•</span>
                          <span><strong className="text-foreground">Rotation:</strong> <span className="text-muted-foreground">Twisting movements</span></span>
                        </li>
                      </ul>
                    </div>
                    <div className="space-y-4">
                      <h4 className="font-semibold mb-3 text-foreground">Regional Mobility:</h4>
                      <ul className="space-y-3 text-sm">
                        <li className="flex items-start">
                          <span className="text-primary mr-2 mt-0.5">•</span>
                          <span><strong className="text-foreground">Cervical:</strong> <span className="text-muted-foreground">Most mobile (rotation, flexion)</span></span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-primary mr-2 mt-0.5">•</span>
                          <span><strong className="text-foreground">Thoracic:</strong> <span className="text-muted-foreground">Limited by ribs (rotation primary)</span></span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-primary mr-2 mt-0.5">•</span>
                          <span><strong className="text-foreground">Lumbar:</strong> <span className="text-muted-foreground">Flexion/extension dominant</span></span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-primary mr-2 mt-0.5">•</span>
                          <span><strong className="text-foreground">Sacral:</strong> <span className="text-muted-foreground">Minimal movement (stability)</span></span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="problems" className="space-y-6">
              <Card className="medical-card/50 backdrop-blur-sm border-border/50">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold mb-6 text-foreground">When Things Go Wrong</h3>
                  <div className="space-y-6">
                    <div className="p-4 bg-muted dark:bg-muted/30 rounded-lg border border-border dark:border-border">
                      <h4 className="font-semibold text-foreground dark:text-foreground mb-2 flex items-centre gap-2">
                        <span className="w-2 h-2 bg-muted rounded-full"></span>
                        Disc Problems
                      </h4>
                      <p className="text-sm text-foreground dark:text-foreground leading-relaxed">
                        Herniation, degeneration, or bulging can compress nerves and cause pain, numbness, or weakness.
                      </p>
                    </div>
                    <div className="p-4 bg-info dark:bg-info/30 rounded-lg border border-info dark:border-info/50">
                      <h4 className="font-semibold text-info dark:text-info mb-2 flex items-centre gap-2">
                        <span className="w-2 h-2 bg-info rounded-full"></span>
                        Spinal Stenosis
                      </h4>
                      <p className="text-sm text-info dark:text-info leading-relaxed">
                        Narrowing of the spinal canal can compress the spinal cord or nerve roots.
                      </p>
                    </div>
                    <div className="p-4 bg-info dark:bg-info/30 rounded-lg border border-info dark:border-info/50">
                      <h4 className="font-semibold text-info dark:text-info mb-2 flex items-centre gap-2">
                        <span className="w-2 h-2 bg-info rounded-full"></span>
                        Facet Joint Arthritis
                      </h4>
                      <p className="text-sm text-info dark:text-info leading-relaxed">
                        Wear and tear of the small joints can cause stiffness and localized pain.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="health" className="space-y-6">
              <Card className="medical-card/50 backdrop-blur-sm border-border/50">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold mb-6 text-foreground">Keeping Your Spine Healthy</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="space-y-4">
                      <h4 className="font-semibold mb-3 text-foreground">Daily Habits:</h4>
                      <ul className="space-y-3 text-sm">
                        <li className="flex items-start">
                          <span className="text-success mr-2 mt-0.5">✓</span>
                          <span className="text-muted-foreground">Maintain good posture</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-success mr-2 mt-0.5">✓</span>
                          <span className="text-muted-foreground">Stay physically active</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-success mr-2 mt-0.5">✓</span>
                          <span className="text-muted-foreground">Strengthen core muscles</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-success mr-2 mt-0.5">✓</span>
                          <span className="text-muted-foreground">Practice proper lifting techniques</span>
                        </li>
                      </ul>
                    </div>
                    <div className="space-y-4">
                      <h4 className="font-semibold mb-3 text-foreground">Lifestyle Factors:</h4>
                      <ul className="space-y-3 text-sm">
                        <li className="flex items-start">
                          <span className="text-success mr-2 mt-0.5">✓</span>
                          <span className="text-muted-foreground">Maintain healthy weight</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-success mr-2 mt-0.5">✓</span>
                          <span className="text-muted-foreground">Don't smoke (affects disc nutrition)</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-success mr-2 mt-0.5">✓</span>
                          <span className="text-muted-foreground">Get adequate sleep</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-success mr-2 mt-0.5">✓</span>
                          <span className="text-muted-foreground">Manage stress effectively</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Related Resources */}
      <section className="py-16 bg-background">
        <div className="container max-w-7xl">
          <div className="text-centre mb-12">
            <h2 className="text-enhanced-heading text-3xl font-bold mb-4">Related Resources</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Continue your spine health journey with these comprehensive resources and expert guidance.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <Card className="medical-card/50 backdrop-blur-sm border-border/50 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105">
              <CardContent className="p-6">
                <div className="flex items-centre mb-4">
                  <div className="p-2 rounded-lg bg-primary/10 mr-3">
                    <Brain className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-lg font-semibold text-foreground">Spine Conditions</h3>
                </div>
                <p className="text-muted-foreground mb-6 text-sm leading-relaxed">
                  Learn about specific spine conditions, their causes, symptoms, and treatment options.
                </p>
                <Button asChild className="w-full">
                  <Link to="/patient-resources/spine-conditions-library">Explore Conditions</Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="medical-card/50 backdrop-blur-sm border-border/50 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105">
              <CardContent className="p-6">
                <div className="flex items-centre mb-4">
                  <div className="p-2 rounded-lg bg-primary/10 mr-3">
                    <Activity className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-lg font-semibold text-foreground">Exercise Library</h3>
                </div>
                <p className="text-muted-foreground mb-6 text-sm leading-relaxed">
                  Access spine-specific exercises designed to strengthen and protect your spine.
                </p>
                <Button asChild className="w-full">
                  <Link to="/patient-resources/exercise-library">View Exercises</Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="medical-card/50 backdrop-blur-sm border-border/50 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105">
              <CardContent className="p-6">
                <div className="flex items-centre mb-4">
                  <div className="p-2 rounded-lg bg-primary/10 mr-3">
                    <Eye className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-lg font-semibold text-foreground">Spine Health Guide</h3>
                </div>
                <p className="text-muted-foreground mb-6 text-sm leading-relaxed">
                  Comprehensive guide to maintaining spine and brain health throughout life.
                </p>
                <Button asChild className="w-full">
                  <Link to="/patient-resources/spine-and-brain-health">Learn More</Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </StandardPageLayout>
  );
};

SpineAnatomy.displayName = 'SpineAnatomy';

export default SpineAnatomy;
