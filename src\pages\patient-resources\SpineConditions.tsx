import { Layers, Users, TrendingUp, Al<PERSON>Triangle, Search, ArrowRight, Activity } from 'lucide-react';
import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { Link } from 'react-router-dom';

import PageHeader from '@/components/PageHeader';
import { SectionHeader, GridLayout } from '@/components/shared/CommonSectionPatterns';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface SpineCondition {
  id: string;
  name: string;
  description: string;
  prevalence: string;
  severity: 'mild' | 'moderate' | 'severe';
  symptoms: string[];
  path: string;
  frequency: number; // 1-15, 1 being most common
  region: 'cervical' | 'thoracic' | 'lumbar' | 'general';
}

const spineConditions: SpineCondition[] = [
  {
    id: 'herniated-disc',
    name: 'Herniated Disc',
    description: 'Displacement of disc material that can compress nearby nerves, causing pain and neurological symptoms.',
    prevalence: '2-3% of population',
    severity: 'moderate',
    symptoms: ['Back pain', 'Leg pain', 'Numbness', 'Weakness'],
    path: '/patient-resources/conditions/herniated-disc',
    frequency: 1,
    region: 'lumbar'
  },
  {
    id: 'sciatica',
    name: 'Sciatica',
    description: 'Pain radiating along the sciatic nerve from the lower back down through the leg.',
    prevalence: '10-40% of population',
    severity: 'moderate',
    symptoms: ['Leg pain', 'Burning sensation', 'Numbness', 'Tingling'],
    path: '/patient-resources/conditions/sciatica',
    frequency: 2,
    region: 'lumbar'
  },
  {
    id: 'spinal-stenosis',
    name: 'Spinal Stenosis',
    description: 'Narrowing of the spinal canal that can compress the spinal cord or nerve roots.',
    prevalence: '8-11% over age 50',
    severity: 'moderate',
    symptoms: ['Walking difficulty', 'Leg pain', 'Numbness', 'Balance problems'],
    path: '/patient-resources/conditions/spinal-stenosis',
    frequency: 3,
    region: 'lumbar'
  },
  {
    id: 'radiculopathy',
    name: 'Radiculopathy',
    description: 'Nerve root compression causing pain, numbness, and weakness in the distribution of the affected nerve.',
    prevalence: '3-5% of population',
    severity: 'moderate',
    symptoms: ['Radiating pain', 'Numbness', 'Weakness', 'Tingling'],
    path: '/patient-resources/conditions/radiculopathy',
    frequency: 4,
    region: 'general'
  },
  {
    id: 'facet-arthropathy',
    name: 'Facet Arthropathy',
    description: 'Arthritis of the facet joints in the spine, causing pain and stiffness.',
    prevalence: '15% over age 40',
    severity: 'mild',
    symptoms: ['Back pain', 'Stiffness', 'Reduced mobility', 'Morning pain'],
    path: '/patient-resources/conditions/facet-arthropathy',
    frequency: 5,
    region: 'general'
  },
  {
    id: 'spondylosis',
    name: 'Spondylosis',
    description: 'Age-related wear and tear of the spinal discs and joints, leading to degenerative changes.',
    prevalence: '85% over age 60',
    severity: 'mild',
    symptoms: ['Chronic pain', 'Stiffness', 'Reduced flexibility', 'Muscle spasms'],
    path: '/patient-resources/conditions/spondylosis',
    frequency: 6,
    region: 'general'
  },
  {
    id: 'discopathy',
    name: 'Discopathy',
    description: 'Degenerative disc disease affecting the intervertebral discs, causing pain and dysfunction.',
    prevalence: '30% by age 30',
    severity: 'moderate',
    symptoms: ['Chronic back pain', 'Stiffness', 'Pain with movement', 'Muscle spasms'],
    path: '/patient-resources/conditions/discopathy',
    frequency: 7,
    region: 'general'
  },
  {
    id: 'arthrosis',
    name: 'Arthrosis',
    description: 'Degenerative joint disease affecting the spine, causing pain and reduced mobility.',
    prevalence: '12% of population',
    severity: 'mild',
    symptoms: ['Joint pain', 'Stiffness', 'Reduced range of motion', 'Grinding sensation'],
    path: '/patient-resources/conditions/arthrosis',
    frequency: 8,
    region: 'general'
  },
  {
    id: 'cervical-myelopathy',
    name: 'Cervical Myelopathy',
    description: 'Spinal cord compression in the neck region, potentially causing serious neurological symptoms.',
    prevalence: '1.6 per 100,000',
    severity: 'severe',
    symptoms: ['Hand clumsiness', 'Gait problems', 'Neck pain', 'Weakness'],
    path: '/patient-resources/conditions/cervical-myelopathy',
    frequency: 9,
    region: 'cervical'
  },
  {
    id: 'spondylolisthesis',
    name: 'Spondylolisthesis',
    description: 'Forward slippage of one vertebra over another, potentially causing nerve compression.',
    prevalence: '6% of population',
    severity: 'moderate',
    symptoms: ['Lower back pain', 'Leg pain', 'Muscle tightness', 'Posture changes'],
    path: '/patient-resources/conditions/spondylolisthesis',
    frequency: 10,
    region: 'lumbar'
  },
  {
    id: 'piriformis-syndrome',
    name: 'Piriformis Syndrome',
    description: 'Compression of the sciatic nerve by the piriformis muscle, causing buttock and leg pain.',
    prevalence: '0.3-6% of population',
    severity: 'mild',
    symptoms: ['Buttock pain', 'Leg pain', 'Numbness', 'Tingling'],
    path: '/patient-resources/conditions/piriformis-syndrome',
    frequency: 11,
    region: 'lumbar'
  },
  {
    id: 'pars-defects',
    name: 'Pars Defects',
    description: 'Stress fractures in the pars interarticularis, often seen in athletes and young adults.',
    prevalence: '6% of population',
    severity: 'moderate',
    symptoms: ['Lower back pain', 'Pain with extension', 'Muscle spasms', 'Stiffness'],
    path: '/patient-resources/conditions/pars-defects',
    frequency: 12,
    region: 'lumbar'
  },
  {
    id: 'sacroiliac-arthropathy',
    name: 'Sacroiliac Arthropathy',
    description: 'Inflammation or dysfunction of the sacroiliac joints, causing lower back and buttock pain.',
    prevalence: '15-25% of back pain',
    severity: 'mild',
    symptoms: ['Lower back pain', 'Buttock pain', 'Hip pain', 'Stiffness'],
    path: '/patient-resources/conditions/sacroiliac-arthropathy',
    frequency: 13,
    region: 'lumbar'
  },
  {
    id: 'thoracic-outlet-syndrome',
    name: 'Thoracic Outlet Syndrome',
    description: 'Compression of nerves or blood vessels between the collarbone and first rib.',
    prevalence: '1% of population',
    severity: 'moderate',
    symptoms: ['Arm pain', 'Numbness', 'Tingling', 'Weakness'],
    path: '/patient-resources/conditions/thoracic-outlet-syndrome',
    frequency: 14,
    region: 'cervical'
  },
  {
    id: 'cauda-equina-syndrome',
    name: 'Cauda Equina Syndrome',
    description: 'Severe compression of nerve roots at the end of the spinal cord, requiring emergency treatment.',
    prevalence: '1-5 per 100,000',
    severity: 'severe',
    symptoms: ['Severe back pain', 'Bladder dysfunction', 'Bowel dysfunction', 'Leg weakness'],
    path: '/patient-resources/conditions/cauda-equina-syndrome',
    frequency: 15,
    region: 'lumbar'
  }
];

const SpineConditions: React.FC = () => {
  const deviceInfo = useDeviceDetection();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all');
  const [selectedRegion, setSelectedRegion] = useState<string>('all');

  // Filter conditions based on search, severity, and region
  const filteredConditions = spineConditions
    .filter(condition => 
      condition.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      condition.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      condition.symptoms.some(symptom => symptom.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .filter(condition => selectedSeverity === 'all' || condition.severity === selectedSeverity)
    .filter(condition => selectedRegion === 'all' || condition.region === selectedRegion)
    .sort((a, b) => a.frequency - b.frequency);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'severe': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'moderate': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'mild': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getRegionColor = (region: string) => {
    switch (region) {
      case 'cervical': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'thoracic': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'lumbar': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'general': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <StandardPageLayout showHeader={false}>
      <Helmet>
        <title>Spine Conditions | Comprehensive Guide | miNEURO</title>
        <meta 
          name="description" 
          content="Comprehensive guide to spine conditions including herniated disc, sciatica, spinal stenosis, and more. Expert spinal surgery and treatment options." 
        />
        <meta name="keywords" content="spine conditions, herniated disc, sciatica, spinal stenosis, back pain, spine surgery" />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/spine-conditions" />
      </Helmet>

      <PageHeader
        title="Spine Conditions"
        subtitle="Comprehensive information about spinal disorders, from common back pain to complex surgical cases requiring specialized care"
        backgroundImage="/images/spine-conditions/spine-anatomy-hero.jpg"
        enableParallax={true}
      />

      <main className="flex-1">
        {/* Overview Section */}
        <section className="py-16 bg-gradient-to-br from-muted/10 to-background">
          <div className="container">
            <SectionHeader
              title="Understanding Spine Conditions"
              subtitle="Expert spinal care for complex disorders"
              description="Our comprehensive approach to spine conditions combines advanced diagnostic techniques with minimally invasive surgical interventions. Each condition requires specialized expertise and personalized treatment planning."
              centered={true}
            />

            {/* Quick Stats */}
            <div className={cn(
              "grid gap-6 mt-12",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
            )}>
              <Card className="medical-card text-center">
                <CardContent className="pt-6">
                  <Layers className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-enhanced-heading mb-2">15</h3>
                  <p className="text-muted-foreground">Spine Conditions Covered</p>
                </CardContent>
              </Card>
              <Card className="medical-card text-center">
                <CardContent className="pt-6">
                  <Users className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-enhanced-heading mb-2">2000+</h3>
                  <p className="text-muted-foreground">Patients Treated Annually</p>
                </CardContent>
              </Card>
              <Card className="medical-card text-center">
                <CardContent className="pt-6">
                  <Activity className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-enhanced-heading mb-2">90%</h3>
                  <p className="text-muted-foreground">Minimally Invasive Procedures</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Search and Filter Section */}
        <section className="py-12 bg-muted/30">
          <div className="container">
            <div className={cn(
              "flex gap-4 mb-8",
              deviceInfo.isMobile ? "flex-col" : "flex-row items-center"
            )}>
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search conditions or symptoms..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2 flex-wrap">
                <div className="flex gap-1">
                  {['all', 'mild', 'moderate', 'severe'].map((severity) => (
                    <Button
                      key={severity}
                      variant={selectedSeverity === severity ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedSeverity(severity)}
                      className="capitalize"
                    >
                      {severity}
                    </Button>
                  ))}
                </div>
                <div className="flex gap-1">
                  {['all', 'cervical', 'thoracic', 'lumbar', 'general'].map((region) => (
                    <Button
                      key={region}
                      variant={selectedRegion === region ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedRegion(region)}
                      className="capitalize"
                    >
                      {region}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Conditions Grid */}
        <section className="py-16">
          <div className="container">
            <GridLayout
              items={filteredConditions}
              renderItem={(condition) => (
                <Card key={condition.id} className="medical-card h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2 flex-wrap">
                        <Layers className="h-5 w-5 text-primary" />
                        <Badge className={getSeverityColor(condition.severity)}>
                          {condition.severity}
                        </Badge>
                        <Badge className={getRegionColor(condition.region)}>
                          {condition.region}
                        </Badge>
                      </div>
                    </div>
                    <CardTitle className="text-xl">{condition.name}</CardTitle>
                    <CardDescription className="text-sm text-muted-foreground">
                      Prevalence: {condition.prevalence}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="flex-1">
                    <p className="text-muted-foreground mb-4">{condition.description}</p>
                    <div className="mb-4">
                      <h4 className="font-semibold mb-2 text-sm">Common Symptoms:</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        {condition.symptoms.map((symptom, index) => (
                          <li key={index} className="flex items-center gap-2">
                            <div className="w-1 h-1 rounded-full bg-primary" />
                            {symptom}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                  <div className="p-6 pt-0">
                    <Button asChild className="w-full">
                      <Link to={condition.path}>
                        Learn More
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </Card>
              )}
              columns={deviceInfo.isMobile ? 1 : 3}
              gap="lg"
            />

            {filteredConditions.length === 0 && (
              <div className="text-center py-12">
                <Layers className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">No conditions found</h3>
                <p className="text-muted-foreground mb-4">
                  Try adjusting your search terms or filters
                </p>
                <Button onClick={() => { setSearchTerm(''); setSelectedSeverity('all'); setSelectedRegion('all'); }}>
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        </section>

        {/* Emergency Information */}
        <section className="py-16 bg-red-50 dark:bg-red-950/20">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center">
              <AlertTriangle className="h-16 w-16 text-red-600 mx-auto mb-6" />
              <h2 className="text-3xl font-bold text-enhanced-heading mb-4">
                Emergency Spine Symptoms
              </h2>
              <p className="text-lg text-muted-foreground mb-8">
                Seek immediate medical attention if you experience any of these symptoms:
              </p>
              <div className={cn(
                "grid gap-4 text-left",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                {[
                  'Loss of bladder or bowel control',
                  'Severe weakness in both legs',
                  'Numbness in the saddle area (groin/buttocks)',
                  'Sudden severe back pain after trauma',
                  'Progressive weakness in arms or legs',
                  'Severe neck pain with fever and headache'
                ].map((symptom, index) => (
                  <div key={index} className="flex items-start gap-3 p-4 bg-white dark:bg-gray-900 rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{symptom}</span>
                  </div>
                ))}
              </div>
              <div className="mt-8">
                <Button size="lg" className="bg-red-600 hover:bg-red-700">
                  Call Emergency Services: 000
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary text-primary-foreground">
          <div className="container text-center">
            <h2 className="text-3xl font-bold mb-4">
              Expert Spine Care
            </h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Our specialized spinal surgery team provides comprehensive diagnosis and treatment for all spine conditions. 
              Schedule a consultation to discuss your specific needs.
            </p>
            <div className={cn(
              "flex gap-4 justify-center",
              deviceInfo.isMobile ? "flex-col items-center" : "flex-row"
            )}>
              <Button asChild size="lg" variant="secondary">
                <Link to="/appointments">Book Consultation</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
                <Link to="/contact">Contact Us</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
    </StandardPageLayout>
  );
};

export default SpineConditions;
