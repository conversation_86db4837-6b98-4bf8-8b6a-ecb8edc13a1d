import {
  Layers,
  Users,
  TrendingUp,
  AlertTriangle,
  Search,
  ArrowRight,
  Activity,
  CheckCircle,
  Clock,
  Stethoscope,
  Shield,
  Filter,
  Star,
  Zap,
  Brain
} from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { Link } from 'react-router-dom';

import PageHeader from '@/components/PageHeader';
import AppointmentCallToActionSection from '@/components/patient-resources/AppointmentCallToActionSection';
import { SectionHeader, GridLayout } from '@/components/shared/CommonSectionPatterns';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface SpineCondition {
  id: string;
  name: string;
  description: string;
  prevalence: string;
  severity: 'mild' | 'moderate' | 'severe';
  symptoms: string[];
  path: string;
  frequency: number; // 1-15, 1 being most common
  region: 'cervical' | 'thoracic' | 'lumbar' | 'general';
  treatmentOptions: string[];
  keyFeatures: string[];
  valueProposition: string;
  estimatedReadTime: string;
}

const spineConditions: SpineCondition[] = [
  {
    id: 'herniated-disc',
    name: 'Herniated Disc',
    description: 'Displacement of disc material that can compress nearby nerves, causing pain and neurological symptoms.',
    prevalence: '2-3% of population',
    severity: 'moderate',
    symptoms: ['Back pain', 'Leg pain', 'Numbness', 'Weakness'],
    path: '/patient-resources/conditions/herniated-disc',
    frequency: 1,
    region: 'lumbar',
    treatmentOptions: ['Conservative Management', 'Epidural Injections', 'Microdiscectomy', 'Physical Therapy'],
    keyFeatures: ['Minimally Invasive Surgery', 'Rapid Recovery', 'High Success Rates', 'Nerve Decompression'],
    valueProposition: 'Advanced disc surgery with minimally invasive techniques for faster recovery and excellent outcomes.',
    estimatedReadTime: '12-15 min'
  },
  {
    id: 'sciatica',
    name: 'Sciatica',
    description: 'Pain radiating along the sciatic nerve from the lower back down through the leg.',
    prevalence: '10-40% of population',
    severity: 'moderate',
    symptoms: ['Leg pain', 'Burning sensation', 'Numbness', 'Tingling'],
    path: '/patient-resources/conditions/sciatica',
    frequency: 2,
    region: 'lumbar',
    treatmentOptions: ['Conservative Care', 'Nerve Blocks', 'Epidural Injections', 'Surgical Decompression'],
    keyFeatures: ['Pain Relief', 'Nerve Protection', 'Function Restoration', 'Long-term Solutions'],
    valueProposition: 'Comprehensive sciatica treatment with proven pain relief and nerve protection strategies.',
    estimatedReadTime: '10-12 min'
  },
  {
    id: 'spinal-stenosis',
    name: 'Spinal Stenosis',
    description: 'Narrowing of the spinal canal that can compress the spinal cord or nerve roots.',
    prevalence: '8-11% over age 50',
    severity: 'moderate',
    symptoms: ['Walking difficulty', 'Leg pain', 'Numbness', 'Balance problems'],
    path: '/patient-resources/conditions/spinal-stenosis',
    frequency: 3,
    region: 'lumbar',
    treatmentOptions: ['Decompressive Laminectomy', 'Spinal Fusion', 'Minimally Invasive Decompression', 'Conservative Management'],
    keyFeatures: ['Walking Improvement', 'Nerve Decompression', 'Stability Restoration', 'Quality of Life Enhancement'],
    valueProposition: 'Expert stenosis treatment to restore mobility and improve quality of life with advanced surgical techniques.',
    estimatedReadTime: '15-18 min'
  },
  {
    id: 'radiculopathy',
    name: 'Radiculopathy',
    description: 'Nerve root compression causing pain, numbness, and weakness in the distribution of the affected nerve.',
    prevalence: '3-5% of population',
    severity: 'moderate',
    symptoms: ['Radiating pain', 'Numbness', 'Weakness', 'Tingling'],
    path: '/patient-resources/conditions/radiculopathy',
    frequency: 4,
    region: 'general',
    treatmentOptions: ['Nerve Root Blocks', 'Decompressive Surgery', 'Physical Therapy', 'Anti-inflammatory Treatment'],
    keyFeatures: ['Nerve Protection', 'Pain Relief', 'Function Restoration', 'Targeted Treatment'],
    valueProposition: 'Precise radiculopathy treatment targeting nerve root compression for effective pain relief.',
    estimatedReadTime: '10-12 min'
  },
  {
    id: 'facet-arthropathy',
    name: 'Facet Arthropathy',
    description: 'Arthritis of the facet joints in the spine, causing pain and stiffness.',
    prevalence: '15% over age 40',
    severity: 'mild',
    symptoms: ['Back pain', 'Stiffness', 'Reduced mobility', 'Morning pain'],
    path: '/patient-resources/conditions/facet-arthropathy',
    frequency: 5,
    region: 'general',
    treatmentOptions: ['Facet Joint Injections', 'Radiofrequency Ablation', 'Physical Therapy', 'Joint Mobilisation'],
    keyFeatures: ['Joint Pain Relief', 'Mobility Improvement', 'Long-lasting Results', 'Minimally Invasive'],
    valueProposition: 'Advanced facet joint treatments for lasting pain relief and improved spinal mobility.',
    estimatedReadTime: '8-10 min'
  },
  {
    id: 'spondylosis',
    name: 'Spondylosis',
    description: 'Age-related wear and tear of the spinal discs and joints, leading to degenerative changes.',
    prevalence: '85% over age 60',
    severity: 'mild',
    symptoms: ['Chronic pain', 'Stiffness', 'Reduced flexibility', 'Muscle spasms'],
    path: '/patient-resources/conditions/spondylosis',
    frequency: 6,
    region: 'general',
    treatmentOptions: ['Conservative Management', 'Spinal Injections', 'Physical Therapy', 'Lifestyle Modifications'],
    keyFeatures: ['Age-related Care', 'Function Preservation', 'Pain Management', 'Quality of Life Focus'],
    valueProposition: 'Comprehensive spondylosis management to maintain function and quality of life as you age.',
    estimatedReadTime: '10-12 min'
  },
  {
    id: 'discopathy',
    name: 'Discopathy',
    description: 'Degenerative disc disease affecting the intervertebral discs, causing pain and dysfunction.',
    prevalence: '30% by age 30',
    severity: 'moderate',
    symptoms: ['Chronic back pain', 'Stiffness', 'Pain with movement', 'Muscle spasms'],
    path: '/patient-resources/conditions/discopathy',
    frequency: 7,
    region: 'general',
    treatmentOptions: ['Disc Replacement', 'Spinal Fusion', 'Regenerative Therapy', 'Conservative Care'],
    keyFeatures: ['Disc Preservation', 'Motion Restoration', 'Advanced Technology', 'Long-term Solutions'],
    valueProposition: 'Innovative disc disease treatment with advanced surgical and regenerative options.',
    estimatedReadTime: '12-15 min'
  },
  {
    id: 'arthrosis',
    name: 'Arthrosis',
    description: 'Degenerative joint disease affecting the spine, causing pain and reduced mobility.',
    prevalence: '12% of population',
    severity: 'mild',
    symptoms: ['Joint pain', 'Stiffness', 'Reduced range of motion', 'Grinding sensation'],
    path: '/patient-resources/conditions/arthrosis',
    frequency: 8,
    region: 'general',
    treatmentOptions: ['Joint Injections', 'Physical Therapy', 'Anti-inflammatory Treatment', 'Lifestyle Modifications'],
    keyFeatures: ['Joint Preservation', 'Pain Management', 'Mobility Enhancement', 'Non-surgical Options'],
    valueProposition: 'Comprehensive arthrosis care focusing on joint preservation and pain-free mobility.',
    estimatedReadTime: '8-10 min'
  },
  {
    id: 'cervical-myelopathy',
    name: 'Cervical Myelopathy',
    description: 'Spinal cord compression in the neck region, potentially causing serious neurological symptoms.',
    prevalence: '1.6 per 100,000',
    severity: 'severe',
    symptoms: ['Hand clumsiness', 'Gait problems', 'Neck pain', 'Weakness'],
    path: '/patient-resources/conditions/cervical-myelopathy',
    frequency: 9,
    region: 'cervical',
    treatmentOptions: ['Cervical Decompression', 'Spinal Fusion', 'Laminoplasty', 'Anterior Discectomy'],
    keyFeatures: ['Spinal Cord Protection', 'Neurological Preservation', 'Advanced Surgery', 'Function Recovery'],
    valueProposition: 'Expert cervical myelopathy treatment to protect spinal cord function and prevent progression.',
    estimatedReadTime: '15-18 min'
  },
  {
    id: 'spondylolisthesis',
    name: 'Spondylolisthesis',
    description: 'Forward slippage of one vertebra over another, potentially causing nerve compression.',
    prevalence: '6% of population',
    severity: 'moderate',
    symptoms: ['Lower back pain', 'Leg pain', 'Muscle tightness', 'Posture changes'],
    path: '/patient-resources/conditions/spondylolisthesis',
    frequency: 10,
    region: 'lumbar',
    treatmentOptions: ['Spinal Fusion', 'Decompression Surgery', 'Conservative Management', 'Stabilisation Procedures'],
    keyFeatures: ['Spinal Stability', 'Nerve Decompression', 'Posture Correction', 'Long-term Stability'],
    valueProposition: 'Advanced spondylolisthesis treatment for spinal stability and lasting pain relief.',
    estimatedReadTime: '12-15 min'
  },
  {
    id: 'piriformis-syndrome',
    name: 'Piriformis Syndrome',
    description: 'Compression of the sciatic nerve by the piriformis muscle, causing buttock and leg pain.',
    prevalence: '0.3-6% of population',
    severity: 'mild',
    symptoms: ['Buttock pain', 'Leg pain', 'Numbness', 'Tingling'],
    path: '/patient-resources/conditions/piriformis-syndrome',
    frequency: 11,
    region: 'lumbar',
    treatmentOptions: ['Piriformis Injections', 'Physical Therapy', 'Muscle Release', 'Stretching Programs'],
    keyFeatures: ['Muscle Relaxation', 'Nerve Decompression', 'Pain Relief', 'Function Restoration'],
    valueProposition: 'Targeted piriformis syndrome treatment for effective muscle relaxation and nerve relief.',
    estimatedReadTime: '8-10 min'
  },
  {
    id: 'pars-defects',
    name: 'Pars Defects',
    description: 'Stress fractures in the pars interarticularis, often seen in athletes and young adults.',
    prevalence: '6% of population',
    severity: 'moderate',
    symptoms: ['Lower back pain', 'Pain with extension', 'Muscle spasms', 'Stiffness'],
    path: '/patient-resources/conditions/pars-defects',
    frequency: 12,
    region: 'lumbar',
    treatmentOptions: ['Conservative Management', 'Spinal Fusion', 'Activity Modification', 'Bracing'],
    keyFeatures: ['Athletic Recovery', 'Fracture Healing', 'Activity Return', 'Young Adult Focus'],
    valueProposition: 'Specialised pars defect treatment for athletes and young adults to return to active lifestyles.',
    estimatedReadTime: '10-12 min'
  },
  {
    id: 'sacroiliac-arthropathy',
    name: 'Sacroiliac Arthropathy',
    description: 'Inflammation or dysfunction of the sacroiliac joints, causing lower back and buttock pain.',
    prevalence: '15-25% of back pain',
    severity: 'mild',
    symptoms: ['Lower back pain', 'Buttock pain', 'Hip pain', 'Stiffness'],
    path: '/patient-resources/conditions/sacroiliac-arthropathy',
    frequency: 13,
    region: 'lumbar',
    treatmentOptions: ['SI Joint Injections', 'Radiofrequency Ablation', 'SI Joint Fusion', 'Physical Therapy'],
    keyFeatures: ['Joint Stabilisation', 'Pain Relief', 'Mobility Improvement', 'Targeted Treatment'],
    valueProposition: 'Advanced sacroiliac joint treatment for lasting pain relief and improved pelvic stability.',
    estimatedReadTime: '8-10 min'
  },
  {
    id: 'thoracic-outlet-syndrome',
    name: 'Thoracic Outlet Syndrome',
    description: 'Compression of nerves or blood vessels between the collarbone and first rib.',
    prevalence: '1% of population',
    severity: 'moderate',
    symptoms: ['Arm pain', 'Numbness', 'Tingling', 'Weakness'],
    path: '/patient-resources/conditions/thoracic-outlet-syndrome',
    frequency: 14,
    region: 'cervical',
    treatmentOptions: ['Thoracic Outlet Decompression', 'First Rib Resection', 'Physical Therapy', 'Nerve Blocks'],
    keyFeatures: ['Nerve Decompression', 'Vascular Relief', 'Arm Function Restoration', 'Specialised Surgery'],
    valueProposition: 'Expert thoracic outlet syndrome treatment for complete nerve and vascular decompression.',
    estimatedReadTime: '10-12 min'
  },
  {
    id: 'cauda-equina-syndrome',
    name: 'Cauda Equina Syndrome',
    description: 'Severe compression of nerve roots at the end of the spinal cord, requiring emergency treatment.',
    prevalence: '1-5 per 100,000',
    severity: 'severe',
    symptoms: ['Severe back pain', 'Bladder dysfunction', 'Bowel dysfunction', 'Leg weakness'],
    path: '/patient-resources/conditions/cauda-equina-syndrome',
    frequency: 15,
    region: 'lumbar',
    treatmentOptions: ['Emergency Decompression', 'Urgent Surgery', 'Neurological Monitoring', 'Rehabilitation'],
    keyFeatures: ['Emergency Care', 'Neurological Preservation', 'Urgent Intervention', 'Function Recovery'],
    valueProposition: 'Emergency cauda equina treatment with urgent surgical intervention to preserve neurological function.',
    estimatedReadTime: '8-10 min'
  }
];

const SpineConditions: React.FC = () => {
  const deviceInfo = useDeviceDetection();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all');
  const [selectedRegion, setSelectedRegion] = useState<string>('all');

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Filter conditions based on search, severity, and region
  const filteredConditions = spineConditions
    .filter(condition =>
      condition.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      condition.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      condition.symptoms.some(symptom => symptom.toLowerCase().includes(searchTerm.toLowerCase())) ||
      condition.treatmentOptions.some(treatment => treatment.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .filter(condition => selectedSeverity === 'all' || condition.severity === selectedSeverity)
    .filter(condition => selectedRegion === 'all' || condition.region === selectedRegion)
    .sort((a, b) => a.frequency - b.frequency);

  // Region statistics
  const regionStats = {
    cervical: spineConditions.filter(c => c.region === 'cervical').length,
    thoracic: spineConditions.filter(c => c.region === 'thoracic').length,
    lumbar: spineConditions.filter(c => c.region === 'lumbar').length,
    general: spineConditions.filter(c => c.region === 'general').length
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'severe': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'moderate': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'mild': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getRegionColor = (region: string) => {
    switch (region) {
      case 'cervical': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'thoracic': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'lumbar': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'general': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <StandardPageLayout showHeader={false}>
      <Helmet>
        <title>Spine Conditions | Expert Spinal Care & Anatomy Guide | miNEURO</title>
        <meta
          name="description"
          content="Comprehensive guide to 15 spine conditions with detailed spinal anatomy education. Expert spinal care including herniated discs, sciatica, spinal stenosis, and more. Advanced treatment options with personalised care pathways."
        />
        <meta name="keywords" content="spine conditions, spinal anatomy, herniated disc, sciatica, spinal stenosis, back pain, spine surgery, radiculopathy, spondylosis, cervical myelopathy, vertebrae, intervertebral discs" />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/spine-conditions" />
      </Helmet>

      <PageHeader
        title="Spine Conditions"
        subtitle="Expert spinal care for 15 spine conditions with advanced treatment options, comprehensive information, and personalised care pathways"
        backgroundImage="/images/spine-conditions/spine-anatomy-hero.jpg"
        enableParallax={true}
      />

      <main className="flex-1">
        {/* Overview Section */}
        <section className="py-16 bg-gradient-to-br from-primary/5 to-background">
          <div className="container max-w-7xl">
            <div className="text-center mb-12">
              <h2 className="text-enhanced-heading text-3xl font-bold mb-4">
                Comprehensive Spine Condition Care
              </h2>
              <p className="text-enhanced-body max-w-4xl mx-auto text-lg">
                Expert spinal care for 15 spine conditions with advanced treatment options,
                personalised care pathways, and comprehensive support throughout your journey.
              </p>
            </div>

            {/* Region Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              <Card className="text-center p-6 border-2 hover:border-primary/30 transition-colors">
                <CardContent className="p-0">
                  <Layers className="h-10 w-10 text-primary mx-auto mb-3" />
                  <h3 className="text-xl font-bold text-enhanced-heading mb-2">Cervical Spine</h3>
                  <p className="text-sm text-muted-foreground mb-2">{regionStats.cervical} Conditions</p>
                  <p className="text-xs text-muted-foreground">Neck and upper spine</p>
                </CardContent>
              </Card>
              <Card className="text-center p-6 border-2 hover:border-primary/30 transition-colors">
                <CardContent className="p-0">
                  <Activity className="h-10 w-10 text-primary mx-auto mb-3" />
                  <h3 className="text-xl font-bold text-enhanced-heading mb-2">Thoracic Spine</h3>
                  <p className="text-sm text-muted-foreground mb-2">{regionStats.thoracic} Conditions</p>
                  <p className="text-xs text-muted-foreground">Mid-back and chest area</p>
                </CardContent>
              </Card>
              <Card className="text-center p-6 border-2 hover:border-primary/30 transition-colors">
                <CardContent className="p-0">
                  <Zap className="h-10 w-10 text-primary mx-auto mb-3" />
                  <h3 className="text-xl font-bold text-enhanced-heading mb-2">Lumbar Spine</h3>
                  <p className="text-sm text-muted-foreground mb-2">{regionStats.lumbar} Conditions</p>
                  <p className="text-xs text-muted-foreground">Lower back and pelvis</p>
                </CardContent>
              </Card>
              <Card className="text-center p-6 border-2 hover:border-primary/30 transition-colors">
                <CardContent className="p-0">
                  <Shield className="h-10 w-10 text-primary mx-auto mb-3" />
                  <h3 className="text-xl font-bold text-enhanced-heading mb-2">General Conditions</h3>
                  <p className="text-sm text-muted-foreground mb-2">{regionStats.general} Conditions</p>
                  <p className="text-xs text-muted-foreground">Multi-level spine issues</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Search and Filter Section */}
        <section className="py-16 bg-muted/30">
          <div className="container max-w-7xl">
            <div className="text-center mb-8">
              <h2 className="text-enhanced-heading text-2xl font-bold mb-2">
                Find Your Condition
              </h2>
              <p className="text-enhanced-body text-muted-foreground">
                Search by condition name, symptoms, or treatment options
              </p>
            </div>

            <div className={cn(
              "flex gap-4 mb-8",
              deviceInfo.isMobile ? "flex-col" : "flex-row items-center"
            )}>
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search conditions, symptoms, or treatments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="flex gap-2">
                <Select value={selectedRegion} onValueChange={setSelectedRegion}>
                  <SelectTrigger className="w-[140px]">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Region" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Regions</SelectItem>
                    <SelectItem value="cervical">Cervical</SelectItem>
                    <SelectItem value="thoracic">Thoracic</SelectItem>
                    <SelectItem value="lumbar">Lumbar</SelectItem>
                    <SelectItem value="general">General</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={selectedSeverity} onValueChange={setSelectedSeverity}>
                  <SelectTrigger className="w-[120px]">
                    <Clock className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Severity</SelectItem>
                    <SelectItem value="mild">Mild</SelectItem>
                    <SelectItem value="moderate">Moderate</SelectItem>
                    <SelectItem value="severe">Severe</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="text-center text-sm text-muted-foreground">
              Showing {filteredConditions.length} of {spineConditions.length} conditions
            </div>
          </div>
        </section>

        {/* Conditions Grid */}
        <section className="py-16">
          <div className="container max-w-7xl">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredConditions.map((condition) => (
                <Card
                  key={condition.id}
                  className="group hover:shadow-xl transition-all duration-300 border-2 hover:border-primary/20 h-full flex flex-col"
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className="p-2 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                          <Layers className="h-5 w-5 text-primary" />
                        </div>
                        <Badge className={getSeverityColor(condition.severity)}>
                          {condition.severity}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        {condition.estimatedReadTime}
                      </div>
                    </div>

                    <CardTitle className="text-xl font-bold group-hover:text-primary transition-colors mb-2">
                      {condition.name}
                    </CardTitle>

                    <div className="flex items-center gap-2 mb-3">
                      <Badge variant="outline" className="text-xs">
                        {condition.region}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {condition.prevalence}
                      </span>
                    </div>

                    <CardDescription className="text-sm leading-relaxed">
                      {condition.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="flex-1 pb-4">
                    <div className="mb-4">
                      <h4 className="font-semibold mb-2 text-sm flex items-center gap-1">
                        <Stethoscope className="h-4 w-4 text-primary" />
                        Key Symptoms:
                      </h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        {condition.symptoms.slice(0, 3).map((symptom, index) => (
                          <li key={index} className="flex items-center gap-2">
                            <CheckCircle className="h-3 w-3 text-primary flex-shrink-0" />
                            {symptom}
                          </li>
                        ))}
                        {condition.symptoms.length > 3 && (
                          <li className="text-xs text-muted-foreground italic">
                            +{condition.symptoms.length - 3} more symptoms
                          </li>
                        )}
                      </ul>
                    </div>

                    <div className="mb-4">
                      <h4 className="font-semibold mb-2 text-sm flex items-center gap-1">
                        <Star className="h-4 w-4 text-primary" />
                        Treatment Options:
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {condition.treatmentOptions.slice(0, 2).map((treatment, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {treatment}
                          </Badge>
                        ))}
                        {condition.treatmentOptions.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{condition.treatmentOptions.length - 2} more
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="mb-4 p-3 bg-primary/5 rounded-lg">
                      <p className="text-sm text-primary font-medium">
                        {condition.valueProposition}
                      </p>
                    </div>
                  </CardContent>

                  <div className="p-6 pt-0">
                    <Button asChild className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                      <Link to={condition.path} className="flex items-center justify-center gap-2">
                        <span>Learn More</span>
                        <ArrowRight className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </Card>
              ))}
            </div>

            {filteredConditions.length === 0 && (
              <div className="text-center py-16">
                <Layers className="h-20 w-20 text-muted-foreground mx-auto mb-6" />
                <h3 className="text-2xl font-bold mb-4">No conditions found</h3>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  Try adjusting your search terms or filters to find the information you're looking for.
                </p>
                <div className="flex gap-2 justify-center">
                  <Button
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedSeverity('all');
                      setSelectedRegion('all');
                    }}
                  >
                    Clear All Filters
                  </Button>
                  <Button variant="outline" asChild>
                    <Link to="/patient-resources">
                      Browse All Resources
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </div>
        </section>

        {/* Comprehensive Spinal Anatomy */}
        <section className="py-20 bg-gradient-to-br from-muted/20 to-background">
          <div className="container max-w-7xl">
            <div className="text-center mb-16">
              <h2 className="text-enhanced-heading text-4xl font-bold mb-6">
                Understanding Your Spinal Anatomy
              </h2>
              <p className="text-enhanced-body text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                The human spine is a remarkable structure consisting of 33 vertebrae that protect your spinal cord
                and provide structural support for your entire body. Understanding spinal anatomy helps you appreciate
                how different conditions affect specific regions and why targeted treatment is essential.
              </p>
            </div>

            {/* Main Spinal Overview with Image */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
              <div className="space-y-6">
                <h3 className="text-enhanced-heading text-2xl font-bold mb-4">
                  The Complete Spinal Column
                </h3>
                <p className="text-enhanced-body text-lg leading-relaxed">
                  Your spine consists of 33 vertebrae stacked in a precise S-shaped curve that provides both
                  strength and flexibility. This natural curvature distributes weight evenly and absorbs shock
                  during movement, protecting your spinal cord and nerve roots.
                </p>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Layers className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-enhanced-heading">Vertebral Structure</h4>
                      <p className="text-muted-foreground text-sm">Each vertebra consists of a body, arch, and processes for muscle attachment</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Shield className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-enhanced-heading">Spinal Canal</h4>
                      <p className="text-muted-foreground text-sm">Protected pathway for the spinal cord and nerve roots</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Activity className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-enhanced-heading">Intervertebral Discs</h4>
                      <p className="text-muted-foreground text-sm">Shock-absorbing cushions between vertebrae</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1559757175-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Complete spinal column anatomy showing all vertebrae from cervical to coccyx"
                  className="w-full h-[400px] object-cover rounded-xl shadow-2xl"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl" />
              </div>
            </div>

            {/* Spinal Regions */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              <Card className="border-2 hover:border-primary/30 transition-colors">
                <CardHeader className="text-center pb-4">
                  <div className="p-4 rounded-full bg-primary/10 w-fit mx-auto mb-4">
                    <Brain className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Cervical Spine</CardTitle>
                  <p className="text-sm text-muted-foreground">C1-C7 • 7 Vertebrae</p>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-muted-foreground mb-4">
                    Supports the head and allows neck movement. Most mobile spinal region.
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Head support (4-5kg)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Neck rotation & flexion</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Nerve supply to arms</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 hover:border-primary/30 transition-colors">
                <CardHeader className="text-center pb-4">
                  <div className="p-4 rounded-full bg-primary/10 w-fit mx-auto mb-4">
                    <Shield className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Thoracic Spine</CardTitle>
                  <p className="text-sm text-muted-foreground">T1-T12 • 12 Vertebrae</p>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-muted-foreground mb-4">
                    Connects to ribs forming the thoracic cage. Provides stability and protection.
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Rib cage attachment</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Organ protection</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Spinal stability</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 hover:border-primary/30 transition-colors">
                <CardHeader className="text-center pb-4">
                  <div className="p-4 rounded-full bg-primary/10 w-fit mx-auto mb-4">
                    <Zap className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Lumbar Spine</CardTitle>
                  <p className="text-sm text-muted-foreground">L1-L5 • 5 Vertebrae</p>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-muted-foreground mb-4">
                    Largest vertebrae bearing most body weight. Primary site of back pain.
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Weight bearing</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Lower body movement</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Leg nerve supply</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 hover:border-primary/30 transition-colors">
                <CardHeader className="text-center pb-4">
                  <div className="p-4 rounded-full bg-primary/10 w-fit mx-auto mb-4">
                    <Activity className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Sacrum & Coccyx</CardTitle>
                  <p className="text-sm text-muted-foreground">S1-S5 & Co1-Co4</p>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-muted-foreground mb-4">
                    Fused vertebrae forming the back of the pelvis and tailbone.
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Pelvic stability</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Muscle attachment</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Sitting support</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Intervertebral Discs and Nerve Pathways */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
              <div className="space-y-8">
                <h3 className="text-enhanced-heading text-2xl font-bold">
                  Intervertebral Discs & Nerve Pathways
                </h3>
                <div className="space-y-6">
                  <div className="flex items-start gap-4 p-4 bg-white/50 rounded-lg">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Activity className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-enhanced-heading mb-2">Disc Structure</h4>
                      <p className="text-muted-foreground text-sm mb-2">
                        Each disc has a tough outer ring (annulus fibrosus) and gel-like centre (nucleus pulposus)
                      </p>
                      <div className="flex flex-wrap gap-1">
                        <Badge variant="outline" className="text-xs">Shock Absorption</Badge>
                        <Badge variant="outline" className="text-xs">Flexibility</Badge>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-4 p-4 bg-white/50 rounded-lg">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Zap className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-enhanced-heading mb-2">Nerve Root Function</h4>
                      <p className="text-muted-foreground text-sm mb-2">
                        31 pairs of spinal nerves branch from the spinal cord to control specific body regions
                      </p>
                      <div className="flex flex-wrap gap-1">
                        <Badge variant="outline" className="text-xs">Motor Control</Badge>
                        <Badge variant="outline" className="text-xs">Sensation</Badge>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-4 p-4 bg-white/50 rounded-lg">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Shield className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-enhanced-heading mb-2">Spinal Ligaments</h4>
                      <p className="text-muted-foreground text-sm mb-2">
                        Strong fibrous bands that connect vertebrae and provide spinal stability
                      </p>
                      <div className="flex flex-wrap gap-1">
                        <Badge variant="outline" className="text-xs">Stability</Badge>
                        <Badge variant="outline" className="text-xs">Movement Control</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="relative">
                <img
                  src="https://images.pexels.com/photos/5327585/pexels-photo-5327585.jpeg?auto=compress&cs=tinysrgb&w=800"
                  alt="Detailed illustration of intervertebral disc anatomy and nerve root pathways"
                  className="w-full h-[400px] object-cover rounded-xl shadow-2xl"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl" />
              </div>
            </div>

            {/* Spinal Curvatures and Movement */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Spinal curvatures showing natural lordotic and kyphotic curves from side view"
                  className="w-full h-[400px] object-cover rounded-xl shadow-2xl"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl" />
              </div>

              <div className="space-y-8">
                <h3 className="text-enhanced-heading text-2xl font-bold">
                  Natural Spinal Curvatures
                </h3>
                <p className="text-enhanced-body text-lg leading-relaxed">
                  The spine's natural S-shaped curve is essential for proper weight distribution,
                  shock absorption, and maintaining balance during movement.
                </p>
                <div className="space-y-4">
                  <div className="p-4 bg-primary/5 rounded-lg">
                    <h4 className="font-semibold text-enhanced-heading mb-2 flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      Cervical Lordosis
                    </h4>
                    <p className="text-muted-foreground text-sm">
                      Natural inward curve of the neck that maintains head position and reduces stress
                    </p>
                  </div>

                  <div className="p-4 bg-primary/5 rounded-lg">
                    <h4 className="font-semibold text-enhanced-heading mb-2 flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      Thoracic Kyphosis
                    </h4>
                    <p className="text-muted-foreground text-sm">
                      Natural outward curve of the mid-back that accommodates the rib cage
                    </p>
                  </div>

                  <div className="p-4 bg-primary/5 rounded-lg">
                    <h4 className="font-semibold text-enhanced-heading mb-2 flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      Lumbar Lordosis
                    </h4>
                    <p className="text-muted-foreground text-sm">
                      Natural inward curve of the lower back that supports body weight
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Common Spinal Problems */}
            <div className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-2xl p-8 mb-16">
              <div className="text-center mb-8">
                <h3 className="text-enhanced-heading text-2xl font-bold mb-4">
                  How Spinal Problems Develop
                </h3>
                <p className="text-enhanced-body text-lg text-muted-foreground max-w-3xl mx-auto">
                  Understanding how spinal conditions develop helps in prevention and early treatment
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-6 bg-white/70 rounded-xl">
                  <div className="p-3 rounded-full bg-primary/10 w-fit mx-auto mb-4">
                    <AlertTriangle className="h-8 w-8 text-primary" />
                  </div>
                  <h4 className="font-semibold text-enhanced-heading mb-2">Disc Degeneration</h4>
                  <p className="text-muted-foreground text-sm">
                    Age-related wear and tear causes discs to lose height and flexibility
                  </p>
                </div>

                <div className="text-center p-6 bg-white/70 rounded-xl">
                  <div className="p-3 rounded-full bg-primary/10 w-fit mx-auto mb-4">
                    <Zap className="h-8 w-8 text-primary" />
                  </div>
                  <h4 className="font-semibold text-enhanced-heading mb-2">Nerve Compression</h4>
                  <p className="text-muted-foreground text-sm">
                    Herniated discs or bone spurs can compress nerve roots causing pain
                  </p>
                </div>

                <div className="text-center p-6 bg-white/70 rounded-xl">
                  <div className="p-3 rounded-full bg-primary/10 w-fit mx-auto mb-4">
                    <Activity className="h-8 w-8 text-primary" />
                  </div>
                  <h4 className="font-semibold text-enhanced-heading mb-2">Spinal Instability</h4>
                  <p className="text-muted-foreground text-sm">
                    Ligament damage or vertebral slippage can cause chronic pain
                  </p>
                </div>
              </div>
            </div>

            {/* Link to Comprehensive Library */}
            <div className="text-center">
              <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-2xl p-8">
                <h3 className="text-enhanced-heading text-2xl font-bold mb-4">
                  Explore Our Comprehensive Spine Conditions Library
                </h3>
                <p className="text-enhanced-body text-lg text-muted-foreground mb-6 max-w-3xl mx-auto">
                  Access detailed information about specific spine conditions organised by spinal region,
                  with comprehensive guides covering causes, symptoms, diagnosis, and treatment options.
                </p>
                <div className="flex gap-4 justify-center flex-wrap">
                  <Button asChild size="lg" className="bg-primary hover:bg-primary/90">
                    <Link to="/patient-resources/spine-conditions-library">
                      <Layers className="mr-2 h-5 w-5" />
                      View Complete Library
                    </Link>
                  </Button>
                  <Button asChild size="lg" variant="outline">
                    <Link to="/patient-resources/spine-anatomy">
                      <Brain className="mr-2 h-5 w-5" />
                      Detailed Spine Anatomy
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Appointment Call to Action */}
        <AppointmentCallToActionSection
          title="Expert Spine Condition Care"
          description="Our specialised spinal surgery team provides comprehensive diagnosis and treatment for all spine conditions. Schedule a consultation to discuss your specific needs and explore personalised treatment options."
          primaryButtonText="Book Consultation"
          primaryButtonLink="/appointments"
          secondaryButtonText="Contact Us"
          secondaryButtonLink="/contact"
        />
      </main>
    </StandardPageLayout>
  );
};

export default SpineConditions;
