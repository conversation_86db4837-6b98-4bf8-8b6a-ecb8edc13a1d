import { <PERSON>ll, Sciss<PERSON>, <PERSON><PERSON><PERSON>, Setting<PERSON> } from 'lucide-react';
import React, { useEffect } from 'react';


import {
  ConditionCauses,
  ConditionHero,
  ConditionSymptoms,
  ConditionTreatment,
  NerveAnatomySection,
  TreatmentComparisonSection,
  ExerciseGuideSection,
  ErgonomicGuidanceSection,
  WarningSignsSection
} from '@/components/medical-conditions/shared';
import ConditionOverviewSection from '@/components/medical-conditions/shared/ConditionOverviewSection';
import ConditionQuickFacts from '@/components/medical-conditions/shared/ConditionQuickFacts';
import StandardPageLayout from '@/components/StandardPageLayout';
import { carpalTunnelSyndromeData } from '@/data/conditions/carpalTunnelSyndrome';

/**
 * Transform enhanced treatment data to legacy ConditionTreatment format
 */
const transformTreatmentData = () => {
  const conservativeOptions = carpalTunnelSyndromeData.treatmentOptions.conservative.map((option) => ({
    icon: option.name.includes('Splinting') ? Settings :
          option.name.includes('Injection') ? Pill :
          Dumbbell,
    title: option.name,
    description: option.description,
    procedures: option.advantages,
    effectiveness: option.effectiveness.includes('60-70%') ? 'moderate' as const :
                  option.effectiveness.includes('70-80%') ? 'high' as const :
                  'variable' as const,
    recovery: option.duration,
    risks: option.limitations
  }));

  const surgicalOptions = carpalTunnelSyndromeData.treatmentOptions.surgical.map((option) => ({
    icon: Scissors,
    title: option.name,
    description: option.description,
    procedures: option.advantages,
    effectiveness: option.outcomes.includes('90-95%') ? 'high' as const :
                  option.outcomes.includes('85-90%') ? 'high' as const :
                  'moderate' as const,
    recovery: option.technique,
    risks: option.risks
  }));

  return { conservativeOptions, surgicalOptions };
};

/**
 * Transform enhanced symptoms data to legacy ConditionSymptoms format
 */
const transformSymptomsData = () => {
  return carpalTunnelSyndromeData.symptoms.map((category) => ({
    title: category.category,
    icon: category.icon,
    description: `Common ${category.category.toLowerCase()} experienced by patients with carpal tunnel syndrome.`,
    symptoms: category.symptoms.map((symptom) =>
      `${symptom.name}: ${symptom.description}`
    ),
    severity: category.symptoms[0]?.severity || 'moderate',
    frequency: 'common'
  }));
};

/**
 * Carpal Tunnel Syndrome Component
 * Comprehensive guide to carpal tunnel syndrome following established patterns
 * Reuses shared medical condition components for consistency
 */
const CarpalTunnelSyndrome: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const { conservativeOptions, surgicalOptions } = transformTreatmentData();
  const transformedSymptoms = transformSymptomsData();

  return (
    <StandardPageLayout 
      title="Carpal Tunnel Syndrome - Comprehensive Guide" 
      showHeader={false}
    >
      <main className="flex-1 pt-20">
        <ConditionHero
          title={carpalTunnelSyndromeData.hero.title}
          subtitle={carpalTunnelSyndromeData.hero.subtitle}
          backgroundImage={carpalTunnelSyndromeData.hero.backgroundImage}
          badge={carpalTunnelSyndromeData.hero.badge}
        />

        <ConditionQuickFacts facts={carpalTunnelSyndromeData.quickFacts} />

        <ConditionOverviewSection
          title={carpalTunnelSyndromeData.overview.title}
          description={carpalTunnelSyndromeData.overview.description}
          keyPoints={carpalTunnelSyndromeData.overview.keyPoints}
          imageSrc={carpalTunnelSyndromeData.overview.imageSrc}
          imageAlt={carpalTunnelSyndromeData.overview.imageAlt}
          imageCaption={carpalTunnelSyndromeData.overview.imageCaption}
        />

        <ConditionCauses
          causes={carpalTunnelSyndromeData.causes}
        />

        <ConditionSymptoms
          symptomCategories={transformedSymptoms}
        />

        <ConditionTreatment
          conservativeOptions={conservativeOptions}
          surgicalOptions={surgicalOptions}
        />

        <NerveAnatomySection
          title={carpalTunnelSyndromeData.nerveAnatomy.title}
          description={carpalTunnelSyndromeData.nerveAnatomy.description}
          anatomyPoints={carpalTunnelSyndromeData.nerveAnatomy.anatomyPoints}
          interactiveFeatures={carpalTunnelSyndromeData.nerveAnatomy.interactiveFeatures}
        />

        <TreatmentComparisonSection
          title={carpalTunnelSyndromeData.treatmentComparison.title}
          description={carpalTunnelSyndromeData.treatmentComparison.description}
          comparisonTable={carpalTunnelSyndromeData.treatmentComparison.comparisonTable}
        />

        <ExerciseGuideSection
          title={carpalTunnelSyndromeData.exerciseGuides.title}
          description={carpalTunnelSyndromeData.exerciseGuides.description}
          exerciseCategories={carpalTunnelSyndromeData.exerciseGuides.exerciseCategories}
        />

        <ErgonomicGuidanceSection
          title={carpalTunnelSyndromeData.ergonomicGuidance.title}
          description={carpalTunnelSyndromeData.ergonomicGuidance.description}
          workplaceSetup={carpalTunnelSyndromeData.ergonomicGuidance.workplaceSetup}
          dailyHabits={carpalTunnelSyndromeData.ergonomicGuidance.dailyHabits}
        />

        <WarningSignsSection
          title={carpalTunnelSyndromeData.warningSignsSection.title}
          description={carpalTunnelSyndromeData.warningSignsSection.description}
          emergencySigns={carpalTunnelSyndromeData.warningSignsSection.emergencySigns}
        />
      </main>
    </StandardPageLayout>
  );
};

CarpalTunnelSyndrome.displayName = 'CarpalTunnelSyndrome';

export default CarpalTunnelSyndrome;
