import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import { 
  SpinalAnatomySection, 
  EmergencyAssessmentTool, 
  CESEmergencyProtocol, 
  RedFlagsWarningSection 
} from '@/components/medical-conditions/cauda-equina-syndrome';
import {
  ConditionHero,
  ConditionOverviewSection,
  ConditionQuickFacts
} from '@/components/medical-conditions/shared';
import StandardPageLayout from '@/components/StandardPageLayout';
import { caudaEquinaSyndromeData } from '@/data/conditions/caudaEquinaSyndrome';
import { useScrollToTop } from '@/hooks/useScrollToTop';

const CaudaEquinaSyndrome: React.FC = () => {
  useScrollToTop();

  useEffect(() => {
    // Track page view for analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'GA_MEASUREMENT_ID', {
        page_title: 'Cauda Equina Syndrome Guide',
        page_location: window.location.href,
      });
    }
  }, []);

  return (
    <>
      <Helmet>
        <title>Cauda Equina Syndrome: Emergency Patient Guide | miNEURO</title>
        <meta 
          name="description" 
          content="Emergency guide to cauda equina syndrome: causes, symptoms, diagnosis, and urgent treatment options. Expert neurosurgical care with emergency surgical intervention and comprehensive rehabilitation." 
        />
        <meta 
          name="keywords" 
          content="cauda equina syndrome, spinal emergency, bladder dysfunction, saddle anaesthesia, emergency surgery, spinal decompression, neurosurgery, Melbourne neurosurgeon" 
        />
        <meta name="author" content="Dr. Ales Aliashkevich" />
        <meta property="og:title" content="Cauda Equina Syndrome: Emergency Patient Guide | miNEURO" />
        <meta 
          property="og:description" 
          content="Emergency guide to cauda equina syndrome covering causes, symptoms, diagnosis, and urgent surgical treatment options with expert neurosurgical care." 
        />
        <meta property="og:type" content="article" />
        <meta property="og:url" content="https://mineuro.com.au/patient-resources/conditions/cauda-equina-syndrome" />
        <meta property="og:image" content="https://mineuro.com.au/images/neurological-conditions/cauda-equina-syndrome-guide-og.jpg" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Cauda Equina Syndrome: Emergency Patient Guide" />
        <meta name="twitter:description" content="Emergency guide to cauda equina syndrome with expert neurosurgical insights and urgent treatment options." />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/conditions/cauda-equina-syndrome" />
        
        {/* Structured Data for Medical Emergency Content */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "MedicalWebPage",
            "name": "Cauda Equina Syndrome: Emergency Patient Guide",
            "description": "Emergency guide to cauda equina syndrome: causes, symptoms, diagnosis, and urgent treatment options",
            "url": "https://mineuro.com.au/patient-resources/conditions/cauda-equina-syndrome",
            "mainEntity": {
              "@type": "MedicalCondition",
              "name": "Cauda Equina Syndrome",
              "alternateName": ["CES", "Cauda Equina Compression"],
              "description": "Rare but serious neurological condition requiring emergency surgical intervention to prevent permanent disability",
              "symptom": [
                "Bladder dysfunction",
                "Bowel dysfunction", 
                "Saddle anaesthesia",
                "Bilateral leg weakness",
                "Severe back pain"
              ],
              "riskFactor": [
                "Large central disc herniation",
                "Spinal stenosis",
                "Spinal trauma",
                "Spinal tumours"
              ],
              "medicalSpecialty": "Neurosurgery"
            },
            "author": {
              "@type": "Person",
              "name": "Dr. Ales Aliashkevich",
              "jobTitle": "Neurosurgeon",
              "affiliation": {
                "@type": "Organization",
                "name": "miNEURO Brain and Spine Surgery"
              }
            },
            "datePublished": "2024-01-01",
            "dateModified": new Date().toISOString().split('T')[0],
            "publisher": {
              "@type": "Organization",
              "name": "miNEURO Brain and Spine Surgery",
              "url": "https://mineuro.com.au"
            }
          })}
        </script>
      </Helmet>

      <StandardPageLayout 
        title="Cauda Equina Syndrome - Emergency Guide" 
        showHeader={false}
      >
        <main className="flex-1 pt-20">
          {/* Hero Section */}
          <ConditionHero
            title={caudaEquinaSyndromeData.hero.title}
            subtitle={caudaEquinaSyndromeData.hero.subtitle}
            backgroundImage={caudaEquinaSyndromeData.hero.backgroundImage}
            badge={caudaEquinaSyndromeData.hero.badge}
            showAssessment={true}
            showBooking={true}
            assessmentLink="#emergency-assessment"
            bookingLink="/appointments"
          />

          {/* Quick Facts */}
          <ConditionQuickFacts facts={caudaEquinaSyndromeData.quickFacts} />

          {/* Overview Section */}
          <ConditionOverviewSection
            title={caudaEquinaSyndromeData.overview.title}
            description={caudaEquinaSyndromeData.overview.description}
            keyPoints={caudaEquinaSyndromeData.overview.keyPoints}
            imageSrc={caudaEquinaSyndromeData.overview.imageSrc}
            imageAlt={caudaEquinaSyndromeData.overview.imageAlt}
            imageCaption={caudaEquinaSyndromeData.overview.imageCaption}
          />

          {/* Red Flags Warning Section */}
          <RedFlagsWarningSection
            title={caudaEquinaSyndromeData.redFlags.title}
            description={caudaEquinaSyndromeData.redFlags.description}
            emergencySymptoms={caudaEquinaSyndromeData.redFlags.emergencySymptoms}
          />

          {/* Emergency Assessment Tool */}
          <div id="emergency-assessment">
            <EmergencyAssessmentTool />
          </div>

          {/* Spinal Anatomy */}
          <SpinalAnatomySection
            title={caudaEquinaSyndromeData.anatomy.title}
            description={caudaEquinaSyndromeData.anatomy.description}
            spinalComponents={caudaEquinaSyndromeData.anatomy.spinalComponents}
          />

          {/* Types and Classifications */}
          <section className="py-20 bg-gradient-to-br from-background via-background/95 to-muted/30">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className="text-enhanced-heading text-3xl lg:text-enhanced-heading text-4xl font-bold mb-6 text-foreground">{caudaEquinaSyndromeData.types.title}</h2>
                <p className="text-lg text-foreground/80 max-w-4xl mx-auto leading-relaxed">
                  {caudaEquinaSyndromeData.types.description}
                </p>
              </div>

              <div className="grid gap-10 md:grid-cols-2 lg:grid-cols-4">
                {caudaEquinaSyndromeData.types.classifications.map((type, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30 border border-border/50">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="font-bold text-xl text-foreground leading-tight">{type.type}</h3>
                      <span className={`text-xs px-3 py-2 rounded-full font-bold ${
                        type.type.includes('Acute') ? 'bg-muted dark:bg-muted/50 text-foreground dark:text-foreground' :
                        type.type.includes('Chronic') ? 'bg-info dark:bg-info/50 text-info dark:text-info' :
                        type.type.includes('Incomplete') ? 'bg-info dark:bg-info/50 text-info dark:text-info' :
                        'bg-muted dark:bg-muted text-muted-foreground dark:text-muted-foreground'
                      }`}>
                        {type.urgency}
                      </span>
                    </div>
                    <p className="text-foreground/80 mb-6 text-base leading-relaxed">{type.description}</p>

                    <div className="content-spacing">
                      <div className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-md">
                        <h4 className="font-bold text-sm text-foreground mb-4">Key Features:</h4>
                        <ul className="space-y-3">
                          {type.characteristics.slice(0, 3).map((char, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                              <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                              <span className="leading-relaxed font-medium">{char}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="space-y-4 pt-2 border-t border-border/30">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-foreground/70 font-medium">Timeframe:</span>
                          <span className="text-sm font-bold text-foreground">{type.timeframe}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-foreground/70 font-medium">Prognosis:</span>
                          <span className="text-sm font-bold text-foreground">{type.prognosis.split(' ').slice(0, 3).join(' ')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Causes and Risk Factors */}
          <section className="py-20 bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20 border-y border-border/50">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className="text-enhanced-heading text-3xl lg:text-enhanced-heading text-4xl font-bold mb-6 text-foreground">{caudaEquinaSyndromeData.causes.title}</h2>
                <p className="text-lg text-foreground/80 max-w-4xl mx-auto leading-relaxed">
                  {caudaEquinaSyndromeData.causes.description}
                </p>
              </div>

              <div className="space-y-12">
                {caudaEquinaSyndromeData.causes.etiologies.map((etiology, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300 border border-border/50">
                    <h3 className="font-bold text-2xl mb-8 text-foreground">{etiology.category}</h3>
                    <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                      {etiology.causes.map((cause, idx) => (
                        <div key={idx} className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-6 border border-border/30 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105">
                          <div className="flex items-center justify-between mb-4">
                            <h4 className="font-bold text-foreground text-lg leading-tight">{cause.cause}</h4>
                            <span className="text-xs px-3 py-2 rounded-full bg-muted-light dark:bg-muted-light text-foreground font-bold">
                              {cause.frequency}
                            </span>
                          </div>
                          <p className="text-base text-foreground/80 mb-4 leading-relaxed">{cause.description}</p>
                          <div className="bg-info/80 dark:bg-info/30 border border-info/70 dark:border-info/50 rounded-lg p-3">
                            <p className="text-sm text-info dark:text-info font-medium">
                              <strong>Mechanism:</strong> {cause.mechanism}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Symptoms Section */}
          <section className="py-20 bg-gradient-to-br from-background via-background/95 to-muted/30">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className="text-enhanced-heading text-3xl lg:text-enhanced-heading text-4xl font-bold mb-6 text-foreground">Symptoms and Clinical Presentation</h2>
                <p className="text-lg text-foreground/80 max-w-4xl mx-auto leading-relaxed">
                  Cauda equina syndrome presents with a characteristic pattern of symptoms affecting bladder, bowel, sexual, and leg function.
                </p>
              </div>

              <div className="grid gap-10 md:grid-cols-3">
                {caudaEquinaSyndromeData.symptoms.map((category, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30 border border-border/50">
                    <h3 className="flex items-center gap-4 font-bold text-xl mb-8 text-foreground">
                      <div className="p-3 rounded-xl bg-primary/10 border border-primary/20">
                        <category.icon className="h-6 w-6 text-primary" />
                      </div>
                      {category.category}
                    </h3>
                    <div className="content-spacing">
                      {category.symptoms.map((symptom, idx) => (
                        <div key={idx} className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-md">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-bold text-foreground text-lg">{symptom.name}</h4>
                            <span className={`text-xs px-3 py-2 rounded-full font-bold ${
                              symptom.severity === 'severe' ? 'bg-muted dark:bg-muted/50 text-foreground dark:text-foreground' :
                              symptom.severity === 'moderate' ? 'bg-info dark:bg-info/50 text-info dark:text-info' :
                              'bg-info dark:bg-info/50 text-info dark:text-info'
                            }`}>
                              {symptom.severity}
                            </span>
                          </div>
                          <p className="text-base text-foreground/80 mb-3 leading-relaxed">{symptom.description}</p>
                          <div className="space-y-2">
                            <p className="text-sm text-foreground/70 font-medium">Frequency: <span className="font-bold">{symptom.frequency}</span></p>
                            <p className="text-sm text-foreground/70 font-medium">Significance: <span className="font-bold">{symptom.significance}</span></p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Diagnosis Section */}
          <section className="py-20 bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20 border-y border-border/50">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className="text-enhanced-heading text-3xl lg:text-enhanced-heading text-4xl font-bold mb-6 text-foreground">{caudaEquinaSyndromeData.diagnosis.title}</h2>
                <p className="text-lg text-foreground/80 max-w-4xl mx-auto leading-relaxed">
                  {caudaEquinaSyndromeData.diagnosis.description}
                </p>
              </div>

              {/* Clinical Assessment */}
              <div className="mb-20">
                <h3 className="text-enhanced-heading text-2xl lg:text-enhanced-heading text-3xl font-bold mb-12 text-center text-foreground">Clinical Assessment</h3>
                <div className="grid gap-10 md:grid-cols-2">
                  {caudaEquinaSyndromeData.diagnosis.clinicalAssessment.map((assessment, index) => (
                    <div key={index} className="medical-card/90 backdrop-blur-sm rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30 border border-border/50">
                      <h4 className="font-bold text-xl mb-6 text-foreground">{assessment.assessment}</h4>
                      <p className="text-foreground/80 mb-8 text-base leading-relaxed">{assessment.description}</p>

                      <div className="content-spacing">
                        <div className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-md">
                          <h5 className="font-bold text-sm text-foreground mb-4">Key Findings:</h5>
                          <ul className="space-y-3">
                            {assessment.findings.map((finding, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                                <span className="leading-relaxed font-medium">{finding}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="bg-info/80 dark:bg-info/30 border border-info/70 dark:border-info/50 rounded-xl p-5 shadow-md">
                          <h5 className="font-bold text-sm text-info dark:text-info mb-3">Clinical Significance</h5>
                          <p className="text-base text-info dark:text-info font-semibold leading-relaxed">{assessment.significance}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Investigations */}
              <div>
                <h3 className="text-enhanced-heading text-2xl lg:text-enhanced-heading text-3xl font-bold mb-12 text-center text-foreground">Emergency Investigations</h3>
                <div className="grid gap-10 lg:grid-cols-3">
                  {caudaEquinaSyndromeData.diagnosis.investigations.map((investigation, index) => (
                    <div key={index} className="medical-card/90 backdrop-blur-sm rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30 border border-border/50">
                      <h4 className="font-bold text-xl mb-6 text-foreground">{investigation.test}</h4>
                      <p className="text-foreground/80 mb-8 text-base leading-relaxed">{investigation.description}</p>

                      <div className="content-spacing">
                        <div className="bg-muted/80 dark:bg-muted/30 border border-border/70 dark:border-border rounded-xl p-5 shadow-md">
                          <h5 className="font-bold text-sm text-foreground dark:text-foreground mb-3">Urgency</h5>
                          <p className="text-base text-foreground dark:text-foreground font-semibold leading-relaxed">{investigation.urgency}</p>
                        </div>

                        <div className="bg-success/80 dark:bg-success/30 border border-success/70 dark:border-success/50 rounded-xl p-5 shadow-md">
                          <h5 className="font-bold text-sm text-success dark:text-success mb-3">Accuracy</h5>
                          <p className="text-base text-success dark:text-success font-semibold leading-relaxed">{investigation.accuracy}</p>
                        </div>

                        <div>
                          <h5 className="font-medium text-sm mb-2">Key Findings:</h5>
                          <ul className="space-y-1">
                            {investigation.findings.slice(0, 3).map((finding, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-info rounded-full mt-1.5 flex-shrink-0" />
                                {finding}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Emergency Management Protocol */}
          <CESEmergencyProtocol
            title={caudaEquinaSyndromeData.emergencyManagement.title}
            description={caudaEquinaSyndromeData.emergencyManagement.description}
            protocol={caudaEquinaSyndromeData.emergencyManagement.protocol}
            treatments={caudaEquinaSyndromeData.treatmentModalities.treatments}
          />

          {/* Surgical Options */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{caudaEquinaSyndromeData.surgicalOptions.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {caudaEquinaSyndromeData.surgicalOptions.description}
                </p>
              </div>

              <div className="grid gap-8 lg:grid-cols-3">
                {caudaEquinaSyndromeData.surgicalOptions.procedures.map((procedure, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{procedure.name}</h3>
                    <p className="text-muted-foreground mb-4">{procedure.description}</p>

                    <div className="content-spacing-sm">
                      <div className="bg-info-light border border-info/30 rounded p-3">
                        <h4 className="font-medium text-sm text-info mb-1">Technique</h4>
                        <p className="text-sm text-foreground">{procedure.technique}</p>
                      </div>

                      <div className="grid gap-4 md:grid-cols-1">
                        <div>
                          <h4 className="font-medium text-sm mb-2">Indications:</h4>
                          <ul className="space-y-1">
                            {procedure.indications.slice(0, 3).map((indication, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                                {indication}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-sm mb-2">Advantages:</h4>
                          <ul className="space-y-1">
                            {procedure.advantages.slice(0, 3).map((advantage, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                                {advantage}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-sm mb-2">Risks:</h4>
                          <ul className="space-y-1">
                            {procedure.risks.slice(0, 3).map((risk, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-muted rounded-full mt-1.5 flex-shrink-0" />
                                {risk}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      <div className="flex justify-between items-center pt-2 border-t border-border">
                        <span className="text-enhanced-muted text-xs">Success Rate:</span>
                        <span className="text-xs font-medium">{procedure.successRate}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Prognosis and Recovery */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{caudaEquinaSyndromeData.prognosis.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {caudaEquinaSyndromeData.prognosis.description}
                </p>
              </div>

              {/* Prognostic Factors */}
              <div className="mb-12">
                <h3 className="text-enhanced-heading text-2xl font-bold mb-6 text-center">Factors Affecting Recovery</h3>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                  {caudaEquinaSyndromeData.prognosis.factors.map((factor, index) => (
                    <div key={index} className="bg-background rounded-lg p-6 shadow-md border text-center">
                      <h4 className="font-semibold text-lg mb-2">{factor.factor}</h4>
                      <div className="mb-3">
                        <span className={`text-xs px-2 py-1 rounded ${
                          factor.impact === 'Most critical factor' ? 'badge-emergency' :
                          factor.impact === 'Major prognostic factor' ? 'badge-info' :
                          'badge-routine'
                        }`}>
                          {factor.impact}
                        </span>
                      </div>
                      <p className="text-enhanced-muted text-sm">{factor.description}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Recovery Outcomes */}
              <div>
                <h3 className="text-enhanced-heading text-2xl font-bold mb-6 text-center">Expected Recovery Outcomes</h3>
                <div className="grid gap-6 md:grid-cols-3">
                  {caudaEquinaSyndromeData.prognosis.outcomes.map((outcome, index) => (
                    <div key={index} className="medical-card p-6">
                      <h4 className="font-semibold text-xl mb-3">{outcome.timeframe}</h4>
                      <div className="mb-4">
                        <span className={`text-sm px-3 py-1 rounded ${
                          outcome.timeframe.includes('<48') ? 'badge-routine' :
                          outcome.timeframe.includes('48-72') ? 'badge-info' :
                          'badge-emergency'
                        }`}>
                          {outcome.expectedOutcome}
                        </span>
                      </div>
                      <div className="space-y-2">
                        <h5 className="font-medium text-sm">Recovery Factors:</h5>
                        <ul className="space-y-1">
                          {outcome.factors.map((factor, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                              {factor}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Prevention and Risk Reduction */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{caudaEquinaSyndromeData.prevention.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {caudaEquinaSyndromeData.prevention.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {caudaEquinaSyndromeData.prevention.strategies.map((strategy, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{strategy.category}</h3>
                    <div className="space-y-3 mb-4">
                      <ul className="space-y-2">
                        {strategy.strategies.map((item, idx) => (
                          <li key={idx} className="text-sm flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div className="bg-info-light border border-info/30 rounded p-3">
                      <h4 className="font-medium text-sm text-info mb-1">Effectiveness</h4>
                      <p className="text-sm text-foreground">{strategy.effectiveness}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Living with CES */}
          <section className="py-20 bg-gradient-to-br from-background via-background/95 to-muted/30">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className="text-enhanced-heading text-3xl lg:text-enhanced-heading text-4xl font-bold mb-6 text-foreground">{caudaEquinaSyndromeData.livingWithCES.title}</h2>
                <p className="text-lg text-foreground/80 max-w-4xl mx-auto leading-relaxed">
                  {caudaEquinaSyndromeData.livingWithCES.description}
                </p>
              </div>

              <div className="grid gap-10 md:grid-cols-3">
                {caudaEquinaSyndromeData.livingWithCES.sections.map((section, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30 border border-border/50">
                    <h3 className="font-bold text-xl mb-6 text-foreground">{section.title}</h3>
                    <div className="space-y-4 mb-8">
                      {section.content.map((paragraph, idx) => (
                        <p key={idx} className="text-base text-foreground/80 leading-relaxed">{paragraph}</p>
                      ))}
                    </div>
                    <div className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-md">
                      <h4 className="font-bold text-sm text-foreground mb-4">Practical Tips:</h4>
                      <ul className="space-y-3">
                        {section.tips.map((tip, idx) => (
                          <li key={idx} className="text-sm text-foreground/90 flex items-start gap-3">
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <span className="leading-relaxed font-medium">{tip}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Follow-up Care */}
          <section className="py-20 bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20 border-y border-border/50">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className="text-enhanced-heading text-3xl lg:text-enhanced-heading text-4xl font-bold mb-6 text-foreground">{caudaEquinaSyndromeData.followUpCare.title}</h2>
                <p className="text-lg text-foreground/80 max-w-4xl mx-auto leading-relaxed">
                  {caudaEquinaSyndromeData.followUpCare.description}
                </p>
              </div>

              <div className="grid gap-10 md:grid-cols-3">
                {caudaEquinaSyndromeData.followUpCare.monitoring.map((timeframe, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30 border border-border/50">
                    <h3 className="font-bold text-xl mb-6 text-foreground">{timeframe.timeframe}</h3>
                    <p className="text-base text-foreground/80 mb-8 leading-relaxed">{timeframe.purpose}</p>
                    <div className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-md">
                      <h4 className="font-bold text-sm text-foreground mb-4">Required Procedures:</h4>
                      <ul className="space-y-3">
                        {timeframe.procedures.map((procedure, idx) => (
                          <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <span className="leading-relaxed font-medium">{procedure}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Support Resources */}
          <section className="py-20 bg-gradient-to-br from-background via-background/95 to-muted/30">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className="text-enhanced-heading text-3xl lg:text-enhanced-heading text-4xl font-bold mb-6 text-foreground">{caudaEquinaSyndromeData.supportResources.title}</h2>
                <p className="text-lg text-foreground/80 max-w-4xl mx-auto leading-relaxed">
                  {caudaEquinaSyndromeData.supportResources.description}
                </p>
              </div>

              <div className="grid gap-10 md:grid-cols-3">
                {caudaEquinaSyndromeData.supportResources.resources.map((resourceCategory, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30 border border-border/50">
                    <h3 className="font-bold text-xl mb-8 text-foreground">{resourceCategory.category}</h3>
                    <div className="content-spacing">
                      {resourceCategory.items.map((item, idx) => (
                        <div key={idx} className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-md">
                          <h4 className="font-bold mb-3 text-foreground">{item.name}</h4>
                          <p className="text-base text-foreground/80 mb-3 leading-relaxed">{item.description}</p>
                          {item.contact && (
                            <div className="bg-primary/10 border border-primary/20 rounded-lg p-3">
                              <p className="text-sm text-primary font-semibold">{item.contact}</p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Warning Signs */}
          <section className="py-16 bg-muted">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{caudaEquinaSyndromeData.warningSigns.title}</h2>
                <p className="text-lg text-foreground max-w-3xl mx-auto">
                  {caudaEquinaSyndromeData.warningSigns.description}
                </p>
              </div>

              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {caudaEquinaSyndromeData.warningSigns.emergencySigns.map((sign, index) => (
                  <div key={index} className="bg-background rounded-lg p-6 shadow-md border border-border">
                    <h3 className="font-semibold text-lg mb-2 text-foreground">{sign.sign}</h3>
                    <p className="text-sm text-foreground mb-3">{sign.description}</p>
                    <div className="bg-muted border border-border rounded p-3">
                      <p className="text-sm font-medium text-foreground">{sign.action}</p>
                    </div>
                    <div className="mt-2">
                      <span className={`text-xs px-2 py-1 rounded ${
                        sign.urgency === 'immediate' ? 'bg-muted text-primary-foreground' :
                        sign.urgency === 'urgent' ? 'bg-primary text-primary-foreground' :
                        'bg-primary text-primary-foreground'
                      }`}>
                        {sign.urgency.toUpperCase()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        </main>
      </StandardPageLayout>
    </>
  );
};

export default CaudaEquinaSyndrome;
