import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import {
  AneurysmAnatomySection,
  TreatmentComparison,
  WarningSignsSection,
  RiskAssessmentTool
} from '@/components/medical-conditions/cerebral-aneurysm';
import {
  ConditionHero,
  ConditionOverviewSection,
  ConditionQuickFacts
} from '@/components/medical-conditions/shared';
import StandardPageLayout from '@/components/StandardPageLayout';
import { cerebralAneurysmData } from '@/data/conditions/cerebralAneurysm';
import { useScrollToTop } from '@/hooks/useScrollToTop';

const CerebralAneurysm: React.FC = () => {
  useScrollToTop();

  useEffect(() => {
    // Track page view for analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'GA_MEASUREMENT_ID', {
        page_title: 'Cerebral Aneurysm Guide',
        page_location: window.location.href,
      });
    }
  }, []);

  return (
    <>
      <Helmet>
        <title>Cerebral Aneurysm: Comprehensive Patient Guide | miNEURO</title>
        <meta 
          name="description" 
          content="Complete guide to cerebral aneurysms: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques and comprehensive monitoring." 
        />
        <meta 
          name="keywords" 
          content="cerebral aneurysm, brain aneurysm, aneurysm clipping, endovascular coiling, subarachnoid haemorrhage, neurosurgery, Melbourne neurosurgeon" 
        />
        <meta name="author" content="Dr. Ales Aliashkevich" />
        <meta property="og:title" content="Cerebral Aneurysm: Comprehensive Patient Guide | miNEURO" />
        <meta 
          property="og:description" 
          content="Expert guide to cerebral aneurysms covering causes, symptoms, diagnosis, and advanced treatment options including surgical and endovascular interventions." 
        />
        <meta property="og:type" content="article" />
        <meta property="og:url" content="https://mineuro.com.au/patient-resources/conditions/cerebral-aneurysm" />
        <meta property="og:image" content="https://mineuro.com.au/images/neurological-conditions/cerebral-aneurysm-guide-og.jpg" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Cerebral Aneurysm: Comprehensive Patient Guide" />
        <meta name="twitter:description" content="Complete guide to cerebral aneurysms with expert neurosurgical insights and treatment options." />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/conditions/cerebral-aneurysm" />
        
        {/* Structured Data for Medical Content */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "MedicalWebPage",
            "name": "Cerebral Aneurysm: Comprehensive Patient Guide",
            "description": "Complete guide to cerebral aneurysms: causes, symptoms, diagnosis, and treatment options",
            "url": "https://mineuro.com.au/patient-resources/conditions/cerebral-aneurysm",
            "mainEntity": {
              "@type": "MedicalCondition",
              "name": "Cerebral Aneurysm",
              "alternateName": ["Brain Aneurysm", "Intracranial Aneurysm"],
              "description": "Weakened area in blood vessel wall causing bulging, with potential for rupture",
              "symptom": [
                "Sudden severe headache",
                "Vision changes",
                "Neck stiffness",
                "Loss of consciousness"
              ],
              "riskFactor": [
                "Family history",
                "Hypertension",
                "Smoking",
                "Female gender",
                "Age over 40"
              ]
            },
            "author": {
              "@type": "Person",
              "name": "Dr. Ales Aliashkevich",
              "jobTitle": "Neurosurgeon",
              "affiliation": {
                "@type": "Organization",
                "name": "miNEURO Brain and Spine Surgery"
              }
            },
            "datePublished": "2024-01-01",
            "dateModified": new Date().toISOString().split('T')[0],
            "publisher": {
              "@type": "Organization",
              "name": "miNEURO Brain and Spine Surgery",
              "url": "https://mineuro.com.au"
            }
          })}
        </script>
      </Helmet>

      <StandardPageLayout 
        title="Cerebral Aneurysm - Comprehensive Guide" 
        showHeader={false}
      >
        <main className="flex-1 pt-20">
          {/* Hero Section */}
          <ConditionHero
            title={cerebralAneurysmData.hero.title}
            subtitle={cerebralAneurysmData.hero.subtitle}
            backgroundImage={cerebralAneurysmData.hero.backgroundImage}
            badge={cerebralAneurysmData.hero.badge}
            showAssessment={true}
            showBooking={true}
            assessmentLink="#risk-assessment"
            bookingLink="/appointments"
          />

          {/* Quick Facts */}
          <ConditionQuickFacts facts={cerebralAneurysmData.quickFacts} />

          {/* Overview Section */}
          <ConditionOverviewSection
            title={cerebralAneurysmData.overview.title}
            description={cerebralAneurysmData.overview.description}
            keyPoints={cerebralAneurysmData.overview.keyPoints}
            imageSrc={cerebralAneurysmData.overview.imageSrc}
            imageAlt={cerebralAneurysmData.overview.imageAlt}
            imageCaption={cerebralAneurysmData.overview.imageCaption}
          />

          {/* Risk Assessment Tool */}
          <div id="risk-assessment">
            <RiskAssessmentTool />
          </div>

          {/* Cerebral Blood Vessel Anatomy */}
          <AneurysmAnatomySection
            title={cerebralAneurysmData.anatomy.title}
            description={cerebralAneurysmData.anatomy.description}
            bloodVessels={cerebralAneurysmData.anatomy.bloodVessels}
          />

          {/* Types of Aneurysms */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAneurysmData.types.title}</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  {cerebralAneurysmData.types.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
                {cerebralAneurysmData.types.classifications.map((type, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="text-enhanced-heading font-semibold text-xl mb-3">{type.type}</h3>
                    <p className="text-enhanced-muted mb-4">{type.description}</p>

                    <div className="content-spacing-sm">
                      <div>
                        <h4 className="text-enhanced-strong font-medium text-sm mb-2">Characteristics:</h4>
                        <ul className="space-y-1">
                          {type.characteristics.map((char, idx) => (
                            <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                              {char}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="flex justify-between items-center pt-2 border-t border-border">
                        <span className="text-enhanced-muted text-xs">Prevalence:</span>
                        <span className="text-enhanced-strong text-xs font-medium">{type.prevalence}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Risk Factors */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAneurysmData.riskFactors.title}</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  {cerebralAneurysmData.riskFactors.description}
                </p>
              </div>

              <div className="grid gap-8 lg:grid-cols-2">
                {/* Modifiable Risk Factors */}
                <div className="medical-card p-6">
                  <h3 className="text-enhanced-heading font-semibold text-xl mb-4 text-success">Modifiable Risk Factors</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    These factors can be changed through lifestyle modifications and medical treatment
                  </p>
                  <div className="content-spacing-sm">
                    {cerebralAneurysmData.riskFactors.modifiable.map((factor, index) => (
                      <div key={index} className="border-l-4 border-success/30 pl-4">
                        <h4 className="text-enhanced-strong font-medium mb-1">{factor.factor}</h4>
                        <p className="text-enhanced-muted text-sm mb-2">{factor.description}</p>
                        <p className="text-enhanced-body text-xs text-success font-medium mb-2">Impact: {factor.impact}</p>
                        <div className="space-y-1">
                          {factor.management.slice(0, 2).map((management, idx) => (
                            <p key={idx} className="text-enhanced-muted text-xs">• {management}</p>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Non-Modifiable Risk Factors */}
                <div className="medical-card p-6">
                  <h3 className="text-enhanced-heading font-semibold text-xl mb-4 text-info">Non-Modifiable Risk Factors</h3>
                  <p className="text-enhanced-muted text-sm mb-4">
                    These factors cannot be changed but help identify individuals at higher risk
                  </p>
                  <div className="content-spacing-sm">
                    {cerebralAneurysmData.riskFactors.nonModifiable.map((factor, index) => (
                      <div key={index} className="border-l-4 border-info/30 pl-4">
                        <h4 className="text-enhanced-strong font-medium mb-1">{factor.factor}</h4>
                        <p className="text-enhanced-muted text-sm mb-2">{factor.description}</p>
                        <p className="text-enhanced-body text-xs text-info font-medium mb-2">Impact: {factor.impact}</p>
                        <div className="space-y-1">
                          {factor.considerations.slice(0, 2).map((consideration, idx) => (
                            <p key={idx} className="text-enhanced-muted text-xs">• {consideration}</p>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Symptoms Section */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">Symptoms and Warning Signs</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  Understanding the difference between unruptured and ruptured aneurysm symptoms is crucial for timely medical intervention.
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-2">
                {cerebralAneurysmData.symptoms.map((category, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="flex items-center gap-2 text-enhanced-heading font-semibold text-xl mb-4">
                      <category.icon className="h-5 w-5 text-primary" />
                      {category.category}
                    </h3>
                    <div className="content-spacing-sm">
                      {category.symptoms.map((symptom, idx) => (
                        <div key={idx} className="border-l-4 border-primary/30 pl-4">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="text-enhanced-strong font-medium">{symptom.name}</h4>
                            <span className={`text-xs px-2 py-1 rounded font-semibold ${
                              symptom.severity === 'severe' ? 'badge-emergency' :
                              symptom.severity === 'moderate' ? 'badge-info' :
                              'badge-routine'
                            }`}>
                              {symptom.severity}
                            </span>
                          </div>
                          <p className="text-enhanced-muted text-sm mb-1">{symptom.description}</p>
                          <p className="text-enhanced-muted text-xs">Frequency: {symptom.frequency}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Warning Signs Section */}
          <WarningSignsSection
            title={cerebralAneurysmData.warningSigns.title}
            description={cerebralAneurysmData.warningSigns.description}
            emergencySigns={cerebralAneurysmData.warningSigns.emergencySigns}
          />

          {/* Diagnosis Section */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAneurysmData.diagnosis.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralAneurysmData.diagnosis.description}
                </p>
              </div>
              
              <div className="grid gap-8 lg:grid-cols-3">
                {cerebralAneurysmData.diagnosis.procedures.map((procedure, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{procedure.name}</h3>
                    <p className="text-muted-foreground mb-4">{procedure.description}</p>
                    
                    <div className="content-spacing-sm">
                      <div className="bg-success-light border border-success/30 rounded p-3">
                        <h4 className="text-enhanced-heading font-medium text-sm text-success mb-1">Accuracy</h4>
                        <p className="text-enhanced-body text-sm text-success">{procedure.accuracy}</p>
                      </div>

                      <div>
                        <h4 className="text-enhanced-heading font-medium text-sm mb-2">Advantages:</h4>
                        <ul className="space-y-1">
                          {procedure.advantages.slice(0, 3).map((advantage, idx) => (
                            <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                              {advantage}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-sm mb-2">Limitations:</h4>
                        <ul className="space-y-1">
                          {procedure.limitations.slice(0, 2).map((limitation, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-info rounded-full mt-1.5 flex-shrink-0" />
                              {limitation}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Treatment Comparison */}
          <TreatmentComparison
            title={cerebralAneurysmData.treatmentModalities.title}
            description={cerebralAneurysmData.treatmentModalities.description}
            treatments={cerebralAneurysmData.treatmentModalities.treatments}
          />

          {/* Surgical Options */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAneurysmData.surgicalOptions.title}</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  {cerebralAneurysmData.surgicalOptions.description}
                </p>
              </div>

              <div className="grid gap-8 lg:grid-cols-3">
                {cerebralAneurysmData.surgicalOptions.procedures.map((procedure, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="text-enhanced-heading font-semibold text-xl mb-3">{procedure.name}</h3>
                    <p className="text-enhanced-muted mb-4">{procedure.description}</p>

                    <div className="content-spacing-sm">
                      <div className="bg-info-light border border-info/30 rounded p-3">
                        <h4 className="text-enhanced-heading font-medium text-sm text-info mb-1">Technique</h4>
                        <p className="text-enhanced-body text-sm text-info">{procedure.technique}</p>
                      </div>

                      <div>
                        <h4 className="text-enhanced-heading font-medium text-sm mb-2">Advantages:</h4>
                        <ul className="space-y-1">
                          {procedure.advantages.slice(0, 3).map((advantage, idx) => (
                            <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                              {advantage}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-enhanced-heading font-medium text-sm mb-2">Risks:</h4>
                        <ul className="space-y-1">
                          {procedure.risks.slice(0, 2).map((risk, idx) => (
                            <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-muted rounded-full mt-1.5 flex-shrink-0" />
                              {risk}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="flex justify-between items-center pt-2 border-t border-border">
                        <span className="text-enhanced-muted text-xs">Success Rate:</span>
                        <span className="text-xs font-medium">{procedure.successRate}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Prevention */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAneurysmData.prevention.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralAneurysmData.prevention.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cerebralAneurysmData.prevention.strategies.map((strategy, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{strategy.category}</h3>
                    <div className="space-y-3 mb-4">
                      {strategy.recommendations.map((recommendation, idx) => (
                        <div key={idx} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                          <span className="text-enhanced-body text-sm">{recommendation}</span>
                        </div>
                      ))}
                    </div>
                    <div className="bg-success-light border border-success/30 rounded p-3">
                      <h4 className="text-enhanced-heading font-medium text-sm text-success mb-1">Evidence Level</h4>
                      <p className="text-enhanced-body text-xs text-success">{strategy.evidence}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Living with Aneurysm */}
          <section className="section-spacing section-background-muted">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAneurysmData.livingWithAneurysm.title}</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  {cerebralAneurysmData.livingWithAneurysm.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cerebralAneurysmData.livingWithAneurysm.sections.map((section, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="text-enhanced-heading font-semibold text-xl mb-4">{section.title}</h3>
                    <div className="content-spacing-sm mb-4">
                      {section.content.map((paragraph, idx) => (
                        <p key={idx} className="text-enhanced-body text-sm">{paragraph}</p>
                      ))}
                    </div>
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Practical Tips:</h4>
                      <ul className="space-y-1">
                        {section.tips.map((tip, idx) => (
                          <li key={idx} className="text-xs text-muted-foreground flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                            {tip}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Follow-up Care */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAneurysmData.followUpCare.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralAneurysmData.followUpCare.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cerebralAneurysmData.followUpCare.monitoring.map((timeframe, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{timeframe.timeframe}</h3>
                    <p className="text-sm text-muted-foreground mb-4">{timeframe.purpose}</p>
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Required Procedures:</h4>
                      <ul className="space-y-1">
                        {timeframe.procedures.map((procedure, idx) => (
                          <li key={idx} className="text-sm flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                            {procedure}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Support Resources */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAneurysmData.supportResources.title}</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  {cerebralAneurysmData.supportResources.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cerebralAneurysmData.supportResources.resources.map((resourceCategory, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="text-enhanced-heading font-semibold text-xl mb-4">{resourceCategory.category}</h3>
                    <div className="content-spacing-sm">
                      {resourceCategory.items.map((item, idx) => (
                        <div key={idx} className="border-b border-border/30 pb-3 last:border-b-0">
                          <h4 className="text-enhanced-strong font-medium mb-1">{item.name}</h4>
                          <p className="text-sm text-muted-foreground mb-1">{item.description}</p>
                          {item.contact && (
                            <p className="text-xs text-primary">{item.contact}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        </main>
      </StandardPageLayout>
    </>
  );
};

export default CerebralAneurysm;
