import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import {
  CavernomaAnatomySection,
  CavernomaTreatmentComparison,
  CavernomaLocationsSection,
  CavernomaSymptomAssessment
} from '@/components/medical-conditions/cerebral-cavernoma';
import {
  ConditionHero,
  ConditionOverviewSection,
  ConditionQuickFacts
} from '@/components/medical-conditions/shared';
import StandardPageLayout from '@/components/StandardPageLayout';
import { cerebralCavernomaData } from '@/data/conditions/cerebralCavernoma';
import { useScrollToTop } from '@/hooks/useScrollToTop';

const CerebralCavernoma: React.FC = () => {
  useScrollToTop();

  useEffect(() => {
    // Track page view for analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'GA_MEASUREMENT_ID', {
        page_title: 'Cerebral Cavernoma Guide',
        page_location: window.location.href,
      });
    }
  }, []);

  return (
    <>
      <Helmet>
        <title>Cerebral Cavernoma: Comprehensive Patient Guide | miNEURO</title>
        <meta 
          name="description" 
          content="Complete guide to cerebral cavernomas (cavernous malformations): causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques and comprehensive monitoring." 
        />
        <meta 
          name="keywords" 
          content="cerebral cavernoma, cavernous malformation, cavernous angioma, brain vascular malformation, cavernoma surgery, brain bleeding, seizures, neurosurgery, Melbourne neurosurgeon" 
        />
        <meta name="author" content="Dr. Ales Aliashkevich" />
        <meta property="og:title" content="Cerebral Cavernoma: Comprehensive Patient Guide | miNEURO" />
        <meta 
          property="og:description" 
          content="Expert guide to cerebral cavernomas covering causes, symptoms, diagnosis, and advanced treatment options including surgical and conservative management approaches." 
        />
        <meta property="og:type" content="article" />
        <meta property="og:url" content="https://mineuro.com.au/patient-resources/conditions/cerebral-cavernoma" />
        <meta property="og:image" content="https://mineuro.com.au/images/neurological-conditions/cerebral-cavernoma-guide-og.jpg" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Cerebral Cavernoma: Comprehensive Patient Guide" />
        <meta name="twitter:description" content="Complete guide to cerebral cavernomas with expert neurosurgical insights and treatment options." />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/conditions/cerebral-cavernoma" />
        
        {/* Structured Data for Medical Content */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "MedicalWebPage",
            "name": "Cerebral Cavernoma: Comprehensive Patient Guide",
            "description": "Complete guide to cerebral cavernomas: causes, symptoms, diagnosis, and treatment options",
            "url": "https://mineuro.com.au/patient-resources/conditions/cerebral-cavernoma",
            "mainEntity": {
              "@type": "MedicalCondition",
              "name": "Cerebral Cavernoma",
              "alternateName": ["Cavernous Malformation", "Cavernous Angioma", "Cavernous Hemangioma"],
              "description": "Cluster of abnormal blood vessels in the brain that can cause bleeding and neurological symptoms",
              "symptom": [
                "Seizures",
                "Sudden severe headache",
                "Neurological deficits",
                "Bleeding symptoms",
                "Progressive symptoms"
              ],
              "riskFactor": [
                "Family history",
                "Genetic mutations",
                "Previous radiation therapy",
                "Multiple lesions"
              ]
            },
            "author": {
              "@type": "Person",
              "name": "Dr. Ales Aliashkevich",
              "jobTitle": "Neurosurgeon",
              "affiliation": {
                "@type": "Organization",
                "name": "miNEURO Brain and Spine Surgery"
              }
            },
            "datePublished": "2024-01-01",
            "dateModified": new Date().toISOString().split('T')[0],
            "publisher": {
              "@type": "Organization",
              "name": "miNEURO Brain and Spine Surgery",
              "url": "https://mineuro.com.au"
            }
          })}
        </script>
      </Helmet>

      <StandardPageLayout 
        title="Cerebral Cavernoma - Comprehensive Guide" 
        showHeader={false}
      >
        <main className="flex-1 pt-20">
          {/* Hero Section */}
          <ConditionHero
            title={cerebralCavernomaData.hero.title}
            subtitle={cerebralCavernomaData.hero.subtitle}
            backgroundImage={cerebralCavernomaData.hero.backgroundImage}
            badge={cerebralCavernomaData.hero.badge}
            showAssessment={true}
            showBooking={true}
            assessmentLink="#symptom-assessment"
            bookingLink="/appointments"
          />

          {/* Quick Facts */}
          <ConditionQuickFacts facts={cerebralCavernomaData.quickFacts} />

          {/* Overview Section */}
          <ConditionOverviewSection
            title={cerebralCavernomaData.overview.title}
            description={cerebralCavernomaData.overview.description}
            keyPoints={cerebralCavernomaData.overview.keyPoints}
            imageSrc={cerebralCavernomaData.overview.imageSrc}
            imageAlt={cerebralCavernomaData.overview.imageAlt}
            imageCaption={cerebralCavernomaData.overview.imageCaption}
          />

          {/* Symptom Assessment Tool */}
          <div id="symptom-assessment">
            <CavernomaSymptomAssessment />
          </div>

          {/* Vascular Anatomy */}
          <CavernomaAnatomySection
            title={cerebralCavernomaData.anatomy.title}
            description={cerebralCavernomaData.anatomy.description}
            vascularStructure={cerebralCavernomaData.anatomy.vascularStructure}
          />

          {/* Types and Classifications */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralCavernomaData.types.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralCavernomaData.types.description}
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-3">
                {cerebralCavernomaData.types.classifications.map((type, index) => (
                  <div key={index} className="medical-card p-6">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-enhanced-heading font-semibold text-xl">{type.type}</h3>
                      <span className={`text-xs px-2 py-1 rounded ${
                        type.type.includes('Sporadic') ? 'badge-info' :
                        type.type.includes('Familial') ? 'badge-medical' :
                        'badge-info'
                      }`}>
                        {type.prevalence.split(' ')[0]}
                      </span>
                    </div>
                    <p className="text-muted-foreground mb-4">{type.description}</p>
                    
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-medium text-sm mb-2">Characteristics:</h4>
                        <ul className="space-y-1">
                          {type.characteristics.slice(0, 3).map((char, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                              {char}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div className="space-y-2 pt-2 border-t border-border">
                        <div className="flex justify-between items-center">
                          <span className="text-enhanced-muted text-xs">Risk Level:</span>
                          <span className="text-xs font-medium">{type.riskLevel}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-enhanced-muted text-xs">Treatment:</span>
                          <span className="text-xs font-medium">{type.treatmentApproach.split(' ').slice(0, 2).join(' ')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Common Locations */}
          <CavernomaLocationsSection
            title={cerebralCavernomaData.locations.title}
            description={cerebralCavernomaData.locations.description}
            commonSites={cerebralCavernomaData.locations.commonSites}
          />

          {/* Symptoms Section */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">Symptoms and Clinical Presentation</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  Cavernoma symptoms depend on location, size, and bleeding history. Many are discovered incidentally before symptoms develop.
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-3">
                {cerebralCavernomaData.symptoms.map((category, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="flex items-center gap-2 font-semibold text-xl mb-4">
                      <category.icon className="h-5 w-5 text-primary" />
                      {category.category}
                    </h3>
                    <div className="content-spacing-sm">
                      {category.symptoms.map((symptom, idx) => (
                        <div key={idx} className="border-l-4 border-primary/20 pl-4">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="text-enhanced-strong font-medium">{symptom.name}</h4>
                            <span className={`text-xs px-2 py-1 rounded ${
                              symptom.severity === 'severe' ? 'badge-emergency' :
                              symptom.severity === 'moderate' ? 'badge-routine' :
                              'badge-routine'
                            }`}>
                              {symptom.severity}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground mb-1">{symptom.description}</p>
                          <p className="text-enhanced-muted text-xs">Frequency: {symptom.frequency}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Warning Signs Section */}
          <section className="py-16 bg-muted">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{cerebralCavernomaData.warningSigns.title}</h2>
                <p className="text-lg text-foreground max-w-3xl mx-auto">
                  {cerebralCavernomaData.warningSigns.description}
                </p>
              </div>
              
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {cerebralCavernomaData.warningSigns.emergencySigns.map((sign, index) => (
                  <div key={index} className="bg-background rounded-lg p-6 shadow-md border border-border">
                    <h3 className="font-semibold text-lg mb-2 text-foreground">{sign.sign}</h3>
                    <p className="text-sm text-foreground mb-3">{sign.description}</p>
                    <div className="bg-muted border border-border rounded p-3">
                      <p className="text-sm font-medium text-foreground">{sign.action}</p>
                    </div>
                    <div className="mt-2">
                      <span className={`text-xs px-2 py-1 rounded ${
                        sign.urgency === 'immediate' ? 'bg-muted text-primary-foreground' :
                        sign.urgency === 'urgent' ? 'bg-primary text-primary-foreground' :
                        'bg-primary text-primary-foreground'
                      }`}>
                        {sign.urgency.toUpperCase()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Diagnosis Section */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralCavernomaData.diagnosis.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralCavernomaData.diagnosis.description}
                </p>
              </div>

              <div className="grid gap-8 lg:grid-cols-3">
                {cerebralCavernomaData.diagnosis.procedures.map((procedure, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{procedure.name}</h3>
                    <p className="text-muted-foreground mb-4">{procedure.description}</p>

                    <div className="space-y-3">
                      <div className="bg-success-light border border-success/30 rounded p-3">
                        <h4 className="font-medium text-sm text-success mb-1">Accuracy</h4>
                        <p className="text-sm text-success">{procedure.accuracy}</p>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Advantages:</h4>
                        <ul className="space-y-1">
                          {procedure.advantages.slice(0, 3).map((advantage, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                              {advantage}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Limitations:</h4>
                        <ul className="space-y-1">
                          {procedure.limitations.slice(0, 2).map((limitation, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-info rounded-full mt-1.5 flex-shrink-0" />
                              {limitation}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Treatment Comparison */}
          <CavernomaTreatmentComparison
            title={cerebralCavernomaData.treatmentModalities.title}
            description={cerebralCavernomaData.treatmentModalities.description}
            treatments={cerebralCavernomaData.treatmentModalities.treatments}
          />

          {/* Surgical Options */}
          <section className="py-16 bg-info">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralCavernomaData.surgicalOptions.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralCavernomaData.surgicalOptions.description}
                </p>
              </div>

              <div className="grid gap-8 lg:grid-cols-3">
                {cerebralCavernomaData.surgicalOptions.procedures.map((procedure, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{procedure.name}</h3>
                    <p className="text-muted-foreground mb-4">{procedure.description}</p>

                    <div className="content-spacing-sm">
                      <div className="bg-info-light border border-info/30 rounded p-3">
                        <h4 className="font-medium text-sm text-info mb-1">Technique</h4>
                        <p className="text-sm text-foreground">{procedure.technique}</p>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Advantages:</h4>
                        <ul className="space-y-1">
                          {procedure.advantages.slice(0, 3).map((advantage, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                              {advantage}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Risks:</h4>
                        <ul className="space-y-1">
                          {procedure.risks.slice(0, 2).map((risk, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-muted rounded-full mt-1.5 flex-shrink-0" />
                              {risk}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="flex justify-between items-center pt-2 border-t border-border">
                        <span className="text-enhanced-muted text-xs">Success Rate:</span>
                        <span className="text-xs font-medium">{procedure.successRate}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Radiation Therapy */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralCavernomaData.radiationTherapy.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralCavernomaData.radiationTherapy.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-1 max-w-4xl mx-auto">
                {cerebralCavernomaData.radiationTherapy.techniques.map((technique, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{technique.name}</h3>
                    <p className="text-muted-foreground mb-4">{technique.description}</p>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <h4 className="font-medium text-sm mb-2">Indications:</h4>
                        <ul className="space-y-1">
                          {technique.indications.map((indication, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                              {indication}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Limitations:</h4>
                        <ul className="space-y-1">
                          {technique.limitations.slice(0, 3).map((limitation, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-info rounded-full mt-1.5 flex-shrink-0" />
                              {limitation}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    <div className="mt-4 p-4 bg-info border border-info rounded">
                      <h4 className="font-medium text-sm text-info mb-2">Important Note</h4>
                      <p className="text-sm text-info">
                        Radiation therapy is generally not recommended for cavernomas as it may increase bleeding risk.
                        It is only considered in very rare circumstances when surgery is not possible.
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Follow-up Care */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralCavernomaData.followUpCare.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralCavernomaData.followUpCare.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cerebralCavernomaData.followUpCare.monitoring.map((timeframe, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{timeframe.timeframe}</h3>
                    <p className="text-sm text-muted-foreground mb-4">{timeframe.purpose}</p>
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Required Procedures:</h4>
                      <ul className="space-y-1">
                        {timeframe.procedures.map((procedure, idx) => (
                          <li key={idx} className="text-sm flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                            {procedure}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Living with Cavernoma */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralCavernomaData.livingWithCavernoma.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralCavernomaData.livingWithCavernoma.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cerebralCavernomaData.livingWithCavernoma.sections.map((section, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{section.title}</h3>
                    <div className="space-y-3 mb-4">
                      {section.content.map((paragraph, idx) => (
                        <p key={idx} className="text-enhanced-muted text-sm">{paragraph}</p>
                      ))}
                    </div>
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Practical Tips:</h4>
                      <ul className="space-y-1">
                        {section.tips.map((tip, idx) => (
                          <li key={idx} className="text-xs text-muted-foreground flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                            {tip}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Support Resources */}
          <section className="py-16 bg-info">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralCavernomaData.supportResources.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralCavernomaData.supportResources.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cerebralCavernomaData.supportResources.resources.map((resourceCategory, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{resourceCategory.category}</h3>
                    <div className="content-spacing-sm">
                      {resourceCategory.items.map((item, idx) => (
                        <div key={idx} className="border-b border-border pb-3 last:border-b-0">
                          <h4 className="font-medium mb-1">{item.name}</h4>
                          <p className="text-sm text-muted-foreground mb-1">{item.description}</p>
                          {item.contact && (
                            <p className="text-xs text-primary">{item.contact}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        </main>
      </StandardPageLayout>
    </>
  );
};

export default CerebralCavernoma;
