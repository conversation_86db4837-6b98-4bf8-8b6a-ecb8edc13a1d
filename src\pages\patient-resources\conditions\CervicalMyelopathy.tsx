import { Brain } from 'lucide-react';
import React from 'react';
import { He<PERSON><PERSON> } from 'react-helmet-async';

import { 
  SpinalAnatomySection, 
  MyelopathySymptomAssessment, 
  SurgicalTreatmentComparison 
} from '@/components/medical-conditions/cervical-myelopathy';
import {
  ConditionHeroSection,
  ConditionOverviewSection,
  ConditionQuickFacts,
  ConditionCauses,
  ConditionSymptoms
} from '@/components/medical-conditions/shared';
import StandardPageLayout from '@/components/StandardPageLayout';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cervicalMyelopathyData } from '@/data/conditions/cervicalMyelopathy';
import { cn } from '@/lib/utils';

const CervicalMyelopathy: React.FC = () => {
  const deviceInfo = useDeviceDetection();

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "MedicalCondition",
    "name": "Cervical Myelopathy",
    "alternateName": ["Cervical Spinal Cord Compression", "Cervical Stenosis with Myelopathy"],
    "description": "Cervical myelopathy is a serious spinal condition that occurs when the spinal cord in the neck becomes compressed or damaged, affecting neurological function.",
    "code": {
      "@type": "MedicalCode",
      "code": "M50.0",
      "codingSystem": "ICD-10"
    },
    "associatedAnatomy": {
      "@type": "AnatomicalStructure",
      "name": "Cervical Spinal Cord"
    },
    "possibleTreatment": [
      {
        "@type": "MedicalTherapy",
        "name": "Anterior Cervical Discectomy and Fusion"
      },
      {
        "@type": "MedicalTherapy", 
        "name": "Posterior Cervical Laminectomy"
      },
      {
        "@type": "MedicalTherapy",
        "name": "Posterior Cervical Fusion"
      }
    ],
    "signOrSymptom": [
      "Hand weakness and clumsiness",
      "Gait disturbance",
      "Numbness and tingling",
      "Muscle weakness",
      "Balance problems"
    ],
    "riskFactor": [
      "Age over 50",
      "Cervical spondylosis",
      "Congenital cervical stenosis",
      "Previous neck trauma"
    ]
  };

  return (
    <>
      <Helmet>
        <title>Cervical Myelopathy: Comprehensive Patient Guide | miNEURO</title>
        <meta 
          name="description" 
          content="Comprehensive guide to cervical myelopathy: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques and comprehensive management for optimal neurological outcomes." 
        />
        <meta 
          name="keywords" 
          content="cervical myelopathy, spinal cord compression, cervical stenosis, ACDF, laminectomy, neurosurgery, spinal surgery, neck surgery, myelopathy treatment" 
        />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/conditions/cervical-myelopathy" />
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      </Helmet>

      <StandardPageLayout>
        <main className="min-h-screen">
          {/* Hero Section */}
          <ConditionHeroSection
            title={cervicalMyelopathyData.hero.title}
            subtitle={cervicalMyelopathyData.hero.subtitle}
            backgroundImage={cervicalMyelopathyData.hero.backgroundImage}
            badge={cervicalMyelopathyData.hero.badge}
          />

          {/* Quick Facts */}
          <ConditionQuickFacts
            facts={cervicalMyelopathyData.quickFacts}
          />

          {/* Overview */}
          <ConditionOverviewSection
            title={cervicalMyelopathyData.overview.title}
            description={cervicalMyelopathyData.overview.description}
            keyPoints={cervicalMyelopathyData.overview.keyPoints}
            imageSrc={cervicalMyelopathyData.overview.imageSrc}
            imageAlt={cervicalMyelopathyData.overview.imageAlt}
            imageCaption={cervicalMyelopathyData.overview.imageCaption}
          />

          {/* Spinal Anatomy Section */}
          <SpinalAnatomySection
            title={cervicalMyelopathyData.anatomy.title}
            description={cervicalMyelopathyData.anatomy.description}
            spinalStructures={cervicalMyelopathyData.anatomy.spinalStructures}
          />

          {/* Types and Classifications */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className={cn(
                  "text-enhanced-heading font-bold mb-6 leading-tight",
                  deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
                )}>
                  {cervicalMyelopathyData.types.title}
                </h2>
                <p className={cn(
                  "text-enhanced-body max-w-4xl mx-auto leading-relaxed",
                  deviceInfo.isMobile ? "text-base" : "text-lg"
                )}>
                  {cervicalMyelopathyData.types.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                {cervicalMyelopathyData.types.classifications.map((classification, index) => (
                  <div
                    key={index}
                    className="medical-card p-8 hover:scale-105 hover:border-primary/30"
                  >
                    <h3 className="text-enhanced-heading font-bold text-xl mb-4 leading-tight">
                      {classification.type}
                    </h3>
                    <p className="text-enhanced-muted mb-6 text-base leading-relaxed">
                      {classification.description}
                    </p>

                    <div className="content-spacing">
                      <div className="bg-primary/10 border border-primary/20 rounded-lg p-4">
                        <h4 className="text-enhanced-strong font-semibold text-sm text-primary mb-2">Prevalence</h4>
                        <p className="text-enhanced-body text-sm font-medium">{classification.prevalence}</p>
                      </div>

                      <div>
                        <h4 className="text-enhanced-strong font-semibold text-sm mb-3">Characteristics:</h4>
                        <ul className="space-y-2">
                          {classification.characteristics.slice(0, 3).map((characteristic, idx) => (
                            <li key={idx} className="text-enhanced-body text-sm flex items-start gap-3">
                              <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                              <span className="text-enhanced-body font-medium leading-relaxed">
                                {characteristic}
                              </span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="flex justify-between items-center pt-4 border-t border-border">
                        <span className="text-enhanced-muted text-sm font-medium">Prognosis:</span>
                        <span className="text-enhanced-strong text-sm font-semibold">{classification.prognosis}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Causes and Risk Factors */}
          <ConditionCauses
            title={cervicalMyelopathyData.causes.title}
            subtitle={cervicalMyelopathyData.causes.description}
            causes={cervicalMyelopathyData.causes.etiologies.map(etiology => ({
              icon: Brain, // Using Brain icon as default, could be customized per category
              title: etiology.category,
              description: `Common causes in the ${etiology.category.toLowerCase()} category`,
              details: etiology.causes.map(cause => `${cause.cause}: ${cause.description}`),
              severity: 'medium' as const
            }))}
            riskFactors={[
              { factor: "Age over 50", description: "Degenerative changes increase with age", modifiable: false },
              { factor: "Previous neck injury", description: "History of cervical spine trauma", modifiable: false },
              { factor: "Poor posture", description: "Prolonged forward head posture", modifiable: true },
              { factor: "Smoking", description: "Accelerates disc degeneration", modifiable: true },
              { factor: "Genetic factors", description: "Family history of spinal conditions", modifiable: false }
            ]}
          />

          {/* Symptoms */}
          <ConditionSymptoms
            symptomCategories={cervicalMyelopathyData.symptoms.map(category => ({
              icon: category.icon,
              title: category.category,
              description: `Common ${category.category.toLowerCase()} associated with cervical myelopathy`,
              symptoms: category.symptoms.map(symptom => `${symptom.name}: ${symptom.description}`),
              severity: 'moderate' as const,
              frequency: 'common' as const
            }))}
            warningSigns={cervicalMyelopathyData.emergencyGuidance?.redFlags?.map(flag => ({
              sign: flag.sign,
              description: flag.description,
              urgency: flag.urgency as 'immediate' | 'urgent' | 'monitor'
            })) || []}
          />

          {/* Symptom Assessment Tool */}
          <div id="symptom-assessment">
            <MyelopathySymptomAssessment />
          </div>

          {/* Diagnosis Section */}
          <section className="py-20 bg-gradient-to-br from-background via-background/95 to-muted/30">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className={cn(
                  "font-bold text-foreground mb-6 leading-tight",
                  deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
                )}>
                  {cervicalMyelopathyData.diagnosis.title}
                </h2>
                <p className={cn(
                  "text-foreground/80 max-w-4xl mx-auto leading-relaxed",
                  deviceInfo.isMobile ? "text-base" : "text-lg"
                )}>
                  {cervicalMyelopathyData.diagnosis.description}
                </p>
              </div>

              {/* Clinical Assessment */}
              <div className="mb-20">
                <h3 className={cn(
                  "font-bold text-foreground mb-12 text-center",
                  deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
                )}>
                  Clinical Assessment
                </h3>
                <div className="grid gap-10 md:grid-cols-3">
                  {cervicalMyelopathyData.diagnosis.clinicalAssessment.map((assessment, index) => (
                    <div
                      key={index}
                      className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30"
                    >
                      <h4 className="font-bold text-xl text-foreground mb-6 leading-tight">
                        {assessment.assessment}
                      </h4>
                      <p className="text-foreground/80 mb-8 text-base leading-relaxed">
                        {assessment.description}
                      </p>

                      <div className="content-spacing">
                        <div className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-md">
                          <h5 className="font-bold text-sm text-foreground mb-4">Key Findings:</h5>
                          <ul className="space-y-3">
                            {assessment.findings.map((finding, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-3">
                                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                                <span className="text-foreground font-semibold leading-relaxed">
                                  {finding}
                                </span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="bg-primary/15 dark:bg-primary/10 border border-primary/30 dark:border-primary/20 rounded-xl p-5 shadow-md">
                          <h5 className="font-bold text-sm text-primary dark:text-primary-foreground mb-3">Clinical Significance</h5>
                          <p className="text-sm text-foreground font-semibold leading-relaxed">{assessment.significance}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Investigations */}
              <div className="mb-20">
                <h3 className={cn(
                  "font-bold text-foreground mb-12 text-center",
                  deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
                )}>
                  Diagnostic Investigations
                </h3>
                <div className="grid gap-10 lg:grid-cols-3">
                  {cervicalMyelopathyData.diagnosis.investigations.map((investigation, index) => (
                    <div
                      key={index}
                      className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30"
                    >
                      <h4 className="font-bold text-xl text-foreground mb-6 leading-tight">
                        {investigation.test}
                      </h4>
                      <p className="text-foreground/80 mb-8 text-base leading-relaxed">
                        {investigation.description}
                      </p>

                      <div className="content-spacing">
                        <div className="bg-success/80 dark:bg-success/30 border border-success/70 dark:border-success/50 rounded-xl p-5 shadow-md">
                          <h5 className="font-bold text-sm text-success dark:text-success mb-3">Accuracy</h5>
                          <p className="text-sm text-success dark:text-success font-semibold leading-relaxed">
                            {investigation.accuracy}
                          </p>
                        </div>

                        <div className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-md">
                          <h5 className="font-bold text-sm text-foreground mb-4">Advantages:</h5>
                          <ul className="space-y-3">
                            {investigation.advantages.slice(0, 3).map((advantage, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                                <div className="w-2 h-2 bg-success dark:bg-success rounded-full mt-2 flex-shrink-0" />
                                <span className="leading-relaxed font-medium">{advantage}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-md">
                          <h5 className="font-bold text-sm text-foreground mb-4">Limitations:</h5>
                          <ul className="space-y-3">
                            {investigation.limitations.slice(0, 2).map((limitation, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                                <div className="w-2 h-2 bg-info dark:bg-info rounded-full mt-2 flex-shrink-0" />
                                <span className="leading-relaxed font-medium">{limitation}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Differential Diagnosis */}
              <div>
                <h3 className={cn(
                  "font-bold text-foreground mb-12 text-center",
                  deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
                )}>
                  Differential Diagnosis
                </h3>
                <div className="grid gap-10 md:grid-cols-3">
                  {cervicalMyelopathyData.diagnosis.differentialDiagnosis.map((condition, index) => (
                    <div
                      key={index}
                      className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30"
                    >
                      <h4 className="font-bold text-xl text-foreground mb-6 leading-tight">
                        {condition.condition}
                      </h4>
                      <div className="content-spacing">
                        <div className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-md">
                          <h5 className="font-bold text-sm text-foreground mb-4">Distinguishing Features:</h5>
                          <ul className="space-y-3">
                            {condition.distinguishingFeatures.map((feature, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                                <span className="leading-relaxed font-medium">{feature}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="bg-info/80 dark:bg-info/30 border border-info/70 dark:border-info/50 rounded-xl p-5 shadow-md">
                          <h5 className="font-bold text-sm text-info dark:text-info mb-3">Key Differences</h5>
                          <p className="text-sm text-info dark:text-info font-semibold leading-relaxed">
                            {condition.keyDifferences}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Treatment Modalities */}
          <section className="py-20 bg-gradient-to-br from-background via-background/95 to-muted/30">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className={cn(
                  "font-bold text-foreground mb-6 leading-tight",
                  deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
                )}>
                  {cervicalMyelopathyData.treatmentModalities.title}
                </h2>
                <p className={cn(
                  "text-muted-foreground max-w-4xl mx-auto leading-relaxed",
                  deviceInfo.isMobile ? "text-base" : "text-lg"
                )}>
                  {cervicalMyelopathyData.treatmentModalities.description}
                </p>
              </div>

              <div className="grid gap-8 lg:grid-cols-3">
                {cervicalMyelopathyData.treatmentModalities.treatments.map((treatment, index) => (
                  <div
                    key={index}
                    className="medical-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30"
                  >
                    <h3 className="font-bold text-xl text-foreground mb-4 leading-tight">
                      {treatment.name}
                    </h3>
                    <p className="text-muted-foreground mb-6 text-base leading-relaxed">
                      {treatment.description}
                    </p>

                    <div className="content-spacing">
                      <div className="bg-info/80 dark:bg-info/30 border border-info/70 dark:border-info/50 rounded-lg p-4">
                        <h4 className="font-semibold text-sm text-info dark:text-info mb-2">Success Rate</h4>
                        <p className="text-sm text-info dark:text-info font-medium leading-relaxed">
                          {treatment.successRate}
                        </p>
                      </div>

                      <div className="content-spacing-sm">
                        <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                          <h4 className="font-semibold text-sm text-foreground mb-3">Indications:</h4>
                          <ul className="space-y-2">
                            {treatment.indications.slice(0, 3).map((indication, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                                <span className="leading-relaxed">{indication}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                          <h4 className="font-semibold text-sm text-foreground mb-3">Advantages:</h4>
                          <ul className="space-y-2">
                            {treatment.advantages.slice(0, 3).map((advantage, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                                <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                                <span className="leading-relaxed">{advantage}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                          <h4 className="font-semibold text-sm text-foreground mb-3">Disadvantages:</h4>
                          <ul className="space-y-2">
                            {treatment.disadvantages.slice(0, 3).map((disadvantage, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                                <div className="w-2 h-2 bg-muted rounded-full mt-2 flex-shrink-0" />
                                <span className="leading-relaxed">{disadvantage}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      <div className="flex justify-between items-center pt-4 border-t border-border/50">
                        <span className="text-sm text-muted-foreground font-medium">Duration:</span>
                        <span className="text-sm font-bold text-foreground">{treatment.duration}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Surgical Options */}
          <SurgicalTreatmentComparison
            title={cervicalMyelopathyData.surgicalOptions.title}
            description={cervicalMyelopathyData.surgicalOptions.description}
            procedures={cervicalMyelopathyData.surgicalOptions.procedures}
          />

          {/* Conservative Management */}
          <section className="py-20 bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20 border-y border-border/50">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className={cn(
                  "font-bold text-foreground mb-6 leading-tight",
                  deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
                )}>
                  {cervicalMyelopathyData.conservativeManagement.title}
                </h2>
                <p className={cn(
                  "text-muted-foreground max-w-4xl mx-auto leading-relaxed",
                  deviceInfo.isMobile ? "text-base" : "text-lg"
                )}>
                  {cervicalMyelopathyData.conservativeManagement.description}
                </p>
              </div>

              <div className="grid gap-10 lg:grid-cols-3">
                {cervicalMyelopathyData.conservativeManagement.approaches.map((approach, index) => (
                  <div
                    key={index}
                    className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30"
                  >
                    <h3 className="font-bold text-xl text-foreground mb-6 leading-tight">
                      {approach.approach}
                    </h3>
                    <p className="text-foreground/80 mb-8 text-base leading-relaxed">
                      {approach.description}
                    </p>

                    <div className="content-spacing">
                      <div className="bg-success/80 dark:bg-success/30 border border-success/70 dark:border-success/50 rounded-xl p-5 shadow-md">
                        <h4 className="font-bold text-sm text-success dark:text-success mb-3">Effectiveness</h4>
                        <p className="text-sm text-success dark:text-success font-semibold leading-relaxed">
                          {approach.effectiveness}
                        </p>
                      </div>

                      <div className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-md">
                        <h4 className="font-bold text-sm text-foreground mb-4">Methods:</h4>
                        <ul className="space-y-3">
                          {approach.methods.slice(0, 3).map((method, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                              <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                              <span className="leading-relaxed font-medium">{method}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-md">
                        <h4 className="font-bold text-sm text-foreground mb-4">Limitations:</h4>
                        <ul className="space-y-3">
                          {approach.limitations.slice(0, 2).map((limitation, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                              <div className="w-2 h-2 bg-info dark:bg-info rounded-full mt-2 flex-shrink-0" />
                              <span className="leading-relaxed font-medium">{limitation}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Prognosis and Outcomes */}
          <section className="py-20 bg-gradient-to-br from-background via-background/95 to-muted/30">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className={cn(
                  "font-bold text-foreground mb-6 leading-tight",
                  deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
                )}>
                  {cervicalMyelopathyData.prognosis.title}
                </h2>
                <p className={cn(
                  "text-muted-foreground max-w-4xl mx-auto leading-relaxed",
                  deviceInfo.isMobile ? "text-base" : "text-lg"
                )}>
                  {cervicalMyelopathyData.prognosis.description}
                </p>
              </div>

              {/* Prognostic Factors */}
              <div className="mb-16">
                <h3 className={cn(
                  "font-bold text-foreground mb-8 text-center",
                  deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
                )}>
                  Factors Affecting Prognosis
                </h3>
                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
                  {cervicalMyelopathyData.prognosis.factors.map((factor, index) => (
                    <div
                      key={index}
                      className="medical-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 text-center"
                    >
                      <h4 className="font-bold text-lg text-foreground mb-4 leading-tight">
                        {factor.factor}
                      </h4>
                      <div className="mb-4">
                        <span className={`text-xs px-3 py-1.5 rounded-full font-semibold ${
                          factor.impact === 'Major prognostic factor' ? 'bg-muted/80 dark:bg-muted/50 text-foreground dark:text-foreground' :
                          factor.impact === 'Critical factor' ? 'bg-info/80 dark:bg-info/50 text-info dark:text-info' :
                          factor.impact === 'Significant factor' ? 'bg-info/80 dark:bg-info/50 text-info dark:text-info' :
                          'bg-info/80 dark:bg-info/50 text-info dark:text-info'
                        }`}>
                          {factor.impact}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground leading-relaxed">{factor.description}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Outcome Scenarios */}
              <div>
                <h3 className={cn(
                  "font-bold text-foreground mb-8 text-center",
                  deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
                )}>
                  Expected Outcomes
                </h3>
                <div className="grid gap-8 md:grid-cols-3">
                  {cervicalMyelopathyData.prognosis.outcomes.map((outcome, index) => (
                    <div
                      key={index}
                      className="medical-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                    >
                      <h4 className="font-bold text-xl text-foreground mb-4 leading-tight">
                        {outcome.scenario}
                      </h4>
                      <div className="mb-6">
                        <span className={`text-sm px-3 py-1.5 rounded-full font-semibold ${
                          outcome.scenario.includes('Early') ? 'bg-success/80 dark:bg-success/50 text-success dark:text-success' :
                          outcome.scenario.includes('Delayed') ? 'bg-info/80 dark:bg-info/50 text-info dark:text-info' :
                          'bg-info/80 dark:bg-info/50 text-info dark:text-info'
                        }`}>
                          {outcome.expectedOutcome}
                        </span>
                      </div>
                      <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                        <h5 className="font-semibold text-sm text-foreground mb-3">Key Factors:</h5>
                        <ul className="space-y-2">
                          {outcome.factors.map((factor, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                              <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                              <span className="leading-relaxed">{factor}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Living with Myelopathy */}
          <section className="py-20 bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20 border-y border-border/50">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className={cn(
                  "font-bold text-foreground mb-6 leading-tight",
                  deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
                )}>
                  {cervicalMyelopathyData.livingWithMyelopathy.title}
                </h2>
                <p className={cn(
                  "text-muted-foreground max-w-4xl mx-auto leading-relaxed",
                  deviceInfo.isMobile ? "text-base" : "text-lg"
                )}>
                  {cervicalMyelopathyData.livingWithMyelopathy.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cervicalMyelopathyData.livingWithMyelopathy.sections.map((section, index) => (
                  <div
                    key={index}
                    className="medical-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                  >
                    <h3 className="font-bold text-xl text-foreground mb-6 leading-tight">
                      {section.title}
                    </h3>
                    <div className="space-y-4 mb-6">
                      {section.content.map((paragraph, idx) => (
                        <p key={idx} className="text-sm text-foreground/90 leading-relaxed">
                          {paragraph}
                        </p>
                      ))}
                    </div>
                    <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                      <h4 className="font-semibold text-sm text-foreground mb-3">Practical Tips:</h4>
                      <ul className="space-y-2">
                        {section.tips.map((tip, idx) => (
                          <li key={idx} className="text-sm text-foreground/90 flex items-start gap-3">
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <span className="leading-relaxed">{tip}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Follow-up Care */}
          <section className="py-20 bg-gradient-to-br from-background via-background/95 to-muted/30">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className={cn(
                  "font-bold text-foreground mb-6 leading-tight",
                  deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
                )}>
                  {cervicalMyelopathyData.followUpCare.title}
                </h2>
                <p className={cn(
                  "text-muted-foreground max-w-4xl mx-auto leading-relaxed",
                  deviceInfo.isMobile ? "text-base" : "text-lg"
                )}>
                  {cervicalMyelopathyData.followUpCare.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cervicalMyelopathyData.followUpCare.monitoring.map((timeframe, index) => (
                  <div
                    key={index}
                    className="medical-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                  >
                    <h3 className="font-bold text-xl text-foreground mb-4 leading-tight">
                      {timeframe.timeframe}
                    </h3>
                    <p className="text-sm text-foreground/90 mb-6 leading-relaxed">
                      {timeframe.purpose}
                    </p>
                    <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                      <h4 className="font-semibold text-sm text-foreground mb-3">Required Procedures:</h4>
                      <ul className="space-y-2">
                        {timeframe.procedures.map((procedure, idx) => (
                          <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <span className="leading-relaxed">{procedure}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Support Resources */}
          <section className="py-20 bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20 border-y border-border/50">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className={cn(
                  "font-bold text-foreground mb-6 leading-tight",
                  deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
                )}>
                  {cervicalMyelopathyData.supportResources.title}
                </h2>
                <p className={cn(
                  "text-muted-foreground max-w-4xl mx-auto leading-relaxed",
                  deviceInfo.isMobile ? "text-base" : "text-lg"
                )}>
                  {cervicalMyelopathyData.supportResources.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cervicalMyelopathyData.supportResources.resources.map((resourceCategory, index) => (
                  <div
                    key={index}
                    className="medical-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                  >
                    <h3 className="font-bold text-xl text-foreground mb-6 leading-tight">
                      {resourceCategory.category}
                    </h3>
                    <div className="content-spacing-sm">
                      {resourceCategory.items.map((item, idx) => (
                        <div key={idx} className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4 border-b border-border/30 last:border-b-0">
                          <h4 className="font-semibold mb-2 text-foreground">{item.name}</h4>
                          <p className="text-sm text-foreground/90 mb-2 leading-relaxed">{item.description}</p>
                          {item.contact && (
                            <p className="text-sm text-primary font-medium">{item.contact}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Warning Signs */}
          <section className="py-20 bg-gradient-to-br from-info-light/80 via-info-light/60 to-info-light/80 dark:from-info-light/30 dark:via-info-light/20 dark:to-info-light/30 border-y border-border dark:border-border">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className={cn(
                  "font-bold mb-6 leading-tight text-foreground dark:text-foreground",
                  deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
                )}>
                  {cervicalMyelopathyData.warningSigns.title}
                </h2>
                <p className={cn(
                  "text-foreground dark:text-foreground max-w-4xl mx-auto leading-relaxed",
                  deviceInfo.isMobile ? "text-base" : "text-lg"
                )}>
                  {cervicalMyelopathyData.warningSigns.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
                {cervicalMyelopathyData.warningSigns.emergencySigns.map((sign, index) => (
                  <div
                    key={index}
                    className="bg-background/90 dark:bg-muted/90 backdrop-blur-sm border border-border/70 dark:border-border rounded-xl p-6 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105"
                  >
                    <h3 className="font-bold text-lg mb-3 text-foreground dark:text-foreground leading-tight">
                      {sign.sign}
                    </h3>
                    <p className="text-sm text-foreground dark:text-foreground mb-4 leading-relaxed">
                      {sign.description}
                    </p>
                    <div className="bg-muted/80 dark:bg-muted/50 border border-border/70 dark:border-border rounded-lg p-4 mb-4">
                      <p className="text-sm font-semibold text-foreground dark:text-foreground leading-relaxed">
                        {sign.action}
                      </p>
                    </div>
                    <div className="flex justify-center">
                      <span className={`text-xs px-3 py-1.5 rounded-full font-bold shadow-lg ${
                        sign.urgency === 'immediate' ? 'bg-muted text-primary-foreground' :
                        sign.urgency === 'urgent' ? 'bg-primary text-primary-foreground' :
                        'bg-primary text-primary-foreground'
                      }`}>
                        {sign.urgency.toUpperCase()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        </main>
      </StandardPageLayout>
    </>
  );
};

export default CervicalMyelopathy;
