import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import {
  BrainAnatomySection,
  ChiariSymptomAssessment,
  ChiariTreatmentComparison
} from '@/components/medical-conditions/chiari-malformation';
import {
  ConditionHero,
  ConditionOverviewSection,
  ConditionQuickFacts
} from '@/components/medical-conditions/shared';
import StandardPageLayout from '@/components/StandardPageLayout';
import { chiariMalformationData } from '@/data/conditions/chiariMalformation';
import { useScrollToTop } from '@/hooks/useScrollToTop';

const ChiariMalformation: React.FC = () => {
  useScrollToTop();

  useEffect(() => {
    // Track page view for analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'GA_MEASUREMENT_ID', {
        page_title: 'Chiari Malformation Guide',
        page_location: window.location.href,
      });
    }
  }, []);

  return (
    <>
      <Helmet>
        <title>Chiari Malformation: Comprehensive Patient Guide | miNEURO</title>
        <meta 
          name="description" 
          content="Comprehensive guide to Chiari malformation: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques and comprehensive management." 
        />
        <meta 
          name="keywords" 
          content="Chiari malformation, cerebellar tonsillar herniation, posterior fossa decompression, syringomyelia, neurosurgery, Melbourne neurosurgeon" 
        />
        <meta name="author" content="Dr. Ales Aliashkevich" />
        <meta property="og:title" content="Chiari Malformation: Comprehensive Patient Guide | miNEURO" />
        <meta 
          property="og:description" 
          content="Comprehensive guide to Chiari malformation covering causes, symptoms, diagnosis, and treatment options with expert neurosurgical care." 
        />
        <meta property="og:type" content="article" />
        <meta property="og:url" content="https://mineuro.com.au/patient-resources/conditions/chiari-malformation" />
        <meta property="og:image" content="https://mineuro.com.au/images/neurological-conditions/chiari-malformation-guide-og.jpg" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Chiari Malformation: Comprehensive Patient Guide" />
        <meta name="twitter:description" content="Comprehensive guide to Chiari malformation with expert neurosurgical insights and treatment options." />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/conditions/chiari-malformation" />
        
        {/* Structured Data for Medical Content */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "MedicalWebPage",
            "name": "Chiari Malformation: Comprehensive Patient Guide",
            "description": "Comprehensive guide to Chiari malformation: causes, symptoms, diagnosis, and treatment options",
            "url": "https://mineuro.com.au/patient-resources/conditions/chiari-malformation",
            "mainEntity": {
              "@type": "MedicalCondition",
              "name": "Chiari Malformation",
              "alternateName": ["Arnold-Chiari Malformation", "Cerebellar Tonsillar Herniation"],
              "description": "Structural defect in the cerebellum where brain tissue extends into the spinal canal",
              "symptom": [
                "Occipital headaches",
                "Balance and coordination problems",
                "Neck pain",
                "Arm weakness or numbness",
                "Difficulty swallowing"
              ],
              "riskFactor": [
                "Genetic factors",
                "Developmental abnormalities",
                "Small posterior fossa",
                "Neural tube defects"
              ],
              "medicalSpecialty": "Neurosurgery"
            },
            "author": {
              "@type": "Person",
              "name": "Dr. Ales Aliashkevich",
              "jobTitle": "Neurosurgeon",
              "affiliation": {
                "@type": "Organization",
                "name": "miNEURO Brain and Spine Surgery"
              }
            },
            "datePublished": "2024-01-01",
            "dateModified": new Date().toISOString().split('T')[0],
            "publisher": {
              "@type": "Organization",
              "name": "miNEURO Brain and Spine Surgery",
              "url": "https://mineuro.com.au"
            }
          })}
        </script>
      </Helmet>

      <StandardPageLayout 
        title="Chiari Malformation - Comprehensive Guide" 
        showHeader={false}
      >
        <main className="flex-1 pt-20">
          {/* Hero Section */}
          <ConditionHero
            title={chiariMalformationData.hero.title}
            subtitle={chiariMalformationData.hero.subtitle}
            backgroundImage={chiariMalformationData.hero.backgroundImage}
            badge={chiariMalformationData.hero.badge}
            showAssessment={true}
            showBooking={true}
            assessmentLink="#symptom-assessment"
            bookingLink="/appointments"
          />

          {/* Quick Facts */}
          <ConditionQuickFacts facts={chiariMalformationData.quickFacts} />

          {/* Overview Section */}
          <ConditionOverviewSection
            title={chiariMalformationData.overview.title}
            description={chiariMalformationData.overview.description}
            keyPoints={chiariMalformationData.overview.keyPoints}
            imageSrc={chiariMalformationData.overview.imageSrc}
            imageAlt={chiariMalformationData.overview.imageAlt}
            imageCaption={chiariMalformationData.overview.imageCaption}
          />

          {/* Brain Anatomy */}
          <BrainAnatomySection
            title={chiariMalformationData.anatomy.title}
            description={chiariMalformationData.anatomy.description}
            brainStructures={chiariMalformationData.anatomy.brainStructures}
          />

          {/* Types and Classifications */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{chiariMalformationData.types.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {chiariMalformationData.types.description}
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
                {chiariMalformationData.types.classifications.map((type, index) => (
                  <div key={index} className="medical-card p-6">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-enhanced-heading font-semibold text-lg">{type.type}</h3>
                      <span className={`text-xs px-2 py-1 rounded ${
                        type.type.includes('Type I') ? 'badge-info' :
                        type.type.includes('Type II') ? 'badge-info' :
                        type.type.includes('Type III') ? 'badge-emergency' :
                        'badge-info'
                      }`}>
                        {type.severity}
                      </span>
                    </div>
                    <p className="text-muted-foreground mb-4 text-sm">{type.description}</p>
                    
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-medium text-sm mb-2">Key Features:</h4>
                        <ul className="space-y-1">
                          {type.characteristics.slice(0, 3).map((char, idx) => (
                            <li key={idx} className="text-xs flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                              {char}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div className="space-y-2 pt-2 border-t border-border">
                        <div className="flex justify-between items-center">
                          <span className="text-enhanced-muted text-xs">Prevalence:</span>
                          <span className="text-xs font-medium">{type.prevalence}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-enhanced-muted text-xs">Prognosis:</span>
                          <span className="text-xs font-medium">{type.prognosis.split(' ').slice(0, 3).join(' ')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Causes and Risk Factors */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{chiariMalformationData.causes.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {chiariMalformationData.causes.description}
                </p>
              </div>
              
              <div className="content-spacing">
                {chiariMalformationData.causes.etiologies.map((etiology, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{etiology.category}</h3>
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                      {etiology.causes.map((cause, idx) => (
                        <div key={idx} className="border-l-4 border-info pl-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-enhanced-strong font-medium">{cause.cause}</h4>
                            <span className="text-xs px-2 py-1 rounded badge-info">
                              {cause.frequency}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">{cause.description}</p>
                          <p className="text-enhanced-muted text-xs">
                            <strong>Mechanism:</strong> {cause.mechanism}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Symptoms Section */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">Symptoms and Clinical Presentation</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  Chiari malformation symptoms vary widely, from no symptoms to severe neurological problems affecting daily life.
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-3">
                {chiariMalformationData.symptoms.map((category, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="flex items-center gap-2 font-semibold text-xl mb-4">
                      <category.icon className="h-5 w-5 text-info" />
                      {category.category}
                    </h3>
                    <div className="content-spacing-sm">
                      {category.symptoms.map((symptom, idx) => (
                        <div key={idx} className="border-l-4 border-info pl-4">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="text-enhanced-strong font-medium">{symptom.name}</h4>
                            <span className={`text-xs px-2 py-1 rounded ${
                              symptom.severity === 'severe' ? 'badge-emergency' :
                              symptom.severity === 'moderate' ? 'badge-info' :
                              'badge-routine'
                            }`}>
                              {symptom.severity}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground mb-1">{symptom.description}</p>
                          <p className="text-enhanced-muted text-xs">Frequency: {symptom.frequency}</p>
                          <p className="text-enhanced-muted text-xs">Age Group: {symptom.ageGroup}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Symptom Assessment Tool */}
          <div id="symptom-assessment">
            <ChiariSymptomAssessment />
          </div>

          {/* Diagnosis Section */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{chiariMalformationData.diagnosis.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {chiariMalformationData.diagnosis.description}
                </p>
              </div>

              {/* Clinical Assessment */}
              <div className="mb-12">
                <h3 className="text-enhanced-heading text-2xl font-bold mb-6 text-center">Clinical Assessment</h3>
                <div className="grid gap-6 md:grid-cols-3">
                  {chiariMalformationData.diagnosis.clinicalAssessment.map((assessment, index) => (
                    <div key={index} className="medical-card p-6">
                      <h4 className="font-semibold text-lg mb-3">{assessment.assessment}</h4>
                      <p className="text-muted-foreground mb-4 text-sm">{assessment.description}</p>

                      <div className="space-y-3">
                        <div>
                          <h5 className="font-medium text-sm mb-2">Key Findings:</h5>
                          <ul className="space-y-1">
                            {assessment.findings.map((finding, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                                {finding}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="bg-info-light border border-info/30 rounded p-3">
                          <h5 className="font-medium text-sm text-info mb-1">Clinical Significance</h5>
                          <p className="text-sm text-foreground">{assessment.significance}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Investigations */}
              <div className="mb-12">
                <h3 className="text-enhanced-heading text-2xl font-bold mb-6 text-center">Diagnostic Investigations</h3>
                <div className="grid gap-8 lg:grid-cols-3">
                  {chiariMalformationData.diagnosis.investigations.map((investigation, index) => (
                    <div key={index} className="medical-card p-6">
                      <h4 className="font-semibold text-xl mb-3">{investigation.test}</h4>
                      <p className="text-muted-foreground mb-4">{investigation.description}</p>

                      <div className="space-y-3">
                        <div className="bg-success-light border border-success/30 rounded p-3">
                          <h5 className="font-medium text-sm text-success mb-1">Accuracy</h5>
                          <p className="text-sm text-success">{investigation.accuracy}</p>
                        </div>

                        <div>
                          <h5 className="font-medium text-sm mb-2">Key Findings:</h5>
                          <ul className="space-y-1">
                            {investigation.findings.slice(0, 3).map((finding, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-info rounded-full mt-1.5 flex-shrink-0" />
                                {finding}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Differential Diagnosis */}
              <div>
                <h3 className="text-enhanced-heading text-2xl font-bold mb-6 text-center">Differential Diagnosis</h3>
                <div className="grid gap-6 md:grid-cols-3">
                  {chiariMalformationData.diagnosis.differentialDiagnosis.map((condition, index) => (
                    <div key={index} className="medical-card p-6">
                      <h4 className="font-semibold text-lg mb-3">{condition.condition}</h4>
                      <div className="space-y-3">
                        <div>
                          <h5 className="font-medium text-sm mb-2">Distinguishing Features:</h5>
                          <ul className="space-y-1">
                            {condition.distinguishingFeatures.map((feature, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-medical-blue rounded-full mt-1.5 flex-shrink-0" />
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="bg-medical-blue border border-medical-blue rounded p-3">
                          <h5 className="font-medium text-sm text-medical-blue mb-1">Key Differences</h5>
                          <p className="text-sm text-medical-blue">{condition.keyDifferences}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Treatment Comparison */}
          <ChiariTreatmentComparison
            title={chiariMalformationData.treatmentModalities.title}
            description={chiariMalformationData.treatmentModalities.description}
            treatments={chiariMalformationData.treatmentModalities.treatments}
          />

          {/* Surgical Options */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{chiariMalformationData.surgicalOptions.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {chiariMalformationData.surgicalOptions.description}
                </p>
              </div>

              <div className="grid gap-8 lg:grid-cols-3">
                {chiariMalformationData.surgicalOptions.procedures.map((procedure, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{procedure.name}</h3>
                    <p className="text-muted-foreground mb-4">{procedure.description}</p>

                    <div className="content-spacing-sm">
                      <div className="bg-info-light border border-info/30 rounded p-3">
                        <h4 className="font-medium text-sm text-info mb-1">Technique</h4>
                        <p className="text-sm text-foreground">{procedure.technique}</p>
                      </div>

                      <div className="grid gap-4 md:grid-cols-1">
                        <div>
                          <h4 className="font-medium text-sm mb-2">Indications:</h4>
                          <ul className="space-y-1">
                            {procedure.indications.slice(0, 3).map((indication, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                                {indication}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-sm mb-2">Advantages:</h4>
                          <ul className="space-y-1">
                            {procedure.advantages.slice(0, 3).map((advantage, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                                {advantage}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-sm mb-2">Risks:</h4>
                          <ul className="space-y-1">
                            {procedure.risks.slice(0, 3).map((risk, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-muted rounded-full mt-1.5 flex-shrink-0" />
                                {risk}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      <div className="flex justify-between items-center pt-2 border-t border-border">
                        <span className="text-enhanced-muted text-xs">Success Rate:</span>
                        <span className="text-xs font-medium">{procedure.successRate}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Conservative Management */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{chiariMalformationData.conservativeManagement.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {chiariMalformationData.conservativeManagement.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {chiariMalformationData.conservativeManagement.approaches.map((approach, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{approach.approach}</h3>
                    <p className="text-muted-foreground mb-4">{approach.description}</p>

                    <div className="content-spacing-sm">
                      <div>
                        <h4 className="font-medium text-sm mb-2">Indications:</h4>
                        <ul className="space-y-1">
                          {approach.indications.map((indication, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                              {indication}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Methods:</h4>
                        <ul className="space-y-1">
                          {approach.methods.map((method, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                              {method}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="bg-success-light border border-success/30 rounded p-3">
                        <h4 className="font-medium text-sm text-success mb-1">Effectiveness</h4>
                        <p className="text-sm text-success">{approach.effectiveness}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Prognosis and Outcomes */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{chiariMalformationData.prognosis.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {chiariMalformationData.prognosis.description}
                </p>
              </div>

              {/* Prognostic Factors */}
              <div className="mb-12">
                <h3 className="text-enhanced-heading text-2xl font-bold mb-6 text-center">Factors Affecting Prognosis</h3>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                  {chiariMalformationData.prognosis.factors.map((factor, index) => (
                    <div key={index} className="bg-background rounded-lg p-6 shadow-md border text-center">
                      <h4 className="font-semibold text-lg mb-2">{factor.factor}</h4>
                      <div className="mb-3">
                        <span className={`text-xs px-2 py-1 rounded ${
                          factor.impact === 'Major prognostic factor' ? 'badge-emergency' :
                          factor.impact === 'Significant factor' ? 'badge-info' :
                          'badge-routine'
                        }`}>
                          {factor.impact}
                        </span>
                      </div>
                      <p className="text-enhanced-muted text-sm">{factor.description}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Outcome Scenarios */}
              <div>
                <h3 className="text-enhanced-heading text-2xl font-bold mb-6 text-center">Expected Outcomes</h3>
                <div className="grid gap-6 md:grid-cols-3">
                  {chiariMalformationData.prognosis.outcomes.map((outcome, index) => (
                    <div key={index} className="medical-card p-6">
                      <h4 className="font-semibold text-xl mb-3">{outcome.scenario}</h4>
                      <div className="mb-4">
                        <span className={`text-sm px-3 py-1 rounded ${
                          outcome.scenario.includes('Asymptomatic') ? 'badge-routine' :
                          outcome.scenario.includes('Surgery') ? 'badge-info' :
                          'badge-info'
                        }`}>
                          {outcome.expectedOutcome}
                        </span>
                      </div>
                      <div className="space-y-2">
                        <h5 className="font-medium text-sm">Key Factors:</h5>
                        <ul className="space-y-1">
                          {outcome.factors.map((factor, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                              {factor}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Living with Chiari */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{chiariMalformationData.livingWithChiari.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {chiariMalformationData.livingWithChiari.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {chiariMalformationData.livingWithChiari.sections.map((section, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{section.title}</h3>
                    <div className="space-y-3 mb-4">
                      {section.content.map((paragraph, idx) => (
                        <p key={idx} className="text-enhanced-muted text-sm">{paragraph}</p>
                      ))}
                    </div>
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Practical Tips:</h4>
                      <ul className="space-y-1">
                        {section.tips.map((tip, idx) => (
                          <li key={idx} className="text-xs text-muted-foreground flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                            {tip}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Follow-up Care */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{chiariMalformationData.followUpCare.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {chiariMalformationData.followUpCare.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {chiariMalformationData.followUpCare.monitoring.map((timeframe, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{timeframe.timeframe}</h3>
                    <p className="text-sm text-muted-foreground mb-4">{timeframe.purpose}</p>
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Required Procedures:</h4>
                      <ul className="space-y-1">
                        {timeframe.procedures.map((procedure, idx) => (
                          <li key={idx} className="text-sm flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                            {procedure}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Support Resources */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{chiariMalformationData.supportResources.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {chiariMalformationData.supportResources.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {chiariMalformationData.supportResources.resources.map((resourceCategory, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{resourceCategory.category}</h3>
                    <div className="content-spacing-sm">
                      {resourceCategory.items.map((item, idx) => (
                        <div key={idx} className="border-b border-border pb-3 last:border-b-0">
                          <h4 className="font-medium mb-1">{item.name}</h4>
                          <p className="text-sm text-muted-foreground mb-1">{item.description}</p>
                          {item.contact && (
                            <p className="text-xs text-primary">{item.contact}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Warning Signs */}
          <section className="py-16 bg-info">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-info">{chiariMalformationData.warningSigns.title}</h2>
                <p className="text-lg text-info max-w-3xl mx-auto">
                  {chiariMalformationData.warningSigns.description}
                </p>
              </div>

              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {chiariMalformationData.warningSigns.emergencySigns.map((sign, index) => (
                  <div key={index} className="bg-background rounded-lg p-6 shadow-md border border-info">
                    <h3 className="font-semibold text-lg mb-2 text-info">{sign.sign}</h3>
                    <p className="text-sm text-info mb-3">{sign.description}</p>
                    <div className="bg-info border border-info rounded p-3">
                      <p className="text-sm font-medium text-info">{sign.action}</p>
                    </div>
                    <div className="mt-2">
                      <span className={`text-xs px-2 py-1 rounded ${
                        sign.urgency === 'immediate' ? 'bg-muted text-primary-foreground' :
                        sign.urgency === 'urgent' ? 'bg-primary text-primary-foreground' :
                        'bg-primary text-primary-foreground'
                      }`}>
                        {sign.urgency.toUpperCase()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        </main>
      </StandardPageLayout>
    </>
  );
};

export default ChiariMalformation;
