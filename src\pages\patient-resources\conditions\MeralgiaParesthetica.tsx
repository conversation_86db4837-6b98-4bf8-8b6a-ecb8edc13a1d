import React from 'react';

import MeralgiaAnatomySection from '@/components/medical-conditions/meralgia-paresthetica/MeralgiaAnatomySection';
import MeralgiaComparisonSection from '@/components/medical-conditions/meralgia-paresthetica/MeralgiaComparisonSection';
import MeralgiaErgonomicsSection from '@/components/medical-conditions/meralgia-paresthetica/MeralgiaErgonomicsSection';
import MeralgiaExerciseSection from '@/components/medical-conditions/meralgia-paresthetica/MeralgiaExerciseSection';
import MeralgiaWarningSignsSection from '@/components/medical-conditions/meralgia-paresthetica/MeralgiaWarningSignsSection';
import { 
  ConditionHero,
  ConditionQuickFacts,
  ConditionOverviewSection,
  ConditionCauses,
  ConditionSymptoms,
  ConditionTreatment
} from '@/components/medical-conditions/shared';
import { meralgiaParestheticaData } from '@/data/conditions/meralgia-paresthetica';

const MeralgiaParesthetica: React.FC = () => {
  const { info, quickFacts, causes, symptoms, treatments, diagnostics: _diagnostics, exercises: _exercises, prevention: _prevention, prognosis: _prognosis } = meralgiaParestheticaData;

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <ConditionHero
        title={info.title}
        subtitle={info.description}
        backgroundImage={info.heroImage.src}
        fallbackImage={info.heroImage.fallback}
        badge="Peripheral Nerve Conditions Library"
        showAssessment={true}
        showBooking={true}
        assessmentLink="#assessment"
        bookingLink="/contact"
      />

      {/* Quick Facts */}
      <ConditionQuickFacts
        title="Meralgia Paresthetica Quick Facts"
        subtitle="Essential information about lateral femoral cutaneous nerve entrapment"
        facts={quickFacts}
      />

      {/* Overview Section */}
      <ConditionOverviewSection
        title="Understanding Meralgia Paresthetica"
        description={[
          "Meralgia paresthetica is a condition characterized by numbness, tingling, and burning pain in the outer thigh. It occurs when the lateral femoral cutaneous nerve (LFCN) becomes compressed or entrapped, typically at the point where it passes under or through the inguinal ligament near the hip.",
          "This purely sensory nerve provides feeling to the skin on the outer thigh, and when compressed, it can cause a distinctive pattern of symptoms that affect the lateral thigh from the hip to just above the knee. The condition is also known as lateral femoral cutaneous nerve entrapment or Bernhardt-Roth syndrome.",
          "While meralgia paresthetica can be uncomfortable and concerning, it is generally not dangerous and often responds well to conservative treatment. Understanding the condition, its causes, and treatment options can help patients make informed decisions about their care and achieve the best possible outcomes."
        ]}
        keyPoints={[
          { text: "Affects the lateral femoral cutaneous nerve (LFCN)", icon: undefined },
          { text: "Causes numbness, tingling, and burning in outer thigh", icon: undefined },
          { text: "Most common in adults aged 30-60 years", icon: undefined },
          { text: "Often related to tight clothing, obesity, or pregnancy", icon: undefined },
          { text: "Excellent prognosis with appropriate treatment", icon: undefined },
          { text: "Conservative treatment successful in 70-80% of cases", icon: undefined }
        ]}
        imageSrc="https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
        imageAlt="Lateral femoral cutaneous nerve anatomy"
        fallbackImageSrc="https://images.pexels.com/photos/7659564/pexels-photo-7659564.jpeg?auto=compress&cs=tinysrgb&w=800"
        imageCaption="The lateral femoral cutaneous nerve pathway and typical distribution of symptoms"
      />

      {/* Specialised Nerve Anatomy Section */}
      <MeralgiaAnatomySection />

      {/* Causes and Risk Factors */}
      <ConditionCauses
        title="Causes and Risk Factors"
        subtitle="Understanding what leads to lateral femoral cutaneous nerve compression and how to identify your risk factors"
        causes={causes}
        riskFactors={[
          { factor: "Obesity (BMI >30)", description: "Increased abdominal pressure compresses the nerve", modifiable: true },
          { factor: "Pregnancy", description: "Anatomical changes and weight gain during pregnancy", modifiable: false },
          { factor: "Tight clothing/belts", description: "External pressure on the inguinal ligament area", modifiable: true },
          { factor: "Prolonged standing", description: "Occupational or lifestyle factors requiring extended standing", modifiable: true },
          { factor: "Age 30-60 years", description: "Peak incidence in middle-aged adults", modifiable: false },
          { factor: "Previous surgery", description: "Abdominal or pelvic surgery creating scar tissue", modifiable: false },
          { factor: "Diabetes mellitus", description: "Increased susceptibility to nerve compression", modifiable: true }
        ]}
      />

      {/* Symptoms and Warning Signs */}
      <ConditionSymptoms
        title="Signs and Symptoms"
        subtitle="Recognizing the characteristic symptoms of meralgia paresthetica and understanding their significance"
        symptomCategories={symptoms}
        warningSigns={[
          { sign: "Progressive muscle weakness", description: "New or worsening leg weakness", urgency: "immediate" },
          { sign: "Bilateral symptoms", description: "Symptoms affecting both legs", urgency: "immediate" },
          { sign: "Severe, uncontrolled pain", description: "Pain interfering with sleep or daily activities", urgency: "urgent" },
          { sign: "Spreading numbness", description: "Symptoms extending beyond typical distribution", urgency: "monitor" }
        ]}
      />

      {/* Comprehensive Treatment Comparison */}
      <MeralgiaComparisonSection />

      {/* Treatment Options */}
      <ConditionTreatment
        title="Treatment Approaches"
        subtitle="Comprehensive treatment options from conservative management to advanced interventions"
        conservativeOptions={treatments.filter(t => t.title !== 'Surgical Intervention')}
        surgicalOptions={treatments.filter(t => t.title === 'Surgical Intervention')}
        treatmentPhases={[
          {
            phase: "Phase 1",
            title: "Conservative Management",
            duration: "6-12 weeks",
            goals: ["Reduce nerve compression", "Improve symptoms", "Prevent progression"],
            treatments: ["Activity modification", "Weight loss", "Loose clothing", "Physical therapy"]
          },
          {
            phase: "Phase 2", 
            title: "Medical Intervention",
            duration: "4-8 weeks",
            goals: ["Pain relief", "Reduce inflammation", "Improve function"],
            treatments: ["Neuropathic medications", "Topical treatments", "Corticosteroid injections"]
          },
          {
            phase: "Phase 3",
            title: "Advanced Treatment",
            duration: "Variable",
            goals: ["Long-term relief", "Restore function", "Prevent recurrence"],
            treatments: ["Repeat injections", "Surgical consultation", "Nerve decompression"]
          }
        ]}
      />

      {/* Exercise and Physical Therapy Guide */}
      <MeralgiaExerciseSection />

      {/* Ergonomic and Lifestyle Recommendations */}
      <MeralgiaErgonomicsSection />

      {/* Warning Signs and When to Seek Help */}
      <MeralgiaWarningSignsSection />
    </div>
  );
};

export default MeralgiaParesthetica;
