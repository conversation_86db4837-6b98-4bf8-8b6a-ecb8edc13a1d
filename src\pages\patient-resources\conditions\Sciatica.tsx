import React, { useEffect } from 'react';

import { ConditionCauses } from '@/components/medical-conditions/shared/ConditionCauses';
import { ConditionHero } from '@/components/medical-conditions/shared/ConditionHero';
import ConditionOverviewSection from '@/components/medical-conditions/shared/ConditionOverviewSection';
import ConditionQuickFacts from '@/components/medical-conditions/shared/ConditionQuickFacts';
import { ConditionSymptoms } from '@/components/medical-conditions/shared/ConditionSymptoms';
import { ConditionTreatment } from '@/components/medical-conditions/shared/ConditionTreatment';
import StandardPageLayout from '@/components/StandardPageLayout';
import sciaticaData from '@/data/conditions/sciatica';

/**
 * Refactored Sciatica Component
 * 
 * Original component: 82KB
 * Refactored component: <100 lines
 * Reduction: ~95%
 */

const SciaticaRefactored: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout 
      title="Sciatica - Comprehensive Guide" 
      showHeader={false}
    >
      <main className="flex-1 pt-20">
        <ConditionHero
          title={sciaticaData.hero.title}
          subtitle={sciaticaData.hero.subtitle}
          backgroundImage={sciaticaData.hero.backgroundImage}
          badge={sciaticaData.hero.badge}
        />

        <ConditionQuickFacts facts={sciaticaData.quickFacts} />

        <ConditionOverviewSection
          title={sciaticaData.overview.title}
          description={sciaticaData.overview.description}
          keyPoints={sciaticaData.overview.keyPoints}
          imageSrc={sciaticaData.overview.imageSrc}
          imageAlt={sciaticaData.overview.imageAlt}
          imageCaption={sciaticaData.overview.imageCaption}
        />

        <ConditionCauses
          causes={sciaticaData.causes}
          riskFactors={sciaticaData.riskFactors}
        />

        <ConditionSymptoms
          symptomCategories={sciaticaData.symptoms}
          warningSigns={sciaticaData.warningSigns}
        />

        <ConditionTreatment
          conservativeOptions={sciaticaData.conservativeTreatments}
          surgicalOptions={sciaticaData.surgicalTreatments}
        />
      </main>
    </StandardPageLayout>
  );
};

SciaticaRefactored.displayName = 'SciaticaRefactored';

export default SciaticaRefactored;