import React, { useEffect } from 'react';

import { ConditionCauses } from '@/components/medical-conditions/shared/ConditionCauses';
import { ConditionHero } from '@/components/medical-conditions/shared/ConditionHero';
import ConditionOverviewSection from '@/components/medical-conditions/shared/ConditionOverviewSection';
import ConditionQuickFacts from '@/components/medical-conditions/shared/ConditionQuickFacts';
import { ConditionSymptoms } from '@/components/medical-conditions/shared/ConditionSymptoms';
import { ConditionTreatment } from '@/components/medical-conditions/shared/ConditionTreatment';
import StandardPageLayout from '@/components/StandardPageLayout';
import { spondylosisData } from '@/data/conditions/spondylosis';

/**
 * Refactored Spondylosis Component
 * 
 * Original component: 1,514 lines
 * Refactored component: <100 lines
 * Reduction: ~95%
 */

const SpondylosisRefactored: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout 
      title="Spondylosis - Comprehensive Guide" 
      showHeader={false}
    >
      <main className="flex-1 pt-20">
        <ConditionHero
          title={spondylosisData.hero.title}
          subtitle={spondylosisData.hero.subtitle}
          backgroundImage={spondylosisData.hero.backgroundImage}
          badge={spondylosisData.hero.badge}
        />

        <ConditionQuickFacts facts={spondylosisData.quickFacts} />

        <ConditionOverviewSection
          title={spondylosisData.overview.title}
          description={spondylosisData.overview.description}
          keyPoints={spondylosisData.overview.keyPoints}
          imageSrc={spondylosisData.overview.imageSrc}
          imageAlt={spondylosisData.overview.imageAlt}
          imageCaption={spondylosisData.overview.imageCaption}
        />

        <ConditionCauses
          causes={spondylosisData.causes}
          riskFactors={spondylosisData.riskFactors}
        />

        <ConditionSymptoms
          symptomCategories={spondylosisData.symptoms}
          warningSigns={spondylosisData.warningSigns}
        />

        <ConditionTreatment
          conservativeOptions={spondylosisData.conservativeTreatments}
          surgicalOptions={spondylosisData.surgicalTreatments}
        />
      </main>
    </StandardPageLayout>
  );
};

SpondylosisRefactored.displayName = 'SpondylosisRefactored';

export default SpondylosisRefactored;
