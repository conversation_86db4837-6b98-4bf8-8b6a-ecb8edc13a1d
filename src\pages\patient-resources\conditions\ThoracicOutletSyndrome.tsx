import React, { useEffect } from 'react';

import { ConditionCauses } from '@/components/medical-conditions/shared/ConditionCauses';
import { ConditionHero } from '@/components/medical-conditions/shared/ConditionHero';
import ConditionOverviewSection from '@/components/medical-conditions/shared/ConditionOverviewSection';
import ConditionQuickFacts from '@/components/medical-conditions/shared/ConditionQuickFacts';
import { ConditionSymptoms } from '@/components/medical-conditions/shared/ConditionSymptoms';
import { ConditionTreatment } from '@/components/medical-conditions/shared/ConditionTreatment';
import StandardPageLayout from '@/components/StandardPageLayout';
import { thoracicOutletSyndromeData } from '@/data/conditions/thoracicoutletsyndrome';

/**
 * Refactored Thoracic Outlet Syndrome Component
 * 
 * Original component: 1,519 lines
 * Refactored component: <100 lines
 * Reduction: ~95%
 */

const ThoracicOutletSyndromeRefactored: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout 
      title="Thoracic Outlet Syndrome - Comprehensive Guide" 
      showHeader={false}
    >
      <main className="flex-1 pt-20">
        <ConditionHero
          title={thoracicOutletSyndromeData.hero.title}
          subtitle={thoracicOutletSyndromeData.hero.subtitle}
          backgroundImage={thoracicOutletSyndromeData.hero.backgroundImage}
          badge={thoracicOutletSyndromeData.hero.badge}
        />

        <ConditionQuickFacts facts={thoracicOutletSyndromeData.quickFacts} />

        <ConditionOverviewSection
          title={thoracicOutletSyndromeData.overview.title}
          description={thoracicOutletSyndromeData.overview.description}
          keyPoints={thoracicOutletSyndromeData.overview.keyPoints}
          imageSrc={thoracicOutletSyndromeData.overview.imageSrc}
          imageAlt={thoracicOutletSyndromeData.overview.imageAlt}
          imageCaption={thoracicOutletSyndromeData.overview.imageCaption}
        />

        <ConditionCauses
          causes={thoracicOutletSyndromeData.causes}
          riskFactors={thoracicOutletSyndromeData.riskFactors}
        />

        <ConditionSymptoms
          symptomCategories={thoracicOutletSyndromeData.symptoms}
          warningSigns={thoracicOutletSyndromeData.warningSigns}
        />

        <ConditionTreatment
          conservativeOptions={thoracicOutletSyndromeData.conservativeTreatments}
          surgicalOptions={thoracicOutletSyndromeData.surgicalTreatments}
        />
      </main>
    </StandardPageLayout>
  );
};

ThoracicOutletSyndromeRefactored.displayName = 'ThoracicOutletSyndromeRefactored';

export default ThoracicOutletSyndromeRefactored;
