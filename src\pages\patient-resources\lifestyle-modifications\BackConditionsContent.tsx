import React from 'react';

import { AccordionSection } from '@/components/patient-resources';

export const BackConditionsContent: React.FC = () => {
  const backConditions = [
    {
      id: 'lumbar-disc-herniation',
      title: 'Lumbar Disc Herniation',
      content: (
        <div className="space-y-4">
          <p className="text-muted-foreground">
            Lumbar disc herniation occurs when the soft centre of a spinal disc pushes through a crack in the tougher exterior casing, potentially irritating nearby nerves and causing back pain and sciatica.
          </p>
          <h4 className="text-base font-semibold">Recommended Lifestyle Modifications:</h4>
          <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
            <li>Avoid prolonged sitting; take frequent standing breaks</li>
            <li>Use proper lifting techniques: bend at the knees, not the waist</li>
            <li>Sleep on a medium-firm mattress in a side-lying position with a pillow between knees</li>
            <li>Maintain a healthy weight to reduce pressure on the spine</li>
            <li>Apply ice for acute pain (first 48-72 hours) and heat for chronic discomfort</li>
            <li>Avoid high-impact activities and exercises that involve twisting or bending</li>
            <li>Consider walking, swimming, or stationary cycling for low-impact exercise</li>
            <li>Practice specific core-strengthening exercises as recommended by your healthcare provider</li>
          </ul>
        </div>
      )
    },
    {
      id: 'spinal-stenosis',
      title: 'Spinal Stenosis',
      content: (
        <div className="space-y-4">
          <p className="text-muted-foreground">
            Spinal stenosis is a narrowing of the spinal canal that puts pressure on the spinal cord and nerves, causing pain, numbness, and weakness in the back and legs, often worsening with walking or standing.
          </p>
          <h4 className="text-base font-semibold">Recommended Lifestyle Modifications:</h4>
          <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
            <li>Use a shopping cart or walker for support when walking longer distances</li>
            <li>Lean forward slightly when walking (flexed posture often relieves symptoms)</li>
            <li>Sit down frequently during activities to rest and relieve symptoms</li>
            <li>Use a stationary bike or recumbent bike for cardiovascular exercise</li>
            <li>Avoid activities that require prolonged standing or walking</li>
            <li>Consider aquatic therapy for low-impact exercise</li>
            <li>Maintain good posture and avoid excessive back extension</li>
            <li>Use proper body mechanics when lifting or carrying objects</li>
          </ul>
        </div>
      )
    },
    {
      id: 'sciatica',
      title: 'Sciatica',
      content: (
        <div className="space-y-4">
          <p className="text-muted-foreground">
            Sciatica refers to pain that radiates along the path of the sciatic nerve, which branches from your lower back through your hips and buttocks and down each leg, typically affecting only one side of your body.
          </p>
          <h4 className="text-base font-semibold">Recommended Lifestyle Modifications:</h4>
          <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
            <li>Avoid prolonged sitting; stand and walk every 30 minutes</li>
            <li>Apply ice for acute pain and heat for muscle tension</li>
            <li>Sleep on your side with a pillow between your knees</li>
            <li>Avoid activities that worsen leg pain, such as bending forward or coughing</li>
            <li>Practice gentle stretching exercises for the piriformis and hamstring muscles</li>
            <li>Use proper posture when sitting, with feet flat on the floor</li>
            <li>Consider walking as tolerated for gentle exercise</li>
            <li>Avoid high-impact activities until symptoms improve</li>
          </ul>
        </div>
      )
    }
  ];

  return (
    <div className="medical-card p-6 rounded-lg shadow-md">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="md:col-span-2">
          <h3 className="text-enhanced-heading text-2xl font-bold mb-4">Back Conditions</h3>
          <p className="text-muted-foreground mb-6">
            Back pain and conditions can significantly impact daily life, but appropriate lifestyle modifications can help manage symptoms and improve function:
          </p>
        </div>
        <div className="relative rounded-lg overflow-hidden shadow-md">
          <img
            src="/images/patient-resources/back-exercise.jpg"
            alt="Back exercise"
            className="w-full h-full object-cover"
          />
        </div>
      </div>

      <AccordionSection
        items={backConditions}
        type="single"
      />
    </div>
  );
};
