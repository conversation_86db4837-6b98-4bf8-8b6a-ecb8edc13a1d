import React from 'react';

import { CardGrid } from '@/components/patient-resources';

export const GeneralGuidelinesContent: React.FC = () => {
  const guidelines = [
    {
      id: 'posture',
      title: 'Maintain Good Posture',
      description: 'Proper alignment reduces stress on your spine',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
      content: (
        <ul className="list-disc pl-5 space-y-2 text-sm text-muted-foreground">
          <li>Keep your head aligned over your shoulders</li>
          <li>Maintain the natural curves of your spine</li>
          <li>Keep your shoulders relaxed and level</li>
          <li>Engage your core muscles for support</li>
        </ul>
      )
    },
    {
      id: 'movement',
      title: 'Stay Active',
      description: 'Regular movement promotes healing and prevents stiffness',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      content: (
        <ul className="list-disc pl-5 space-y-2 text-sm text-muted-foreground">
          <li>Take frequent breaks from static positions</li>
          <li>Incorporate gentle stretching throughout the day</li>
          <li>Start with low-impact activities</li>
          <li>Gradually increase activity levels</li>
        </ul>
      )
    },
    {
      id: 'weight',
      title: 'Maintain Healthy Weight',
      description: 'Reduces stress on your spine and joints',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      content: (
        <ul className="list-disc pl-5 space-y-2 text-sm text-muted-foreground">
          <li>Follow a balanced, nutritious diet</li>
          <li>Stay hydrated throughout the day</li>
          <li>Limit processed foods and excess sugar</li>
          <li>Consult with a nutritionist if needed</li>
        </ul>
      )
    },
    {
      id: 'stress',
      title: 'Manage Stress',
      description: 'Stress can worsen pain and slow healing',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
      ),
      content: (
        <ul className="list-disc pl-5 space-y-2 text-sm text-muted-foreground">
          <li>Practice relaxation techniques</li>
          <li>Consider meditation or mindfulness</li>
          <li>Get adequate sleep (7-9 hours)</li>
          <li>Seek support when needed</li>
        </ul>
      )
    },
    {
      id: 'smoking',
      title: 'Avoid Smoking',
      description: 'Smoking impairs healing and accelerates disc degeneration',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636" />
        </svg>
      ),
      content: (
        <ul className="list-disc pl-5 space-y-2 text-sm text-muted-foreground">
          <li>Reduces blood flow to spinal tissues</li>
          <li>Impairs the body's healing response</li>
          <li>Accelerates disc degeneration</li>
          <li>Seek help to quit if needed</li>
        </ul>
      )
    },
    {
      id: 'hydration',
      title: 'Stay Hydrated',
      description: 'Proper hydration supports disc health',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
        </svg>
      ),
      content: (
        <ul className="list-disc pl-5 space-y-2 text-sm text-muted-foreground">
          <li>Drink 8-10 glasses of water daily</li>
          <li>Limit caffeine and alcohol</li>
          <li>Eat water-rich foods</li>
          <li>Monitor urine colour as a hydration indicator</li>
        </ul>
      )
    }
  ];

  return (
    <div className="medical-card p-6 rounded-lg shadow-md">
      <div className="text-centre mb-8">
        <h3 className="text-enhanced-heading text-2xl font-bold mb-4">General Guidelines for Spine Health</h3>
        <p className="text-muted-foreground">
          These fundamental principles apply to most spine conditions and can help promote healing and prevent future problems:
        </p>
      </div>

      <CardGrid
        cards={guidelines}
        columns={3}
      />
    </div>
  );
};
