/**
 * Route definitions and metadata for the application
 */

// Supported languages - Australian English only
export type SupportedLanguage = 'en';
export const SUPPORTED_LANGUAGES: SupportedLanguage[] = ['en'];

// Route paths
export const ROUTE_PATHS = {
  HOME: '/',
  APPOINTMENTS: '/appointments',
  EXPERTISE: '/expertise',
  PATIENT_RESOURCES: '/patient-resources',
  CONTACT: '/contact',
  FAQ: '/faq',
  PRIVACY_POLICY: '/privacy-policy',
  TERMS_CONDITIONS: '/terms-conditions',
  SPECIALTIES: '/specialties',
  MEDICOLEGAL: '/medicolegal',
  LOCATIONS: '/locations',
  GALLERY: '/gallery',
  CONSULTING_ROOMS: '/consulting-rooms',
  GP_RESOURCES: '/gp-resources',
  NOT_FOUND: '*',

  // Patient Resources Routes
  PATIENT_RESOURCES_ROUTES: {
    // Category Landing Pages
    BRAIN_CONDITIONS: '/patient-resources/brain-conditions',
    SPINE_CONDITIONS: '/patient-resources/spine-conditions',
    PERIPHERAL_NERVE_CONDITIONS: '/patient-resources/peripheral-nerve-conditions',
    EXERCISE_LIBRARY: '/patient-resources/exercise-library',
    BRAIN_AND_SPINE_HEALTH: '/patient-resources/brain-and-spine-health',

    // Legacy routes (maintained for compatibility)
    CONDITION_INFORMATION: '/patient-resources/condition-information',
    SPINE_CONDITIONS_LIBRARY: '/patient-resources/spine-conditions-library',
    PERIPHERAL_NERVE_CONDITIONS_LIBRARY: '/patient-resources/peripheral-nerve-conditions-library',
    SPINE_ANATOMY: '/patient-resources/spine-anatomy',
    ASSESSMENT_TOOLS: '/patient-resources/assessment-tools',
    INDIVIDUAL_SPINE_HEALTH_PROGRAMME: '/patient-resources/individual-spine-health-programme',
    SPINE_AND_BRAIN_HEALTH: '/patient-resources/spine-and-brain-health',
    CERVICAL_SPINE_INJURY: '/patient-resources/cervical-spine-injury',
    CERVICAL_SPINE_EXERCISES: '/patient-resources/cervical-spine-exercises',
    SPINE_SAFE_EXERCISES: '/patient-resources/spine-safe-exercises',
    EXERCISE_PAIN_MED_RISKS: '/patient-resources/exercise-pain-medication-risks',
    AGE_SPECIFIC_SPINE_RECOMMENDATIONS: '/patient-resources/age-specific-spine-recommendations',
    YOUTHFUL_SPINE: '/patient-resources/youthful-spine',
    LIFESTYLE_MODIFICATIONS: '/patient-resources/lifestyle-modifications',
    PATIENT_DASHBOARD: '/patient-resources/patient-dashboard',
    SPINE_HEALTH_APP: '/patient-resources/spine-health-app',

    // Additional exercise pages
    LUMBAR_SPINE_EXERCISES: '/patient-resources/lumbar-spine-exercises',
    EXERCISES_PAIN_MEDICATION_TRANSITION: '/patient-resources/exercises-pain-medication-transition',
    SPINE_SAFE_EXERCISES_DEGENERATIVE_DISC: '/patient-resources/spine-safe-exercises-degenerative-disc-disease',
    HIDDEN_RISKS_EXERCISING_PAIN_MEDICATIONS: '/patient-resources/hidden-risks-exercising-pain-medications',

    // Brain and Spine Health pages
    EXPERT_GUIDE_SPINE_BRAIN_HEALTH: '/patient-resources/expert-guide-spine-brain-health',
    BRAIN_ANATOMY: '/patient-resources/brain-anatomy',
    NERVE_ANATOMY: '/patient-resources/nerve-anatomy',

    // Legacy compatibility routes
    CONDITION_INFO: '/patient-resources/condition-info',
    SURGICAL_TECHNOLOGIES: '/patient-resources/surgical-technologies',
    RECOVERY_REHABILITATION: '/patient-resources/recovery-rehabilitation',
    INDIVIDUAL_SPINE_HEALTH_PROGRAM: '/patient-resources/individual-spine-health-program'
  },

  // Medical Conditions Routes
  CONDITIONS: {
    HERNIATED_DISC: '/patient-resources/conditions/herniated-disc',
    SPINAL_STENOSIS: '/patient-resources/conditions/spinal-stenosis',
    SCIATICA: '/patient-resources/conditions/sciatica',
    RADICULOPATHY: '/patient-resources/conditions/radiculopathy',
    ARTHROSIS: '/patient-resources/conditions/arthrosis',
    DISCOPATHY: '/patient-resources/conditions/discopathy',
    FACET_ARTHROPATHY: '/patient-resources/conditions/facet-arthropathy',
    SACROILIAC_ARTHROPATHY: '/patient-resources/conditions/sacroiliac-arthropathy',
    PIRIFORMIS_SYNDROME: '/patient-resources/conditions/piriformis-syndrome',
    THORACIC_OUTLET_SYNDROME: '/patient-resources/conditions/thoracic-outlet-syndrome',
    OCCIPITAL_NEURALGIA: '/patient-resources/conditions/occipital-neuralgia',
    SPONDYLOSIS: '/patient-resources/conditions/spondylosis',
    PARS_DEFECTS: '/patient-resources/conditions/pars-defects',
    SPONDYLOLISTHESIS: '/patient-resources/conditions/spondylolisthesis',
    FACET_JOINT_SYNDROME: '/patient-resources/conditions/facet-joint-syndrome',
    CERVICAL_DISC_HERNIATION: '/patient-resources/conditions/cervical-disc-herniation',
    WHIPLASH: '/patient-resources/conditions/whiplash',
    THORACIC_COMPRESSION_FRACTURE: '/patient-resources/conditions/thoracic-compression-fracture',
    BRAIN_TUMOUR: '/patient-resources/conditions/brain-tumour',
    TRIGEMINAL_NEURALGIA: '/patient-resources/conditions/trigeminal-neuralgia',
    CEREBRAL_ANEURYSM: '/patient-resources/conditions/cerebral-aneurysm',
    CEREBRAL_MENINGIOMA: '/patient-resources/conditions/cerebral-meningioma',
    CEREBRAL_CAVERNOMA: '/patient-resources/conditions/cerebral-cavernoma',
    CEREBRAL_AVM: '/patient-resources/conditions/cerebral-avm',
    HEMIFACIAL_SPASM: '/patient-resources/conditions/hemifacial-spasm',
    CAUDA_EQUINA_SYNDROME: '/patient-resources/conditions/cauda-equina-syndrome',
    CHIARI_MALFORMATION: '/patient-resources/conditions/chiari-malformation',
    HYDROCEPHALUS: '/patient-resources/conditions/hydrocephalus',
    CERVICAL_MYELOPATHY: '/patient-resources/conditions/cervical-myelopathy',

    // Peripheral Nerve Conditions
    CARPAL_TUNNEL_SYNDROME: '/patient-resources/conditions/carpal-tunnel-syndrome',
    ULNAR_NEUROPATHY: '/patient-resources/conditions/ulnar-neuropathy',
    MERALGIA_PARESTHETICA: '/patient-resources/conditions/meralgia-paresthetica',
    TARSAL_TUNNEL_SYNDROME: '/patient-resources/conditions/tarsal-tunnel-syndrome',
    PERONEAL_NERVE_PALSY: '/patient-resources/conditions/peroneal-nerve-palsy',
    PERIPHERAL_NERVE_TUMORS: '/patient-resources/conditions/peripheral-nerve-tumors',
    PERIPHERAL_NEUROPATHY: '/patient-resources/conditions/peripheral-neuropathy'
  },

  // Expertise/Technologies Routes
  EXPERTISE_ROUTES: {
    CERVICAL_DISC_REPLACEMENT: '/expertise/cervical-disc-replacement',
    CERVICAL_DISC_REPLACEMENT_TECHNOLOGY: '/expertise/cervical-disc-replacement/technology-implants',
    CERVICAL_DISC_REPLACEMENT_SURGERY: '/expertise/cervical-disc-replacement/surgery-recovery',
    CERVICAL_DISC_REPLACEMENT_RISKS: '/expertise/cervical-disc-replacement/risks-comparison',
    LUMBAR_DISC_REPLACEMENT: '/expertise/lumbar-disc-replacement',
    LUMBAR_DISC_REPLACEMENT_TECHNOLOGY: '/expertise/lumbar-disc-replacement/technology-implants',
    LUMBAR_DISC_REPLACEMENT_SURGERY: '/expertise/lumbar-disc-replacement/surgery-recovery',
    LUMBAR_DISC_REPLACEMENT_RISKS: '/expertise/lumbar-disc-replacement/risks-comparison',
    IMAGE_GUIDED_SURGERY: '/expertise/image-guided-surgery',
    IMAGE_GUIDED_BRAIN_SURGERY: '/expertise/image-guided-brain-surgery',
    IMAGE_GUIDED_SPINE_SURGERY: '/expertise/image-guided-spine-surgery',
    ROBOTIC_SPINE_SURGERY: '/expertise/robotic-spine-surgery',
    ROBOTIC_SPINE_SURGERY_TECHNOLOGY: '/expertise/robotic-spine-surgery/technology',
    ROBOTIC_SPINE_SURGERY_PROCEDURES: '/expertise/robotic-spine-surgery/procedures',
    ANTERIOR_CERVICAL_DISCECTOMY_FUSION: '/expertise/anterior-cervical-discectomy-fusion',
    POSTERIOR_CERVICAL_FORAMINOTOMY: '/expertise/posterior-cervical-foraminotomy',
    MINIMALLY_INVASIVE_SPINE_SURGERY: '/expertise/minimally-invasive-spine-surgery',
    BRAIN_TUMOUR_SURGERY: '/expertise/brain-tumour-surgery',
    LUMBAR_FUSION: '/expertise/lumbar-fusion',
    SPINAL_FUSION: '/expertise/spinal-fusion'
  },

  // Location Routes
  LOCATION_ROUTES: {
    SURREY_HILLS: '/locations/surrey-hills',
    MORNINGTON: '/locations/mornington',
    FRANKSTON: '/locations/frankston',
    LANGWARRIN: '/locations/langwarrin',
    BUNDOORA: '/locations/bundoora',
    WERRIBEE: '/locations/werribee',
    HEIDELBERG: '/locations/heidelberg',
    MOONEE_PONDS: '/locations/moonee-ponds',
    SUNBURY: '/locations/sunbury',
    DANDENONG: '/locations/dandenong',
    WANTIRNA: '/locations/wantirna',
    LOCATION_DETAIL: '/locations/location-detail'
  },

  // GP Resources Routes
  GP_RESOURCES_ROUTES: {
    REFERRAL_PROTOCOLS: '/gp-resources/referral-protocols',
    DIAGNOSTICS: '/gp-resources/diagnostics',
    CARE_COORDINATION: '/gp-resources/care-coordination',
    EMERGENCIES: '/gp-resources/emergencies'
  }
} as const;

// Route redirects
export const ROUTE_REDIRECTS: Record<string, string> = {
  '/home': ROUTE_PATHS.HOME,
  '/about': ROUTE_PATHS.EXPERTISE,
  '/services': ROUTE_PATHS.EXPERTISE,
  '/resources': ROUTE_PATHS.PATIENT_RESOURCES,
  '/contact-us': ROUTE_PATHS.CONTACT,
  '/find-us': ROUTE_PATHS.LOCATIONS,
  '/privacy': ROUTE_PATHS.PRIVACY_POLICY,
  '/terms': ROUTE_PATHS.TERMS_CONDITIONS,
  '/book': ROUTE_PATHS.APPOINTMENTS,
  '/booking': ROUTE_PATHS.APPOINTMENTS
};

// Route metadata interface
export interface RouteMetadata {
  title: string;
  description: string;
  keywords?: string[];
  category: 'core' | 'patient-resources' | 'expertise' | 'locations' | 'gp-resources';
  priority: 'high' | 'medium' | 'low';
  changeFreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
}

// Route metadata definitions
export const ROUTE_METADATA: Record<string, RouteMetadata> = {
  [ROUTE_PATHS.HOME]: {
    title: 'miNEURO - Expert Neurosurgical Care',
    description: 'Leading neurosurgical practice providing expert spine and brain care across Melbourne',
    keywords: ['neurosurgery', 'spine surgery', 'brain surgery', 'Melbourne'],
    category: 'core',
    priority: 'high',
    changeFreq: 'weekly'
  },
  [ROUTE_PATHS.APPOINTMENTS]: {
    title: 'Book Appointment',
    description: 'Schedule your neurosurgical consultation with Dr. Ales Aliashkevich',
    keywords: ['appointments', 'booking', 'consultation', 'neurosurgery'],
    category: 'core',
    priority: 'high',
    changeFreq: 'daily'
  },
  [ROUTE_PATHS.EXPERTISE]: {
    title: 'Expertise & Technologies',
    description: 'Advanced neurosurgical procedures and cutting-edge medical technologies',
    keywords: ['neurosurgical procedures', 'medical technology', 'spine surgery'],
    category: 'expertise',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.PATIENT_RESOURCES]: {
    title: 'Patient Resources',
    description: 'Comprehensive resources for spine health, exercises, and condition information',
    keywords: ['patient resources', 'spine health', 'exercises', 'conditions'],
    category: 'patient-resources',
    priority: 'high',
    changeFreq: 'weekly'
  },
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_ANATOMY]: {
    title: 'Spine Anatomy Guide',
    description: 'Comprehensive guide to spine anatomy, structure, and function for patient education',
    keywords: ['spine anatomy', 'vertebrae', 'spinal cord', 'anatomy education', 'spine structure'],
    category: 'patient-resources',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_CONDITIONS_LIBRARY]: {
    title: 'Spine & Neurological Conditions Library',
    description: 'Comprehensive library of spine and neurological conditions with detailed information and treatment options',
    keywords: ['spine conditions', 'neurological conditions', 'spinal disorders', 'brain conditions', 'back pain', 'neck pain', 'spine health'],
    category: 'patient-resources',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.ASSESSMENT_TOOLS]: {
    title: 'Assessment Tools',
    description: 'Interactive assessment tools for spine and neurological conditions to help evaluate symptoms and guide treatment decisions',
    keywords: ['symptom assessment', 'spine assessment', 'neurological assessment', 'health tools', 'condition evaluation'],
    category: 'patient-resources',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.CONTACT]: {
    title: 'Contact Us',
    description: 'Get in touch with our neurosurgical team for appointments and consultations',
    keywords: ['contact', 'appointments', 'consultation'],
    category: 'core',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.FAQ]: {
    title: 'Frequently Asked Questions',
    description: 'Common questions about neurosurgery, spine surgery, and treatments',
    keywords: ['FAQ', 'questions', 'neurosurgery', 'spine surgery'],
    category: 'core',
    priority: 'medium',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.LOCATIONS]: {
    title: 'Our Locations',
    description: 'Find our neurosurgical clinics across Melbourne and surrounding areas',
    keywords: ['locations', 'clinics', 'Melbourne', 'neurosurgery'],
    category: 'locations',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.GALLERY]: {
    title: 'Medical Facility Gallery',
    description: 'Explore our state-of-the-art medical facilities and advanced neurosurgical equipment',
    keywords: ['gallery', 'facilities', 'equipment', 'neurosurgery'],
    category: 'core',
    priority: 'medium',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.CONSULTING_ROOMS]: {
    title: 'Consulting Room Rental',
    description: 'Rent fully-equipped medical consulting rooms in Surrey Hills and surrounding areas',
    keywords: ['consulting rooms', 'medical rental', 'Surrey Hills', 'healthcare'],
    category: 'core',
    priority: 'medium',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.SPECIALTIES]: {
    title: 'Neurosurgical Specialties',
    description: 'Specialised neurosurgical procedures and treatments',
    keywords: ['specialties', 'procedures', 'neurosurgery', 'treatments'],
    category: 'expertise',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.MEDICOLEGAL]: {
    title: 'Medico-Legal Services',
    description: 'Expert medico-legal assessments and reports',
    keywords: ['medico-legal', 'assessments', 'reports', 'expert'],
    category: 'core',
    priority: 'medium',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.GP_RESOURCES]: {
    title: 'GP Resources',
    description: 'Resources and protocols for general practitioners',
    keywords: ['GP resources', 'referrals', 'protocols', 'healthcare'],
    category: 'gp-resources',
    priority: 'medium',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.PRIVACY_POLICY]: {
    title: 'Privacy Policy',
    description: 'Privacy policy and data protection information',
    keywords: ['privacy', 'data protection', 'policy'],
    category: 'core',
    priority: 'low',
    changeFreq: 'yearly'
  },
  [ROUTE_PATHS.TERMS_CONDITIONS]: {
    title: 'Terms & Conditions',
    description: 'Terms of service and usage conditions',
    keywords: ['terms', 'conditions', 'service'],
    category: 'core',
    priority: 'low',
    changeFreq: 'yearly'
  },
  [ROUTE_PATHS.CONDITIONS.BRAIN_TUMOUR]: {
    title: 'Brain Tumour: Comprehensive Patient Guide',
    description: 'Complete guide to brain tumours: types, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques.',
    keywords: ['brain tumour', 'brain tumor', 'glioma', 'meningioma', 'neurosurgery', 'brain surgery', 'image-guided surgery', 'brain cancer treatment'],
    category: 'patient-resources',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.CONDITIONS.TRIGEMINAL_NEURALGIA]: {
    title: 'Trigeminal Neuralgia: Comprehensive Patient Guide',
    description: 'Complete guide to trigeminal neuralgia: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques and pain management.',
    keywords: ['trigeminal neuralgia', 'facial pain', 'neuralgia', 'microvascular decompression', 'gamma knife', 'carbamazepine', 'facial nerve pain', 'tic douloureux'],
    category: 'patient-resources',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.CONDITIONS.CEREBRAL_ANEURYSM]: {
    title: 'Cerebral Aneurysm: Comprehensive Patient Guide',
    description: 'Complete guide to cerebral aneurysms: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical and endovascular techniques.',
    keywords: ['cerebral aneurysm', 'brain aneurysm', 'aneurysm clipping', 'endovascular coiling', 'subarachnoid haemorrhage', 'neurosurgery', 'flow diverter', 'aneurysm rupture'],
    category: 'patient-resources',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.CONDITIONS.CEREBRAL_MENINGIOMA]: {
    title: 'Cerebral Meningioma: Comprehensive Patient Guide',
    description: 'Complete guide to cerebral meningiomas: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques and radiation therapy.',
    keywords: ['cerebral meningioma', 'brain meningioma', 'meningioma surgery', 'stereotactic radiosurgery', 'brain tumour', 'neurosurgery', 'meninges tumour', 'benign brain tumour'],
    category: 'patient-resources',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.CONDITIONS.CEREBRAL_CAVERNOMA]: {
    title: 'Cerebral Cavernoma: Comprehensive Patient Guide',
    description: 'Complete guide to cerebral cavernomas (cavernous malformations): causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques and monitoring.',
    keywords: ['cerebral cavernoma', 'cavernous malformation', 'cavernous angioma', 'brain vascular malformation', 'cavernoma surgery', 'brain bleeding', 'seizures', 'neurosurgery'],
    category: 'patient-resources',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.CONDITIONS.CEREBRAL_AVM]: {
    title: 'Cerebral AVM: Comprehensive Patient Guide',
    description: 'Complete guide to cerebral arteriovenous malformations (AVMs): causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical, endovascular, and radiation therapy techniques.',
    keywords: ['cerebral AVM', 'arteriovenous malformation', 'brain AVM', 'AVM surgery', 'endovascular treatment', 'radiosurgery', 'brain bleeding', 'seizures', 'neurosurgery'],
    category: 'patient-resources',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.CONDITIONS.HEMIFACIAL_SPASM]: {
    title: 'Hemifacial Spasm: Comprehensive Patient Guide',
    description: 'Complete guide to hemifacial spasm: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with botulinum toxin injections and advanced surgical techniques.',
    keywords: ['hemifacial spasm', 'facial spasm', 'facial nerve', 'botulinum toxin', 'botox', 'microvascular decompression', 'facial twitching', 'neurosurgery'],
    category: 'patient-resources',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.CONDITIONS.CAUDA_EQUINA_SYNDROME]: {
    title: 'Cauda Equina Syndrome: Emergency Patient Guide',
    description: 'Emergency guide to cauda equina syndrome: causes, symptoms, diagnosis, and urgent treatment options. Expert neurosurgical care with emergency surgical intervention and comprehensive rehabilitation.',
    keywords: ['cauda equina syndrome', 'spinal emergency', 'bladder dysfunction', 'saddle anaesthesia', 'emergency surgery', 'spinal decompression', 'neurosurgery', 'spinal cord'],
    category: 'patient-resources',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.CONDITIONS.CHIARI_MALFORMATION]: {
    title: 'Chiari Malformation: Comprehensive Patient Guide',
    description: 'Comprehensive guide to Chiari malformation: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques and comprehensive management.',
    keywords: ['chiari malformation', 'cerebellar tonsillar herniation', 'posterior fossa decompression', 'syringomyelia', 'neurosurgery', 'brain anatomy', 'cerebellar herniation'],
    category: 'patient-resources',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.CONDITIONS.HYDROCEPHALUS]: {
    title: 'Hydrocephalus: Comprehensive Patient Guide',
    description: 'Comprehensive guide to hydrocephalus: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced shunt systems and comprehensive management.',
    keywords: ['hydrocephalus', 'cerebrospinal fluid', 'shunt systems', 'ventriculoperitoneal shunt', 'brain ventricles', 'neurosurgery', 'CSF drainage'],
    category: 'patient-resources',
    priority: 'high',
    changeFreq: 'monthly'
  },
  [ROUTE_PATHS.CONDITIONS.CERVICAL_MYELOPATHY]: {
    title: 'Cervical Myelopathy: Comprehensive Patient Guide',
    description: 'Comprehensive guide to cervical myelopathy: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques and comprehensive management for optimal neurological outcomes.',
    keywords: ['cervical myelopathy', 'spinal cord compression', 'cervical stenosis', 'ACDF', 'laminectomy', 'neurosurgery', 'spinal surgery', 'neck surgery'],
    category: 'patient-resources',
    priority: 'high',
    changeFreq: 'monthly'
  }
};

// Utility functions
export function getRouteWithLanguage(path: string, _language: SupportedLanguage): string {
  // Since we only support English, return the path as-is
  return path;
}

export function getRouteMetadata(path: string): RouteMetadata | undefined {
  return ROUTE_METADATA[path];
}

export function isValidLanguage(lang: string): lang is SupportedLanguage {
  return SUPPORTED_LANGUAGES.includes(lang as SupportedLanguage);
}

export function getAllRoutePaths(): string[] {
  const paths: string[] = [];

  function extractPaths(obj: Record<string, unknown>): void {
    Object.values(obj).forEach(value => {
      if (typeof value === 'string') {
        paths.push(value);
      } else if (typeof value === 'object' && value !== null) {
        extractPaths(value as Record<string, unknown>);
      }
    });
  }

  extractPaths(ROUTE_PATHS);
  return [...new Set(paths)]; // Remove duplicates
}

export function getRoutesByCategory(category: RouteMetadata['category']): string[] {
  return Object.entries(ROUTE_METADATA)
    .filter(([, metadata]) => metadata.category === category)
    .map(([path]) => path);
}

// Route validation
export function validateRoutePath(path: string): boolean {
  const allPaths = getAllRoutePaths();
  return allPaths.includes(path) || path === ROUTE_PATHS.NOT_FOUND;
}

// Generate sitemap data
export function generateSitemapData() {
  return Object.entries(ROUTE_METADATA).map(([path, metadata]) => ({
    path,
    ...metadata,
    languages: SUPPORTED_LANGUAGES
  }));
}
