import React from 'react';
import { Navigate } from 'react-router-dom';


import { ROUTE_PATHS, ROUTE_REDIRECTS } from './route-definitions';
import { registerRoutes, RouteLoaderRegistry } from './route-loader';

import { logRoute } from '@/lib/dev-console';

// Register all route loaders
registerRoutes({
  // Core pages
  [ROUTE_PATHS.HOME]: () => import('@/pages/Index'),
  [ROUTE_PATHS.APPOINTMENTS]: () => import('@/pages/Appointments'),
  [ROUTE_PATHS.EXPERTISE]: () => import('@/pages/Expertise'),
  [ROUTE_PATHS.PATIENT_RESOURCES]: () => import('@/pages/PatientResources'),
  [ROUTE_PATHS.CONTACT]: () => import('@/pages/Contact'),
  [ROUTE_PATHS.FAQ]: () => import('@/pages/Faq'),
  [ROUTE_PATHS.PRIVACY_POLICY]: () => import('@/pages/PrivacyPolicy'),
  [ROUTE_PATHS.TERMS_CONDITIONS]: () => import('@/pages/TermsConditions'),
  [ROUTE_PATHS.SPECIALTIES]: () => import('@/pages/Specialties'),
  [ROUTE_PATHS.MEDICOLEGAL]: () => import('@/pages/Medicolegal'),
  [ROUTE_PATHS.LOCATIONS]: () => import('@/pages/Locations'),
  [ROUTE_PATHS.GALLERY]: () => import('@/pages/Gallery'),
  [ROUTE_PATHS.CONSULTING_ROOMS]: () => import('@/pages/ConsultingRooms'),
  [ROUTE_PATHS.GP_RESOURCES]: () => import('@/pages/GPResources'),

  // Location Routes
  [ROUTE_PATHS.LOCATION_ROUTES.SURREY_HILLS]: () => import('@/pages/locations/surrey-hills'),
  [ROUTE_PATHS.LOCATION_ROUTES.MORNINGTON]: () => import('@/pages/locations/mornington'),
  [ROUTE_PATHS.LOCATION_ROUTES.FRANKSTON]: () => import('@/pages/locations/frankston'),
  [ROUTE_PATHS.LOCATION_ROUTES.LANGWARRIN]: () => import('@/pages/locations/langwarrin'),
  [ROUTE_PATHS.LOCATION_ROUTES.BUNDOORA]: () => import('@/pages/locations/bundoora'),
  [ROUTE_PATHS.LOCATION_ROUTES.WERRIBEE]: () => import('@/pages/locations/werribee'),
  [ROUTE_PATHS.LOCATION_ROUTES.HEIDELBERG]: () => import('@/pages/locations/heidelberg'),
  [ROUTE_PATHS.LOCATION_ROUTES.MOONEE_PONDS]: () => import('@/pages/locations/moonee-ponds'),
  [ROUTE_PATHS.LOCATION_ROUTES.SUNBURY]: () => import('@/pages/locations/sunbury'), // Sunbury location page
  [ROUTE_PATHS.LOCATION_ROUTES.DANDENONG]: () => import('@/pages/locations/dandenong'),
  [ROUTE_PATHS.LOCATION_ROUTES.WANTIRNA]: () => import('@/pages/locations/wantirna'),
  [ROUTE_PATHS.LOCATION_ROUTES.LOCATION_DETAIL]: () => import('@/pages/locations/LocationDetail'),

  // Expertise Routes
  [ROUTE_PATHS.EXPERTISE_ROUTES.CERVICAL_DISC_REPLACEMENT]: () => import('@/pages/expertise/CervicalDiscReplacement'),




  // GP Resources Routes
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.REFERRAL_PROTOCOLS]: () => import('@/pages/gp-resources/ReferralProtocols'),
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.DIAGNOSTICS]: () => import('@/pages/gp-resources/Diagnostics'),
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.CARE_COORDINATION]: () => import('@/pages/gp-resources/CareCoordination'),
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.EMERGENCIES]: () => import('@/pages/gp-resources/Emergencies'),

  // Patient Resources
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.CONDITION_INFORMATION]: () => import('@/pages/patient-resources/ConditionInformation'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_CONDITIONS_LIBRARY]: () => import('@/pages/patient-resources/SpineConditionsLibrary'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.PERIPHERAL_NERVE_CONDITIONS_LIBRARY]: () => import('@/pages/patient-resources/PeripheralNerveConditionsLibrary'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_ANATOMY]: () => import('@/pages/patient-resources/SpineAnatomy'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.EXERCISE_LIBRARY]: () => import('@/pages/patient-resources/ExerciseLibrary'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.INDIVIDUAL_SPINE_HEALTH_PROGRAMME]: () => import('@/pages/patient-resources/IndividualSpineHealthProgramme'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_AND_BRAIN_HEALTH]: () => import('@/pages/patient-resources/SpineAndBrainHealth'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.CERVICAL_SPINE_INJURY]: () => import('@/pages/patient-resources/CervicalSpineInjury'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.CERVICAL_SPINE_EXERCISES]: () => import('@/pages/patient-resources/CervicalSpineExercises'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_SAFE_EXERCISES]: () => import('@/pages/patient-resources/SpineSafeExercises'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.EXERCISE_PAIN_MED_RISKS]: () => import('@/pages/patient-resources/ExercisePainMedRisks'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.AGE_SPECIFIC_SPINE_RECOMMENDATIONS]: () => import('@/pages/patient-resources/AgeSpecificSpineRecommendations'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.YOUTHFUL_SPINE]: () => import('@/pages/patient-resources/YouthfulSpine'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.LIFESTYLE_MODIFICATIONS]: () => import('@/pages/patient-resources/LifestyleModifications'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.ASSESSMENT_TOOLS]: () => import('@/pages/patient-resources/AssessmentTools'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.PATIENT_DASHBOARD]: () => import('@/pages/patient-resources/PatientDashboard'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_HEALTH_APP]: () => import('@/pages/patient-resources/SpineHealthApp'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.CONDITION_INFO]: () => import('@/pages/patient-resources/ConditionInformation'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.BRAIN_CONDITIONS]: () => import('@/pages/patient-resources/SpineConditionsLibrary'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_CONDITIONS]: () => import('@/pages/patient-resources/SpineConditionsLibrary'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SURGICAL_TECHNOLOGIES]: () => import('@/pages/patient-resources/SpineHealthApp'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.RECOVERY_REHABILITATION]: () => import('@/pages/patient-resources/SpineAndBrainHealth'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.INDIVIDUAL_SPINE_HEALTH_PROGRAM]: () => import('@/pages/patient-resources/IndividualSpineHealthProgramme'),

  // Medical Conditions
  [ROUTE_PATHS.CONDITIONS.HERNIATED_DISC]: () => import('@/pages/patient-resources/conditions/HerniatedDisc'),
  [ROUTE_PATHS.CONDITIONS.SPINAL_STENOSIS]: () => import('@/pages/patient-resources/conditions/SpinalStenosis'),
  [ROUTE_PATHS.CONDITIONS.SCIATICA]: () => import('@/pages/patient-resources/conditions/Sciatica'),
  [ROUTE_PATHS.CONDITIONS.RADICULOPATHY]: () => import('@/pages/patient-resources/conditions/Radiculopathy'),
  [ROUTE_PATHS.CONDITIONS.ARTHROSIS]: () => import('@/pages/patient-resources/conditions/Arthrosis'),
  [ROUTE_PATHS.CONDITIONS.DISCOPATHY]: () => import('@/pages/patient-resources/conditions/Discopathy'),
  [ROUTE_PATHS.CONDITIONS.FACET_ARTHROPATHY]: () => import('@/pages/patient-resources/conditions/FacetArthropathy'),
  [ROUTE_PATHS.CONDITIONS.SACROILIAC_ARTHROPATHY]: () => import('@/pages/patient-resources/conditions/SacroiliacArthropathy'),
  [ROUTE_PATHS.CONDITIONS.PIRIFORMIS_SYNDROME]: () => import('@/pages/patient-resources/conditions/PiriformisSyndrome'),
  [ROUTE_PATHS.CONDITIONS.THORACIC_OUTLET_SYNDROME]: () => import('@/pages/patient-resources/conditions/ThoracicOutletSyndrome'),
  [ROUTE_PATHS.CONDITIONS.OCCIPITAL_NEURALGIA]: () => import('@/pages/patient-resources/conditions/OccipitalNeuralgia'),
  [ROUTE_PATHS.CONDITIONS.SPONDYLOSIS]: () => import('@/pages/patient-resources/conditions/Spondylosis'),
  [ROUTE_PATHS.CONDITIONS.PARS_DEFECTS]: () => import('@/pages/patient-resources/conditions/ParsDefects'),
  [ROUTE_PATHS.CONDITIONS.SPONDYLOLISTHESIS]: () => import('@/pages/patient-resources/conditions/Spondylolisthesis'),
  [ROUTE_PATHS.CONDITIONS.FACET_JOINT_SYNDROME]: () => import('@/pages/patient-resources/conditions/FacetArthropathy'),
  [ROUTE_PATHS.CONDITIONS.CERVICAL_DISC_HERNIATION]: () => import('@/pages/patient-resources/conditions/HerniatedDisc'),
  [ROUTE_PATHS.CONDITIONS.WHIPLASH]: () => import('@/pages/patient-resources/conditions/Radiculopathy'),
  [ROUTE_PATHS.CONDITIONS.THORACIC_COMPRESSION_FRACTURE]: () => import('@/pages/patient-resources/conditions/SpinalStenosis'),
  [ROUTE_PATHS.CONDITIONS.BRAIN_TUMOUR]: () => import('@/pages/patient-resources/conditions/BrainTumour'),
  [ROUTE_PATHS.CONDITIONS.TRIGEMINAL_NEURALGIA]: () => import('@/pages/patient-resources/conditions/TrigeminalNeuralgia'),
  [ROUTE_PATHS.CONDITIONS.CEREBRAL_ANEURYSM]: () => import('@/pages/patient-resources/conditions/CerebralAneurysm'),
  [ROUTE_PATHS.CONDITIONS.CEREBRAL_MENINGIOMA]: () => import('@/pages/patient-resources/conditions/CerebralMeningioma'),
  [ROUTE_PATHS.CONDITIONS.CEREBRAL_CAVERNOMA]: () => import('@/pages/patient-resources/conditions/CerebralCavernoma'),
  [ROUTE_PATHS.CONDITIONS.CEREBRAL_AVM]: () => import('@/pages/patient-resources/conditions/CerebralAVM'),
  [ROUTE_PATHS.CONDITIONS.HEMIFACIAL_SPASM]: () => import('@/pages/patient-resources/conditions/HemifacialSpasm'),
  [ROUTE_PATHS.CONDITIONS.CAUDA_EQUINA_SYNDROME]: () => import('@/pages/patient-resources/conditions/CaudaEquinaSyndrome'),
  [ROUTE_PATHS.CONDITIONS.CHIARI_MALFORMATION]: () => import('@/pages/patient-resources/conditions/ChiariMalformation'),
  [ROUTE_PATHS.CONDITIONS.HYDROCEPHALUS]: () => import('@/pages/patient-resources/conditions/Hydrocephalus'),
  [ROUTE_PATHS.CONDITIONS.CERVICAL_MYELOPATHY]: () => import('@/pages/patient-resources/conditions/CervicalMyelopathy'),

  // Peripheral Nerve Conditions
  [ROUTE_PATHS.CONDITIONS.CARPAL_TUNNEL_SYNDROME]: () => import('@/pages/patient-resources/conditions/CarpalTunnelSyndrome'),
  [ROUTE_PATHS.CONDITIONS.ULNAR_NEUROPATHY]: () => import('@/pages/patient-resources/conditions/UlnarNeuropathy'),
  [ROUTE_PATHS.CONDITIONS.MERALGIA_PARESTHETICA]: () => import('@/pages/patient-resources/conditions/MeralgiaParesthetica'),
  [ROUTE_PATHS.CONDITIONS.TARSAL_TUNNEL_SYNDROME]: () => import('@/pages/patient-resources/conditions/TarsalTunnelSyndrome'),
  [ROUTE_PATHS.CONDITIONS.PERONEAL_NERVE_PALSY]: () => import('@/pages/patient-resources/conditions/PeronealNervePalsy'),
  [ROUTE_PATHS.CONDITIONS.PERIPHERAL_NERVE_TUMORS]: () => import('@/pages/patient-resources/conditions/PeripheralNerveTumors'),

  // Expertise/Technologies
  [ROUTE_PATHS.EXPERTISE_ROUTES.CERVICAL_DISC_REPLACEMENT_TECHNOLOGY]: () => import('@/pages/expertise/cervical-disc-replacement/TechnologyImplants'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.CERVICAL_DISC_REPLACEMENT_SURGERY]: () => import('@/pages/expertise/cervical-disc-replacement/SurgeryRecovery'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.CERVICAL_DISC_REPLACEMENT_RISKS]: () => import('@/pages/expertise/cervical-disc-replacement/RisksComparison'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.LUMBAR_DISC_REPLACEMENT]: () => import('@/pages/expertise/LumbarDiscReplacement'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.LUMBAR_DISC_REPLACEMENT_TECHNOLOGY]: () => import('@/pages/expertise/lumbar-disc-replacement/TechnologyImplants'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.LUMBAR_DISC_REPLACEMENT_SURGERY]: () => import('@/pages/expertise/lumbar-disc-replacement/SurgeryRecovery'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.LUMBAR_DISC_REPLACEMENT_RISKS]: () => import('@/pages/expertise/lumbar-disc-replacement/RisksComparison'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.IMAGE_GUIDED_SURGERY]: () => import('@/pages/expertise/ImageGuidedSurgery'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.IMAGE_GUIDED_BRAIN_SURGERY]: () => import('@/pages/expertise/ImageGuidedBrainSurgery'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.IMAGE_GUIDED_SPINE_SURGERY]: () => import('@/pages/expertise/ImageGuidedSpineSurgery'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.ROBOTIC_SPINE_SURGERY]: () => import('@/pages/expertise/RoboticSpineSurgery'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.ROBOTIC_SPINE_SURGERY_TECHNOLOGY]: () => import('@/pages/expertise/RoboticSpineSurgeryTechnology'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.ROBOTIC_SPINE_SURGERY_PROCEDURES]: () => import('@/pages/expertise/RoboticSpineSurgeryProcedures'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.ANTERIOR_CERVICAL_DISCECTOMY_FUSION]: () => import('@/pages/expertise/CervicalDiscReplacement'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.POSTERIOR_CERVICAL_FORAMINOTOMY]: () => import('@/pages/expertise/CervicalDiscReplacement'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.MINIMALLY_INVASIVE_SPINE_SURGERY]: () => import('@/pages/expertise/RoboticSpineSurgery'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.BRAIN_TUMOUR_SURGERY]: () => import('@/pages/expertise/ImageGuidedSurgery'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.LUMBAR_FUSION]: () => import('@/pages/expertise/LumbarDiscReplacement'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.SPINAL_FUSION]: () => import('@/pages/expertise/RoboticSpineSurgery'),





  // Special routes
  [ROUTE_PATHS.NOT_FOUND]: () => import('@/pages/NotFound'),
});

// Get the route loader registry
const routeRegistry = RouteLoaderRegistry.getInstance();

// Define route structure that matches React Router's expectations
export interface RouteConfig {
  path: string;
  element: React.ReactNode | null;
  children?: RouteConfig[];
}

// Helper function to create route elements
function createRouteElement(path: string): React.ReactNode | null {
  try {
    const LazyComponent = routeRegistry.getLazyComponent(path);
    return <LazyComponent />;
  } catch (error) {
    logRoute(`Failed to create route element for path: ${path}`, error);
    return null;
  }
}

// Helper function to create redirect elements
function createRedirectElement(to: string): React.ReactNode | null {
  try {
    return <Navigate to={to} replace />;
  } catch (error) {
    logRoute(`Failed to create redirect element to: ${to}`, error);
    return null;
  }
}

// Generate base routes from route definitions
function generateBaseRoutes(): RouteConfig[] {
  const routes: RouteConfig[] = [];

  // Add all registered routes
  routeRegistry.getRegisteredRoutes().forEach(path => {
    routes.push({
      path,
      element: createRouteElement(path)
    });
  });

  // Add redirects
  Object.entries(ROUTE_REDIRECTS).forEach(([from, to]) => {
    routes.push({
      path: from,
      element: createRedirectElement(to)
    });
  });

  return routes;
}

// Define base routes (without language prefix)
export const baseRoutes: RouteConfig[] = generateBaseRoutes();

// Since we only support English, no language-specific routes are needed
export const getAllRoutes = (): RouteConfig[] => {
  // Return only the base routes without language prefixes
  return baseRoutes;
};

// Export route utilities
export { ROUTE_PATHS, ROUTE_REDIRECTS, SUPPORTED_LANGUAGES } from './route-definitions';
export { preloadRoute, RouteLoaderRegistry } from './route-loader';

