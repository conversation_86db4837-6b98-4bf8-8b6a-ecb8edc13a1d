import { <PERSON>ache<PERSON>anager, <PERSON>ache<PERSON><PERSON><PERSON> } from '../cache/CacheManager';
import { ServiceBase, CacheOptions } from '../types';

import { ApiClient } from './ApiClient';

/**
 * Base service class with common functionality
 */
export abstract class BaseService implements ServiceBase {
  protected apiClient: ApiClient;
  protected cacheManager: CacheManager;
  
  abstract readonly name: string;
  abstract readonly version: string;

  constructor(apiClient: ApiClient, cacheManager: CacheManager) {
    this.apiClient = apiClient;
    this.cacheManager = cacheManager;
  }

  /**
   * Check if service is healthy
   */
  async isHealthy(): Promise<boolean> {
    try {
      // Implement health check logic
      // This could ping a health endpoint or check dependencies
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get data with caching
   */
  protected async getCached<T>(
    cacheKey: string,
    fetchFn: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.cacheManager.get<T>(cacheKey);
    if (cached !== null) {
      return cached;
    }

    // Fetch fresh data
    const data = await fetchFn();
    
    // Cache the result
    await this.cacheManager.set(cacheKey, data, options);
    
    return data;
  }

  /**
   * Invalidate cache by tags
   */
  protected async invalidateCache(tags: string[]): Promise<void> {
    await this.cacheManager.invalidateByTags(tags);
  }

  /**
   * Create cache key from array
   */
  protected createCacheKey(keyParts: readonly string[]): string {
    return CacheKeys.toString(keyParts);
  }

  /**
   * Create cache key with parameters
   */
  protected createCacheKeyWithParams(
    keyParts: readonly string[], 
    params: Record<string, unknown>
  ): string {
    return CacheKeys.withParams(keyParts, params);
  }
}

/**
 * Service factory for creating service instances
 */
export class ServiceFactory {
  private static apiClient: ApiClient;
  private static cacheManager: CacheManager;

  static initialize(apiClient: ApiClient, cacheManager: CacheManager): void {
    this.apiClient = apiClient;
    this.cacheManager = cacheManager;
  }

  static getApiClient(): ApiClient {
    if (!this.apiClient) {
      throw new Error('ServiceFactory not initialized. Call initialize() first.');
    }
    return this.apiClient;
  }

  static getCacheManager(): CacheManager {
    if (!this.cacheManager) {
      throw new Error('ServiceFactory not initialized. Call initialize() first.');
    }
    return this.cacheManager;
  }

  static createService<T extends BaseService>(
    ServiceClass: new (apiClient: ApiClient, cacheManager: CacheManager) => T
  ): T {
    return new ServiceClass(this.getApiClient(), this.getCacheManager());
  }
}
