/**
 * Centralised service layer initialization and exports
 */

import { CacheManager } from './api/cache/CacheManager';
import { ApiClient } from './api/core/ApiClient';
import { ServiceFactory } from './api/core/BaseService';
import { ServiceErrorHandler } from './api/errors/ServiceErrorHandler';
import { MedicalDataService } from './api/MedicalDataService';
import { ApiConfig } from './api/types';

// Environment configuration
const getApiConfig = (): ApiConfig => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'https://api.mineuro.com.au';
  
  return {
    baseURL,
    timeout: 30000,
    retryAttempts: 3,
    headers: {
      'X-Client-Version': '1.0.0',
      'X-Client-Platform': 'web'
    }
  };
};

// Initialize service layer
let isInitialized = false;
let apiClient: ApiClient;
let cacheManager: CacheManager;
let errorHandler: ServiceErrorHandler;

// Service instances
let medicalDataService: MedicalDataService;

/**
 * Initialize the service layer
 */
export function initializeServices(): void {
  if (isInitialized) {
    return;
  }

  // Create core instances
  const config = getApiConfig();
  apiClient = new ApiClient(config);
  cacheManager = new CacheManager({
    maxMemoryEntries: 100,
    cleanupIntervalMs: 5 * 60 * 1000 // 5 minutes
  });
  errorHandler = ServiceErrorHandler.getInstance();

  // Initialize service factory
  ServiceFactory.initialize(apiClient, cacheManager);

  // Create service instances
  medicalDataService = ServiceFactory.createService(MedicalDataService);

  // Add request/response interceptors
  apiClient.addRequestInterceptor({
    onRequest: async (config) => {
      // Add request ID for tracking
      config.headers = {
        ...config.headers,
        'X-Request-ID': generateRequestId()
      };
      return config;
    }
  });

  apiClient.addResponseInterceptor({
    onResponse: async (response) => {
      // Log successful responses in development
      if (import.meta.env.DEV) {
        console.log('API Response:', {
          url: response.config.url,
          status: response.status,
          data: response.data
        });
      }
      return response;
    },
    onResponseError: async (error) => {
      // Log errors in development
      if (import.meta.env.DEV) {
        console.error('API Error:', error);
      }
      throw error;
    }
  });

  isInitialized = true;
}

/**
 * Get medical data service instance
 */
export function getMedicalDataService(): MedicalDataService {
  if (!isInitialized) {
    initializeServices();
  }
  return medicalDataService;
}

/**
 * Get API client instance
 */
export function getApiClient(): ApiClient {
  if (!isInitialized) {
    initializeServices();
  }
  return apiClient;
}

/**
 * Get cache manager instance
 */
export function getCacheManager(): CacheManager {
  if (!isInitialized) {
    initializeServices();
  }
  return cacheManager;
}

/**
 * Get error handler instance
 */
export function getErrorHandler(): ServiceErrorHandler {
  if (!isInitialized) {
    initializeServices();
  }
  return errorHandler;
}

/**
 * Health check for all services
 */
export async function checkServiceHealth(): Promise<{
  healthy: boolean;
  services: Record<string, boolean>;
}> {
  if (!isInitialized) {
    initializeServices();
  }

  const services = {
    medicalData: await medicalDataService.isHealthy()
  };

  const healthy = Object.values(services).every(status => status);

  return { healthy, services };
}

/**
 * Clear all caches
 */
export async function clearAllCaches(): Promise<void> {
  if (!isInitialized) {
    return;
  }
  
  await cacheManager.clear();
}

/**
 * Get service statistics
 */
export function getServiceStats(): {
  cache: ReturnType<CacheManager['getStats']>;
  errors: ReturnType<ServiceErrorHandler['getErrorStats']>;
} {
  if (!isInitialized) {
    initializeServices();
  }

  return {
    cache: cacheManager.getStats(),
    errors: errorHandler.getErrorStats()
  };
}

/**
 * Cleanup service layer resources
 */
export function cleanupServices(): void {
  if (!isInitialized) {
    return;
  }

  cacheManager.destroy();
  errorHandler.clearStats();
  isInitialized = false;
}

/**
 * Generate unique request ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Auto-initialize in browser environment
if (typeof window !== 'undefined') {
  initializeServices();
}

// Export types for consumers
export type { 
  MedicalCondition, 
  Procedure, 
  Exercise, 
  ServiceError,
  ApiResponse 
} from './api/types';

// Export service classes for advanced usage
export { ApiClient } from './api/core/ApiClient';
export { ServiceFactory } from './api/core/BaseService';
export { CacheManager } from './api/cache/CacheManager';
export { ServiceErrorHandler } from './api/errors/ServiceErrorHandler';
export { MedicalDataService } from './api/MedicalDataService';

// Export utilities
export { NetworkUtils } from './api/network/NetworkUtils';
export { CacheKeys } from './api/cache/CacheManager';
