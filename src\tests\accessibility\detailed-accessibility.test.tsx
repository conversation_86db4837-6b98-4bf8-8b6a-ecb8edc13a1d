import { render } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import { describe, it, expect } from 'vitest';

import ConsultingRooms from '@/pages/ConsultingRooms';
import Faq from '@/pages/Faq';
import { EnhancedTestWrapper } from '@/tests/utils/enhanced-test-helpers';
import { setupAllStandardMocks } from '@/tests/utils/standard-mocks';

// Add jest-axe matchers
expect.extend(toHaveNoViolations);

// Setup standardized mocks
setupAllStandardMocks();

describe('🔍 Detailed Accessibility Analysis', () => {
  describe('Faq Page', () => {
    it('identifies specific accessibility violations with ALL rules', async () => {
      const { container } = render(
        <EnhancedTestWrapper>
          <Faq />
        </EnhancedTestWrapper>
      );

      // Test with optimized axe rules for faster execution
      const results = await axe(container, {
        rules: {
          // Disable some slower rules that aren't critical for this test
          'colour-contrast': { enabled: false },
          'link-in-text-block': { enabled: false }
        }
      });

      if (results.violations.length > 0) {
        if (import.meta.env.DEV) {
          console.log('🚨 FAQ ACCESSIBILITY VIOLATIONS:');
        }
        results.violations.forEach((violation, index) => {
          if (import.meta.env.DEV) {
            console.log(`\n${index + 1}. Rule: ${violation.id}`);
          }
          if (import.meta.env.DEV) {
            console.log(`   Description: ${violation.description}`);
          }
          if (import.meta.env.DEV) {
            console.log(`   Impact: ${violation.impact}`);
          }
          if (import.meta.env.DEV) {
            console.log(`   Help: ${violation.help}`);
          }
          if (import.meta.env.DEV) {
            console.log(`   Help URL: ${violation.helpUrl}`);
          }

          violation.nodes.forEach((node, nodeIndex) => {
            if (import.meta.env.DEV) {
              console.log(`   Node ${nodeIndex + 1}:`);
            }
            if (import.meta.env.DEV) {
              console.log(`     Target: ${node.target.join(', ')}`);
            }
            if (import.meta.env.DEV) {
              console.log(`     HTML: ${node.html.substring(0, 150)}...`);
            }
            if (import.meta.env.DEV) {
              console.log(`     Failure Summary: ${node.failureSummary}`);
            }
            if (node.any && node.any.length > 0) {
              if (import.meta.env.DEV) {
                console.log(`     Any checks: ${node.any.map(check => check.message).join(', ')}`);
              }
            }
            if (node.all && node.all.length > 0) {
              if (import.meta.env.DEV) {
                console.log(`     All checks: ${node.all.map(check => check.message).join(', ')}`);
              }
            }
          });
        });
      } else {
        if (import.meta.env.DEV) {
          console.log('✅ FAQ: No accessibility violations found!');
        }
      }

      // Don't fail the test, just log the violations
      expect(results.violations.length).toBeGreaterThanOrEqual(0);
    }, 15000); // Increase timeout to 15 seconds for accessibility tests
  });

  describe('ConsultingRooms Page', () => {
    it('identifies specific accessibility violations with ALL rules', async () => {
      const { container } = render(
        <EnhancedTestWrapper>
          <ConsultingRooms />
        </EnhancedTestWrapper>
      );

      // Test with optimized axe rules for faster execution
      const results = await axe(container, {
        rules: {
          // Disable some slower rules that aren't critical for this test
          'colour-contrast': { enabled: false },
          'link-in-text-block': { enabled: false }
        }
      });

      if (results.violations.length > 0) {
        if (import.meta.env.DEV) {
          console.log('🚨 CONSULTING ROOMS ACCESSIBILITY VIOLATIONS:');
        }
        results.violations.forEach((violation, index) => {
          if (import.meta.env.DEV) {
            console.log(`\n${index + 1}. Rule: ${violation.id}`);
          }
          if (import.meta.env.DEV) {
            console.log(`   Description: ${violation.description}`);
          }
          if (import.meta.env.DEV) {
            console.log(`   Impact: ${violation.impact}`);
          }
          if (import.meta.env.DEV) {
            console.log(`   Help: ${violation.help}`);
          }
          if (import.meta.env.DEV) {
            console.log(`   Help URL: ${violation.helpUrl}`);
          }

          violation.nodes.forEach((node, nodeIndex) => {
            if (import.meta.env.DEV) {
              console.log(`   Node ${nodeIndex + 1}:`);
            }
            if (import.meta.env.DEV) {
              console.log(`     Target: ${node.target.join(', ')}`);
            }
            if (import.meta.env.DEV) {
              console.log(`     HTML: ${node.html.substring(0, 150)}...`);
            }
            if (import.meta.env.DEV) {
              console.log(`     Failure Summary: ${node.failureSummary}`);
            }
            if (node.any && node.any.length > 0) {
              if (import.meta.env.DEV) {
                console.log(`     Any checks: ${node.any.map(check => check.message).join(', ')}`);
              }
            }
            if (node.all && node.all.length > 0) {
              if (import.meta.env.DEV) {
                console.log(`     All checks: ${node.all.map(check => check.message).join(', ')}`);
              }
            }
          });
        });
      } else {
        if (import.meta.env.DEV) {
          console.log('✅ CONSULTING ROOMS: No accessibility violations found!');
        }
      }

      // Don't fail the test, just log the violations
      expect(results.violations.length).toBeGreaterThanOrEqual(0);
    }, 15000); // Increase timeout to 15 seconds for accessibility tests
  });
});