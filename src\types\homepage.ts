// TypeScript types for homepage components and data structures

export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouchDevice: boolean;
  hasHover: boolean;
  screenSize: 'mobile' | 'tablet' | 'desktop';
  orientation: 'portrait' | 'landscape';
  pixelRatio: number;
  isLoaded: boolean;
}

export interface TranslationFunction {
  (key: string, fallback?: string): string;
}

export interface ServiceCard {
  id: string;
  title: string;
  description: string;
  icon: string;
  link: string;
  animationDelay: string;
  gradientColors: string;
}

export interface ExpertiseCard {
  id: string;
  title: string;
  description: string;
  icon: string;
  link: string;
  gradientColors: string;
}

export interface BenefitItem {
  id: string;
  title: string;
  description: string;
  icon: string;
}

export interface ConsultationStep {
  id: string;
  title: string;
  description: string;
  icon: string;
}

export interface FeedbackSection {
  id: string;
  title: string;
  description: string;
  items: string[];
  bgColor: string;
  borderColor?: string;
}

export interface SectionHeader {
  subtitle: string;
  title: string;
}

export interface TreatmentDecisionContent {
  title: string;
  description: string;
  items?: string[];
}

export interface ConsultationPreparation {
  beforeVisit: string[];
  duringVisit: string[];
}

// Component Props Interfaces
export interface WelcomeSectionProps {
  deviceInfo: DeviceInfo;
  getTranslation: TranslationFunction;
}

export interface ServicesOverviewProps {
  deviceInfo: DeviceInfo;
  services: ServiceCard[];
}

export interface ExpertiseShowcaseProps {
  deviceInfo: DeviceInfo;
  expertise: ExpertiseCard[];
}

export interface MinimallyInvasiveSectionProps {
  principles: string[];
}

export interface WhenLessIsMoreProps {
  benefits: BenefitItem[];
  principles: string[];
}

export interface ArthroplastySectionProps {
  // No specific props needed - uses static content
  className?: string;
}

export interface ImageGuidedSurgeryProps {
  keyBenefits: string[];
  patientOutcomes: string[];
}

export interface TreatmentDecisionsProps {
  content: {
    primaryGoal: TreatmentDecisionContent;
    buildingTrust: TreatmentDecisionContent;
    secondOpinions: TreatmentDecisionContent;
  };
}

export interface AppointmentsSectionProps {
  consultationSteps: ConsultationStep[];
  preparation: ConsultationPreparation;
}

export interface FeedbackSectionProps {
  sections: FeedbackSection[];
}

// Homepage Data Hook Interface
export interface HomepageData {
  services: ServiceCard[];
  expertise: ExpertiseCard[];
  misPhilosophyPrinciples: string[];
  whenLessIsMoreBenefits: BenefitItem[];
  imageGuidedBenefits: string[];
  patientOutcomes: string[];
  treatmentDecisionsContent: {
    primaryGoal: TreatmentDecisionContent;
    buildingTrust: TreatmentDecisionContent;
    secondOpinions: TreatmentDecisionContent;
  };
  consultationSteps: ConsultationStep[];
  consultationPreparation: ConsultationPreparation;
  feedbackSections: FeedbackSection[];
  sectionHeaders: Record<string, SectionHeader>;
}

// Icon type definitions for SVG components
export type IconType = 
  | 'minimal-invasive'
  | 'imaging-navigation'
  | 'advantages'
  | 'robotic-surgery'
  | 'brain'
  | 'spine'
  | 'nerve'
  | 'legal'
  | 'training'
  | 'technology'
  | 'precision'
  | 'calendar'
  | 'document'
  | 'lightbulb';

// Animation delay type
export type AnimationDelay = '100ms' | '200ms' | '300ms' | '400ms';

// Theme-aware colour combinations
export type GradientColors =
  | 'bg-info-light border border-info/30'
  | 'bg-success-light border border-success/30'
  | 'bg-info-light border border-info/30'
  | 'bg-medical-blue-light border border-medical-blue/30'
  | 'bg-muted-light border border-border/70'
  | 'bg-muted border border-border/30'
  | 'bg-primary/10 border border-primary/30'
  | 'bg-secondary border border-border/30';

// Theme-aware background colour types
export type BackgroundColor =
  | 'bg-primary/5'
  | 'bg-info-light'
  | 'bg-muted/50'
  | 'bg-card'
  | 'bg-background';

// Border colour types
export type BorderColor = 
  | 'border-info/30'
  | 'border-primary/10';

// Screen size types
export type ScreenSize = 'mobile' | 'tablet' | 'desktop';

// Orientation types
export type Orientation = 'portrait' | 'landscape';

// Component state types
export interface ComponentState {
  isLoading: boolean;
  hasError: boolean;
  errorMessage?: string;
}

// SEO data interface for homepage
export interface HomepageSEOData {
  title: string;
  description: string;
  keywords: string[];
  structuredData: Record<string, unknown>;
  openGraph: {
    title: string;
    description: string;
    image: string;
    url: string;
  };
  twitter: {
    card: string;
    title: string;
    description: string;
    image: string;
  };
}

// Homepage component props
export interface HomepageProps {
  className?: string;
}

// Section component base props
export interface SectionProps {
  className?: string;
  children?: React.ReactNode;
  id?: string;
}

// Card component props
export interface CardProps {
  className?: string;
  children: React.ReactNode;
  animationDelay?: AnimationDelay;
  onClick?: () => void;
}

// Button variant types for homepage
export type ButtonVariant = 'default' | 'outline' | 'ghost' | 'link';
export type ButtonSize = 'sm' | 'default' | 'lg';

// Link types for navigation
export interface NavigationLink {
  to: string;
  label: string;
  external?: boolean;
}

// Image props for SafeImage component
export interface SafeImageProps {
  src: string;
  alt: string;
  className?: string;
  fallbackSrc?: string;
}

// Responsive utilities
export interface ResponsiveClasses {
  mobile: string;
  tablet?: string;
  desktop: string;
}

// Animation configuration
export interface AnimationConfig {
  duration: string;
  delay: string;
  easing: string;
}

// Error boundary props
export interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error }>;
}

// Loading state props
export interface LoadingStateProps {
  isLoading: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

// Accessibility props
export interface AccessibilityProps {
  'aria-label'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-hidden'?: boolean;
  role?: string;
  tabIndex?: number;
}

// Performance monitoring
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
}

// Component ref types
export type ComponentRef<T = HTMLDivElement> = React.RefObject<T>;

// Event handler types
export type ClickHandler = (event: React.MouseEvent<HTMLElement>) => void;
export type KeyboardHandler = (event: React.KeyboardEvent<HTMLElement>) => void;
export type FocusHandler = (event: React.FocusEvent<HTMLElement>) => void;
