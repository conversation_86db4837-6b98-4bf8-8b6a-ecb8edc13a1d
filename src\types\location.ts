// TypeScript types for location pages and components

export interface LocationHeroData {
  title: string;
  subtitle: string;
  introduction1: string;
  introduction2?: string;
  introduction3?: string;
  imageUrl?: string;
  ctaText?: string;
  ctaLink?: string;
}

export interface LocationContactData {
  address: {
    street: string;
    suburb: string;
    state: string;
    postcode: string;
  };
  phone: string;
  email: string;
  hours: {
    weekdays: string;
    note?: string;
  };
  consultingHours?: {
    title: string;
    details: string;
    appointmentNote: string;
    urgentNote?: string;
  };
  appointmentProcess?: {
    title: string;
    details1: string;
    details2: string;
  };
}

export interface LocationMapData {
  embedUrl: string;
  title: string;
  transportOptions: {
    publicTransport: string;
    car: string;
  };
  gettingHereTitle?: string;
  publicTransportTitle?: string;
  carTitle?: string;
}

export interface LocationFacility {
  icon: React.ReactNode;
  title: string;
  description: string;
}

export interface AdditionalFacility {
  title: string;
  description: string;
  additionalInfo?: string;
}

export interface GalleryImage {
  src: string;
  alt: string;
}

export interface LocationFacilitiesData {
  title: string;
  subtitle: string;
  description: string;
  facilities: LocationFacility[];
  additionalFacilities?: AdditionalFacility[];
  gallery?: {
    title: string;
    description: string;
    images: GalleryImage[];
  };
}

export interface LocationAmenitiesData {
  title: string;
  description: string;
  locationDetails: string;
  medicalFacilities: {
    title: string;
    description: string;
    additionalInfo: string;
  };
  surroundingAmenities: {
    title: string;
    description: string;
    additionalInfo: string;
  };
  transportation: {
    title: string;
    items: string[];
  };
  parking: {
    title: string;
    description: string;
    additionalInfo: string;
  };
  images: Array<{
    src: string;
    alt: string;
  }>;
}

export interface AmenityItem {
  name: string;
  description: string;
}

export interface AmenityCategory {
  title: string;
  items: AmenityItem[];
}

export interface NearbyAmenitiesData {
  title: string;
  subtitle: string;
  description: string;
  categories: AmenityCategory[];
}

export interface Hospital {
  title: string;
  description: string;
  address: string;
  phone: string;
  website: string;
  websiteText: string;
  image: {
    src: string;
    alt: string;
  };
}

export interface NearbyHospitalsData {
  title: string;
  subtitle: string;
  description: string;
  hospitals: Hospital[];
}

export interface ConsultingLocation {
  title: string;
  description: string;
  address: string;
  phone: string;
  viewDetailsText: string;
  viewDetailsLink: string;
  image: {
    src: string;
    alt: string;
  };
}

export interface OtherConsultingLocationsData {
  title: string;
  subtitle: string;
  description: string;
  locations: ConsultingLocation[];
}

export interface LocationPricingContactData {
  title: string;
  description: string;
  contactInfo: {
    address: {
      line1: string;
      line2: string;
    };
    email: string;
    phone: string;
  };
  contactDescription: string;
  schedulingTitle: string;
  schedulingDescription: string;
  schedulingButtonText: string;
  schedulingButtonLink: string;
}

export interface TherapeuticIntervention {
  title: string;
  description: string;
  icon: React.ReactNode;
}

export interface TherapeuticInterventionsData {
  title: string;
  subtitle: string;
  description: string;
  interventions: TherapeuticIntervention[];
}

export interface InsuranceCategory {
  title: string;
  description1?: string;
  description2?: string;
  description3?: string;
  tac?: string;
  workCover?: string;
  veteranAffairs?: string;
}

export interface InsuranceAndFundingData {
  title: string;
  subtitle: string;
  categories: InsuranceCategory[];
}

export interface ConditionCategory {
  title: string;
  conditions: string[];
}

export interface ConditionsTreatedData {
  title: string;
  subtitle: string;
  description: string;
  categories: ConditionCategory[];
}

export interface LocationCTAData {
  title: string;
  description: string;
  buttons: Array<{
    text: string;
    link: string;
    variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'muted';
  }>;
}

export interface CompleteLocationData {
  hero: LocationHeroData;
  contact: LocationContactData;
  map: LocationMapData;
  facilities: LocationFacilitiesData;
  amenities: LocationAmenitiesData;
  therapeuticInterventions?: TherapeuticInterventionsData;
  nearbyAmenities: NearbyAmenitiesData;
  conditionsTreated?: ConditionsTreatedData;
  nearbyHospitals: NearbyHospitalsData;
  otherLocations: OtherConsultingLocationsData;
  insuranceAndFunding?: InsuranceAndFundingData;
  pricingContact?: LocationPricingContactData;
  cta: LocationCTAData;
}
